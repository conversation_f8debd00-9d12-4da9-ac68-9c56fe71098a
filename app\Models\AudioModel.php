<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class AudioModel extends Model
{
    protected $table = 'audio';
	protected $allowedFields = ['title', 'slug', 'audio'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_audio($start = 0, $limit = 10, $search_term = NULL, $order = "audio.created_at DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND audio.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT *
                            FROM audio
                            WHERE audio.deleted_at IS NULL
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($id){
        $data = $this->query("SELECT *
                            FROM audio
                            WHERE audio.deleted_at IS NULL
                            AND audio.id = " . $id .  "
                        ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}
}