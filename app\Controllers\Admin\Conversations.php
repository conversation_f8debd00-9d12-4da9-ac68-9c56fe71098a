<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Conversations extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ConversationsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_conversations'] = $this->model->all_conversations_admin();
        // $data['conversations_count'] = $this->model->where(['author' => 'admin'])->countAllResults();

        echo view('admin/conversations/index_view', $data);
    }
    public function mark_as_seen()
    {
        $SubscribersConversationsModel = model('SubscribersConversationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        $save_message_seen = array('id' => $data['id'], 'admin_seen' => 1);

        $response['admin_seen'] = $SubscribersConversationsModel->where(["id" => $data['id'], 'admin_seen' => 1])->first();
        if(empty($response['admin_seen'])){
            $response['success'] = $SubscribersConversationsModel->save($save_message_seen);
        }

        return $this->respond($response);
    }

    public function search_message()
    {
        $ConversationsModel = model('ConversationsModel');
        $request = service('request');
        $data = $request->getPost();

        $response['conversation'] = $ConversationsModel->admin_conversations_search($data['search_term']);
        $response['html'] = view('admin/conversations/ajax-conversation-search', $response);

        return $this->respond($response);
    }
    public function new_message()
    {
        $SubscribersConversationsModel = model('SubscribersConversationsModel');
        $NotificationsModel = model('NotificationsModel');
        $ConversationsModel = model('ConversationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        
        if($data['conversation_id'] == 0){
            $new_conversation = $ConversationsModel->save(['subscriber_id' => $data['receiver_id']]);
            $data['conversation_id'] = $ConversationsModel->getInsertID();
        }
        $files = $this->request->getFiles();
        if (isset($files['file']) AND $files['file']->isValid()){
            $file = $files['file'];
            $response['ClientExtension'] = $file->getClientExtension();
            $response['guessExtension'] = $file->guessExtension();
            $response['getClientMimeType'] = $file->getClientMimeType();
            $response['getMimeType'] = $file->getMimeType();
            $name = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/conversations', $name);

            $uploaded_file = 'uploads/conversations/' . $name;
        }else{
            $uploaded_file = '';
        }

        if($uploaded_file != ''){
            $data['message'] = $uploaded_file;
        }
        
        $save_message_useful = array(
            'conversation_id' => $data['conversation_id'],
            'sender_id' => $data['sender_id'],
            'receiver_id' => $data['receiver_id'],
            'message' => $data['message'],
            'type' => $data['type'],
            'file' => $uploaded_file,
            'file_name' => isset($data['file_name']) ? $data['file_name'] : '',
        );

        if(!empty($data['message'])){
            $response['success'] = $SubscribersConversationsModel->save($save_message_useful);
            $response['message'] = $data['message'];
            $response['file'] = $uploaded_file;
            $response['type'] = $data['type'];
            $response['file_name'] = isset($data['file_name']) ? $data['file_name'] : '';
            $response['date'] = date('m/d/Y');
            $response['initials'] = user_avatar($data['sender_id']) != NULL ? '<img src="' . user_avatar($data['sender_id']) . '" class="img-fluid" />' : user_initials($data['sender_id']);

            $notification_data = array(
                'content'   => 'You have a new response from Sebastien.',
                'link'      => base_url() . '/asksebastien',
                'author'    => 'system',
                'subscriber_id' => $data['receiver_id'],
                'type' => 'new_conversation_notif',
                'date'    => date('Y-m-d H:i:s')
            );
            $response['notification_saved'] = $NotificationsModel->save($notification_data);

        }else{
            $response['msg'] = 'Please enter your message';
        }

        return $this->respond($response);
    }

    public function get_users_conversations($conversation_id = 0)
    {
        if($conversation_id != 0){
            $conversation = $this->model->where(['id' => $conversation_id])->first();
            $response['conversation'] = $this->model->query("SELECT subscribers_conversations.*,
                                                                        IF(subscribers_conversations.sender_id = 0, 'seb', CONCAT(subscribers.firstname, ' ', subscribers.lastname)) AS user_name
                                                                        FROM subscribers_conversations
                                                                        LEFT JOIN subscribers ON (subscribers.id = subscribers_conversations.sender_id AND subscribers_conversations.sender_id != 0)
                                                                        WHERE conversation_id = " . $conversation_id . "
                                                                        AND hide = 0
                                                                        ORDER BY subscribers_conversations.date asc
                                                                    ")->getResultArray();

            $response['html'] = view('admin/conversations/ajax-conversation-single', $response);
            $response['conversation_user_id'] = (int)$conversation['subscriber_id'];
        }else{
            $response['conversation'] = [];
            $response['conversation_user_id'] = 0;
        }
        return $this->respond($response);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';

            if(isset($data['date']) AND Time::createFromFormat("m/d/Y H:i:s" , $data['date'], 'America/Los_Angeles'))
            {
                $tmp = Time::createFromFormat("m/d/Y H:i:s" , $data['date'], 'America/Los_Angeles');
                $data['date'] = $tmp->toDateTimeString('Y-m-d H:i:s');
            }

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}