<footer>
    <div class="container">
        <div class="row footer-top-row aic">
            <div class="foo-first col-8 flex aic jcl white">
                <img src="images/lod-logo-footer.svg" alt="" class="mr-2 lagree-method-footer" style="width: 70px !important;">
                <span class="medium line-height-normal f-14-mob">The official Lagree workouts <br class="mobile">on-demand platform.</span>
            </div>
            <div class="foo-selling col-4 flex aic jcr showdesk">
            <a href="/subscribe" class="btn">subscribe now</a>
				 <!--<div class="footer-social showmob">
                    <?php if(isset($settings['facebook']) AND $settings['facebook'] != ''){ ?>
                    <a href="<?php echo $settings['facebook']; ?>" class="fb" title="lagree facebook" target="_blank"><img src="images/footer-facebook.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['instagram']) AND $settings['instagram'] != ''){ ?>
                    <a href="<?php echo $settings['instagram']; ?>" class="ig" title="lagree instagram" target="_blank"><img src="images/footer-instagram.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['tiktok']) AND $settings['tiktok'] != ''){ ?>
                    <a href="<?php echo $settings['tiktok']; ?>" class="tt" title="lagree tiktok" target="_blank"><img src="images/footer-tiktok.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['youtube']) AND $settings['youtube'] != ''){ ?>
                    <a href="<?php echo $settings['youtube']; ?>" class="yt" title="lagree youtube" target="_blank"><img src="images/footer-youtube.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                </div>-->
            </div>
        </div>
        <div class="foo-nav row flex-row">
            <div class="col-3 col-mob-6 footcol-one">
            <h6 class="footer-menu-title">Classes</h6>
                <ul class="mb-5 foomenulist">
                    <li><a href="/classes/micro" class="" title="Micro Classes">Micro Classes</a></li>
                    <li><a href="/classes/mini" class="" title="Mini Classes">Mini Classes</a></li>
                    <li><a href="/classes/minipro" class="" title="Mini Pro Classes">Mini Pro Classes</a></li>
                    <li><a href="/classes/mega" class="" title="Mega Classes">Mega Classes</a></li>
                    <li><a href="/classes/megapro" class="" title="Mega Classes">Mega Pro Classes</a></li>
                    <li><a href="/classes/evo" class="" title="Evo Classes">Evo Classes</a></li>
                    <li><a href="/classes/evo2" class="" title="Evo 2 Classes">Evo 2 Classes</a></li>
                    <li><a href="/classes/classes" class="" title="All Classes">View All</a></li>
                </ul>
            </div>
            <div class="col-3 col-mob-6 footcol-one">
            <h6 class="footer-menu-title">Exercises</h6>
                <ul class="mb-5 foomenulist">
                    <li><a href="/exercises/micro" class="" title="Micro Exercises">Micro Exercises</a></li>
                    <li><a href="/exercises/mini" class="" title="Mini Exercises">Mini Exercises</a></li>
                    <li><a href="/exercises/minipro" class="" title="Mini Pro Exercises">Mini Pro Exercises</a></li>
                    <li><a href="/exercises/mega" class="" title="Mega Exercises">Mega Exercises</a></li>
                    <li><a href="/exercises/megapro" class="" title="Mega Pro Exercises">Mega Pro Exercises</a></li>
                    <li><a href="/exercises/evo" class="" title="Evo Exercises">Evo Exercises</a></li>
                    <li><a href="/exercises/evo2" class="" title="Evo 2 Exercises">Evo 2 Exercises</a></li>
                    <li><a href="/exercises/" class="" title="View All">View All</a></li>

                </ul>
            </div>
            <div class="col-3 col-mob-6 footcol-one">
            <h6 class="footer-menu-title">Help Center</h6>
                <ul class="mb-5 foomenulist">
                    <li><a href="/what-is-lod" class="" title="What is LagreeOD">What is LagreeOD</a></li>
                    <li><a href="/help-center" class="" title="Help Center / FAQs">Help Center / FAQs</a></li>
                    <li><a href="https://www.lagreefitness.com/machine-comparison" class="" target="_blank" title="Machine Comparison">Machine Comparison</a></li>
                    <li><a href="https://www.lagreefitness.com/class-comparison" class="" target="_blank" title="Machine Comparison">Class Comparison</a></li>
                    <?php if(!isset($logged_user)){ ?>
                    <li><a href="javascript:;" class="" title="My account" data-popup="login-popup" onclick="$('[name=return_url]').val('/account')">My Account</a></li>
                    <?php }else{ ?>
                    <li><a href="/account" class="" title="My account">My Account</a></li>
                    <?php } ?>
                    <li><a href="/contact-us" class="" title="Contact Us">Contact Us</a></li>
                    <li><a href="/privacy-policy" class="" title="Privacy Policy">Privacy Policy</a></li>
                </ul>
            </div>
            <div class="col-3 col-mob-6 footcol-one">
            <h6 class="footer-menu-title">Lagree Shop</h6>
                <ul class="mb-5 foomenulist">
                    <li><a href="https://www.lagreeshop.com/category/machines" target="_blank">Machines</a></li>
                    <li><a href="https://www.lagreeshop.com/category/accessories" target="_blank">Accessories</a></li>
                    <li><a href="https://www.lagreeshop.com/category/lagree-to-go" target="_blank">Lagree To-Go</a></li>
                    <li><a href="https://www.lagreeshop.com/category/apparel" target="_blank">Apparel</a></li>
                    <li><a href="https://www.lagreeshop.com/category/new-arrivals" target="_blank">New Arrivals</a></li>
                    <li><a href="https://www.lagreeshop.com/category/events-workshops" target="_blank">Events & Workshops</a></li>
                    <li><a href="https://www.lagreeshop.com/category/supplements" target="_blank">Supplements</a></li>
                </ul>
            </div>
        </div>


        <!-- <div class="reverseonmob">
        <div class="foot-lftrgt foo-second">
            <div class="footbtm-left">
                <p><a href="/what-is-lod">First time here? Learn more about LagreeOD</a></p>
            </div>
            <div class="footbtm-right showdesk signup-foo">
                <p class="showdesk">SIGN UP TO GET THE LATEST NEWS <a onclick="window._klOnsite = window._klOnsite || []; window._klOnsite.push(['openForm', 'WDA8rM']);" class="btn">SIGN UP</a></p>
                <p class="showmob"><a onclick="window._klOnsite = window._klOnsite || []; window._klOnsite.push(['openForm', 'WDA8rM']);"  class="custom-btn">SIGN UP FOR THE LATEST NEWS</a></p>
            </div>
        </div>

        <div class="showmob mobfoo-btn">
        <a onclick="window._klOnsite = window._klOnsite || []; window._klOnsite.push(['openForm', 'WDA8rM']);" class="btn white-bg black mr-3">SIGN UP FOR THE LATEST NEWS</a>
        </div>
    </div>-->

    <div class="showmob mobfoo-btn">
        <a href="/subscribe" class="btn white-bg black mr-3">SUBSCRIBE NOW</a>
        </div>

        <div class="row foo-bottom py-120 hubcol">
            <div class="col-6 flex aic jcl line-height-small px-mob-3 foo-hub">
            <div class="lghub">
                <span class="white f-1">Lagree HUB<!--<sup style="top: -4px;">TM</sup>-->: </span>
                <a href="javascript:;" class="white flex aic jcl ml-1" data-popup="hub-popup">
                    <img class="hublogo" src="images/lod-logo-footer.svg" alt="" class="mr-05" height="30">
                    <img src="images/arrow-down.svg" alt="" class="" style="filter: invert(1);width: 8px;">
                </a>
            </div>
            </div>
            <div class="col-6 flex aic jcr px-mob-3">
                <span class="f-12 white copyspan">© LagreeOD <?php echo date("Y"); ?> by Lagree Fitness. <a class="mobprivacy" href="https://www.lagreeod.com/privacy-policy">Privacy</a></span>
                <!--<div class="footer-social showdesk">
                    <?php if(isset($settings['facebook']) AND $settings['facebook'] != ''){ ?>
                    <a href="<?php echo $settings['facebook']; ?>" class="fb" title="lagree facebook" target="_blank"><img src="images/footer-facebook.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['instagram']) AND $settings['instagram'] != ''){ ?>
                    <a href="<?php echo $settings['instagram']; ?>" class="ig" title="lagree instagram" target="_blank"><img src="images/footer-instagram.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['tiktok']) AND $settings['tiktok'] != ''){ ?>
                    <a href="<?php echo $settings['tiktok']; ?>" class="tt" title="lagree tiktok" target="_blank"><img src="images/footer-tiktok.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                    <?php if(isset($settings['youtube']) AND $settings['youtube'] != ''){ ?>
                    <a href="<?php echo $settings['youtube']; ?>" class="yt" title="lagree youtube" target="_blank"><img src="images/footer-youtube.svg" alt="" class="img-fluid" /></a>
                    <?php } ?>
                </div> -->
            </div>
        </div>
    </div>

	<!-- <div id="bnavwrap" class="bottom-nav">
	  <div class="b-nav-box"><a href="/classes"><img class="noactiveicon" src="images/icon1.svg"><img class="activeicon" src="images/icon1-active.svg"><p>Classes</p></a></div>
	  <div class="b-nav-box"><a href="/exercises"><img class="noactiveicon" src="images/icon2.svg"><img class="activeicon" src="images/icon2-active.svg"><p>Exercises</p></a></div>
	  <div class="b-nav-box"><a href="/collections"><img class="noactiveicon" src="images/icon3.svg"><img class="activeicon" src="images/icon3-active.svg"><p>Collections</p></a></div>
	  <div class="b-nav-box"><a href="/shop"><img class="noactiveicon" src="images/icon4.svg"><img class="activeicon" src="images/icon4-active.svg"><p>Shop</p></a></div>
	  <div class="b-nav-box" onclick="$('.chat-window').addClass('active')"><a class="noclick" href="#"><img class="noactiveicon" src="images/icon5.svg"><img class="activeicon" src="images/icon5-active.svg"><p>Help</p></a></div>
	</div> -->

</footer>
<script src="js/cookie.js"></script>
<!-- <script async type="text/javascript" src="https://static.klaviyo.com/onsite/js/klaviyo.js?company_id=UfyZcp"></script> -->

<?php echo isset($settings['footer_js']) ? $settings['footer_js'] : ''; ?>

<?php
if(isset($logged_user) AND session('finished_survey') == NULL AND session('active_survey') != NULL){
?>
<script>
setTimeout(function(){
    load_active_survey()
},2000);
function load_active_survey(){
    $.ajax({
        type: 'POST',
        url: 'surveys/active_survey',
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                $('body').addClass('show-popup');
                $('.survey-popup').addClass('show');
                $('.survey-popup .popup-body').html(data.html);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function cancel_survey(user_id = 0, survey_id = 0){
    if(user_id != 0 && survey_id != 0){
        $.ajax({
            type: 'POST',
            url: 'surveys/cancel_survey',
            data: {
                user_id,
                survey_id
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
function single_answer(xx){
    var type = xx.data('type');
    var last = xx.hasClass('last_question');
    if(type == 'check'){
        xx.toggleClass('selected');
    }else if (type == 'radio'){
        xx.closest('.answers_container').find('.single_answer').removeClass('selected');
        xx.addClass('selected');
        xx.closest('.single-questions').hide().next().show();
        process_answer(xx);
        if(last){
            process_answer(xx, 'finish');
        }
    }
};
var all_answers = {};
function process_answer(xx, finish = ''){
    var question = xx.closest('.single-questions');
    var error = 0;
    var type = question.data('type');
    var question_id = question.data('question_id');
    
    if(type == 'check'){
        var answer_val = [];
        if(question.find('.check_answer.selected').length > 0){
            question.find('.check_answer.selected').each(function(i, elem){
                var answer_id = $(elem).data('answer_id');
                answer_val.push($(elem).text() + '//' + answer_id);
            });
            question.find('input').val(JSON.stringify(answer_val));
            console.log('question ' + question_id + ': ' + answer_val);
            all_answers[question_id] = JSON.stringify(answer_val);
        }else{
            if(question.data('mandatory') == 1){
                console.log('check error');
                app_msg('Please select answer');
                error += 1;
            }
        }
    }else if(type == 'radio'){
        var answer_val = [];
        var answer_id = question.find('.radio_answer.selected').data('answer_id');
        answer_val.push(question.find('.radio_answer.selected').text() + '//' + answer_id);
        question.find('input').val(JSON.stringify(answer_val));
        console.log('question ' + question_id + ': ' + question.find('.radio_answer.selected').text() + '//' + answer_id);
        all_answers[question_id] = JSON.stringify(answer_val);
    }else if(type == 'text'){
        if(question.find('textarea').val() != ''){
            question.find('input').val(question.find('textarea').val());
            console.log('question ' + question_id + ': ' + question.find('textarea').val());
            all_answers[question_id] = question.find('textarea').val();
        }else{
            console.log('check error');
            console.log('textarea error or empty');
            if(question.data('mandatory') == 1){
                app_msg('Please write your answer');
                error += 1;
            }
        }
    }        
    if(!question.hasClass('last_question') && error == 0){
        xx.closest('.single-questions').hide().next().show();
    }
    console.log("all_answers: ", all_answers);
    if(finish != '' && error == 0){
        var form = $('#survey_user_form');
        var url = form.attr("action");
        
        $.ajax({
            type: 'POST',
            url: url,
            data: {
                all_answers
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $('.survey-thank-you').addClass('show');
                    $('.survey-popup').removeClass('show');
                }
                if(data.login_required){
                    app_msg(data.login_required);
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
</script>
<?php
}else{
    if($settings['show_popup'] == 1){
        if(!isset($_COOKIE[$settings['cookie_name']])) {
?>
            <style>
                .cookie-popup {
                    max-width: 550px !important;
                    width: 100% !important;
                }
                .popup-body-cookie {
                    padding: 50px;
                    text-align: center;
                }
                .cookie-popup p {
                    font-weight: 300 !important;
                }
                .cookie-popup .close {
                    padding: 15px !important;
                    color: #dfdfdf;
                }
                .cookie-popup .close:hover {
                    color: #000;
                }
                .cookie-popup-image img {
                    max-height: 50vh;
                    width: 100%;
                    object-fit: cover;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                }

                @media(max-width: 480px){
                    body.show-popup .overlay .popup.p-0.cookie-popup {
                        max-width: 550px !important;
                        width: 96% !important;
                        left: 2% !important;
                        transform: none !important;
                    }

                    body.show-popup .overlay .cookie-popup.show,
                    body .overlay .cookie-popup {
                        transform: none !important;
                        width: 96% !important;
                    }
                    .popup-body-cookie {
                        padding: 40px;
                        text-align: center;
                    }
                }
            </style>
            <script>
                if(readCookie('<?php echo isset($settings['cookie_name']) ? $settings['cookie_name'] : ''; ?>') == null){
                    setTimeout(function(){
                        // close_all();
                        document.querySelector('body').classList.add('show-popup');
                        document.querySelectorAll('.cookie-popup')[0].classList.add('show');
                        createCookie('<?php echo isset($settings['cookie_name']) ? $settings['cookie_name'] : ''; ?>', 'opened', 1);
                    },2000);
                    console.log('RADI COOKIE');
                }
            </script>

<?php
        }
    }
}
?>