$(document).ready(function(){
	$(".loading").click(function(){
		$("#big-load").fadeIn();
	});

	$(".open-course-lection").click(function(){
		var id = $(this).data("lection-id");
		var $this_url = $(this);
		console.log("Lecture: " + id);
		$.ajax({
			type: "POST",
			url: base_url + "courses/get_lection",
			data: {lection_id : id},
			success: function(data) {
				var template = _.template($("#lection-details-tpl").html());
				if(data.mark){
					if ($this_url.find("span").hasClass("watched") == false) {
						$this_url.find("span").addClass("watched");
					}
				}
				if(data.media.videos.length > 0){
					data.height = 460;
				}
				else{
					data.height = 750;
				}
				$(".course-lection-content").html(template(data));

				$('#tabwrap').basicTabs();

				$('.scrolls').jScrollPane({
					animateScroll: true,
					autoReinitialise: true
				});

				$(".video-lection-course").each(function(index){
					var $this = $(this);
					jwplayer("" + $this.attr("id")).setup({
						file: $this.data("video"),
						aspectratio: "24:10",
						autostart: false,
						repeat: false,
						width: "100%",
						events: {
							onComplete : function(){
								$.ajax({
									type: "POST",
									url: base_url + "courses/mark_as_watched",
									data: {lection_id: $("#lection-id-video").val()},
									success: function(data) {
										var lection_id = $("#lection-id-video").val();
										var $this = $("#lection-video-" + lection_id);
										if ($this.find("span").hasClass("watched") == false) {
											$this.find("span").addClass("watched");
										}
									},
									error: function(xhr, textStatus, error) {
									},
									dataType: "json",
									cache: false
								});
							}
						}
					});
				});
				$("#big-load").fadeOut();
			},
			error: function(xhr, textStatus, error) {
			},
			dataType: "json",
			cache: false
		});
	});
	$(".open-course-lection:first").trigger("click");

	$('.fancybox').fancybox({
		nextClick : true,
		helpers: {
			overlay: {
				locked: false
			}
		}
	});
});