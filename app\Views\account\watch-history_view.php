<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="favs-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root" class="watchlater-wrap">
    <section class="py-0">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="title-filter">
                        <h2 class="f-24 semibold line-height-small text-uppercase inner-title mt-1 mb-1 mt-mob-0 mb-mob-0">watch history</h2>
                        <!--<a class="btn greyborder" href="#">CLEAR</a>-->
                    </div>
                </div>
            </div>
    </section>
<?php if(isset($all_watched) AND is_array($all_watched) AND count($all_watched) > 0){ ?>
    <section class="pt-0 watch-hist-section">
        <div class="container-fluid">
            <div class="row watch-hist-wrap">
            <?php
            $prev_date = '';
            foreach($all_watched as $single){
                if($prev_date != $single['date_watched']){
                    $prev_date = $single['date_watched'];
                    $data['new_date'] = TRUE;
                }else{
                    $data['new_date'] = FALSE;
                }
                $data['single'] = $single;
                echo view('front/classes/single_class_table.php', $data);
            }
            ?>
            </div>
            <?php if(count($pager->links()) > 1){ ?>
            <div class="row">
                <div class="col-12">
                    <?php $pager->setSurroundCount(2) ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                        <?php if ($pager->hasPrevious()){ ?>
                            <li>
                                <a href="<?= $pager->getPrevious() ?>" aria-label="<?= lang('Pager.previous') ?>">
                                    <span aria-hidden="true">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 24.7 40">
                                            <path id="Path_4917" data-name="Path 4917" d="M35.3,24.7,20,9.433,4.7,24.7,0,20,20,0,40,20Z" transform="translate(0 40) rotate(-90)" fill="#969696" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        <?php } ?>

                        <?php foreach ($pager->links() as $link): ?>
                            <li <?= $link['active'] ? 'class="active"' : '' ?>>
                                <a href="<?= $link['uri'] ?>">
                                    <?= $link['title'] ?>
                                </a>
                            </li>
                        <?php endforeach ?>

                        <?php if ($pager->hasNext()){ ?>
                            <li>
                                <a href="<?= $pager->getNext() ?>" aria-label="<?= lang('Pager.next') ?>">
                                    <span aria-hidden="true">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 24.7 40">
                                            <path id="Path_4916" data-name="Path 4916" d="M470.3,1414.3,455,1429.562,439.7,1414.3,435,1419l20,20,20-20Z" transform="translate(-1414.295 475) rotate(-90)" fill="#969696" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        <?php } ?>
                        </ul>
                    </nav>
                </div>
            </div>
            <?php } ?>
        </div>
    </section>
<?php }else{ ?>
    <section class="pt-0 watch-hist-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="top-border py-4 f-14 midGray line-height-small">This list has no videos.</div>
                </div>
            </div>
        </div>
    </section>
<?php } ?>
</main>

<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
// $(document).ready(function(){
//     setTimeout(function(){
        // to_timezone()
//     }, 1000);
// });
$('[data-time]').each(function(){
    var time = $(this).data('time');
    if(time != ''){
        $(this).find('.video-container').prepend('<span class="current_video_state" style="width: ' + time + '%"></span>');
    };
});

</script>
</body>
</html>