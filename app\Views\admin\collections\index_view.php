<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.set_sort_byy.selected {
	color: #000 !important;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Collections</h1>
                <a href="admin/collections/edit" class="btn black-bg white ml-auto" title="Upload Class">New Collection</a>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $collections_count == 1 ? $collections_count . ' Collection' : $collections_count . ' Collections'; ?></h5>
                <div class="flex aic jcsb">
                <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/collections/sort_by/collections.created_at/desc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Date Added' ? 'selected' : ''; ?>" title="">Date Added</a></li>
                        <li><a href="admin/collections/sort_by/collections.title/asc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Ascending' ? 'selected' : ''; ?>" title="">Ascending</a></li>
                        <li><a href="admin/collections/sort_by/collections.title/desc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Descending' ? 'selected' : ''; ?>" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/collections/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            </div>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="collections" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <!--<form class="edit_checked" style="display: none" method="post" action="admin/classes/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-12 link flex aic edit_bulk midGray" style="background: #fff !important;">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>-->
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders sortable">
<?php
$c=0;
foreach($all_collections as $single){
$c++;
$classes = (isset($single['selected_classes']) AND $single['selected_classes'] != '') ? json_decode($single['selected_classes']) : FALSE;
?>
                        <div class="table-row" data-rowid="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/collections/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ''; ?>" alt="" class="img-fluid" style="max-width: 210px;max-height: 120px;width: 210px;object-fit: cover;height: 120px;" /></a>
                                <div class="flex flex-column normal">
                                    <a href="admin/collections/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title mb-05 medium flex aic"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-12"><?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] + ((isset($single['howtoCount']) AND $single['howtoCount'] != '') ? $single['howtoCount'] : 0) : 0; ?> videos</span>
                                    <div class="row-actions f-1">
                                        <a href="admin/collections/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="collections">Delete</a>-->
                                    </div>
                                </div>
                                <div class="flex aic jcr ml-auto table-options">
                                    <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle"></span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($collections_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_collections); ?><span class="midGray mx-1">of <?php echo $collections_count; ?></span>
                    <a href="admin/collections/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/collections/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_collections) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_collections)) == $collections_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>


<script>
//should add the class to active link in "Date Added" section on index page - it doesn't work???
$(document).ready(function() {
var text = $(".dropdown-button").text();
$('.dropdown-menu li a.link:contains("' + text + '")').addClass('black-active');
});
</script>

<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
</script>
</body>
</html>