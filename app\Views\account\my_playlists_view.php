<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="collection-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
 
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb">
        <div class="playlist-wrapper">
            <div class="row mx-0">
                <div class="col-12 px-0">
                    <div class="playlist-main-title">
                        <h2 class="f-24 flex aic jcsb mob-w10 line-height-small semibold">MY PLAYLISTS</h2>
                    </div>
                    <div class="nrplaylists">
                        <p><span class="playlists-count"><?php echo count($all_playlists); ?></span> Playlist<?php echo count($all_playlists) < 2 ? '' : 's'; ?> </p>
                    </div>
                </div>
                <div class="col-12 px-0">
                    <div class="pb-5">
                        <?php 
                        // echo '<pre>';
                        // print_r($all_playlists);
                        // die();
                        ?>
<?php foreach($all_playlists as $single){ ?>
                        <div class="single-user-playlist" data-playlist_id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-user-playlist-content">
                                <div class="row aic">
                                    <div class="col-12 flex">
                                        <div class="playlist-img" <?php echo (isset($single['image']) AND $single['image'] != '') ? '' : ''; ?>>
                                            <?php
                                                if(isset($single['all_playlists_classes'][0]['type']) AND ($single['all_playlists_classes'][0]['type'] != '' OR $single['all_playlists_classes'][0]['type'] != NULL)){
                                                    $url = base_url() . '/playlists/' . $single['slug'] . '/' . ($single['all_playlists_classes'][0]['type'] == 'classes' ? 'c_' : '') .  $single['all_playlists_classes'][0]['slug'];
                                                    $popup = '';
                                                }else{
                                                    $url = 'javascript:;';
                                                    $popup = 'onclick="app_msg(\'Playlist is empty\')"';
                                                }
                                            ?>
                                            <a href="<?php echo $url ?>" <?php echo $popup; ?>><img src="images/playlist0.svg" alt="" class="img-fluid" /></a>
                                        </div>
                                        <div class="w100 position-relative pl-3 flex flex-column ail jcc pl-mob-2">
                                            <div class="playlist-option dropdown">
                                                <span class="option-btn" data-dropdown>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                                        <g id="option-icon" transform="translate(-1501 -625)">
                                                            <g id="Rectangle_2141" data-name="Rectangle 2141" transform="translate(1501 625)" fill="none" stroke="#f0f0f0" stroke-width="1" style="fill: #fff;">
                                                                <rect width="30" height="30" rx="15" stroke="none"/>
                                                                <rect x="0.5" y="0.5" width="29" height="29" rx="14.5" fill="none"/>
                                                            </g>
                                                            <circle id="Ellipse_50" data-name="Ellipse 50" cx="1" cy="1" r="1" transform="translate(1511 639)"/>
                                                            <circle id="Ellipse_51" data-name="Ellipse 51" cx="1" cy="1" r="1" transform="translate(1515 639)"/>
                                                            <circle id="Ellipse_52" data-name="Ellipse 52" cx="1" cy="1" r="1" transform="translate(1519 639)"/>
                                                        </g>
                                                    </svg>
                                                </span>
                                                <ul class="dropdown-menu drop-right">
                                                    <?php if($single['ownership'] == 'my'){ ?>
                                                        <li><a href="javascript:;" class="f-12" data-popup="edit-playlist-popup" data-popup-title="Edit Playlist" onclick="load_playlist(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" title="Edit playlist">Rename playlist</a></li>
                                                        <li><a href="javascript:;" class="f-12 delete_record" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="playlists" data-popup="delete-popup" title="Delete">Delete</a></li>
                                                    <?php }else{ ?>
                                                        <li><a href="javascript:;" class="f-12 remove_playlist" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" title="Remove From My List">Remove from my list</a></li>
                                                    <?php } ?>
                                                </ul>
                                            </div>
                                            <h2 class="f-12 mb-1 line-height-small pr-4"><a href="<?php echo $url ?>" <?php echo $popup; ?> class="line-height-small"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a></h2>
                                            <p class="f-12 line-height-small mb-0 mt-0 midGray"><?php echo count($single['all_playlists_classes']); ?> Video<?php echo count($single['all_playlists_classes']) > 1 ? 's' : ''; ?> (<?php echo only_minutes($single['total_duration']); ?> minutes)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
<?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
</body>
</html>