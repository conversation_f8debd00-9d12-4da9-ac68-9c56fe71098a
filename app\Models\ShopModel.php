<?php namespace App\Models;

use CodeIgniter\Model;

class ShopModel extends Model
{
    protected $table = 'shop';
	// protected $allowedFields = [];
	protected $returnType     = 'array';

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[events.slug,id,{id}]',
        // 'content'     => 'required',
        'template'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function get_cart(){
        $data = $this->query("SELECT * FROM blog_categories WHERE blog_categories.deleted_at IS NULL AND status = 0 ORDER BY sort ASC")->getResultArray();

        return $data;
    }

    public function all_posts_category($start = 0, $limit = 0, $category = 0){
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        $data = $this->query("SELECT blog.*, blog_categories.title AS category, blog_categories.slug AS category_slug
                            FROM blog
                            LEFT JOIN blog_categories ON blog_categories.id = blog.category
                            WHERE blog.deleted_at IS NULL
                            AND blog.category = '" . $category . "'
                            GROUP BY blog.id
                            ORDER BY date desc
                            " . $limit_size . "
                        ")->getResultArray();

        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}