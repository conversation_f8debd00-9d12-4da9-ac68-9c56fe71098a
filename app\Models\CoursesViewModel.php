<?php namespace App\Models;

use CodeIgniter\Model;

class CoursesViewModel extends Model
{
    protected $table = 'courses_videos_views';
	protected $allowedFields = ['course_video_id', 'user_id', 'date'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}