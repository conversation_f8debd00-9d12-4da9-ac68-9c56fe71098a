@import url("base.css");
@import "include-media";

// colors
$white: #fff;
$lightGray: #f8f8f8;
$gray: #f0f0f0;
$borderGray: #DDDDDD;
$lightText: #bcbcbc;
$midGray: #969696;
$darkGray: #333;
$red: #000000;
$darkRed: #BF2828;
$normalRed: #db1818;
$newRed: #ff0000;
$yellow: #F8C158;
$green: #2C864F;
$textGreen: #52C15A;
$lightGreen: rgb(83, 193, 90);
$lightGreenBg: rgba(83, 193, 90, 0.15);
$blue: rgb(33, 89, 244);
$lightBlueBg: rgba(33, 89, 244, 0.15);
$lightRedBg: rgba(220, 24, 24, 0.15);
$black: #000;

.tw-color {color: #4EA5D9}
.fb-color {color: #4267B2}
.fb-bg-color {background-color: #4267B2}
.ln-color {color: #0077B5}
.ig-color {color: #2D333B}
.yt-color {color: $red}

.social-link {
    display: block;
}

//static variables
$header: 100px;
$gap: 20px;
$bigGap: 150px;
$fontSize: 16px;
$fieldHeight: 60px;
$border2x: 2px solid $gray;
$border: 1px solid $gray;
$borderNew: 1px solid $borderGray;
$borderLight: 1px solid $lightGray;
$borderRadius: 5px;

$transition1: all 0.25s ease-in-out 0s;
$transition2: all 0.4s cubic-bezier(.13,.56,.38,.89) 0s;

.transition1 { transition: $transition1};
.transition2 { transition: $transition2};
/* scrollbar */
* {
    scrollbar-color: #000 #F0F0F0;
    scrollbar-width: thin;
  }
::-webkit-scrollbar {
    width: 2px;
    background-color: #000;
    height: 2px;
}
::-webkit-scrollbar-thumb {
    background-color: #000;
}
::-webkit-scrollbar-track {
    background-color: #f0f0f0;
}

 

/* selection */
::-moz-selection {
	color: #fff;
	background: $black;
}
::selection {
	color: #fff;
	background: $black;
}
:focus {
	outline: none !important;
}
button:focus {
	outline: none !important;
}
p {
    line-height: 1.5;
    word-break: break-word;
}
textarea {
    resize: vertical;
}
hr {

}
.line-height-normal {line-height: 1.5;}
.line-height-20 {line-height:20px;}
.lh-small { line-height: 1; }
.lh-all * {line-height: 1.1;}
body {overflow-x:hidden;}
body .hidden { display: none !important; }
.text-center {text-align: center !important;}
.text-left {text-align: left !important;}
.text-right {text-align: right !important;}
.text-underline {text-decoration: underline !important;}
.text-uppercase {text-transform: uppercase !important;}
.text-capitalize {text-transform: capitalize !important;}
.text-transf-none {text-transform: none !important;}

.light { font-weight: 300 !important}
.normal { font-weight: 400 !important}
.medium { font-weight: 500 !important}
.semibold { font-weight: 600 !important}
b, strong, .bold { font-weight: 600 !important}

.white { color: $white !important; }
.lightGray { color: $lightGray !important; }
.gray { color: $gray !important; }
.midGray { color: $midGray !important; }
.darkGray { color: $darkGray !important; }
.red { color: $red !important; }
.normalRed { color: $normalRed !important; }
.newRed { color: #DB1818 !important; }
.black { color: $black !important; }
.green { color: $green !important; }
.blue { color: $blue !important; }
.textGreen { color: $textGreen !important; }
.lightGreen { color: $lightGreen !important; }
.darkRed { color: $darkRed !important; }
.yellow { color: $yellow !important; }

.red-hover:hover { color: $red;}

.white-bg { background-color: $white !important; }
.lightGray-bg { background-color: $lightGray !important; }
.gray-bg { background-color: $gray !important; }
.midGray-bg { background-color: $midGray !important; }
.darkGray-bg { background-color: $darkGray !important; }
.red-bg { background-color: $red !important; }
.black-bg { background-color: $black !important; }
.green-bg { background-color: $green !important; }
.lightGreen-bg { background-color: $lightGreenBg !important; }
.lightBlue-bg { background-color: $lightBlueBg !important; }
.lightRed-bg { background-color: $lightRedBg !important; }
.yellow-bg { background-color: $yellow !important; }
.e5e5e5-bg { background-color: #e5e5e5 !important; }
.darkRed-bg { background-color: $darkRed !important; }

.btn.white-bg { color: $darkGray; border: 1px solid $gray;}
.btn.gray-bg { color: $darkGray; }
.btn.lightGray-bg { color: $darkGray; }
.btn.gray-bg { color: $darkGray; }
.btn.midGray-bg { color: $midGray; }
.btn.darkGray-bg { color: $white; }
.btn.red-bg { color: $white;}
.btn.breyborder-bg { border:1px solid #ddd;}
 
.btn-badge.white-bg:hover, body .btn.white-bg:hover, button.white-bg:hover, .btn.breyborder-bg:hover { background-color: $black !important; color: $white !important; border-color: $black}
.btn-badge.lightGray-bg:hover, .btn.lightGray-bg:hover, button.lightGray-bg:hover { background-color: $gray !important; }
.btn-badge.gray-bg:hover, .btn.gray-bg:hover, button.gray-bg:hover { background-color: $midGray !important; }
.btn-badge.midGray-bg:hover, .btn.midGray-bg:hover, button.midGray-bg:hover { background-color: $darkGray !important; }
.btn-badge.darkGray-bg:hover, .btn.darkGray-bg:hover, button.darkGray-bg:hover { background-color: #666 !important; }
.btn-badge.black-bg:hover, .btn.black-bg:hover, button.black-bg:hover { background: #fff !important; border: 1px solid #000 !important; color: #000 !important;}
.btn.border-gray:hover, button.border-gray:hover, .btn-badge.red-bg:hover, .btn.red-bg:hover, button.red-bg:hover { background-color: #f0f0f0 !important; color: #000 !important}
.btn.btn-border.border-gray:hover, button.btn-border.border-gray:hover { background-color: #000 !important; color: #fff !important; border-color: rgba(0,0,0,0) !important;}
.btn.black-bg.to-border-hover:hover { background-color: $white !important; color: $black !important; border-color: $black}

.btn.fb-bg-color {background-color: #4267B2 !important}
.btn.fb-bg-color:hover {background-color: #224385 !important}

body .btn.grayBtn-bg {background: #bcbcbc;color: #333;}
body .grayBtn-bg.red-bg {color: #fff;}

.link.link-white:hover { color: $lightGray !important; }
.link.link-lightGray:hover { color: $gray !important; }
.link.link-gray:hover { color: $midGray !important; }
.link.link-midGray:hover { color: $darkGray !important; }
.dark-form .link.link-midGray:hover { color: $white !important; }
.link.link-darkGray:hover { color: #000 !important; }
.link.link-darkRed:hover { color: #771616 !important; }
.link.link-normalRed:hover { color: #8a0707 !important; }
.link.link-red:hover, .link.link-red.delete_multiple:hover { color: #333 !important; }
.link.link-black:hover { color: #666 !important; }

.border-normalRed { border-color: #db1818 !important; }
// buttons, links
.link:hover {
    text-decoration: underline;
}
.link {
    text-decoration: none;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
}
.invert {
    filter: invert(1);
}
a:hover .invert{
    filter: invert(0) !important;
}
// gap
.gap-1 { gap: calc($gap / 2) !important; }
.gap-2 { gap: $gap !important; }
.gap-3 { gap: $gap * 1.5 !important; }
.gap-4 { gap: $gap * 2 !important; }
.gap-5 { gap: $gap * 2.5 !important; }

// radius
.radius-4 { border-radius: 4px !important; }
.radius-5 { border-radius: 5px !important; }
.radius-8 { border-radius: 8px !important; }
.radius-10 { border-radius: 10px !important; }
.radius-20 { border-radius: 20px !important; }
.radius-30 { border-radius: 30px !important; }
.radius-full { border-radius: 50% !important; }

// margin
.m-auto { margin: auto; }
.m-0 { margin: 0 !important; }
.m-1 { margin: calc($gap / 2) !important; }
.m-2 { margin: $gap !important; }
.m-3 { margin: $gap * 1.5 !important; }
.m-4 { margin: $gap * 2 !important; }
.m-5 { margin: $gap * 2.5 !important; }

// margin-top
.mt-auto { margin-top: auto !important; }
.mt-0 { margin-top: 0; }
.mt-05 { margin-top: 5px !important; }
.mt-1 { margin-top: calc($gap / 2) !important; }
.mt-15 { margin-top: 15px !important; }
.mt-2 { margin-top: $gap; }
.mt-20 { margin-top: 20px !important; }
.mt-25 { margin-top: 25px !important; }
.mt-3 { margin-top: $gap * 1.5 !important; }
.mt-4 { margin-top: $gap * 2 !important; }
.mt-45 { margin-top: 45px !important; }
.mt-5 { margin-top: $gap * 2.5 !important; }
.mt-55 { margin-top: 55px !important; }
.mt-6 { margin-top: $gap * 3 !important; }
.mt-80 { margin-top: $gap * 4 !important; }
.mt-85 { margin-top:85px !important; }
.mt-90 { margin-top:90px !important; }
.mt-100 { margin-top: $gap * 5 !important; }
.mt--100 { margin-top: -($gap * 5) !important; }

// margin-bottom
.mb-auto { margin-bottom: auto !important; }
.mb-0 { margin-bottom: 0 !important; }
.mb-05 { margin-bottom: calc($gap / 4) !important; }
.mb-1 { margin-bottom: calc($gap / 2) !important; }
.mb-2 { margin-bottom: $gap !important; }
.mb-15 { margin-bottom: 15px !important; }
.mb-25 { margin-bottom: 25px !important; }
.mb-3 { margin-bottom: $gap * 1.5 !important; }
.mb-4 { margin-bottom: $gap * 2 !important; }
.mb-45 { margin-bottom: 45px !important; }
.mb-5 { margin-bottom: $gap * 2.5 !important; }
.mb-55 { margin-bottom: 55px !important; }
.mb-6 { margin-bottom: $gap * 3 !important; }
.mb-80 { margin-bottom: $gap * 4 !important; }
.mb-100 { margin-bottom: $gap * 5 !important; }
.mb-150 { margin-bottom: $gap * 7.5 !important; }
.mb-200 { margin-bottom: $gap * 10 !important; }
.mb-250 { margin-bottom: $gap * 12.5 !important; }

// margin-left
.ml-auto { margin-left: auto !important; }
.ml-0 { margin-left: 0 !important; }
.ml-05 { margin-left: calc($gap / 4) !important; }
.ml-1 { margin-left: calc($gap / 2) !important; }
.ml-15 { margin-left: 15px !important; }
.ml-2 { margin-left: $gap !important; }
.ml-3 { margin-left: $gap * 1.5 !important; }
.ml-4 { margin-left: $gap * 2 !important; }
.ml-5 { margin-left: $gap * 2.5 !important; }
.ml-6 { margin-left: $gap * 3 !important; }

// margin-right
.mr-auto { margin-right: auto !important; }
.mr-0 { margin-right: 0 !important; }
.mr-05 { margin-right: calc($gap / 4) !important; }
.mr-1 { margin-right: calc($gap / 2) !important; }
.mr-15 { margin-right: 15px !important; }
.mr-2 { margin-right: $gap !important; }
.mr-3 { margin-right: $gap * 1.5 !important; }
.mr-4 { margin-right: $gap * 2 !important; }
.mr-5 { margin-right: $gap * 2.5 !important; }
.mr-6 { margin-right: $gap * 3 !important; }
.mr-7 { margin-right: 70px !important; }
.mr-150 { margin-right: 150px !important; }

// margin-x
.mx-auto { margin-right: auto !important; margin-left: auto !important; }
.mx-0  { margin-right: 0 !important;margin-left: 0 !important; }
.mx-05 { margin-right: calc($gap / 4) !important;margin-left: calc($gap / 4) !important; }
.mx-1  { margin-right: calc($gap / 2) !important;margin-left: calc($gap / 2) !important; }
.mx-2  { margin-right: $gap !important;margin-left: $gap !important; }
.mx-3  { margin-right: $gap * 1.5 !important;margin-left: $gap * 1.5 !important; }
.mx-4  { margin-right: $gap * 2 !important;margin-left: $gap * 2 !important; }
.mx-5  { margin-right: $gap * 2.5 !important;margin-left: $gap * 2.5 !important; }

// margin-y
.my-auto { margin-top: auto; margin-bottom: auto !important; }
.my-0  { margin-top: 0;margin-bottom: 0 !important; }
.my-05 { margin-top: calc($gap / 4) !important;margin-bottom: calc($gap / 4) !important; }
.my-1  { margin-top: calc($gap / 2) !important;margin-bottom: calc($gap / 2) !important; }
.my-15 { margin-top: 15px !important;margin-bottom: 15px !important; }
.my-2  { margin-top: $gap !important;margin-bottom: $gap !important; }
.my-25  { margin-top: 25px !important;margin-bottom: 25px !important; }
.my-3  { margin-top: $gap * 1.5 !important;margin-bottom: $gap * 1.5 !important; }
.my-4  { margin-top: $gap * 2 !important;margin-bottom: $gap * 2 !important; }
.my-5  { margin-top: $gap * 2.5 !important;margin-bottom: $gap * 2.5 !important; }
.my-6  { margin-top: $gap * 3 !important;margin-bottom: $gap * 3 !important; }
.my-80  { margin-top: $gap * 4 !important;margin-bottom: $gap * 4 !important; }
.my-90  {  margin-top: 90px !important; }
.my-100  { margin-top: $gap * 5 !important;margin-bottom: $gap * 5 !important; }

// padding
.p-0 { padding: 0 !important; }
.p-1 { padding: calc($gap / 2) !important; }
.p-2 { padding: $gap !important; }
.p-3 { padding: $gap * 1.5 !important; }
.p-4 { padding: $gap * 2 !important; }
.p-5 { padding: $gap * 2.5 !important; }
.p-8 { padding: $gap * 4 !important; }
.p-10 { padding: $gap * 5 !important; }

// padding-top
.pt-0 { padding-top: 0 !important; }
.pt-05 { padding-top: 5px !important; }
.pt-1 { padding-top: calc($gap / 2) !important; }
.pt-2 { padding-top: $gap !important; }
.pt-25 { padding-top: 25px !important; }
.pt-3 { padding-top: $gap * 1.5 !important; }
.pt-4 { padding-top: $gap * 2 !important; }
.pt-45 { padding-top: 45px !important; }
.pt-5 { padding-top: $gap * 2.5 !important; }
.pt-55 { padding-top: 55px !important; }
.pt-6 { padding-top: $gap * 3 !important; }
.pt-85 { padding-top: 85px !important; }
.pt-90 { padding-top: 90px !important; }
.pt-100 { padding-top: 100px !important; }
.pt-150 { padding-top: 150px !important; }

// padding-bottom
.pb-0 { padding-bottom: 0 !important; }
.pb-05 { padding-bottom: 5px !important; }
.pb-1 { padding-bottom: calc($gap / 2) !important; }
.pb-2 { padding-bottom: $gap !important; }
.pb-25 { padding-bottom: 25px !important; }
.pb-3 { padding-bottom: $gap * 1.5 !important; }
.pb-4 { padding-bottom: $gap * 2 !important; }
.pb-45 { padding-bottom: 45px !important; }
.pb-5 { padding-bottom: $gap * 2.5 !important; }
.pb-55 { padding-bottom: 55px !important; }
.pb-6 { padding-bottom: 60px !important; }
.pb-8 { padding-bottom: $gap * 4 !important; }
.pb-90 { padding-bottom: 90px !important; }
.pb-100 { padding-bottom: 100px !important; }

// padding-left
.pl-0 { padding-left: 0 !important; }
.pl-05 { padding-left: 5px !important; }
.pl-1 { padding-left: calc($gap / 2) !important; }
.pl-2 { padding-left: $gap !important; }
.pl-3 { padding-left: $gap * 1.5 !important; }
.pl-4 { padding-left: $gap * 2 !important; }
.pl-5 { padding-left: $gap * 2.5 !important; }
.pl-75 { padding-left: 75px !important; }
.pl-150 { padding-left: 150px !important; }

// padding-right
.pr-0 { padding-right: 0 !important; }
.pr-05 { padding-right: 5px !important; }
.pr-1 { padding-right: calc($gap / 2) !important; }
.pr-2 { padding-right: $gap !important; }
.pr-3 { padding-right: $gap * 1.5 !important; }
.pr-4 { padding-right: $gap * 2 !important; }
.pr-5 { padding-right: $gap * 2.5 !important; }
.pr-75 { padding-right: 75px !important; }
.pr-150 { padding-right: 150px !important; }

// padding-y
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-05 { padding-top: 5px !important; padding-bottom: 5px !important; }
.py-1 { padding-top: calc($gap / 2) !important; padding-bottom: calc($gap / 2) !important; }
.py-2 { padding-top: $gap !important; padding-bottom: $gap !important; }
.py-25 { padding-top: 25px !important; padding-bottom: 25px !important; }
.py-3 { padding-top: $gap * 1.5 !important; padding-bottom: $gap * 1.5 !important; }
.py-4 { padding-top: $gap * 2 !important; padding-bottom: $gap * 2 !important; }
.py-5 { padding-top: $gap * 2.5 !important; padding-bottom: $gap * 2.5 !important; }
.py-6 { padding-top: $gap * 3 !important; padding-bottom: $gap * 3 !important; }
.py-8 { padding-top: $gap * 4 !important; padding-bottom: $gap * 4 !important; }
.py-100 { padding-top: 100px !important; padding-bottom: 100px !important; }

// padding-x
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: calc($gap / 2) !important; padding-right: calc($gap / 2) !important; }
.px-15 { padding-left: 15px !important; padding-right: 15px !important; }
.px-2 { padding-left: $gap !important; padding-right: $gap !important; }
.px-3 { padding-left: $gap * 1.5 !important; padding-right: $gap * 1.5 !important; }
.px-4 { padding-left: $gap * 2 !important; padding-right: $gap * 2 !important; }
.px-5 { padding-left: $gap * 2.5 !important; padding-right: $gap * 2.5 !important; }
.px-100 { padding-left: $gap * 5 !important; padding-right: $gap * 5 !important; }
.px-120 { padding-left: $gap * 6 !important; padding-right: $gap * 6 !important; }

// font
.f-0 { font-size: calc($fontSize / 2) !important; }
.f-10 { font-size: 10px !important; }
.f-11 { font-size: 11px !important; }
.f-1 { font-size: 12px !important; }
.f-12 { font-size: 12px !important; }
.f-14 { font-size: 14px !important; }
.f-16 { font-size: $fontSize !important; }
.f-18 { font-size: 18px !important; }
.f-20 { font-size: 20px !important; }
.f-2 { font-size: $fontSize !important; }
.f-3 { font-size: $fontSize * 1.5 !important; }
.f-4 { font-size: $fontSize * 2 !important; }
.f-5 { font-size: $fontSize * 2.5 !important; }
.f-6 { font-size: $fontSize * 5 !important; }

// border
.no-border {border: none !important}
.left-border {border-left: $border !important}
.top-border {border-top: $border !important}
.right-border {border-right: $border !important}
.bottom-border {border-bottom: $border !important}
.border {border: $border !important}
.borderNew {border: $borderNew !important}

// HEADING
h1, .h1 { font-weight: normal; font-size: $fontSize * 4 !important; letter-spacing: 0.05em; line-height: 1.15; }
h2, .h2-big { font-weight: normal; font-size: $fontSize * 3 !important; letter-spacing: 0.05em; line-height: 1.15; }
h2, .h2 { font-weight: normal; font-size: $fontSize * 2.25 !important; letter-spacing: 0.05em; line-height: 1.15; }
h3, .h3 { font-weight: normal; font-size: $fontSize * 1.5 !important; }
h4, .h4 { font-weight: normal; font-size: $fontSize * 1.25 !important; }
h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }

h1.big-title {
    font-size: $fontSize * 4 !important;
}
h1.secondary, .h1.secondary { color: $darkGray; }
h2.secondary, .h2.secondary { color: $darkGray; }
h3.secondary, .h3.secondary { color: $darkGray; }
h4.secondary, .h4.secondary { color: $darkGray; }
h5.secondary, .h5.secondary { color: $darkGray; }

.maxwidth570 {
  max-width: 570px;
  width: 100%;
}

a, button { transition: $transition1; }
img {
    max-width: 100%;
    display: block;
}
.d-block {
    display: block;
}
.d-inline-block {
    display: inline-block;
}
hr {
	border: none;
	height: 1px;
	width: 100%;
	background: #F0F0F0;
	margin: 150px 0;
}
.row{
    display:flex;
    flex-wrap:wrap;
    margin-right: -($gap * 0.75);
    margin-left:-($gap * 0.75)
}
.container {
    margin: auto;
    max-width: 1290px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container640 {
    margin: auto;
    max-width: 640px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container-footer {
    margin: auto;
    max-width: 1400px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container-fluid {
    margin: auto;
    max-width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.row-vertical{
    display:flex;
    flex-direction: column;
    flex-wrap:wrap;
    margin-right:-($gap * 0.75);
    margin-left:-($gap * 0.75)
}
.transition1 { transition: $transition1;}
.transition2 { transition: $transition2;}

.float-right {
    float: right !important;
}
.float-left {
    float: left !important;
}
.flex-column{
    flex-direction: column !important;
}
.flex-inline {
	display: inline-flex;
}
.flex-row{
    flex-direction: row !important;
}
.flex {
    display: flex !important;
}
.nowrap {
    flex-wrap: nowrap;
}
.h100 {
    height: 100%;
}
.h100vh {
    height: 100vh;
}
.h-auto {
    height: auto !important;
}
.h450 {
	height: 23.45vw;
}
.h360 {
	height: 18.75vw;
}
.d-block {
    display: block;
}
.w100 {
    width: 100%;
}
.aic { align-items: center !important; }
.ail { align-items: flex-start !important; }
.air { align-items: flex-end !important; }
.jcr { justify-content: flex-end !important; }
.jcl { justify-content: flex-start !important; }
.jcsb { justify-content: space-between !important; }
.jcc { justify-content: center !important;}
[class*='col-']{
    position: relative;
}
.arrow-down {
    width: 8px;
    height: 5px;
    background: url(../images/arrow-down.svg) no-repeat center center/cover;
    display: inline-block;
}
.arrow-down.down-triangle {background:url(/admin_assets_new/images/triangle-down.svg) no-repeat center center/cover; margin-right: 15px;}
.block-btn {
	width: 100%;
}
.big-big-gap {
    margin-left: -($gap * 2);
    margin-right: -($gap * 2);

    [class*='col-'] {
        padding-left: $gap * 2;
        padding-right: $gap * 2;
    }
}
.big-gap {
    margin-right: -($gap * 1.25);
    margin-left: -($gap * 1.25);

    [class*='col-'] {
        padding-left: $gap * 1.25;
        padding-right: $gap * 1.25;
    }
}
.normal-gap {

    [class*='col-'] {
        padding-left: $gap * 0.75;
        padding-right: $gap * 0.75;
    }
}
.col-12{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 100%;
    flex:0 0 100%;
    max-width: 100%;
}
.col-10{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 83.333333%;
    flex:0 0 83.333333%;
    max-width: 83.333333%;
}
.col-9{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 75%;
    flex:0 0 75%;
    max-width: 75%;
}
.col-8{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 66.6666%;
    flex:0 0 66.6666%;
    max-width: 66.6666%;
}
.col-7{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 58.333333%;
    flex:0 0 58.333333%;
    max-width: 58.333333%;
}
.col-6{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 50%;
    flex:0 0 50%;
    max-width: 50%;
}
.col-5{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 41.666667%;
    flex:0 0 41.666667%;
    max-width: 41.666667%;
}
.col-4{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 33.3333%;
    flex:0 0 33.3333%;
    max-width: 33.3333%;
}
.col-3 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 25%;
    flex:0 0 25%;
    max-width: 25%;
}
.col-2-5 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 25%;
    flex:0 0 25%;
    max-width: 25%;
}
.col-2 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 16.666667%;
    flex:0 0 16.666667%;
    max-width: 16.666667%;
}
.col {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: auto;
}

.offset-1 { margin-left: 8.333333%; }
.offset-2 { margin-left: 16.666667%; }
.offset-3 { margin-left: 25%; }
.offset-4 { margin-left: 33.333333%; }
.offset-5 { margin-left: 41.666667%; }
.offset-6 { margin-left: 50%; }

@media(max-width: 767px){
.col-12, .col-10, .col-6, .col-4, .col-3, .col-2-5 {
    position:relative;
    width:100%;
    padding-right:15px;
    padding-left:15px;
}
}
// groups
@mixin fieldLabel {
    font-size: $fontSize * 0.75;
    font-weight: 400;
    display: block;
    padding: 10px 0;
    color: $darkGray;
}
@mixin height_no_header {
    height: calc(100vh - #{$header});
}
@mixin small_header {
    min-height: 600px;
}
@mixin smaller_header {
    min-height: 300px;
}
@mixin baseFont {
    font-size: $fontSize;
    font-weight: 400;
    color: $black;
    line-height: 1.4;
}
@mixin centerPosition {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}
@mixin centerAbsPosition {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}

// main style
body {
    @include baseFont;
    &.show-popup {
        overflow: hidden;
    }

    main {
        position: relative;
        padding-top: $header;
    }
    &.login main,
    &.homepage main,
    &.transparent main{
        padding-top: 0;
    }
    &.transparent header {
        border: none !important;
        background: rgba(255, 255, 255, 0);

        .white-logo {
            display: block;
        }
        .black-logo {
            display: none;
        }
        a{
            font-weight: 500;
            color: $white;

            &:hover{
                color: $red;
            }
        }
    }
}
section {
    padding: $bigGap;
    position: relative;
}
section.normal-padding {
    padding: calc($bigGap / 2);
}
.logo-small {
    display: none;
    margin: $gap*1.2 0 $gap*1.2 0;
    text-align: center;
}
.logo {
    display: block;
    text-align: center;
}
.icon-rotated {
    display: inline-block;
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
.rounded {
    border-radius: 100px;
}
header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 $gap * 2.5;
    height: $header;
    width: 100%;
    transition: background-color 0.25s ease-in-out 0s, border 0.25s ease-in-out 0s;
    background: rgba(255, 255, 255, 1);
    border-bottom: $borderLight;

    .white-logo {
        display: none;
    }
    .black-logo {
        display: block;
    }

    a {
        font-size: 14px;
        text-transform: uppercase;
        color: $black;
        letter-spacing: 0.1em;
        margin-right: $gap * 2.5;
        font-weight: 500;

        &:last-of-type:not(.logo) {
            margin-right: 0;
        }
        &:hover {
            color: $red;
        }
    }
    .menu-button {
        padding: 10px;
        margin-left: 10px;
        display: none;

        &:hover {
            color: $gray;

            rect {
                fill: $red;
            }
        }
        &.active {
            color: $gray;

            rect {
                fill: $red;
            }
        }
    }
}
.right-menu {
    margin-left: auto;
    display: flex;
    align-items: center;
    position: relative;
}
.dropdown-button {
    z-index: 11;
    background: $white;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    user-select: none;
}
.dropdown-container .noselect {
	user-select: none;
	width: 100%;
	max-width: 500px;
	height: 40px;
	padding-left: 15px;
	position: relative;
    border:1px solid #ddd;
    background:#fdfdfd;
    border-radius:8px;
}
.dropdown-wrap {max-width:500px;}
.dropdown {
	position: relative;

    > .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 150px;
        width: auto;
        z-index: 12;
        background: $white;
        transition: all 0.25s ease-in-out 0s;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transform: translateY(-10px);
        padding: 25px 45px 25px 25px;
        border-radius: 10px;
        border: 1px solid #F0F0F0;
        margin-top: 5px;
        box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);

        &.drop-right{
            right: 0;
            left: auto;
        }
        > li {
            padding: 0;
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            &:last-of-type {
                margin-bottom: 0px;
            }

            a {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: $white;
                line-height: 1.4;
                transition: $transition1;
                white-space: nowrap;

                img {
                    margin-right: 10px;
                    width: 25px;
                    height: 25px;
                }
                &:hover {
                    color: $red;
                }
            }
        }
    }
    &.opened > .dropdown-menu{
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        transform: translateY(0px);
        border-radius:10px !important;

    }
    &.opened > .dropdown-button{
        color: $darkGray;
    }
    &.opened > .dropdown-button:hover{
        color: $darkGray;
        background: $white;
    }
}
.dropdown.opened > .panel-button {
    z-index: 13;
}
.dropdown.opened:last-child > .panel-button,
.right-menu > .dropdown.opened > li:last-child .panel-button {
	border-radius: 0 10px 0px 0 !important;
}
.upload-image.big-uplad-image.cover_image_size {
    height: 150px;
}
.upload-image.big-uplad-image.mob_cover_image_size {
	width: 200px;
	height: 270px;
}
.btn {
    min-height: 45px;
    padding: 0 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: $transition1;
    border-radius: 50px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    text-align: center;
    white-space: nowrap;
    line-height: 1;
    user-select: none;
    letter-spacing: 0.1em !important;
    border: 1px solid #000;
    cursor: pointer;

    &:disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    &.btn-sm {
        min-height: 40px;
        padding: 0 15px;
    }
    &.f-10 {
        font-size: 10px !important;
    }
    &.btn-xs {
        min-height: 30px;
        padding: 0 15px;
    }
    &.btn-wide {
        padding: 0 $gap * 2.5;
    }
    &.btn-tall {
        height: 45px;
    }
    &.btn-border {
        border-radius: 50px;
        border: 1px solid #000000;

        &:hover {
            border-color: #000;
            background: #000;
	    color: #fff !important;

            img {
                filter: invert(1);
            }
        }
    }
    &.border-gray {
      border: 1px solid #f0f0f0;
    }
    &.border-black {
      border: 1px solid #000;
    }
    &.status-orange {
      border: none !important;
      border-radius: 50px !important;
      background: #E8AF44 !important;
      color: #fff !important;
    }
    &.btn-badge-40 {
        width: 40px;
        height: 40px;
        min-width: 40px;
        max-width: 40px;
        min-height: 40px;
        max-height: 40px;
        border: $borderNew;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100px !important;
        cursor: pointer;
        padding: 0 !important;

        &:hover {
            border-color: #333;
        }
    }
    &.btn-badge {
        width: 56px;
        padding: 0 10px;

        &.lightWhite-bg {
            background: rgba(255, 255, 255, 0.2);

            &:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        }
        &.big-badge {
            height: 120px;
            width: 120px;
            border-radius: 70px;
            cursor: pointer;
        }
    }
    &.btnadmin {
      text-transform: initial !important;
    letter-spacing: 0.05em;
    font-weight: 500 !important;
    }
}
@supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
    .overlay-blur {
        -webkit-backdrop-filter: blur(30px);
        backdrop-filter: blur(30px);
    }
}
@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    body.show-popup main,
    body.show-popup footer {
        filter: blur(30px);
    }
    body.show-popup header {
        filter: blur(10px);
    }
}
.overlay {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100vh;
    left: 0;
    transition: all 0.5s ease-in-out 0s;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    background: rgba(0,0,0,0.51);


    .overlay-blur {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100vh;
        left: 0;
        z-index: 1;
        transition: $transition1;
        background-color: rgba(0, 0, 0, 0.6);
    }
    .popup {
        position: absolute;
        top: 50%;
        left: 50%;
        transition: $transition2;
        transform: translate(-50%,-50%) scale(1.05);
        width: 100%;
        max-width: 500px;
        z-index: 1002;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        background: $white;
        padding: 70px $gap * 4;
        box-shadow: 0 0 100px 0 rgba(0,0,0,0.2);
        border-radius: 10px;

        &.small-padding {
            padding: 25px 50px;

            .popup-body {
                padding: 0px 50px 25px 50px;
            }
        }
        .popup-body {
            padding: 0 ($gap * 4) ($gap * 3);
        }
        .popup-header {
            padding: 29px 0;
            border-bottom: 1px solid #F0F0F0;
        }
    }
}
.most-title {
    font-size:12px;
}
.most-title:hover {
    color: $red;
}
.table-row img {
    transition: $transition1;

    &:hover {
        opacity: 0.6;
    }
}
.featured {
	pointer-events: none;
}
.rate-class {
	text-align: center;
	margin: 50px 0;
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 50px;
	display: flex;
	align-items: center;
	justify-content: center;

    .rate-value {
        border: 1px solid #BCBCBC;
        width: 20%;
        border-right: none;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #000;

        &.active,
        &:hover {
            background: #BCBCBC;
            color: #fff;
            cursor: pointer;
        }

        &:last-of-type {
            border-right: 1px solid #BCBCBC;
            border-radius: 0 30px 30px 0;
        }
        &:first-of-type {
            border-radius: 30px 0 0 30px;
        }
    }
}
.share-social {
	text-align: center;
	margin: 50px;
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 50px;
	display: flex;
	align-items: center;
	justify-content: center;

    .share-item {
        display: inline-flex;
        width: 90px;
        height: 90px;
        align-items: center;
        justify-content: center;
        border: 1px solid #F0F0F0;
        border-radius: 50px;
        margin: 0 10px;

        &:hover {
            background: #000;
            border-color: #000;
            box-shadow: 0 0 2px 0 rgba(0,0,0,0.4);

            svg path {
                fill: #fff;
            }
        }
    }
}

.show-popup .overlay {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;

    .popup.show {
        transform: translate(-50%,-50%) scale(1);
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
    }
}
.close_page {
	position: absolute;
	top: 30px;
	right: 30px;
	cursor: pointer;

    &:hover {
        color: $darkGray;
    }
}
.close_page.close {}
.close_page:hover img {
	filter: invert(1);
}
.close-popup {
    cursor: pointer;

    &:hover {
        opacity: 0.5;
    }
}
.image-overlay {
    position: relative;

    &::before {
        content: "";
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to top, #333, rgba(0,0,0,0));
        opacity: 0.8;
        z-index: 0;
        border-radius: 8px;
    }
}
.big-icon {
	font-size: 60px;
}
.upload-image.has-error {
	background: #ff9494;

    &::before {
        content: "required";
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        background: red;
        font-size: 12px;
        padding: 2px 5px;
        text-transform: uppercase;
        border-radius: 4px;
    }
}
.upload-zone_audio.has-error,
.upload-zone.has-error {
	border-color: red;
	border-width: 2px;
}
.checkbox.has-error label {
	color: red;
}
.no-wrap {
    white-space: nowrap;
}
.thumbs-container {
    // min-height: 100px;
}
.input-container {
	position: relative;
    width: 100%;
    margin-bottom: 25px;
    max-width: 500px;

    &.has-error::before {
        content: "required";
        font-size: 10px;
        position: absolute;
        top: -16px;
        right: 8px;
        color: #DB1818;
        text-transform: uppercase;
    }
}
.popup .input-container {margin-bottom: 30px !important;}

.line-input {
	width: 100%;
	height: 40px;
    border-radius: 8px !important;
    border: 1px solid #ddd;
    padding-left: 15px;
    font-size: 14px;
    background: #fdfdfd;
    max-width: 500px;
    -webkit-appearance: none;

    &.small {
        height: 35px;
    }
    &.no-bg {
        background: rgba(0,0,0,0);
    }
    &.error {
        border-width: 1px !important;
        border-color: #DB1818 !important;
    }
    &.bottbord {
      border-left:none;
      border-top:none;
      border-right:none;
      padding-left: 0;
  }
}

.line-input:hover {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1);}
.dropdown-button:hover, .dropdown-container.is-active .dropdown-button, .dropdown-container.is-active .dropdown-list {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;} 
[type="checkbox"]:not(:checked) + label:hover::before, [type="checkbox"]:checked + label:hover::before {box-shadow: 0 0 50px rgba(51, 51, 51, 0.3) !important;} 

[type="checkbox"]:not(:checked) + label::before, [type="checkbox"]:checked + label::before, .custom-toggle-checkbox::before, .custom-toggle-checkbox.checked::before,
[type="radio"]:not(:checked) + label::before, [type="radio"]:checked + label::before {border: 1px solid #ddd; border-radius: 4px;}

.input-container select.line-input {
  background: #fdfdfd url(/admin_assets_new/images/triangle-down.svg) no-repeat right 15px center;
  background-size: 8px;
}
textarea.line-input {padding-left:30px;}
.popup textarea.line-input {
    padding-left:20px;
    padding-top: 17px;
    font-size: 14px !important;
}
.popup .btn {min-height:42px; height:42px; max-height:42px;}
body.login .line-input {
    box-shadow: none !important;
}
.input-container.loginemail {margin-bottom: 15px !important;}
.input-container.loginpass {margin-bottom: 40px !important;}
.input-container.loginbtn {margin-bottom: 0;}

.round-input {
	width: 100%;
	height: 56px;
    border-radius: 30px;
    padding: 0px 45px 0px 25px;
	border: 1px solid $gray;
    font-size: 16px;
    background: $white;

    &.no-bg {
        background: rgba(0,0,0,0);
    }
    &:focus, &:active {
        border-color: $black;
    }
}
.round-select {
	width: auto;
	height: 56px;
	border-radius: 30px;
    padding: 0px 45px 0px 25px;
	border: 1px solid $gray;
	font-size: 18px;
	background: $lightGray url(../images/arrow-down.svg) no-repeat right 30px center;
	appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;

    &:focus,
    &:active {
        border: 1px solid $black !important;
    }
    &:hover {
        border-color: $lightText;
        cursor: pointer;
    }
    &.w100 {
        width: 100% !important
    }
}
.line-select {
	width: 100%;
	height: 60px;
	border: none;
	border-bottom: 1px solid #D4D8DD;
	font-size: 18px;
	background: #fff url(../images/arrow-down.svg) no-repeat right 15px center;
	-moz-appearance: none;
	-webkit-appearance: none;
}
select:valid + .select-label {
	opacity: 1;
}
select:invalid { color: gray; outline: 0 !important; box-shadow: none !important}
select:valid  + .select-label{ opacity: 0.3;transform: translateY(-50%); }
select + .select-label {
    position: absolute;
    transition: all 0.25s ease-in-out 0s;
	top: 0;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(0%);
	opacity: 0;
	font-size: 14px;
}
.clear_select {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 40px;
	font-size: 30px;
	display: none;
	line-height: 1;
	height: 40px;
    cursor: pointer;
    &:hover {
        opacity: 0.5;
    }
}
textarea {
    line-height: 1.8;
    padding: 25px;
    border: 1px solid #ddd !important;
    background:#fdfdfd;
 
    &.line-input {
        min-height: 275px;
    }
    &.short-textarea {
        min-height: 120px;
    }
}
.line-input:placeholder-shown + .input-label {
    opacity: 0;
    top: 17px;
    font-size: 18px;
}
.input-label {
    display: block;
    width: 100%;
    position: absolute;
    bottom: 18px;
    left: 1px;
    opacity: 0.4;
    top: -10px;
    font-size: 14px;
    transition: $transition2;
    pointer-events: none;
    text-align: left;
}
.reveal_password {
	width: auto;
	height: auto;
	display: block;
	color: $red;
	position: absolute;
	top: 0;
	right: 15px;
	z-index: 1;
	line-height: 40px;
	cursor: pointer;
	font-size: 14px;
}
#submit_login .reveal_password {top:2px;}
#submit_login .btn {padding-top:0 !important; padding-bottom: 0 !important; min-height: 42px;}
.reveal_password.active svg path {
	fill: #333 !important;
}
sup {
	vertical-align: top;
	font-size: 60%;
	display: inline;
	margin-left: 5px;
	top: 2px;
	position: relative;
}
.big-header-image {
    @include height_no_header;
    padding: 0 !important;
    margin: 0;
    position: relative;
}
.small-header-image {
    @include small_header;
    padding: 0 !important;
    margin: 0;
    position: relative;

    .image-overlay img{
        @include small_header;
        object-fit: cover;
    }
}
.smaller-header-image {
    @include smaller_header;
    padding: 0 !important;
    margin: 0;
    position: relative;
}
.panel {
	background: #fff;
	display: flex;
	flex-direction: column;
	padding: 40px 30px;
	align-items: center;
    justify-content: flex-start;
    // height: 100%;

    &.big-padding {
        padding: 50px 60px;
    }
    &.dark-panel {
        background: $darkGray;
    }

    &.with-shadow {
        box-shadow: 20px 0px 30px rgba(212, 216, 221, 0.15);
    }
    .panel-header {
        width: 100%;
        padding: $gap*1.5;
        text-align: center;

        &.help-panel-header {
            padding: $gap*2.5 $gap*1.5 $gap*1.5;
            height: 180px;
            display: flex;
            align-items: center;
            flex-direction: column;

            p {
                font-size: $fontSize;
            }
        }
    }
    .panel-body {
        width: 100%;
        padding: 50px;
    }
}

.hero-container {
	position: absolute;
	top: 50%;
	left: 150px;
    z-index: 1;
	transform: translateY(-50%);
    max-width: 640px;

    h1 {
        font-size: $fontSize*4 !important;
        color: #fff;
        font-weight: 500;
        margin-bottom: 20px;
        letter-spacing: 1px;
        line-height: 80px;
    }
    h3 {
        margin-bottom: 70px;
        color: #fff;
    }
}
.hero-banner {
	position: relative;

    img {
        height: 100%;
        object-fit: cover;
    }
}
.homepage-sponsors-pagination {
	bottom: -6px;
}
.swiper.homepage-sponsors {
	padding-bottom: 30px;
}
.swiper-pagination-bullet-active {
	background: #666 !important;
}
.max400 {
    max-width: 400px;
}
.img-panel {
	position: relative;
}
.img-panel-content {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 100px;
	text-align: left;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: center;
}
.homepage-teachers .img-fluid,
.homepage-collection .img-fluid {
	width: 100%;
	max-height: 500px;
	object-fit: cover;
    height: 100%;
}
.homepage-collection .swiper-slide {
	height: 500px;
}
.hero-badge {
	position: absolute;
	bottom: 80px;
	right: 80px;
	z-index: 1;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination {
	bottom: 70px;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background: #fff !important;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination .swiper-pagination-bullet {
	background: rgba(255, 255, 255, 0.3) !important;
    opacity: 1 !important;
}
footer {
	background: $lightGray;
}
.footer-menu-title {
	font-weight: 600;
	margin-bottom: 20px;
}
footer ul:last-of-type {
    margin-bottom: 0 !important;
}
footer ul li {
	line-height: 1.4;
	padding-bottom: 15px;
	font-weight: 300;
}
footer ul li a:hover {
	color: $red;
    text-decoration: underline;
}
.footer-logo {
    width: 200px;
}
.footer-social {
	display: flex;
	align-items: center;
}
.footer-social a {
    background: url(../images/footer-social.png) no-repeat 0 0;
    display: block;
    height: 45px;
    width: 45px;
    margin: 0 8px;
}
.footer-social .fb {background-position: 0 0;}
.footer-social .fb:hover {background-position: 0 -45px;}
.footer-social .ig {background-position: -45px 0;}
.footer-social .ig:hover {background-position: -45px -45px;}
.footer-social .tw {background-position: -90px 0;}
.footer-social .tw:hover {background-position: -90px -45px;}
.footer-social .yt {background-position: -135px 0;}
.footer-social .yt:hover {background-position: -135px -45px;}
.main-text p {
	margin-bottom: 10px;
}
.classes-page .image-overlay img {
    height: 500px;
	width: 100%;
	object-fit: cover;
}
.classes-page .video-container .image-overlay img {
    height: 300px;
	width: 100%;
	object-fit: cover;
}
.favorite {
	position: absolute;
	top: 30px;
	right: 30px;
	width: 20px;
	height: 18px;
	z-index: 1;
	background: url(../images/heart-full.svg) no-repeat center center;
	filter: invert(1);
}
.favorite:hover {
    opacity: 0.6;
}
.duration-container {
    position: relative;
    width: 150px;
}
.get_duration {
    position: absolute;
    right: 0;

    img {
        cursor: pointer;

        &:hover {
            opacity: 0.6;
        }
    }
}
.locked {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 12px;
    height: 15px;
    z-index: 1;
    background: url(../images/lock.svg) no-repeat center center;
}
.duration {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 1;
    color: #fff;
    font-size: 16px;
    text-shadow: 0 0 3px #999;
}
.filters {
    display: flex;
    align-items: center;
    flex-direction: column;
}
.filters .custom-selectbox-holder {margin-bottom: 5px;}

.video-container{
    position: relative;
    display: block;

    .play-button {
        width: 110px;
        height: 110px;
        border-radius: 50%;
        border: 2px solid #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 1;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: $transition1;

        &:hover {
            background: $white;
            cursor: pointer;

            span {
                border-left: 20px solid #000;
            }
        }
        span {
            transition: $transition1;
            border: 13px solid rgba(0,0,0,0);
            border-left: 20px solid #fff;
            margin-left: 17px;
        }
    }
    &:hover {
        cursor: pointer;

        .play-button {
            background: $white;

            & > span {
                border-left: 20px solid #000;
            }
        }
    }
}
.single-collection-desc h3,
.video-text-container h4 {
    transition: $transition1;
}
.single-collection-desc h3:hover,
.video-text-container h4:hover {
    color: $red;
}
.icon-star {
    width: 10px;
    height: 10px;
    background: url(../images/star.svg) no-repeat center center;
    display: inline-block;
    background-size:cover;
}
.single-video-item {
    margin-bottom: 80px;
}
.single-collection-desc {
    margin-top: 40px;
}
.single-collection-item {
    margin-bottom: 80px;
}
.single-collection-item .image-overlay::before {
    transition: $transition1;
}
.single-collection-item:hover .image-overlay::before {
    background: -webkit-gradient(linear, left bottom, left top, from(#222), to(rgba(0, 0, 0, 0)));
    background: linear-gradient(to top, #222, rgba(0, 0, 0, 0));
    opacity: 0.9;
}
.border-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 1px solid #F0F0F0;
    width: 130px;
    margin: 0 15px;
    height: 190px;

    .border-box-cell {
        line-height: 1.6;
        text-align: center;
        font-size: 20px;
        height: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        align-items: center;
        font-size: 20px;
    }
}
.live-badge {
	position: absolute;
	top: 30px;
	right: 30px;
}
.single-teacher-item {
	border: 1px solid #F0F0F0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 550px;
	text-align: center;

    &:hover{
        background: #F8F8F8;
        border-color: #F8F8F8;
    }
}
.teacher-panel {
	position: relative;
	display: flex;
	flex-direction: column;
}
.teacher-img {
	width: 250px;
	height: 250px;
	min-width: 250px;
	min-height: 250px;
	border-radius: 50%;
	overflow: hidden;

    &.white-border {
        border: 5px solid #fff;
    }
}
.teacher-badge {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
}
.favs {
    display: block;
    width: 20px;
    height: 18px;
    background: url(../images/heart.svg) no-repeat center center / cover;

    &:hover {
        background: url(../images/heart-full.svg) no-repeat center center / cover;
    }
}
.logged-in {
    display: none;
}
body.logged {
    .logged-in {
        display: flex;
    }
    .logged-out {
        display: none;
    }
}
.side-links li {
    margin-bottom: 40px;

    a {
        font-weight: 400;
    }
}
.calendar-icon {
    background: url(../images/calendar.svg) no-repeat center center;
    display: block;
    width: 15px;
    height: 15px;
    position: absolute;
    top: 13px;
    right: 15px;
    user-select: none;
    pointer-events: none;
}
.avatar {
	width: 60px;
	height: 60px;
	border-radius: 40px;
	overflow: hidden;
    position: relative;
    display: block;

    .initials {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        text-transform: uppercase;
        background: rgba(0, 0, 0, 0);
    }
    img {
        max-width: 100%;
        max-height: 120px;
        width: 100%;
        height: 100%;
        overflow: hidden;
        object-fit: cover;
        border-radius: 50%;
    }
}
.big-avatar {
	width: 120px;
	height: 120px;
	border-radius: 60px;
	overflow: hidden;
    position: relative;

    .initials {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid #eaeaea;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
    }
}
.bigger-avatar {
	width: 250px !important;
	height: 250px !important;
	border-radius: 160px;
	overflow: hidden;
	object-fit: cover !important;
    position: relative;
}

.side-link:hover {
    color: $red;
}
.side-link.active{
    font-weight: 500;
    color: #969696 !important;
}
.sticky {
    position: sticky;
    top: calc(#{$header} + 20px)
}
.single-help-item {
    margin-bottom: 80px;

    h3 {
        margin-bottom: 40px;
    }
    &:last-of-type {
        margin-bottom: 0;
    }
}
.uploading {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1111;
	background: #fff;
	height: 100%;
	display: flex;
	align-items: center;
	width: 100%;
	opacity: 0.96;
	padding-left: 120px;
    animation: pulse 2s infinite;
}
@keyframes pulse {
    0% { color: #999}
    50% { color: #000}
    100% { color: #999}
}

.admin-menu {
    width: 300px;
    background: $black;
    position: fixed;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 111;
    padding: 0 25px 40px 25px;
    overflow-y: auto;

    .side-links {
        padding: 0 15px;

        li {
            margin-bottom: 20px;
        }
        .side-link {
            font-size: 11px;
            color: #ffffff;
            font-weight: 600;
            display: block;

            &:hover {
                color: #969696;
                -webkit-transition: all 0.2s ease-out 0s;
                transition: all 0.2s ease-out 0s;
            }
        }
    }
    .admin-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 50px 10px;
        border-bottom: 1px solid rgba(0,0,0,0.15);
        margin-bottom: 20px;
    }
    /*.side-link.active{
        font-weight: 600;
        color: $white;
    }*/

}
.admin-logo {margin-bottom: 60px;}
.admin-logo img {height: 13px; width: auto; }

.main-content {
    margin-left: 290px;
    padding: 0 60px;
}
.dashboard-widget {
    border: $border;
    padding: 45px 50px 40px 50px;
    border-radius: 10px;
}
.class-item {
    position: relative;
}
button.edit_bulk {
  letter-spacing: 0.05em;
}
button.ml-3.f-14.link.link-red.red.flex.aic.white-bg.edit_bulk:hover {
    background: #fff !important;
}
.search-container {
	position: relative;
    height: 56px;
}
.search-form {
	position: absolute;
	top: 0;
	height: 56px;
	right: 0;

    &.show .seach-input,
    &:hover .seach-input,
    &:active .seach-input,
    .seach-input:focus,
    .seach-input:active {
        width: 300px;
        color: #000;
    }
    &.show .seach-input.search-wide,
    &:hover .seach-input.search-wide,
    &:active .seach-input.search-wide,
    .seach-input.search-wide:focus,
    .seach-input.search-wide:active {
        width: 500px;
        color: #000;
    }
    .seach-input {
        height: 56px;
        border: 1px solid #ddd;
        border-radius: 50px;
        width: 56px;
        background: #fff;
        padding: 0 15px;
        font-size: 14px;
        color: #fff;
        transition: all 0.25s ease-in-out 0s;
    }
    .search-button {
        background: rgba(0,0,0,0);
        position: absolute;
        border-radius: 50px;
        top: 4px;
        right: 4px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
           
        }
    }
}
.ajax-search-classes.search-form .seach-input {
  border-radius:8px !important;
  border-color:#ddd !important;
  background-color: #fdfdfd;
}
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 25px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}
.minH45 {
	min-height: 45px;
}
.opacity30 {
    opacity: 0.3;
}
.btn.black-bg.grayBtn-bg {
    background: #bcbcbc !important;
}
.table-arrow {
    cursor: pointer;
    user-select: none;

    &.disabled {
        opacity: 0.3;
        pointer-events: none;
    }
    &:hover {
        opacity: 0.5;
    }
}
.upload-zone_audio,
.upload-zone {
	background: #f8f8f8;
	border: 1px solid #F0F0F0;
	min-height: 500px;
    position: relative;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #90fde5;
	border: 2px dashed #333;
}
.upload-zone.dragOver::before {
	content: "Drop your video file here";
	font-size: 24px;
	color: #00000063;
}
.upload-zone.multi-zone.dragOver::before {
	content: "Drop your videos file here";
	font-size: 24px;
	color: #00000063;
}
.upload-zone_audio.dragOver::before {
	content: "Drop your audio file here";
	font-size: 24px;
	color: #00000063;
}
.upload-zone_audio.multi-zone.dragOver::before {
	content: "Drop your audio file here";
	font-size: 24px;
	color: #00000063;
}
.reorder {
    cursor: pointer;
}
.single-class {
    border-bottom: $border;
    padding-bottom: 25px;
    margin-bottom: 35px;

    .single-class-image img {
        width: 100%;
        height: 160px;
        object-fit: cover;
    }
    .single-class-title {
        font-size: 18px;
        font-weight: 600;
        margin: 10px 0;
    }
    .single-class-desc {
        color: #999;
        font-size: 12px;
        line-height: 20px;
    }
    .single-class-desc.show-time {
        font-size:0 !important;
    }
    .single-class-desc.show-time span.custom_duration {
        font-size:12px !important;
    }
}
body .mobile-flex-vertical h1.single-class-title {
  font-weight: 600 !important;
}
.remove-class {
	position: absolute;
	top: 0px;
	right: 20px;
	font-size: 16px;
	text-align: center;
	cursor: pointer;
	padding: 0 0 !important;
	min-height: 15px !important;
	min-width: 15px !important;
	width: 18px;
	height: 18px;
	font-weight: 500 !important;
	text-align: center !important;
	display: flex;
	align-items: center;
	justify-content: center;
}
.single-subscriber-image {
    position: relative;
}
.upload-image {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f8f8;
    border: $border;
	margin-right: 30px;
	overflow: hidden;
    cursor: pointer;
    width: 320px;
    height: 180px;
    position: relative;
    border-radius:8px;

    &.big-uplad-image {
        width: 450px;
        height: 280px;
    }
    &.medium-uplad-image {
      width: 450px;
      height: 150px;
  }
    &.small-uplad-image {
      width: 210px;
      height: 120px;
    }
    &.profile-photo {
        width: 140px;
        height: 140px;
        min-width: 140px;
        min-height: 140px;
        border-radius: 100px;
        border: none;
    }
    img.no-img {
        opacity: 1;
        width: 18px !important;
        height: 14px !important;
        border-radius: 0;
        position: relative;
    }
    &:hover {
        opacity: 0.5;
    }
}
.login-box {
	width: 500px;
	padding: 60px 50px;
	background: #fff;
	box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
    border-radius: 10px;
}
#audio[type="file"],
#video[type="file"] {
	opacity: 0;
	left: 0;
	top: 0;
    z-index: 111111;
	position: absolute;
	width: 100%;
	height: 100%;
}
#mob_cover_image[type="file"],
#cover_image[type="file"],
#popup_image[type="file"],
#file[type="file"],
#image[type="file"] {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	left: 0;
	z-index: 1;
	opacity: 0;
	cursor: pointer;
}
.hide {
	display: none !important;
}
.upload-image .image_preview.no-img.has_image {
	opacity: 1 !important;
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
}
#progress-bar-status-show_audio,
#progress-bar-status-show {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 12px;
	background: #000000;
	text-align: right;
	padding: 0;
	font-size: 10px;
	line-height: 12px;
	width: 0;
	color: #fff;
    border-radius: 4px;

}
.before_upload_audio,
.before_upload {
	display: flex;
	align-items: center;
	flex-direction: column;
    position: relative;
    z-index: 1;
    text-align: center;
}
.video_placeholder {
    width: 100%;
    max-height: 70vh;
}
.audio_placeholder {
	width: 100%;
	max-height: 180px;
	display: flex;
	align-items: center;
	padding: 40px 10px;
}
.audio-icon {
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 40px;
	min-width: 100px;
	min-height: 100px;
	border-radius: 100px;
	box-shadow: 0 0 9px 0 rgba(0,0,0,0.3);
	padding: 12px;
    position: relative;
    z-index: 1;
}

.after_upload_audio,
.after_upload {
    position: relative;
    left: 0;
    height: 100%;
    z-index: 0;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    display: block;
    max-height: 70vh;
    border-radius: 8px;
}
.after_upload_audio {max-width: 500px;height:40px;}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active{
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    filter: none !important;
}
.big-avatar img {
    max-width: 100%;
    max-height: 120px;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
#thecanvas {
	width: 1000px;
	height: 600px;
    position: absolute;
}
.seach-input.search-wide {
	position: relative;
	z-index: 2;
}
.search-button {
	position: relative;
	z-index: 3;
}
.ajax-class {
    background: #fff;
    padding: 20px 30px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-top: 1px solid #F0F0F0;


    &:hover {
        background: #f8f8f8;
    }

}
.single-class .single-class-image img {
	width: 100%;
	height: 160px;
	object-fit: cover;
}
.single-class-rest {
	display: block;
	width: 100%;
	overflow: hidden;
    padding-right: 0;
}
.single-class-rest .single-class-title.link.link-black.textGreen {line-height: 32px; font-size: 14px !important; margin-top: 2px; margin-bottom: 0;}
/* HOWTO selected class*/
.single-selected-howto .single-class .single-class-image {
    margin-right: 25px;
    img {
        width: 160px;
        height: 90px;
        min-width: 160px;
    }
}
.single-selected-howto .single-class {
	display: flex;
}
.single-selected-howto .single-class .single-class-title {
	font-size: 14px;
    font-weight: 600;
    margin: 0 0 2px 0;
}
.single-selected-howto .single-class {
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 20px;
	margin-bottom: 20px;
}
.single-selected-howto .single-class .single-class-desc {
	color: #999;
	font-size: 12px;
  line-height: 20px;
}
.single-selected-howto .single-class {
    overflow: hidden;
}
.single-selected-howto .single-class .single-class-image::before {
	content: "VIDEOS";
	position: absolute;
	top: 0;
	left: 0;
	background: #000;
	color: #fff;
	font-size: 10px;
	font-weight: bold;
	width: 90px;
	text-align: center;
	-webkit-transform: rotate(-40deg);
	transform: rotate(-90deg) translateY(-13px) translateX(-42%);
	transform-origin: 50% 50%;
  display: none;
}

/* EXERCISES selected class*/
.single-selected-exercises .single-class .single-class-image {
    margin-right: 25px;
    img {
        width: 160px;
        height: 90px;
        min-width: 160px;
    }
}
.single-selected-exercises .single-class {
	display: flex;
}
.single-selected-exercises .single-class .single-class-title {
	font-size: 12px;
    font-weight: 500;
    margin: 0 0 2px 0;
}
.single-selected-exercises .single-class {
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 25px;
	margin-bottom: 25px;
}
.single-selected-exercises .single-class .single-class-desc {
	color: #999;
	font-size: 12px;
  line-height: 20px;
}
.single-selected-exercises .single-class {
    overflow: hidden;
}

.single-selected-exercises .single-class .single-class-image::before {
	content: "EXERCISES";
	position: absolute;
	top: 0;
	left: 0;
	background: #000;
	color: #fff;
	font-size: 10px;
	font-weight: bold;
	width: 90px;
	text-align: center;
	-webkit-transform: rotate(-40deg);
	transform: rotate(-90deg) translateY(-13px) translateX(-42%);
	transform-origin: 50% 50%;
  display: none;
}

.single-selected-class .single-class .single-class-image {
    margin-right: 20px;
    img {
        width: 160px;
        height: 90px;
        min-width: 160px;
        border-radius: 6px;
    }
}
.single-selected-class .single-class {
	display: flex;
}
.single-selected-class .single-class .single-class-title {
	font-size: 12px;
    font-weight: 600;
    margin: 0 0 2px 0;
}
.single-selected-class .single-class {
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 20px;
	margin-bottom: 20px;
}
.single-selected-class .single-class .single-class-desc {
	color: #999;
	font-size: 12px;
  line-height: 20px;
}
.ajax-search-classes.search-form {
	position: relative;
	width: 100%;
  padding: 0 9px;

    [type='text']{
        width: 100% !important;
        height: 40px;
        font-size: 12px !important;
        letter-spacing: 0.05em;
    }
    .search-button {
        height: 28px;
    }
}

.search-ajax-classes-container .ajax-class, .rout-exerc .single-selected-exercises, .coll-builder .ajax-class, .coll-builder .single-selected-class   {padding:9px 20px !important; border:1px solid #f0f0f0; border-radius: 8px; margin-bottom: 10px; min-height:54px;} 
.coll-builder .single-selected-class .single-class  {margin-bottom: 0; padding-bottom: 0; border-bottom: none;}
.search-ajax-classes-container .ajax-class:last-of-type {margin-bottom: 0 !important;}
.coll-builder .ajax-class span {display:block;}
.rout-exerc .single-selected-exercises {margin-bottom: 10px;}
.single-selected-exercises.added-exercise-rout {margin-top:10px;}

.routine-redinfo {padding-top:35px; padding-bottom: 35px;}

.ajax-search-classes.search-form .search-button {
  height: 28px;
  top: 2px;
}
.not-empty-routine, .empty-routine {margin-bottom: 15px;}

.ajax-class.added {
	opacity: 0.4;
	pointer-events: none;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
    height: 70px;
    min-height: 70px;
    margin-right: 20px;

    img {
        min-height: 70px;
        height: 70px;
        object-fit: cover;
        width: 100%;
        border-radius: 6px;
    }
}
.add_button {
    cursor: pointer;
}
.upload-image.single-subscriber-image {
	height: 140px;
	width: 140px;
	min-width: 140px;
	min-height: 140px;
	border-radius: 50%;
  margin-right: 40px;
}
.search-ajax-classes {
    max-height: 470px;
    overflow-y: auto;
    padding: 0 0;
    width:100%;
}
.search-ajax-classes .ajax-class {
  padding: 25px 0 23px 0;
}
.search-ajax-classes .ajax-class:last-of-type {
	border-bottom: 1px solid #f0f0f0;
}
.round-input.error {
	border-color: red;
}
select.error {
	border-color: red;
}
.base_url {
	position: absolute;
	bottom: 0;
	left: 20px;
	z-index: 1;
	color: #999;
	line-height: 40px;
  font-size: 14px !important;
}
.thumbs-container img:hover {
    transform: scale(1.5);
    z-index: 11;
    position: relative;
    box-shadow: 0 0 30px 0 rgba(0,0,0,0.35);
}
.thumbs-container img {
    transition: transform 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
}
.thumbs-container img.selected {
    box-shadow: 0 0 0 5px $red;
}
.thumbnail1_hidden {
    display: none !important;
}
.pagination {
    height: 100px;
    border-top: $border;
    border-bottom: $border;
}
body .mobile { display: none !important; }
body .mobile-flex { display: none !important; }
body .mobile-inline { display: none !important; }
body .desktop { display: block !important; }
body .desktop-flex { display: flex !important; }
body .desktop-inline { display: inline-block !important; }
.tabs {
    height: auto;
    position: sticky;
    top: 0;
    z-index: 111111;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
}
.sticky_tabs .tab {
    width: 10%;
}
.sticky_tabs .tab img {
    height: 3vw !important;
}
.tab {
    height: auto;
    transition: all 0.25s ease-in-out 0s;
    padding: 0 2px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    width: 20%;
    margin-bottom: 4px;

    &::before {
        content: "";
        // background: #f0f0f0;
        position: absolute;
        top: 0;
        left: 2px;
        z-index: -1;
        width: calc(100% - 4px);
        height: 100%;
        transition: all 0.25s ease-in-out 0s;
    }
    &.active::before {
        // height: 2px;
        box-shadow: 0 0 0 2px #000 !important;
    }
    &:hover img {
        opacity: 0.4;
    }
}
.tab.bulk_form_error::before {
    box-shadow: 0 0 0 2px red !important;
}
body h1, body .h1, body h1 span, body .h1 span,
body h1.light, body .h1.light, body h1.light span, body .h1.light span,
body h1.normal, body .h1.normal, body h1.normal span, body .h1.normal span,
body h1.medium, body .h1.medium, body h1.medium span, body .h1.medium span,
body h2, body .h2, body h2 span, body .h2 span,
body h2.light, body .h2.light, body h2.light span, body .h2.light span,
body h2.normal, body .h2.normal, body h2.normal span, body .h2.normal span,
body h2.medium, body .h2.medium, body h2.medium span, body .h2.medium span,
body h3, body .h3, body h3 span, body .h3 span,
body h3.light, body .h3.light, body h3.light span, body .h3.light span,
body h3.normal, body .h3.normal, body h3.normal span, body .h3.normal span,
body h3.medium, body .h3.medium, body h3.medium span, body .h3.medium span,
body h4, body .h4, body h4 span, body .h4 span,
body h4.light, body .h4.light, body h4.light span, body .h4.light span,
body h4.normal, body .h4.normal, body h4.normal span, body .h4.normal span,
body h4.medium, body .h4.medium, body h4.medium span, body .h4.medium span {
    // font-family: 'Arquitecta' !important;
    font-weight: 600 !important;
    // letter-spacing: 0 !important;
    text-transform: uppercase !important;
}

#main_form h3.mb-3 {
	font-size: 18px !important;
}
.num_of_items.text-capitalize.medium {
	font-weight: 500 !important;
}
.custom-select:nth-child(1){ z-index: 5000; position: relative;}
.custom-select:nth-child(2){ z-index: 4000; position: relative;}
.custom-select:nth-child(3){ z-index: 3000; position: relative;}
.custom-select:nth-child(4){ z-index: 2000; position: relative;}
.custom-select:nth-child(5){ z-index: 1000; position: relative;}
.custom-selectbox-holder {
    width: 100% !important;
}
/*  CUSTOM SELECT  */
.custom-select {
    width: 100%;
}
.small .custom-selectbox-holder {
	height: 40px;
	margin-bottom: 5px;
}
.small .custom-selectbox .select_val {
	height: 40px;
	line-height: 40px;
    font-size: 12px;
}
.custom-selectbox-holder {
	position: relative;
	height: 50px;
	width: 175px;
	margin: 0 0 15px;
}
.custom-selectbox-holder.instructor {
	width: 195px;
}
.custom-selectbox {
	height: auto !important;
	border-radius: 8px !important;
	/* padding: 0px 20px 0px 20px !important; */
	border: 1px solid #ddd !important;
    transition: all 0.15s ease-in-out 0s;
    background: #fdfdfd !important;
    position: absolute;
    top: 1000;
    left: 0;
    width: 100%;
    z-index: 111;
}
.custom-selectbox.not-empty:not(.opened) {
	background: #fdfdfd !important;
}
.custom-selectbox.opened {
    box-shadow: 0 0 50px rgba(51, 51, 51, 0.1);
}
.custom-selectbox .select_val {
	display: block;
	line-height: 40px;
	height: 40px;
	background: url(/admin_assets_new/images/triangle-down.svg) no-repeat right 15px center !important;
	cursor: pointer;
	padding: 0 15px;
    font-size: 14px;
}
.custom-selectbox.opened .select_val {
	border-bottom: 1px solid #f0f0f0 !important;
}
.custom-selectbox .dropdown-holder {
	background: none !important;
    position: relative;
    max-height: 220px;
    overflow-y: auto;
    display: none;
}
.custom-selectbox ul {
	background: none !important;
    position: relative;
    max-height: 220px;
    overflow-y: auto;
    display: none;
    margin: 10px;
}
.custom-selectbox ul li {
	font-size: 12px !important;
	padding: 8px 0px 0px 35px !important;
	position: relative;
	line-height: 20px;
	display: block;
	cursor: pointer;
    margin-bottom: 5px;
}
.custom-selectbox ul li:last-child {
	margin-bottom: 13px;
}
.custom-selectbox ul li::before {
	content: "";
	border: 1px solid #ddd;
	position: absolute;
	top: 8px;
	left: 9px;
	width: 16px;
	height: 16px;
    border-radius: 4px;
    transition: all 0.25s ease-in-out 0s;
}
.custom-selectbox ul li.checked::before {
	border: 1px solid #000;
    background: #000;
}
.custom-selectbox ul li span {
	display: block;
	line-height: 25px;
}
.position-relative {
    position: relative !important;
}
.machine_first {display:block; margin-top:-50px;}

/* CONVERSATIONS */
.red-dot {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	position: absolute;
	top: 0px;
	right: 0px;
	background: #DB1818;
    display: block;
}
.red-dot-small {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	position: absolute;
	top: 8px;
	right: -5px;
	background: #DB1818;
}
.conversation_with {
	position: sticky;
	top: 0;
	background: #fff;
	z-index: 1;
	border-radius: 11px 11px 0 0;
}
.conversation-list {
	padding: 30px;

    .single-msg {
        display: flex;
        margin-bottom: 30px;

        &:last-of-type {
            margin-bottom: 0 !important;
        }
        &.unread + .unread {
            margin-top: 0px;
            position: relative;

            &::after  {display: none;}
            &::before {display: none;}
        }
        &.unread {
            margin-top: 115px;
            position: relative;

            &::after {
                content: "";
                height: 1px;
                position: absolute;
                bottom: calc(100% + 55px);
                left: 0%;
                z-index: 2;
                width: 100%;
                background: #000;
            }
            &::before {
                content: attr(data-msg);
                height: auto;
                position: absolute;
                bottom: calc(100% + 45px);
                left: 50%;
                z-index: 3;
                transform: translateX(-50%);
                width: auto;
                background: #fff;
                padding: 5px 18px;
                font-size: 12px;
                display: inline-block;
                line-height: 1;
            }
            .avatar40::before {
                content: "";
                width: 10px;
                height: 10px;
                border-radius: 50%;
                position: absolute;
                top: 0px;
                right: 0px;
                background: #DB1818;
                display: block;
            }
        }
        .avatar40 {
            margin-left: 0;
            margin-right: 20px;
            overflow: visible;

            img {
                border-radius: 30px;
                overflow: hidden;
                width: 40px;
                height: 40px;
                object-fit: cover;
            }
        }
        &.seb-msg {
            flex-direction: row-reverse;

            .avatar40 {
                margin-right: 0;
                margin-left: 20px;
            }

            .message {
                background: #f8f8f8;
                border: none !important;
            }
        }
        .msg-area {
            max-width: calc(100% - 120px);
            min-width: 200px;

            .message {
                padding: 15px 20px;
                line-height:20px;
                border: 1px solid #f0f0f0;
                border-radius: 10px;
                font-size: 12px;
                font-weight: 400;
                word-break: break-word;

                &.with_url {
                    padding-left: 40px;
                    background: #f8f8f8 url(../images/upload-link-icon.svg) no-repeat center left 15px/15px 8px !important;
                }
                &.with_file {
                    padding-left: 40px;
                    background: #f8f8f8 url(../images/upload-file-icon.svg) no-repeat center left 15px/8px 15px !important;
                }
                &.with_img {
                    padding: 0px;
                    background: none !important;
                    max-width: 320px !important;
                    max-height: 180px !important;
                    border-radius: 10px;
                    position: relative;
                    text-align: right;

                    .download_img_icon {
                        position: relative;
                        max-width: 320px !important;
                        max-height: 180px !important;
                        display: inline-block;
                        text-align: right;
                        border-radius: 10px;
                        overflow: hidden;
                        box-shadow: 0 0 0 1px #dadada;

                        &::before {
                            content: "";
                            background: url(../images/download_img.svg) no-repeat center center/35px 35px !important;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 11;
                            width: 35px;
                            height: 35px;
                        }
                        img {
                            position: relative;
                            max-width: 320px !important;
                            max-height: 180px !important;
                            object-fit: contain !important;
                        }
                        &::after {
                            content: "";
                            transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
                            border-radius: 10px;
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0);
                            z-index: 10;
                        }
                    }
                    &:hover .download_img_icon::after {
                        background: rgba(0,0,0,0.5);
                    }
                }
            }
            .msg-useful {
                font-size: 10px;
                font-weight: 300;
                display: flex;
                align-items: center;
                margin-top: 10px;
                line-height: 1;
                font-weight: 300;

                img {
                    width: 12px;
                }
            }
        }
        .msg-date {
            font-weight: 400;
            line-height: 1;
            font-size: 10px;
            text-align: right;
            margin-top: 10px;
            color: #969696;
        }
    }
}
.conversation-write {
	width: 100%;
	padding: 30px 30px 35px;
	// border-bottom: 1px solid #F0F0F0 !important;

    form {
        position: relative;
        // padding: 28px 0 15px;

        textarea {
            height: 20px;
            width: 100%;
            padding: 0px;
            resize: none;
            line-height: 1.5;
            font-size: 14px;

            // &:placeholder-shown {
            //     padding-top: 8px;
            // }
        }
        button {
            margin-top: 0px;
            border-radius: 30px !important;
        }
    }
}
.select-conv,
.select-conv .flex {
    min-height:290px;
}
.paste {
	width: 24px;
	height: 24px;
	display: inline-block;
	position: absolute;
	top: 43px;
	right: 45px;
	opacity: 0.5;
	cursor: pointer;
}

.conversation-list.bg--loading::after {
	top: 50% !important;
}
.h80 {
	height: 80px;
}
.minH300 {
    min-height: 300px;
}
.avatar40 {
	width: 40px;
	height: 40px;
	border-radius: 30px;
	overflow: hidden;
    position: relative;
}
.initials {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid #eaeaea;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: #fff;
    color: #000;
    text-transform: uppercase;
}
.single-conversation {
	height: 40px;
    padding-left: 15px;
    margin-bottom: 15px;
    cursor: pointer;

    &:hover {
        background: $lightGray;
    }
    &.new_msg {
        background-color: #F8F8F8;
    }
}
.ask-names .single-conversation {
    padding-right:15px;
}
.ask-txt {
    height:72px;
}
.ask-txt .btn {
    border-color: #DB1818;
    font-size:10px !important;
    min-height: 30px;
    height: 30px;
    padding: 0 15px;
}
.ask-txt .btn:hover {
    background-color: #DB1818; 
    color:#fff !important; 
}
#ask-load-more {
    font-size: 10px !important;
    margin-top: 15px;
 }
.insert-question {
    background: #fdfdfd;
    padding: 15px 20px !important;
}
.insert-question:hover {
    box-shadow: 0 0 50px rgba(51, 51, 51, 0.1);
}
.popup-header p {
	font-size: 14px !important;
	text-transform: uppercase;
	font-weight: 600;
}
.col.max230 {
	width: 100%;
	max-width: 295px;
}
.max230 .single_conversation {
	width: 100%;
	max-width: 230px;
}
.clear_search {
	display: block;
	position: absolute;
	top: 50%;
	right: 60px;
	font-size: 31px;
	font-weight: 300;
	transform: translateY(-50%);
	cursor: pointer;
}
.single-course-items {
	// margin-top: 30px;
}
.single-course-video {
    padding: 15px 10px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
        background: #f9f9f9;
    }
}
.single-course-title {
    padding: 30px 10px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
        background: #f9f9f9;
    }
}
.remove-course-video {
	color: $midGray;
	font-size: 12px;
	cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
}
.edit-course-video {
	color: black;
	font-size: 12px;
    margin-right: 15px;
	cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
}

.rows-with-borders .table-row {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px;
}
.rows-with-borders.noimg-table .table-row {
    padding-top: 16px;
    padding-bottom: 16px;
}
.rows-with-borders .table-row.noimgtablerow {
  padding: 26px 20px;
}
.rows-with-borders .table-row.table-row30
{
  padding: 30px 20px;
}
.rows-with-borders .table-row:last-of-type {
  border-bottom: none;
}

.rows-with-borders .table-row:hover {
  background-color: #FCFCFC;
}

.col-name {
  margin-left:auto;
}

.col-name p {
  display:inline-block;
}
.save-delete {
  float: right;
  margin-top: 8px;
}
.normaladmin, .normaladmin:hover {
  background: #f0f0f0 !important;
  border-radius: 50px !important;
  border: none !important;
  color: #969696 !important;
  margin-right: 29px;
}
.superadmin {
  border-radius: 50px !important;
  text-transform: initial !important;
  text-align: center;
  margin-right: 11px;
  border:none;
}
.externaluser, .externaluser:hover {
  border-radius: 50px !important;
  text-transform: initial !important;
  background: #f0f0f0 !important;
  border: none !important;
  color: #969696 !important;
  letter-spacing: 0.05em;
  margin-right: 20px !important;
}
.teacherdate {
  width: 270px;
  text-align: center;
  padding-left: 12px;
}

.subs-date {
  width: 80px;
  text-align: center !important;
}
.superadminbox {
  border-top: 1px solid #F0F0F0;
  border-bottom: 1px solid #F0F0F0;
  padding-top: 47px;
  padding-bottom: 47px;
  margin-top: 50px !important;
}
.delete_checked span {color: #db1818 !Important;}
.delete_checked span:hover {color: #000000 !Important;}
.comments-list .class-item {
  padding: 16px 0 16px 20px;
}
.comments-list .table-row:hover {
  background-color: #FCFCFC;
}
.reversecols {flex-direction: row-reverse;}
.remove-textual {font-size: 12px; cursor: pointer;}
.row-actions {display: none !important;}
.image_options a:first-of-type {margin-bottom:3px;}
.settingsmenu { width: 100%; max-width: 285px; padding-right: 40px;}
.sett-whitemenu {margin-top:-20px;}
.save-delete .delete_record {color: $normalRed; font-weight: 600 !important}
.default-buttons .btn.btn-tall {height: 42px;min-height: 42px;}
.default-buttons .delete_record {border:1px solid #DB1818; padding: 0 25px; height: 42px; line-height: 40px; text-decoration: none !important; border-radius:50px;}
.default-buttons .delete_record:hover {background: #DB1818; color:#fff !important;}
.savedraft {background: none; border: 1px solid #E8AF44; color:#E8AF44 !important; margin-left: 20px;}
.savedraft:hover {background: #E8AF44; color:#fff !important;}
.cancel-link {font-size: 12px; color: #969696; font-weight: 600; text-transform: uppercase; border: 1px solid #f0f0f0; padding: 0 25px; height: 42px; line-height: 40px; border-radius:50px; margin-left:auto;}
.cancel-link:hover {color: #fff; background:#000;border-color: #000;}
.firstlettupp {text-transform: lowercase;}
.firstlettupp:first-letter {text-transform: uppercase;}
.maxredempt {width: 115px; text-align: center;}
.featvideorow a.settvideoph {width:210px; display: block;}
.videos-columns {justify-content: space-between;}
.videos-columns .col-6 {max-width: 515px;}
.videos-columns .ajax-class:hover {background: #FCFCFC;}
.sett-featvideos-wrap .col-12:hover {background: #FCFCFC;}
.removefeatvideo {margin-top:-17px;}
.single-subscriber {display:flex; align-items: center;}
.row.big-gap.reversecols .search-form .search-button:hover {background: none !important;}
// .table.comments-list .table-row .class-item {border-bottom: none;}
// .table.comments-list .table-row:last-of-type .class-item {border-bottom: none;}
.table.comments-list .table-row .class-item + .class-item {padding-left: 60px;padding-top: 0px;}
.upload-zone_audio {min-height: 285px !important;}
.upload-zone_audio.no-border {min-height: 0 !important;}

/*Admin black side bar menu accordion*/
.openmenu {color:#ffffff; border-top: 1px solid rgba(255, 255, 255, 0.15); padding-top: 27px; font-size: 11px; font-weight: 600; cursor: pointer; margin-bottom: 27px !important;
background:url(/admin_assets_new/images/menu-down.svg) right bottom 5px no-repeat; background-size:12px 7.4px;}
.openmenu.accordion-active {background:url(/admin_assets_new/images/menu-up.svg) right bottom 5px no-repeat;}
.openmenu:hover {color: #969696; -webkit-transition: all 0.2s ease-out 0s; transition: all 0.2s ease-out 0s;}
.admin-submenu {display: none; padding-bottom: 7px;}
.admin-submenu li a.side-link {font-weight: 400 !important;}
.admin-submenu li a.side-link:hover, .admin-submenu li a.side-link.active {color:#969696 !important;}
.set_sort_by.selected {color: #000 !important;}
.centerbtns {max-width: 1290px; margin: 0 auto;}
.admin-submenu.currenturlbox {display:block !important;}

/**/
.logdesk-popup {display:none; position: fixed; width:100%; height:100%; background:#fff; top:0; left:0; z-index: 99999999999999999999;}
.login-desktop {width:90%; max-width:420px; padding: 35px; text-align:center; position: relative; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #ffffff;}
.login-desktop img {display:block; margin:0 auto;}
.login-desktop p {color:#969696; margin-top:23px; margin-bottom: 0; font-size: 14px; line-height:26px;}
.small-circle {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	display: block;
}
.model_missing {
	background: rgba(232, 175, 68, 0.2) !important;
    color: #cc6900 !important;
    .calendar-events-time {
        color: #cc6900 !important;
    }
    &:hover {
        background: rgba(232, 175, 68, 0.35) !important;
    }
}
.unpaidbtn {height:30px;}
.dashb-container a {width:100px;}
.class-item .checkbox.contact-forms {height:20px;}
.teacherrole {width: 130px !important; min-width: 130px !important; max-width: 130px !important;}
.teacherstatus-index {margin-right: 100px;}
.teacherstatus {width: 80px !important; min-width: 80px !important; max-width: 80px !important;}
.modelsrole {margin-right: 40px;}
.modelsstatus {margin-right: 107px;}
// .main-content {overflow-x:hidden;}
.exercises_add {color:#000 !important;}
.exercises_add:hover, .add_new_duration_popup:hover {opacity: 1 !important;}
.add-course-video #add-course-video .row .mr-150 {margin-right: 15% !important;}

[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    top: 50%;
    right: calc(100% + 15px);
    white-space: nowrap;
    padding: 5px 7px;
    background: #000;
    color: #fff;
    font-size: 10px;
    transform: translateY(-50%);
    line-height: 1;
    font-weight: 400;
    z-index: 11;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out 0s;
}
[data-tooltip] {
    position: relative;
}
[data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
}

.t-dash-intro {border-top:1px solid #f0f0f0; border-bottom:1px solid #f0f0f0; width:100%; padding-top:30px; padding-bottom: 35px;}    
.t-dash-intro h6 {font-size:18px !important; font-weight:500; margin-bottom: 5px;}    
.t-dash-intro p {font-size:12px; color:#969696;}    
.t-dash-text {margin-top:39px;}
.t-dash-text p {margin-bottom:25px; font-size:14px; line-height:25px;}
.add_image_input {
	position: absolute;
	top: 0;
	left: 0;
	width: 1px;
	height: 1px;
}
.file_uploaded:hover {
	box-shadow: none !important;
}
.remove_tag:hover {
    color: #999 !important
}
.dropdown-sel { display: inline-flex; align-items: center; background: #000; border-radius: 50px; padding:  5px 15px 5px 10px; margin: 15px 10px 0 0;font-size:10px; font-weight:500; color:#fff;}

.remove_tag {
	width: 12px;
	height: 15px;
	display: flex;
	text-align: center;
	align-items: center;
	font-size: 18px;
	transform: translateX(8px);
    cursor: pointer;
    justify-content: flex-start; 
    margin-left: -3px; 
    font-weight: 400 !important; 
    margin-top: -1px;
}
.border-div {border: 1px solid #f0f0f0; border-radius: 10px; padding:35px 30px 30px; margin-bottom: 30px;width: 100%;} 
.border-div .machine_first {margin-bottom: 5px !important; color:#FF0000 !important;}
.bodyparts-div .checkbox.mb-15:last-of-type {margin-bottom: 0 !important;}
.addtransit {border: 1px solid #f0f0f0; border-radius: 10px; padding: 19px 30px; margin-top: 15px;}
.addtransit .btn {padding-left:0; padding-right:0; min-height: 40px !important; height: 40px !important; width: 140px;}
.classroutinecol .btn {min-height:42px; height:42px;}
.add-part .btn:hover {opacity:1 !important;}
.save-routine-popup {max-width:400px !important;}
#video-is-uploading {z-index: 9; font-size: 14px;}
.row.big-gap .table.rows-with-borders .table-row img.img-fluid:not(.handle) {border-radius: 6px;}
.dashboard-widget-list .class-item img.img-fluid {border-radius: 6px !important;}
.popup.add-video-popup .videos-columns .ajax-class .single-class-image img {border-radius:6px;}
.nothingfound {margin-top: 50px; text-align: center;}
.tox.tox-tinymce {border-radius: 10px !important;}
.page-title h1 {padding-top: 78px; padding-bottom: 78px; font-size: 20px !important;}
.help-box {display: flex; align-items: flex-start; justify-content: space-between; padding-bottom: 30px;}
.help-box #main_form {width:40%;}
.normal-list {list-style: disc;}
.normal-list li {margin-bottom: 10px; font-size: 14px; line-height: 26px;}
.normal-list li:last-of-type {margin-bottom: 0;}
.view-instructions-popup {padding: 10px 20px !important; max-width: 600px !important;}
.classes-filter, .exercises-filter  {max-width:400px !important;} 
.bulkedit-wrap {border:1px solid #f0f0f0; border-radius:10px; max-width: 1500px; margin: 0 auto; padding: 38px 15px 18px 15px; justify-content: space-between;}
.rb-title {margin-bottom:35px !important;}
.bulkactions {width:100%; border: 1px solid #f0f0f0; border-radius: 8px; margin-bottom: 10px; padding: 15px 20px;}
.bulkactions a.btn {font-size: 10px !important; padding: 5px 10px; min-height: 0;}
.bulkedit-box {max-width: 603px;}
.bulkedit-box .ajax-class.exercises-class {padding: 17px 20px !important;}
.bulkpopup-info {border-bottom: 1px solid #f0f0f0;
    padding-top: 20px;
    padding-bottom: 20px;}

.scrollbar-track-y {
	width: 6px !important;
    border-radius: 5px !important;
}
.scrollbar-thumb {
	width: 6px !important;
	background: #000 !important;
}
.courses-bodyparts #body_parts_container label {line-height:1;}
.progress-wrap {border-radius:8px;}
.progress-value {border-top-left-radius: 8px; border-bottom-left-radius: 8px;}
.routine-info-box {
    border: 1px solid #f0f0f0;
    border-radius:10px;
    color:#DB1818;
    font-size: 10px;
    line-height: 15px;
    padding:15px;
    text-align: center;
    font-weight: 500;
}
.routine-info-box p {
    padding-left: 40px;
    padding-right: 40px;
}
.btn-txt-twolines {
    display: flex;
    flex-direction: column;
}
.email_routine_wrap {display:flex;align-items: center;}

.custom-selectbox-holder .custom-selectbox ul span.dropdown_checkbox:last-child {
    margin-bottom: 0 !important;
}
.custom-selectbox-holder .custom-selectbox ul span.dropdown_checkbox {
    font-size: 12px !important;
    padding: 0px 0px 0px 50px !important;
    position: relative;
    line-height: 20px;
    display: block;
    cursor: pointer;
    margin-bottom: 10px;
    user-select: none;
}
.custom-selectbox-holder .custom-selectbox ul span.dropdown_checkbox::before {
	content: "";
	border: 1px solid #ddd;
	position: absolute;
	top: 0px;
    z-index: 0;
    left: 20px;
	width: 18px;
	height: 18px;
	border-radius: 4px;
	transition: all 0.25s ease-in-out 0s;
    display: block !important;
}
.custom-selectbox-holder .custom-selectbox ul span.dropdown_checkbox.checked::after {
	content: "";
	position: absolute;
	top: 6px;
    left: 26px;
    z-index: 1;
	width: 8px;
	height: 8px;
	border-radius: 1px;
    background: #000;
}
.custom-selectbox.advanced-dropdown.opened .select_val {
	border-bottom: 1px solid #ddd !important;
}
.routines-list .routine_add span.f-10 {
	line-height: 1 !important;
}
.teachers-search-container .select_teacher {
    display: flex !important;
	align-items: center !important;
	justify-content: space-between;
}
.routines-list .routine_add {
	display: flex !important;
	align-items: center !important;
	justify-content: space-between;
	line-height: 1 !important;
}
.routines-list-wrap {
	min-height: 230px;
}
.custom-selectbox.advanced-dropdown ul {
	max-height: 600px !important;
	padding-bottom: 10px;
}
.routines-list {
	max-height: 300px;
	overflow-y: auto;
	margin: 0 20px;
}
.teachers-list-wrap {
	border: 1px solid #ddd;
	border-radius: 10px;
	height: 42px;
	position: relative;

    &.opened {
        border-radius: 10px 10px 0 0;

        .teachers-list {
            opacity: 1;
            pointer-events: auto;
            visibility: visible;
            transform: translateY(0px);
        }
    }
    .selected_teacher {
        display: block;
        position: relative;
        line-height: 40px;
        padding: 0 15px;
        font-size: 12px;
        color: #969696;
        user-select: none;
        background: url(/admin_assets_new/images/triangle-down.svg) no-repeat right 15px center !important;
    }

    .teachers-list {
        position: absolute;
        top: 100%;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 0 0 10px 10px;
        display: flex;
        flex-direction: column;
        width: calc(100% + 2px);
        max-height: 270px;
        background:#fff;
        z-index: 111;
        left: -1px;
        overflow-y: auto;
        opacity: 0;
        pointer-events: none;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.25s ease-in-out 0s;

        & > span {
            font-size: 12px;
            margin-bottom: 10px;
            color: #000;
            display: flex;
            align-items: center;
            padding-left: 35px;
            justify-content: space-between;
            position: relative;
            min-height: 20px;
            user-select: none;

            &.checked::after {
                content: "";
                position: absolute;
                top: 6px;
                left: 6px;
                z-index: 1;
                width: 8px;
                height: 8px;
                border-radius: 1px;
                background: #000;            
            }
            &::before {
                content: "";
                border: 1px solid #ddd;
                position: absolute;
                top: 0px;
                z-index: 0;
                left: 0px;
                width: 18px;
                height: 18px;
                border-radius: 4px;
                transition: all 0.25s ease-in-out 0s;
                display: block !important;
            }
            
        }
    }
}





/*  RESPONSIVE  */
@media(max-width: 1536px){
    .upload-image.profile-photo {
        width: 140px;
        height: 140px;
        min-width: 140px;
        min-height: 140px;
        padding: 0px;
    }
    .col.max230 {
        max-width: 235px;
    }
    .routine-info-box p {
        padding-left: 20px;
        padding-right: 20px;
    }
}
@include media(">laptop", "<1600px"){
    body {
        padding: 0;
    }
}
@include media(">tablet", "<laptop"){
    body {
        padding: 0;
    }
}
@include media("<tablet"){
    body .mobile { display: block !important; }
    body .mobile-flex { display: flex !important; }
    body .mobile-inline { display: inline-block !important; }
    body .desktop { display: none !important; }
    body .desktop-flex { display: none !important; }
    body .desktop-inline { display: none !important; }

    body {
        padding: 0;
    }
    section {
        padding: 50px;
        position: relative;
    }
    .admin-menu {
        width: 100%;
        height: 80px;
        z-index: 111111;
        padding: 0 25px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .admin-menu .admin-logo {
        padding: 10px;
        border-bottom: none;
        margin-bottom: 0;
    }
    .admin-menu .side-links {
        padding: 0 5px;
        position: fixed;
        top: 80px;
        background: #f8f8f8;
        left: 0;
        width: 100%;
    }

}




@media(max-width:992px){
/*.logdesk-popup {display:block;}*/
.help-box {flex-direction: column;}
.help-box #main_form, .help-box #main_form .input-container, .help-box #main_form .input-container input {width:100%;}
.help-box a.btn {width:100%;}


}

@media screen and (max-width: 767px) {
    .main-content {margin-left: 0;padding:35px 0 0 0;}    
    .mt-90, .mt-80, .mt-85 {margin-top: 35px !important;}
    .mb-5 {margin-bottom: 30px !important;}
    .mb-55 {margin-bottom: 33px !important;}
    .mt-5 {margin-top: 35px !important;}
    .mt-6 {margin-top: 38px !important;}
    .mt-45 {margin-top: 35px !important;}
    .mb-6 {margin-bottom: 35px !important;}
    .mt-55 {margin-top: 35px !important;}
    .mb-45 {margin-bottom:35px !important;}
    .pt-55 {padding-top: 35px !important;}
    .pt-6 {padding-top: 40px !important;}
    :not(.popup-body).pb-5 {padding-bottom: 30px !important;}
    .pb-55 {padding-bottom: 35px !important;}
    .py-6 {padding-top: 35px !important; padding-bottom: 35px !important;}
    .mr-150 {margin-right: 50px !important;}
    .my-80, .my-5, .my-6 {margin-top: 40px !important; margin-bottom: 40px !important;}
    .ml-mob-0 {margin-left:0 !important;}
    .mt-mob-0 {margin-top:0 !important;}
    .mt-mob-3 {margin-top:30px !important;}
    .mb-mob-0, #main_form.mb-mob-0 {margin-bottom:0 !important;}
    .mb-mob-1 {margin-bottom:10px !important;}
    .mb-mob-2 {margin-bottom:20px !important;}
    .mb-mob-3, .input-container.mb-mob-3 {margin-bottom:30px !important;}
    .mb-mob-4 {margin-bottom:40px !important;}
    .mb-mob-5 {margin-bottom:50px !important;}
    .mt-mob-15 {margin-top:15px !important;}
    .mt-mob-25 {margin-top:25px !important;}
    .mt-mob-6 {margin-top:60px !important;}
    .pt-mob-15 {padding-top:15px !important;}
    .pl-mob-0 {padding-left:0 !important;}
    .input-container.mb-mob-4 {margin-bottom: 40px !important;}
    .hide-mob {display: none !important;}
    .flex.aic.ml-2 .checkbox.contact-forms {margin-left:-20px;}
    .rows-with-borders .table-row {padding-left:0 !important; padding-right:0 !important;}
    .input-container {margin-bottom: 10px !important;}
    body.login .input-label {top: 0; font-size: 11px;}
    .superadminbox {padding-top: 38px; padding-bottom: 38px; margin-top: 40px !important;}
    h1.h3 {font-size:18px !important;}
    h4 {font-size:16px !important;}
    .btn {min-height: 34px; padding: 0 20px; font-size: 10px !important;}
    .popup-body .btn {font-size: 12px !important;}
    .admin-menu .admin-logo {margin-bottom:0 !important;}   
    .admin-menu ul.side-links {display:none; background: #000; padding-left: 30px !important; padding-right: 30px !important; overflow-y: scroll; height: 100%;} 
    body.menu-opened .admin-menu ul.side-links {display:block;}
    body .mobile-inline svg {fill:#fff !important;}
    .table.rows-with-borders .class-item a:not(.avatar) img {height: auto !important; width: 130px !important; max-width: 130px;}
    #teacher_container.col-4 {flex: 0 0 100%; max-width: 100%;} 
    .row.big-gap.reversecols .col-6 {width: 100%; flex: 0 0 100%; max-width: 100%;}
    .row.big-gap.reversecols .col-6:nth-of-type(2) {margin-bottom:0;}
    .reversecols {flex-direction: column-reverse;}
    .teacherdate {width: 170px;}
    .row.big-gap.reversecols .custom-select.small.pl-05 {padding-left:0 !important;}
    .dashb-container {padding-bottom: 33px !important;}
    .row.big-gap.dashboard-wrapper .col-6 {flex: 0 0 100%; max-width: 100%;}
    .dashboard-widget {padding: 35px;}
    .class-item a.mr-2, .class-item a.mr-3 {margin-right:15px !important;}
    .settings-wrapper {flex-wrap:wrap !important;}
    .settingsmenu:not(.ask-names) {max-width:100%; padding-bottom: 37px;margin-top: -14px; padding-right: 0;}
    .settingsmenu ul.sett-whitemenu {overflow-x: scroll; width: 100% !important; overflow-y: hidden; margin-left: 0 !important; white-space: nowrap; border-bottom:1px solid #f0f0f0;}
    .settingsmenu ul.sett-whitemenu li {display:inline-block;}
    .settingsmenu ul.sett-whitemenu li a {border:none !important;}
    .sett-right {padding-left:0 !important;}
    .sett-right #popup.settings-tabs, .sett-right #announcement.settings-tabs {margin-left:0;} 
    #main_form .col-8, #main_form .col-6 {flex: 0 0 100%; max-width: 100%;}
    .sett-right.featvideoscol {padding-left: 15px !important;}
    .featvideorow a.settvideoph {width: 130px;}
    .single-video-item .image-overlay img {height: 75px !important;}
    .fac-mob {flex-direction: column;}
    .fac-mob .ml-auto {margin-left: initial !important; margin-top: 15px !important;}
    .ml-mob-5 {margin-left: 5px !important;}
    .mr-mob-10 {margin-right:10px !important;}
    form#video_container {min-height: 200px !important;}
    .popup.add-course-video .popup-body {padding: 25px 25px 60px 25px !important;}
    .popup.add-course-video .col-6 {flex: 100%; max-width: 100%;}
    .survey-content .col-7 {width: 100%; flex: 0 0 100%; max-width: 100%;}
    .progress-wrap {width: 100% !important;}
    .survay-wrap {overflow-x: scroll;}
    .all-surveys-table {width:950px;}
    .all-surveys-table .surveys-user-column {width: 23% !important;}
.single-video-item .ml-3 {margin-left:15px !important;}
.blockmob {display:block;}
.most-title.nrviews {width: 80px;}
.upload-image {margin-right: 15px;}
.col-6.bungee_tension {border-top: 1px solid #f0f0f0; margin-top: 25px; padding-top: 35px;}
.popup.add-course-video form.container {padding-bottom: 65px !important;}
.flex.aic.jcsb.minH45 h1.h3 {margin-top:3px;}
.class-item .checkbox.contact-forms {height:15px;}
.class-item .f-14 {font-size: 12px !important;}
.class-item .f-1, .class-item .f-12 {font-size: 11px !important;}
[type="checkbox"]:not(:checked) + label, [type="checkbox"]:checked + label, [type="radio"]:not(:checked) + label, [type="radio"]:checked + label {padding-left: 27px;}
[type="checkbox"]:not(:checked) + label::before, [type="checkbox"]:checked + label::before, [type="radio"]:not(:checked) + label::before, [type="radio"]:checked + label::before {height: 15px; width: 15px;}
[type="checkbox"]:not(:checked) + label::after, [type="checkbox"]:checked + label::after {left: 4.5px !important; top: -1px !important;}
[type="radio"]:checked + label::after {left: 5px; top: 1px;}
.class-item .midGray, .class-item .midGray.mb-05 {line-height: 18px; margin-bottom: 0 !important;}
.bordertopmob {border-top: 1px solid #f0f0f0; padding-top: 30px; margin-left: 0 !important;}
.teacherdate {margin-right: 27px;}
.teacherrole {width: 65px !important; min-width: 65px !important; max-width: 65px !important;}
.teacherstatus-index {margin-right: 96px;}
.teacherstatus {width: 93px !important; min-width: 93px !important; max-width: 93px !important;}
.externaluser, .externaluser:hover {margin-right: 10px !important;}
.modelsrole {margin-right: 6px;}
.modelsstatus {margin-right: 50px;}
.single-comment .class-item .f-12 {font-size: 12px !important;}
.cancel-link {font-size: 10px;}
.allexercisescol {margin-bottom: 25px !important;}
.allexercisescol .twodropdwn .col-6:first-of-type {margin-bottom: 10px;}
.machine_first {margin-top: -30px;}
.page-title h1 {padding-top: 90px; padding-bottom: 45px;}
.page-title .ml-auto {margin-top: 47px;}
.bulkedit-box {max-width: 100%;}
.bulkedit-wrap {flex-direction: column;}
.bulkedit-right {margin-top:40px;}
.courses-bodyparts #body_parts_container label:before {margin-top:2px;}
.popup.add-routine-exercise-popup {top: 100px;transform: translate(-50%, 0%) scale(1) !important;}
}

@media(max-width:600px){
.login-desktop p {font-size: 12px;line-height: 20px;}
.login-box {height:100%;padding: 60px;}
.dashboard-widget-title.mb-4 {margin-bottom: 20px !important;}
.table.rows-with-borders .class-item a:not(.avatar) img {width: 90px !important; max-width: 90px;}
.teacherdate {width: 100px;}
.dashboard-widget {padding: 25px;}
/*.single-selected-exercises .single-class:last-of-type {margin-bottom: 0;}*/
.wmc-mob {width: min-content !important;}
.popup.add-video-popup .popup-body.p-5 {padding-left:25px !important; padding-right: 25px !important;}
.popup.add-video-popup .col-6.search-ajax-classes .ajax-class {flex-direction: column; padding-left:10px; padding-right: 0;}
.popup.add-video-popup .col-6.search-ajax-classes .ajax-class .pr-2 {width:100%; margin-top: 10px;}
.popup.add-video-popup .ajax-class .single-class-image {margin-right: auto;}
.ajax-class.added::before {position: relative !important; top: 0 !important; right: initial !important; transform: none !important; margin-right: auto;}
.teachers-table .table-row a.avatar {display: none !important;}
.table.rows-with-borders.sett-slider-row .class-item a img {width: 65px !important; height: 45px !important;}
.sett-slider-row .class-item .reorder {margin-right: 20px;}
.dd-list .dd-list {padding-left: 15px;}
.show-popup .overlay .popup.show {width:90%;}
.teacherstatus-index {margin-right: 65px;}
.dashb-container a {width:80px;}
.border-div {padding: 35px 20px 30px;}
.gap-mob-2 { gap: 20px !important; }
.bodyparts-wrapper {flex-direction: column;}
.bodyparts-wrapper .bodyparts-div:not(:last-of-type) {margin-bottom: 20px;}
.btn.loginbtn {width: 100%; height: 46px !important; min-height: 46px !important; font-size: 12px !important;}
.login-box {width: 100%; height: 100%; padding:27px 0 0;}
.admin-logo  {padding-left: 20px !important;
    padding-right: 20px !important;
    padding-bottom: 23px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 30px !important;}
.input-container.loginpass {margin-bottom: 30px !important;}
#submit_login .input-container {width: calc(100% - 100px); margin-left: auto; margin-right: auto;}
.cc-threebtn-box {flex-direction: column;}
.cc-threebtn-box h4 {width:100%;}
.coursecontent-box :nth-child(1 of .first-nested) {
	padding-top: 15px;
}
.coursecontent-box > .dd-item.dd3-item > .dd-handle.dd3-handle {
	margin-left: 8px !important;
}
.coursecontent-box > .dd-item.dd3-item {
	padding-left: 10px !important;
	padding-right: 10px !important;
}
.edit-course-video {
 	margin-right: 10px !important;
}
.dd-list .dd-list {
	padding-left: 25px !important;
	padding-bottom: 13px !important;
}
.dd3-content {
	padding-left: 32px !important;
}
.popup.add-course-video {margin-top: 75px;} 
.fd-mob-column {flex-direction: column;}
}

@media screen and ( max-height: 850px )
{
   .popup.add-routine-exercise-popup {
    transform: translate(-50%, 0%) scale(1) !important;
    top: 100px;
    max-height: calc(100vh - 100px);
  overflow-y: auto;
    }
}