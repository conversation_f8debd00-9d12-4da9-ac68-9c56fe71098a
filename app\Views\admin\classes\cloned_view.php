                            <div class="col-12 pl-0 pr-0 selected_exercise sortable-placeholder single-selected-exercises white-bg selected_routine_exercises" data-rowid="<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="exercises" data-duration="<?php echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? $single['custom_duration'] : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? $single['duration'] : 0); ?>" data-sort="<?php echo (isset($single['csc_sort']) AND $single['csc_sort'] != '') ? $single['csc_sort'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                                <span class="video_preview_tooltip">
                                    <video loop muted playsinline data-src="<?php echo isset($single['video_preview']) ? $single['video_preview'] : ''; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                                </span>
                                <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid mr-2 handle" style="margin-top: 13px;">
                                <div class="single-class aic">
                                    <div class="single-class-rest">
                                        <div class="single-class-title link link-black black f-12 medium <?php echo (isset($single['transition']) AND $single['transition'] == 1) ? 'textGreen' : ''; ?>" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 100%;display: block;padding-right: 140px;" data-popup="<?php echo(isset($single['transition']) AND $single['transition'] != 1) ? 'edit-routine-exercise-popup' : 'edit-transition-popup'; ?>" onclick="<?php echo(isset($single['transition']) AND $single['transition'] != 1) ? 'edit_routine_exercise' : 'edit_transition'; ?>(<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : 0; ?>, $(this))">
                                            <?php echo (isset($single['orientation']) AND $single['orientation'] != '' AND $single['orientation'] != 'N') ? '<span class="mr-05" ' . ($single['orientation'] == 'R' ? 'style="color: #db1818"' : 'style="color: #aa2feb"') . '">(' . $single['orientation'] . ')</span>' : ''; ?><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                        </div>
                                        <?php if(isset($single['transition']) AND $single['transition'] != 1){ ?>
                                        <div class="single-class-desc normal" style="padding-right: 140px;">
                            
                                        <span class="custom_duration"><?php echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? ' ' . duration_standard($single['custom_duration']) : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''); ?></span>
                                        <?php // echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                        </div>
                                        <?php } ?>
                                        <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-exercises').remove();remove_class_from_selected(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>, <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>, 'Exercises')"><img src="images/removeicon.jpg"></span>
                                        <a href="javascript:;" class="link link-midGray midGray f-10 clone_exercise" onclick="clone_exercise('ClassesExercises', <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : 0; ?>)" title="">Clone</a>
                                        <?php
                                            // $max_1 = TRUE;
                                            if(isset($single['springs_count']) AND $single['springs_count'] != ''){
                                                $single_spring_count = json_decode($single['springs_count'], TRUE);
                                                if(is_array($single_spring_count) AND count($single_spring_count) > 0 AND $single_spring_count[0] == ''){
                                                    $single_spring_count = [];
                                                }else{
                                                    // foreach($single_spring_count as $s){
                                                    //     if($s > 1){
                                                    //         $max_1 = FALSE;
                                                    //     }
                                                    // }
                                                }
                                            }else{
                                                $single_spring_count = [];
                                            }
                                        ?>
                                        <div class="colors <?php echo (count($single_spring_count) > 0) ? 'with_number' : ''; ?>">
                                            <?php
                                            foreach($springs as $single_spring){
                                                if($single_spring['color'] != ''){
                                                    $color_id[$single_spring['id']] = $single_spring['color'];
                                                }
                                            }

                                            if(isset($single['springs']) AND $single['springs'] != ''){
                                                $colors = json_decode($single['springs'], true);
                                                
                                                foreach($colors as $k => $single_color){
                                            ?>
                                                <span class="<?php echo $color_id[$single_color]; ?>-bg"><?php echo (isset($single_spring_count[$k])) ? $single_spring_count[$k] : ''; ?></span>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
