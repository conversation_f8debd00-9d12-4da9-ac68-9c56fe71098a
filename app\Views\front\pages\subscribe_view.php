<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="subscribe-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="pt-0 pb-05 mbsec px-2">
        <div class="container1080 mb-05">
            <div class="row">
                <div class="col-12 subscribe-title">
                    <h1 class="semibold m-0 f-24 line-height-small">SUBSCRIBE</h1>
                </div>
            </div>
            <form id="register_subscribe" action="register/validate_subscribe_step_1" class="row nowrap flex" autocomplete="off">
                <input type="hidden" name="stripe_customer" value="<?php echo (isset($logged_user)) ? $logged_user['stripe_customer'] : '' ?>">
                <div class="col pr-4 pr-mob-1 max500 order-mob-2">
                    <div class="subscribe-image-field">
                        <img src="images/subscribe-x2.jpg" alt="" class="img-fluid" />
                        <div class="lightGray-bg subscribe-whats-box">
                            <h2 class="f-14 semibold text-uppercase line-height-normal mb-3">WHAT'S INSIDE:</h2>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> 1,000+ classes right at your fingertips </p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> 1,000+ exercises right at your fingertips </p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Stream from any device, anywhere, anytime</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Courses</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Opportunity to Ask Sebastien a question</p>
                            <p class="f-12 flex aic line-height-normal mb-2"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Custom playlists</p>
                            <p><a href="/what-is-lod" class="link link-black black f-12 line-height-small text-underline" title="See the full list of features">Learn more about LagreeOD</a></p>
                        </div>
                    </div>
                </div>
                <div class="col pl-4 pr-0 w100 px-mob-1 mb-mob-3 order-mob-1">
                    <div class="panel subsc-right border for--loading">
                        <div class="subscribe_steps_wrapper">
                            <div class="subscribe_step step_1 active">
                                <div class="w100 subsc-plans">
                                    <h4 class="line-height-small f-14 semibold">CHOOSE YOUR PLAN</h4>
                                    <div class="panel mb-15 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'selected' : ''; ?>">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe3" data-plan="Weekly" data-price="3.99" data-unit="" value="Weekly Subscription">
                                            <label for="subscribe3" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small">
                                                    <p class="medium line-height-small" style="margin-bottom:7px;">WEEKLY</p>
                                                    <span class="f-12 midGray line-height-small">$3.99/week</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="panel mb-15 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'selected' : ''; ?>">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe1" data-plan="Monthly" data-price="9.99" data-unit="/mo" value="Monthly Subscription">
                                            <label for="subscribe1" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small">
                                                    <p class="medium line-height-small" style="margin-bottom:7px;">MONTHLY</p>
                                                    <span class="f-12 midGray line-height-small">$9.99/month</span>
                                                </span>
                                                <!--<span class="f-0 ml-auto mr-2 mr-mob-3">MOST POPULAR</span>-->
                                            </label>
                                        </div>
                                    </div>
                                    <div class="panel mb-0 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'selected' : ''; ?>">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe2" data-plan="Annual" data-price="99.99" data-unit="" value="Annual Subscription">
                                            <label for="subscribe2" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small mr-1">
                                                    <p class="medium line-height-small" style="margin-bottom:7px;">ANNUALLY</p>
                                                    <span class="line-height-small f-12 midGray annual-price" style="white-space: nowrap">$99.99/year</span>
                                                </span>
                                                <span class="f-10 line-height-small ml-auto mr-2 mr-mob-3 medium">
                                                    <span class="best-value">Save 20%</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <hr class="my-6">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="w100 flex sic jcsb pb-5">
                                            <h4 class="line-height-small f-14 semibold" style="white-space: nowrap;">Create an Account</h4>
                                            <div class="text-transform-none">
                                                <p class="midGray f-1 line-height-small" style="letter-spacing: 0;"><span class="desktop-inline line-height-small mr-05">Have an account?</span> <a href="javascript:;" data-popup="login-popup" class="red text-underline line-height-small">Log in</a></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="input-container">
                                        <label>First Name</label>
                                            <input type="text" name="firstname" class="line-input step_1_input" id="subscribe_firstname" placeholder="Enter">
                                            <!--<span class="input-label">First name</span>-->
                                            <span id="Firstname_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-container">
                                        <label>Last Name</label>
                                            <input type="text" name="lastname" class="line-input step_1_input" id="subscribe_lastname" placeholder="Enter">
                                            <!--<span class="input-label">Last name</span>-->
                                            <span id="Lastname_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-container">
                                        <label>Email</label>
                                            <input type="text" name="email" class="line-input step_1_input" id="subscribe_email" placeholder="Email" onblur="check_email()">
                                            <!--<span class="input-label">Email</span>-->
                                            <span id="Email_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-container mb-0">
                                            <span class="reveal_password">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                                <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                            </svg>
                                            </span>
                                            <label>Password</label>
                                            <input type="password" name="password" class="line-input step_1_input" id="subscribe_password" placeholder="Enter">
                                            <!--<span class="input-label">Password</span>-->
                                            <span id="Password_error" class="input-error"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="subscribe_step step_2">
                                <div class="row">
                                    <div class="col-12 mb-6">
                                        <h4 class="line-height-small f-14 semibold">enter verification code</h4>
                                        <!-- <p class="f-12">Code has is sent to your email. Please enter the code below</p> -->
                                    </div>
                                    <div class="col-12 flex" style="gap: 10px;">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                        <input type="text" class="code-field border w100" maxlength="1" onkeypress="return onlyNumberKey(event)" placeholder="0" value="" style="height: 65px;padding: 0;  font-size: 20px;text-align: center">
                                    </div>
                                </div>
                            </div>

                            <div class="subscribe_step step_3">
                                <div class="payment-part">
                                    <div class="row">
                                        <div class="col-12">
                                            <h4 class="line-height-small f-14 semibold flex aic jcsb">
                                                Payment Details
                                                <a href="javascript:;" class="f-12 link link-midGray midGray text-transform-none" title="← Go back">← Go back</a>
                                            </h4>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 flex">
                                            <div class="input-container">
                                            <label>Name on card</label>
                                                <input type="text" name="card[name]" class="line-input" id="name" placeholder="Enter">
                                                <!--<span class="input-label">Name on card</span>-->
                                                <span id="name_error" class="input-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-12 flex ddyycvc">
                                            <div class="input-container mb-0">
                                            <label>Card number</label>
                                                <input type="text" name="card[number]" class="line-input" id="card-number" maxlength="19" placeholder="Enter" onkeypress="return onlyNumberKey(event)">
                                                <!--<span class="input-label">Card number</span>-->
                                                <span id="card-number_error" class="input-error"></span>
                                            </div>
                                            <div style="width: 50px;" class="input-container ml-auto mb-0 cardinfo">
                                                <input type="text" name="card[exp_month]" class="line-input card_month" id="card-month-year" placeholder="MM" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                                <!--<span class="input-label">MM</span>-->
                                                <span id="card-number_error" class="input-error"></span>
                                            </div>
                                            <div style="width: 50px;" class="input-container ml-auto mb-0 cardinfo">
                                                <input type="text" name="card[exp_year]" class="line-input card_year" id="card-month-year" placeholder="YY" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                                <!--<span class="input-label">YY</span>-->
                                                <span id="card-number_error" class="input-error"></span>
                                            </div>
                                            <div style="width: 60px" class="input-container ml-auto mb-0 cardinfo">
                                                <input type="text" name="card[cvc]" class="line-input" id="card-cvc" placeholder="CVC" maxlength="3" onkeypress="return onlyNumberKey(event)">
                                                <!--<span class="input-label">CVC</span>-->
                                                <span id="card-number_error" class="input-error"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 mb-05">
                                            <div class="top-border bottom-border coupon-form">
                                                <h4 class="f-14 flex aic jcsb line-height-small mb-4 pb-05">
                                                    <span class="mr-05 line-height-small semibold">Have a coupon?</span>
                                                </h4>
                                                <div class="code-container">
                                                    <label>Enter code</label>
                                                    <input type="text" name="coupon" class="line-input subscribe-code" placeholder="Enter">
                                                    <a href="javascript:;" class="check-code sub-check check-code-buttom pr-2">Apply</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-left w100 f-12" style="margin-top: -20px;">
                                        <span style="display: none;" class="valid_coupon">Applied Coupon: <span class="coupon_message semibold"></span> <a href="javascript:;" class="link link-black black text-underline remove_coupon" title="Remove">Remove</a></span>
                                        <span style="display: none;" class="not_valid_coupon">Coupon is invalid!</span>
                                    </div>
                                </div>
                                <div class="second-form-part">
                                    <div class="row mt-2 mb-2">
                                        <div class="col-12 pt-05 pb-05">
                                            <h4 class="f-14 flex aic jcsb line-height-small">
                                                <span class="line-height-small semibold">
                                                    <span class="subscription-type mr-05 line-height-small semibold">Please select</span>
                                                    Subscription
                                                </span>
                                                <span class="ml-auto line-height-small semibold">
                                                    <b class="line-height-small">
                                                        $<span class="subscription-price line-height-small semibold">0</span><sup class="subscription-unit">/mo</sup>
                                                    </b>
                                                </span>
                                            </h4>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 midGray subscribe-txt">
                                        <p class="lh-20 f-10">By clicking below, you agree to our <a class="midGray lh-20" href="/privacy-policy" target="_blank"><u class="lh-20">Privacy Policy</u></a>, and automatic renewal. You authorize this site, appearing as OTT* MAXIMUM FITNESS, to hold and charge your card automatically based on your subscription plan. Cancel any time in your account settings.</p>
                                        </div>
                                    </div>
                                    <div class="row row-mob flex aic">
                                        <div class="col-6 mob-half line-height-normal f-12">
                                            Are you human<br>
                                            <input type="text" name="s1" id="s1" value="" style="width: 20px;padding: 0;border: 0;display: inline;pointer-events: none;" readonly>+&nbsp;<span class="s2"></span>&nbsp;=&nbsp;
                                        </div>
                                        <div class="col-6 mob-half">
                                            <input type="text" name="sum" required class="line-input border px-2 f-14" placeholder="Enter" />
                                        </div>
                                    </div>
                                    <hr class="my-4">
                                    <div class="row">
                                        <div class="col-12 flex aic jcc pb-4">
                                            <button type="submit" class="btn f-14 black-bg white w100 h50"><?php echo (isset($logged_user['stripe_subscription']) AND $logged_user['stripe_subscription'] != NULL) ? 'Change Your Subscription' : 'Start Your Subscription'; ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w100 continue_button_wrap">
                            <div class="row">
                                <div class="col-12">
                                    <div class="px-6 pb-4">
                                        <hr class="my-4">
                                        <a href="javascript:;" class="btn black-bg white w100 continue_button disabled h50" title="CONTINUE" onclick="next_step()">CONTINUE</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
$(document).ready(function(){
    var item = $('.subscribe_step.step_1');
    var h = item.outerHeight();
    $('.subscribe_steps_wrapper').css({height: h + 'px'});
});
var subscription_type = $('[name=subscription_type]'),
    firstname = $('#subscribe_firstname'),
    lastname = $('#subscribe_lastname'),
    email = $('#subscribe_email'),
    password = $('#subscribe_password');

$('.step_1_input').on('keypress blur', function(){
    if(firstname.val() != '' && lastname.val() != '' && email.val() != '' && password.val() != ''){
        $('.continue_button').removeClass('disabled');
    }else{
        $('.continue_button').addClass('disabled');
    }
});
function next_step(){
    var active = $('.subscribe_step.active');
    var i = active.index();
    var h = active.next().outerHeight();
    console.log(i);

    if(i < 2){
    }
    // send code
    if(i == 0){

        if(subscription_type.val() == '' || firstname.val() == '' || lastname.val() == '' || email.val() == '' || password.val() == ''){
            subscription_type == '' ? subscription_type.closest('.subscription-option').addClass('error') : subscription_type.closest('.subscription-option').removeClass('error');
            firstname == '' ? firstname.addClass('error') : firstname.removeClass('error');
            lastname == '' ? lastname.addClass('error') : lastname.removeClass('error');
            email == '' ? email.addClass('error') : email.removeClass('error');
            password == '' ? password.addClass('error') : password.removeClass('error');
            app_msg('Please fill all required fields');
        }else{
            $('.subscribe_steps_wrapper').css({height: h + 'px'});
            $('.subscribe_step').removeClass('active');
            $('.subscribe_step').eq(i+1).addClass('active');
        }
        
    }
    if(i+1 == 2){
        $('.continue_button_wrap').hide();
    }else{
        $('.continue_button_wrap').show();
    }
}
function check_email(){
    var email = $('#subscribe_email').val();

    if(email != ''){
        $.ajax({
            type: 'POST',
            url: 'register/check_email',
            data: {
                email
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $('#Email_error').html('Account with this email already exists. <a href="javascript:;" data-popup="login-popup" class="link link-black black text-underline">Login?</a>');
                    $('.continue_button').addClass('disabled');
                }else{
                    $('.continue_button').removeClass('disabled');
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
function send_code(){
    var email = $("#subscribe_email").val();
    
    if(email != ''){
        $.ajax({
            type: 'POST',
            url: 'register/send_code',
            data: {
                email
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
$('.code-field').on('keyup', function(){
    if($(this).val() != ''){
        $(this).next().focus();
    }
    var empty_code_field = 0;
    $('.code-field').each(function(i, el){
        if($(this).val() == ''){
            empty_code_field++; 
        }
    });
    if(empty_code_field == 0){
        var email = $("#subscribe_email").val();
        var code = '';

        $('.code-field').each(function(i, el){
            var v = $(this).val();
            code = code + v;
        });
        console.log(code);
        console.log(email);
        
        $('.code-field').css({opacity: '0.3'});
        $.ajax({
            type: 'POST',
            url: '/register/verify_email_code',
            data: {
                code,
                email
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $('.continue_button').trigger('click');
                }else{
                    app_msg('The code you have entered is invalid.')
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }else{
        $('.code-field').css({opacity: '1'});
    }
});
$('.code-field').on('focusin', function(){
    $(this).attr('placeholder', '');
});
$('.code-field').on('focusout', function(){
    $(this).attr('placeholder', '0');
    var empty_code_field = 0;
    $('.code-field').each(function(i, el){
        if($(this).val() == ''){
            empty_code_field++; 
        }
    });
    if(empty_code_field == 0){
        $('.code-field').css({opacity: '0.3'});
    }else{
        $('.code-field').css({opacity: '1'});
    }
});
</script>	
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/subscribe.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script> -->
<!-- <script src="js/google_login.js"></script> -->
<script>
console.log('GUEST SUBSCRIBE TEMPLATE');
$('.check-code').on('click', function(){
    var code = $('.subscribe-code').val();
    var plan_selected = $('.subscription-option.selected').find('input').data('plan');
    var button = $(this);
    button.addClass('btn--loading');
    console.log(plan_selected);
    if(code == ''){
        app_msg('Please enter coupon code!');
        setTimeout(function(){
            button.removeClass('btn--loading');
        },1200);
    }else{
        $.ajax({
            type: 'POST',
            url: 'register/check_coupon',
            data: {
                code: code
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if(data.success){
                    if(data.valid.valid){
                        button.removeClass('btn--loading');
                        console.log('INCLUDES: ', data.valid.metadata.includes(plan_selected));
                        // if(!data.valid.metadata.includes(plan_selected)){
                        //     $('.subscribe-code').val('');
                        //     app_msg('Coupon does not apply to selected subscription plan.')
                        // }else{
                            $('.coupon-form').addClass('disabled');
                            $('.valid_coupon').show().find('.coupon_message').text(data.valid.name);
                            $('.not_valid_coupon').hide();
                        // }
                    }else{
                        setTimeout(function(){
                            $('.subscribe-code').val('');
                            button.removeClass('btn--loading');
                        },2000);
                        $('.coupon-form').removeClass('disabled');
                        $('.valid_coupon').hide();
                        $('.not_valid_coupon').show();
                    }
                    button.removeClass('btn--loading');
                }else{
                    console.log(data.message);
                    $('.not_valid_coupon').show();
                    $('.valid_coupon').hide();
                    setTimeout(function(){
                        $('.subscribe-code').val('');
                        button.removeClass('btn--loading');
                    },2000);
                }
            },
            error: function (request, status, error) {
                alert('Error');
                button.removeClass('btn--loading');
            }
        });
    }
});
$('.remove_coupon').on('click', function(){
    $('.subscribe-code').val('');
    $('.coupon-form').removeClass('disabled');
    $('.valid_coupon').hide();
    $('.not_valid_coupon').hide();
});
$('.subscription-option').on('click', function(){
    $('.subscription-option').removeClass('selected');
    $(this).addClass('selected');
    $('.remove_coupon').trigger('click');
});
$(document).ready(function(){
    $('#subscribe3').trigger('click').change();
});
</script>
</body>
</html>