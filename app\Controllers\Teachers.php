<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Teachers extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('TeachersModel');
        $controller = 'teachers';
	}

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['all_teachers'] = $this->model->all_certified_teachers();

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Teachers of Lagree On Demand | Online Lagree Fitness Trainers';
		$data['current']['seo_description'] = 'Meet our online Lagree teachers who are serving up workouts on the Micro, Mini, and Megaformer. Our Lagree On Demand teachers can help you meet your fitness goals!';
		echo view('front/teachers/index_view', $data);
    }

    public function slug($slug = '')
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['current'] = $this->model->current($slug);
		$data['current']['seo_title'] = $data['current']['firstname'] . ' ' . $data['current']['lastname'];
		$data['current']['seo_description'] = 'Lagree On Demand Teachers';
		$data['current']['seo_keywords'] = 'Lagree On Demand Teachers';
        if(isset($data['current']['id']) AND $data['current']['id'] > 0){
            $data['teacher_classes'] = $this->model->teacher_classes($data['current']['id']);
        }else{            
            $data['teacher_classes'] = [];
        }

        // $data['total_duration'] = 0;
        // foreach($data['selected_classes_for_selection'] as $single){
        //     $data['total_duration'] = $data['total_duration'] + ((isset($single['duration']) AND $single['duration'] !='') ? $single['duration'] : 0);
        // }
        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/teachers/single_view', $data);
    }
    public function calendar($date = '')
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        if($date == '') {
            $data['date'] = date('Y-m-d');
        }else{
            $data['date'] = $date;
        }
		$data['current']['seo_title'] = 'Calendar';
		$data['current']['seo_description'] = 'Lagree On Demand Calendar';
		$data['current']['seo_keywords'] = 'Lagree On Demand Calendar';
        // $data['teacher_classes'] = $this->model->teacher_classes($data['current']['id']);

        $data['events'] = $this->model->query("SELECT calendar_events.id, IF(calendar_events.paid != 5, IF(calendar_events.date < NOW(), IF(calendar_events.paid = 2, 1, calendar_events.paid), 2), 5) as paid, calendar_events.class_id, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing'))) as teacher_model, IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing')) as any_model
                                                            FROM calendar_events 
                                                            INNER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                            LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                            LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                            INNER JOIN classes ON classes.id = calendar_events.class_id
                                                            WHERE date = '" . $data['date'] . "'
                                                            AND paid != 5
                                                            AND calendar_events.deleted_at IS NULL
                                                            AND classes.deleted_at IS NULL
                                                        ")->getResultArray();

		return view('front/teachers/calendar_view', $data);
    }

    public function routine_tv($id = 0)
    {
        $db = \Config\Database::connect();
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        $data['current']['title'] = 'Routine TV';
        $data['current']['seo_title'] = 'Routine TV';
        $data['current']['seo_description'] = 'Routine TV';
        $data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();

        if($id == 0) {
            return redirect()->to(base_url('teachers/calendar'));
        }else{
            $ClassesModel = model('ClassesModel');
            $data['exercises'] = $ClassesModel->exercises_for_single_class($id);
                                                        
            $data['event'] = $ClassesModel->query("SELECT calendar_events.*, 
                                                        CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(teachers.firstname != '', teachers.firstname, 'Model is missing'))) as teacher_model, teachers.firstname as teacher_firstname
                                                        FROM calendar_events 
                                                        LEFT OUTER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                        LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                        LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                        WHERE calendar_events.class_id = " . $id . " 
                                                        AND calendar_events.deleted_at IS NULL")->getRowArray();
    
            foreach($data['exercises'] as $key => $single){            
                $data['exercises'][$key]['custom_duration'] = duration_standard($single['custom_duration']);
            }
        }

		return view('front/teachers/calendar-event_view', $data);
    }

    public function ajax_calendar()
    {
        $post = $this->request->getPost();

        if($post['date'] == '') {
            $post['date'] = date('Y-m-d');
        }
        $data['events'] = $this->model->query("SELECT calendar_events.id, IF(calendar_events.paid != 5, IF(calendar_events.date < NOW(), IF(calendar_events.paid = 2, 1, calendar_events.paid), 2), 5) as paid, calendar_events.class_id, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing'))) as teacher_model, IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing')) as any_model
                                                            FROM calendar_events 
                                                            INNER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                            LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                            LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                            INNER JOIN classes ON classes.id = calendar_events.class_id
                                                            WHERE date = '" . $post['date'] . "'
                                                            AND paid != 5
                                                            AND calendar_events.deleted_at IS NULL
                                                            AND classes.deleted_at IS NULL
                                                        ")->getResultArray();
        // if(!empty($data['events'])){
            $data['success'] = TRUE;
        // }
		// return view('front/teachers/calendar_view', $data);
		return $this->respond($data);
    }

    public function ajax_date()
    {
        $post = $this->request->getPost();

        $ClassesModel = model('ClassesModel');
        $response['success'] = TRUE;
        $response['exercises'] = $ClassesModel->exercises_for_single_class($post['id']);
                                                    
        $response['event'] = $ClassesModel->query("SELECT calendar_events.*, 
                                                    CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(teachers.firstname != '', teachers.firstname, 'Model is missing'))) as teacher_model
                                                    FROM calendar_events 
                                                    LEFT OUTER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                    LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                    LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                    WHERE calendar_events.class_id = " . $post['id'] . " 
                                                    AND calendar_events.deleted_at IS NULL")->getRowArray();

        // echo '<pre>';
        // print_r($response['exercises']);
        // die();
        foreach($response['exercises'] as $key => $single){            
            $response['exercises'][$key]['custom_duration'] = duration_standard($single['custom_duration']);
        }

		return $this->respond($response);
    }
}