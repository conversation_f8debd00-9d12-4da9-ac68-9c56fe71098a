<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="classes-page" onload="filter_classes()">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="px-100">
        <div class="container-fluid">
            <div class="row">
                <div class="col-7">
                    <h1 class="light mb-2 h2">All Classes</h1>
                    <p class="light f-16">Get unlimited access to our growing collections of Lagree's Microfomer and Megaformer classes. New classes added every day!</p>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 px-100 pb-7">
        <!-- <div class="container-fluid">
            <div class="row aic">
                <div class="col-6">
                    <h2>Featured Collections</h2>
                </div>
                <div class="col-6 text-right desktop">
                    <a href="/collections" class="link link-midGray midGray f-14" title="View All Collections">View All Collections</a>
                </div>
            </div>
        </div> -->
        <!-- <div class="container-fluid">
            <div class="row my-100 big-gap">
                <div class="col-12">
                    <div class="swiper featured-collection">
                        <div class="swiper-wrapper">
            <?php
            foreach($featured_collections as $single){
            $classes = (isset($single['selected_classes']) AND $single['selected_classes'] != '') ? json_decode($single['selected_classes']) : FALSE;
            ?>
                            <div class="swiper-slide">
                                <div class="img-panel h100 single-featured-collection">
                                    <div class="image-overlay h100"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ''; ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" /></div>
                                    <div class="img-panel-content ail">
                                        <a href="collections/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="white">
                                            <h2 class="mb-2 white semibold"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h2>
                                        </a>
                                        <h3 class="mb-4 f-18 white light mb-mob-2 desktop"><?php echo (isset($single['short_desc']) AND $single['short_desc'] != '') ? character_limiter($single['short_desc'], 100) : ''; ?></h3>
                                        <div class="buttons">
                                            <a href="collections/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="btn white-bg black" title="Watch">Watch</a>
                                            <span class="white f-14 f-12-mob ml-2 light"><?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] : 0; ?> CLASSES</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
            <?php
            }
            ?>
                        </div>
                        <div class="featured-pagination"></div>
                    </div>
                </div>
            </div>
        </div> -->
        <!-- <div class="container-fluid">
            <div class="row aic flex-row">
                <div class="col-6 col-mob-6">
                    <h2 class="mob-f-18">Recently Added</h2>
                </div>
                <div class="col-6 col-mob-6 text-right">
                </div>
            </div>
        </div>
        <hr class="my-5 mb-mob-3 mobile"> -->
        <div class="container-fluid mb-100">
            <div class="row big-big-gap">
                <div class="col-12 flex aic">
                    <div class="mobile-flex filters-switch">FILTER CLASSES <img src="images/arrow-down.svg" alt="" class="ml-1 img-fluid d-inline-block"></div>
                    <div class="dropdown d-inline-block ml-auto mobile-inline">
                        <!-- <span class="black desktop-inline f-12 light">Sort by:</span> -->
                        <span class="dropdown-button f-12" data-dropdown><span class="sort_by_title">Date Added</span> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.created_at/desc" title="">Date Added</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.title/asc" title="">Ascending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.title/desc" title="">Descending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="countView/desc" title="">Popularity</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classRate/desc" title="">Best Rated</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 flex aic jcsb mobile-flex-vertical">
                    <div class="filters">
                        <div class="custom-select">
                            <div class="custom-selectbox-holder">
                                <div class="custom-selectbox">
                                    <span class="select_val">Machines <span class="select_count"></span></span>
                                    <ul>
                                    <?php foreach($machines as $single){ ?>
                                        <li data-val="<?php echo $single['id']; ?>"><?php echo $single['title']; ?></li>
                                    <?php } ?>
                                    </ul>
                                    <input type="hidden" name="machine[]" class="select-machines">
                                </div>
                            </div>
                        </div>
                        <div class="custom-select">
                            <div class="custom-selectbox-holder" data-type="machine">
                                <div class="custom-selectbox">
                                    <span class="select_val">Duration <span class="select_count"></span></span>
                                    <ul>
                                        <?php if($duration_less10[0]['cnt'] > 0){ ?><li data-val="< 600">< 10 min</li><?php } ?>
                                        <?php if($duration_less25[0]['cnt'] > 0){ ?><li data-val="> 600 AND classes.duration < 1500">10-25 min</li><?php } ?>
                                        <?php if($duration_more25[0]['cnt'] > 0){ ?><li data-val="> 1500">> 25 min</li><?php } ?>
                                    </ul>
                                    <input type="hidden" name="duration[]" class="select-duration">
                                </div>
                            </div>
                        </div>
                        <div class="custom-select">
                            <div class="custom-selectbox-holder">
                                <div class="custom-selectbox">
                                    <span class="select_val">Difficulty <span class="select_count"></span></span>
                                    <ul>
                                    <?php foreach($difficulty as $single){ ?>
                                        <li data-val="<?php echo $single['id']; ?>"><?php echo $single['title']; ?></li>
                                    <?php } ?>
                                    </ul>
                                    <input type="hidden" name="difficulty[]" class="select-difficulty">
                                </div>
                            </div>
                        </div>
                        <div class="custom-select">
                            <div class="custom-selectbox-holder instructor">
                                <div class="custom-selectbox">
                                    <span class="select_val">Teacher <span class="select_count"></span></span>
                                    <ul>
                                    <?php foreach($all_teachers as $single){ ?>
                                        <li data-val="<?php echo $single['id']; ?>"><?php echo $single['firstname']; ?> <?php echo $single['lastname']; ?></li>
                                    <?php } ?>
                                    </ul>
                                    <input type="hidden" name="teacher[]" class="select-teacher">
                                </div>
                            </div>
                        </div>
                        <div class="custom-select">
                            <div class="custom-selectbox-holder">
                                <div class="custom-selectbox">
                                    <span class="select_val">Body parts <span class="select_count"></span></span>
                                    <ul>
                                    <?php foreach($body_parts as $single){ ?>
                                        <li data-val="<?php echo $single['id']; ?>"><?php echo $single['title']; ?></li>
                                    <?php } ?>
                                    </ul>
                                    <input type="hidden" name="body_parts[]" class="select-body_parts">
                                </div>
                            </div>
                        </div>
                        <span class="link link-midGray midGray clear_filter f-12 ml-2" style="display: none;">x Clear</span>
                    </div>
                    <div class="dropdown d-inline-block desktop-inline">
                        <!-- <span class="black desktop-inline f-12 light">Sort by:</span> -->
                        <span class="dropdown-button f-12" data-dropdown><span class="sort_by_title">Date Added</span> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.created_at/desc" title="">Date Added</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.title/asc" title="">Ascending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classes.title/desc" title="">Descending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="countView/desc" title="">Popularity</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by f-12" data-sort="classRate/desc" title="">Best Rated</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row no_products text-center px-5 py-5" style="display: none;">
                <h3 class="h4 text-center w100">There are no classes matching your filters. Try removing some.</h3>
            </div>
            <div class="row big-big-gap products_list" style="min-height: 100px !important"></div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="midGray load_more">Loading More...</h3>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
<script src="js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>