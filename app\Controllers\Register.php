<?php namespace App\Controllers;

use App\Models\FormModel;
use App\Models\StripeModel;
use App\Models\EmailModel;
use App\Models\EmailCodeModel;
use App\Models\BlacklistModel;
use App\Models\NotificationsModel;
use App\Models\PaymentAttemptsModel;
use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Register extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
		$this->prices = array(
			'Monthly Subscription' => $_ENV['price_month'],
			'Annual Subscription' => $_ENV['price_year'],
			'Weekly Subscription' => $_ENV['price_week'],
			'Weekly Subscription Price' => 3.99,
			'Monthly Subscription Price' => 9.99,
			'Annual Subscription Price' => 99.99,
			'Buy Class' => $_ENV['class_price'],
			'Rent Class' => $_ENV['class_rent'],
			'Teachers cut' => $_ENV['teachers_cut'],
		);
	}

    public function index()
	{
        return view('form_view');
    }

    public function validate_subscribe_step_1()
    {
		$email_model = new EmailModel();
        $BlacklistModel = new BlacklistModel();
		$EmailCodeModel = new EmailCodeModel();
        $NotificationsModel = new NotificationsModel;
		$logged_user = $this->user;
		$request = service('request');

        $validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$rules['subscription_type']    = [
            'label'  => 'Subscription type',
            'rules'  => 'required',
        ];

        $messages = $this->model->validationMessages;
		$data = $this->request->getPost();
        $data['code_verified'] = 1;
        session()->set('code_verified', 'verified');

        // $data['code_verified'] = session()->get('code_verified');
        
        $response['code_verified'] = $data['code_verified'];
		$response['post'] = $data;
		$response['rules'] = $rules;

        // CHECK BLACKLIST
        if(isset($data['email']) AND $data['email'] != ''){
            $blocked = $BlacklistModel->where(['email' => $data['email']])->first();
            if($blocked != NULL){
                $ip_address = getenv('REMOTE_ADDR');
                $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                $this->_block_user_sucuri($ip_address);
                $response['success'] = FALSE;
                //                    

                return $this->respond($response);
            }
        }                
        // END CHECK BLACKLIST

        if(isset($data['email']) AND strpos($data['email'], '@perfecthm.com') === FALSE){
            $validation->reset();
            $validation->setRules($rules);
            if (!$validation->run($data)){
                $response['message'] = implode('</br>', $validation->getErrors());
                $response['json'] = $validation->getErrors();
                $response['list'] = service('validation')->listErrors();
                return $this->respond($response);
            }else{
                $to = $data['email'];
                $subject = 'Lagree On Demand - Confirmation Code';
                $code = $this->generateEmailCode();
                $data_save = [
                    'email' => $data['email'],
                    'code' => $code
                ];
                session()->set('subscribe_info', $data);
                $template = 'front/email_templates/send-code';

                if(isset($data['code_verified']) AND $data['code_verified'] != NULL){
                    $response['success'] = TRUE;
                }else{
                    $response['save_code'] = $EmailCodeModel->save($data_save);
                    $response['email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_save, $template);
                }
            }
        }
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function subscribe()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

        session()->set('code_verified', 'verified');

        if(isset($data['logged_user']) AND $data['logged_user'] != NULL){
            return redirect()->to('/account/payments');
        }

        $data['code_verified'] = session()->get('code_verified');
        if(isset($data['code_verified']) AND $data['code_verified'] != NULL){
            $data['session'] = session()->get('subscribe_info');
        }
        $data['current']['title'] = "Subscribe Confirmation | Lagree On Demand";
        $data['current']['seo_title'] = 'Subscribe Confirmation | Lagree On Demand';
        $data['current']['seo_description'] = 'Lagree On Demand Subscribe Confirmation';
        $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Confirmation';

        echo view('front/pages/subscribe_step_1_view', $data);
    }
    public function subscribe_confirmation()
    {
        $BlacklistModel = new BlacklistModel();

        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

        session()->set('code_verified', 'verified');

        if(isset($data['logged_user']) AND $data['logged_user'] != NULL){
            return redirect()->to('/account/payments');
        }

        $data['session'] = session()->get('subscribe_info');
        $data['code_verified'] = session()->get('code_verified');
        if(isset($data['code_verified']) AND $data['code_verified'] != NULL){
            return redirect()->to('/subscribe-payment');
        }
        if(!isset($data['session']['email']) OR $data['session']['email'] == ''){
            return redirect()->to('/subscribe');
        }else{
            // CHECK BLACKLIST
            if(isset($data['session']['email']) AND $data['session']['email'] != ''){
                $blocked = $BlacklistModel->where(['email' => $data['session']['email']])->first();
                if($blocked != NULL){
                    $ip_address = getenv('REMOTE_ADDR');
                    $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                    $this->_block_user_sucuri($ip_address);
                    // $response['success'] = FALSE;
                    //                    
                    $data['current']['title'] = "Subscribe Confirmation Failed | Lagree On Demand";
                    $data['current']['seo_title'] = 'Subscribe Confirmation Failed | Lagree On Demand';
                    $data['current']['seo_description'] = 'Lagree On Demand Subscribe Confirmation Failed';
                    $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Confirmation Failed';
            
                    echo view('front/pages/404_view', $data);
                }else{
                    $data['current']['title'] = "Subscribe Confirmation | Lagree On Demand";
                    $data['current']['seo_title'] = 'Subscribe Confirmation | Lagree On Demand';
                    $data['current']['seo_description'] = 'Lagree On Demand Subscribe Confirmation';
                    $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Confirmation';
            
                    create_session_nums();

                    echo view('front/pages/subscribe_step_2_view', $data);        
                }
            }else{
                $data['current']['title'] = "Subscribe Payment Failed | Lagree On Demand";
                $data['current']['seo_title'] = 'Subscribe Payment Failed | Lagree On Demand';
                $data['current']['seo_description'] = 'Lagree On Demand Subscribe Payment Failed';
                $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Payment Failed';
        
                echo view('front/pages/404_view', $data);
            }
            // END CHECK BLACKLIST
        }
    }
    public function subscribe_payment()
    {
        $BlacklistModel = new BlacklistModel();

        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        
        if(isset($data['logged_user']) AND $data['logged_user'] != NULL){
            return redirect()->to('/account/payments');
        }

        $data['session'] = session()->get('subscribe_info');
        $data['code_verified'] = session()->get('code_verified');
        if(!isset($data['session']['email']) OR $data['session']['email'] == '' OR !isset($data['code_verified']) OR $data['code_verified'] == ''){
            session()->set('code_verified', NULL);
            return redirect()->to('/subscribe');
        }else{
            // CHECK BLACKLIST
            if(isset($data['session']['email']) AND $data['session']['email'] != ''){
                $blocked = $BlacklistModel->where(['email' => $data['session']['email']])->first();
                if($blocked != NULL){
                    $ip_address = getenv('REMOTE_ADDR');
                    $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                    $this->_block_user_sucuri($ip_address);
                    // $response['success'] = FALSE;
                    //                    
                    $data['current']['title'] = "Subscribe Verification Failed | Lagree On Demand";
                    $data['current']['seo_title'] = 'Subscribe Verification Failed | Lagree On Demand';
                    $data['current']['seo_description'] = 'Lagree On Demand Subscribe Verification Failed';
                    $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Verification Failed';
            
                    echo view('front/pages/404_view', $data);
                }else{
                    $data['plan_price'] = $this->prices[$data['session']['subscription_type'] . ' Price'];
        
                    $data['current']['title'] = "Subscribe Payment | Lagree On Demand";
                    $data['current']['seo_title'] = 'Subscribe Payment | Lagree On Demand';
                    $data['current']['seo_description'] = 'Lagree On Demand Subscribe Payment';
                    $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Payment';
            
                    create_session_nums();

                    echo view('front/pages/subscribe_step_3_view', $data);            
                }
            }else{
                $data['current']['title'] = "Subscribe Verification Failed | Lagree On Demand";
                $data['current']['seo_title'] = 'Subscribe Verification Failed | Lagree On Demand';
                $data['current']['seo_description'] = 'Lagree On Demand Subscribe Verification Failed';
                $data['current']['seo_keywords'] = 'Lagree On Demand Subscribe Verification Failed';
        
                echo view('front/pages/404_view', $data);
        }
            // END CHECK BLACKLIST
        }

    }
    public function validate_subscribe_step_3()
    {
        $BlacklistModel = new BlacklistModel();
        $PaymentAttemptsModel = new PaymentAttemptsModel();
        $stripe_model = new StripeModel();
		$email_model = new EmailModel();
        $NotificationsModel = new NotificationsModel();        

        $data_send['logged_user'] = $this->user;
		$data_send['settings'] = $this->settings;
        
        $session = session()->get('subscribe_info');
        $code_verified = session()->get('code_verified');
        
        $response['success'] = FALSE;

        if(!isset($session['email']) OR $session['email'] == ''){
            session()->set('code_verified', NULL);
            // return redirect()->to('/subscribe');
            $response['success'] = FALSE;
            
            return $this->respond($response);
        }else{
            // CHECK BLACKLIST
            $blocked = $BlacklistModel->where(['email' => $session['email']])->first();
            $ip_address = getenv('REMOTE_ADDR');

            $response['blocked'] = $blocked;
            if(isset($session['email']) AND $session['email'] != ''){
                if($blocked != NULL){
                    $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                    $this->_block_user_sucuri($ip_address);
                    $response['success'] = FALSE;
                    $response['message'] = 'Your future payment are blocked. Please contact our support.';                   

                    return $this->respond($response);
                }
            }
            if(!isset($code_verified) OR $code_verified != 'verified'){
                $response['success'] = FALSE;
                $response['message'] = 'Code is not verified.';                   
            }else{
                $response['success'] = FALSE;
                $logged_user = $this->user;
                $request = service('request');
        
                $validation =  \Config\Services::validation();
                $rules = [];
                $rules['subscription_type']    = [
                    'label'  => 'Subscription type',
                    'rules'  => 'required',
                ];
                $rules['card.name']    = [
                    'label'  => 'Name on card',
                    'rules'  => 'required',
                ];
                $rules['card.number']    = [
                    'label'  => 'Card number',
                    'rules'  => 'required|numeric',
                ];
                $rules['card.exp_month']    = [
                    'label'  => 'Month',
                    'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
                ];
                $rules['card.exp_year']    = [
                    'label'  => 'Year',
                    'rules'  => 'required|numeric|exact_length[2]',
                ];
                $rules['card.cvc']    = [
                    'label'  => 'CVC',
                    'rules'  => 'required|numeric',
                ];
                $messages = $this->model->validationMessages;
                $data = $this->request->getPost();
                
                $response['data'] = $data;
                $response['rules'] = $rules;
                // $constant_data = array(
                //     'source' => "LOD Subscribe",
                //     'firstname' => $data['firstname'],
                //     'lastname' => $data['lastname'],
                //     'email' => $data['email']
                // );
        
                if(isset($session['subscription_type']) AND $session['subscription_type'] != ''){ $data['subscription_type'] = $session['subscription_type']; }
                if(isset($session['firstname']) AND $session['firstname'] != ''){ $data['firstname'] = $session['firstname']; }
                if(isset($session['lastname']) AND $session['lastname'] != ''){ $data['lastname'] = $session['lastname']; }
                if(isset($session['email']) AND $session['email'] != ''){ $data['email'] = $session['email']; }
                if(isset($session['password']) AND $session['password'] != ''){ $data['password'] = $session['password']; }
        
                if(isset($data['email']) AND strpos($data['email'], '@perfecthm.com') === FALSE AND strpos($data['email'], 'emaildbox.pro') === FALSE){
                    if(isset($data['stripe_customer']) AND $data['stripe_customer'] !=''){
                        unset($rules['password']);
                        unset($rules['email']);
                    }
        
                    $s1 = session("broj1");
                    $s2 = session("broj2");
        
                    $validation->reset();
                    $validation->setRules($rules);
                    if(($s1 + $s2) == $data['sum']){
                        if (!$validation->run($data)){
                            // handle validation errors
                            //$prepared['validation_list'] = $validation->listErrors();
                            //$response['message'] = implode('</br>', $this->model->errors());
                            $response['message'] = implode('</br>', $validation->getErrors());
                            $response['json'] = $validation->getErrors();
                            return $this->respond($response);
                        }else{
                            $token = $stripe_model->create_card_token($data['card']);
                            if (!$token['success'])
                            {
                                $customer_data = $token;
                                if(isset($session['email']) AND $session['email'] != '' AND $token['type'] == 'card_error'){
                                    $customer_data['payment_attempt_saved'] = $PaymentAttemptsModel->save([
                                        'email'  => $data['email'], 
                                        'ip_address' => $ip_address, 
                                    ]);
                                    $customer_data['payment_attempt_save_errors'] = $BlacklistModel->errors();

                                    $payment_attempt = $PaymentAttemptsModel->query("SELECT * FROM payment_attempts WHERE email = '" . $session['email'] . "' AND  created_at BETWEEN (DATE_SUB(NOW(),INTERVAL 5 MINUTE)) AND NOW()")->getResultArray();
                                    $customer_data['payment_attempt'] = count($payment_attempt);
                                    if(count($payment_attempt) == 3){
                                        $customer_data['added_to_black_list'] = $BlacklistModel->save([
                                            'email'  => $data['email'], 
                                            'ip_address' => $ip_address, 
                                        ]);
                                        $customer_data['added_to_black_list_errors'] = $BlacklistModel->errors();
                                    }
        
                                    $blocked = $BlacklistModel->where(['email' => $data['email']])->first();
                                    if($blocked != NULL){
                                        $customer_data['added_IP_to_black_list'] = $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                                        $this->_block_user_sucuri($ip_address);
                                        $customer_data['success'] = FALSE;
                                        $customer_data['added_IP_to_black_list_errors'] = $BlacklistModel->errors();

                                        return $this->respond($customer_data);
                                    }
                                }

                                return $this->respond($token);
                            }
                            $data['source'] = $token['token'];
       
                            if(isset($logged_user)){
                                $customer = $logged_user;
                                $customer['success'] = TRUE;
                            }else{
                                $customer = $stripe_model->create_customer($data);
                            }
        
                            if (!$customer['success'])
                            {
                                $customer_data = $customer;
                                if(isset($session['email']) AND $session['email'] != '' AND $customer['type'] == 'card_error'){
                                    $customer_data['payment_attempt_saved'] = $PaymentAttemptsModel->save([
                                        'email'  => $data['email'], 
                                        'ip_address' => $ip_address, 
                                    ]);                                    
                                    $customer_data['payment_attempt_save_errors'] = $BlacklistModel->errors();

                                    $payment_attempt = $PaymentAttemptsModel->query("SELECT * FROM payment_attempts WHERE email = '" . $session['email'] . "' AND  created_at BETWEEN (DATE_SUB(NOW(),INTERVAL 5 MINUTE)) AND NOW()")->getResultArray();
                                    $customer_data['payment_attempt'] = count($payment_attempt);
                                    if(count($payment_attempt) == 3){
                                        $customer_data['added_to_black_list'] = $BlacklistModel->save([
                                            'email'  => $data['email'], 
                                            'ip_address' => $ip_address, 
                                        ]);
                                        $customer_data['added_to_black_list_errors'] = $BlacklistModel->errors();
                                    }
        
                                    $blocked = $BlacklistModel->where(['email' => $data['email']])->first();
                                    if($blocked != NULL){
                                        $customer_data['added_IP_to_black_list'] = $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                                        $this->_block_user_sucuri($ip_address);
                                        $customer_data['success'] = FALSE;
                                        $customer_data['added_IP_to_black_list_errors'] = $BlacklistModel->errors();

                                        return $this->respond($customer_data);
                                    }
                                }

                                return $this->respond($customer_data);
                            }
                            if(isset($logged_user)){
                                $data['stripe_customer'] = $customer['stripe_customer'];
                            }else{
                                $data['stripe_customer'] = $customer['customer'];
                            }
        
                            // $coupon
                            $coupon = $data['coupon'];

                            // Use safe subscription creation to prevent duplicates during registration
                            $subscription = $stripe_model->create_subscription_safe($data['stripe_customer'], $this->prices[$data['subscription_type']], $coupon, 'register/validate_subscribe_step_3 - user email: ' . $session['email'] . ', user code: ' . $code_verified, false);

                            if (!$subscription['success'])
                            {

                                if(isset($data['email']) AND $data['email'] != ''){
                                    $blocked = $BlacklistModel->where(['email' => $data['email']])->first();
                                    if($blocked != NULL){
                                        $subscription['added_IP_to_black_list'] = $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                                        $this->_block_user_sucuri($ip_address);
                                        $response['success'] = FALSE;
                                        $subscription['added_IP_to_black_list_errors'] = $BlacklistModel->errors();

                                        return $this->respond($subscription);
                                    }
                                }
        
                                $to = $data['email'];
                                $subject = 'Lagree On Demand - Your Payment Failed';
                                $data = [
                                    'last_digits' => substr($data['card']['number'], -4),
                                    'next_charge' => date('m/d/Y', strtotime("+1 week"))
                                ];
                                $template = 'front/email_templates/failed-payment';
                                $subscription['email_failed_payment_sent'] = $email_model->send_template($to, FALSE, $subject, $data, $template);

                                return $this->respond($subscription);
                            }
                            if($subscription['subscription_info']['status'] == 'active'){
                                session()->set('subscription', $subscription['subscription_info']['status']);
                                session()->set('subscription_plan', $subscription['subscription_info']['plan']['interval']);
                                $data['stripe_subscription'] = $subscription['subscription'];
                                $response['stripe_subscription_api'] = $subscription;
                                $response['message'] = 'Your payment was successful, welcome to Lagree On Demand!';
                                $response['success'] = $this->model->save($data);
        
        
                                $user_id = $this->model->getInsertID();
                                // $sent = $email_model->send_verification_link($user_id);
                                session()->remove('subscribe_info');
                                session()->remove('code_verified');
                                // $user_data = array('id' => $user_id, 'first_login' => 1);
                                // $response['first_login_saved'] = $this->model->save($user_data);
        
                                $response['return_url'] = site_url('/');
                                session()->set('user', $user_id);
        
                                $notification_data = [
                                    'content'   => 'Welcome to LagreeOD',
                                    'author'    => 'system',
                                    'subscriber_id' => $user_id,
                                    'type' => 'welcome_od',
                                    'date'    => date('Y-m-d H:i:s')
                                ];
                                $response['notification_saved'] = $NotificationsModel->save($notification_data);
        
        
                                //email user
                                $to = $data['email'];
                                $subject = 'Lagree On Demand - Subscription Confirmed';
                                $template_data = [
                                    'classes_link' => base_url() . '/classes/',
                                    'contact_link' => base_url() . '/contact-us/'
                                ];
                                $template = 'front/email_templates/subscribtion-confirmation';
                                $response['email_to_user'] = $this->email_model->send_template($to, FALSE, $subject, $template_data, $template);
                            }else{
                                $response['message'] = 'We couldn\'t charge your card, please try again';
                                $to = $data['email'];
                                $subject = 'Lagree On Demand - Your Payment Failed';
                                $data = [
                                    'subscribe_url' => base_url() . '/subscribe',
                                    'card_type' => $token['brand'],
                                    'last_digits' => $token['last4'],
                                    'subscription_type' => $data['subscription_type']
                                ];
                                $template = 'front/email_templates/failed-payment-stripe';
                                $response['success'] = FALSE;
                                $response['stripe_subscription_api']['subscription_info']['status'] = $subscription['subscription_info']['status'];
                                if(isset($session['email']) AND $session['email'] != ''){
                                    $PaymentAttemptsModel->save([
                                        'email'  => $session['email'], 
                                        'ip_address' => $ip_address, 
                                        'stripe_status' => $subscription['subscription_info']['status']
                                    ]);
                                    $payment_attempt = $PaymentAttemptsModel->query("SELECT * FROM payment_attempts WHERE email = '" . $session['email'] . "' AND  created_at BETWEEN (DATE_SUB(NOW(),INTERVAL 5 MINUTE)) AND NOW()")->getResultArray();
                                    $response['payment_attempt'] = count($payment_attempt);
                                    if(count($payment_attempt) == 3){
                                        $BlacklistModel->save([
                                            'email'  => $session['email'], 
                                            'ip_address' => $ip_address, 
                                            'stripe_status' => $subscription['subscription_info']['status']
                                        ]);
                                    }
        
                                    if($blocked != NULL){
                                        $ip_address = getenv('REMOTE_ADDR');
                                        $BlacklistModel->save(['id'  => $blocked['id'], 'ip_address' => $ip_address]);
                                        $this->_block_user_sucuri($ip_address);
                                        $response['success'] = FALSE;

                                        return $this->respond($response);
                                    }
                                }
                                $response['fail_email'] = $email_model->send_template($to, FALSE, $subject, $data, $template);
                                return $this->respond($response);
                            }
                        }
                    }else{
                        $response['message'] = 'Wrong result. Please sum these two numbers correctly.';
                    }
                }    
            }
            // END CHECK BLACKLIST            

            return $this->respond($response);
        }
    }

    public function check_sess_numbers()
    {
        $response['broj1'] = session('broj1');
        $response['broj2'] = session('broj2');

		return $this->respond($response);
    }
    public function check_coupon()
    {
		$stripe_model = new StripeModel();
		$data = $this->request->getPost();
        $response['success'] = FALSE;

        $coupon = $data['code'];
        if($coupon != ''){
            $response = $stripe_model->retrieve_coupon($coupon);
        }
		// echo '<pre>';
		// var_dump($sent);
		// die();
		return $this->respond($response);
    }
    public function send_code()
    {
		$email_model = new EmailModel();
		$EmailCodeModel = new EmailCodeModel();
		$request = service('request');
		$data = $this->request->getPost();

        $to = $data['email'];
        $subject = 'Lagree On Demand - Your Payment Failed';
        $code = $this->generateEmailCode();
        $data_save = [
            'email' => $data['email'],
            'code' => $code
        ];
        $template = 'front/email_templates/send-code';
        $response['existing_code'] = $EmailCodeModel->where(['email' => $to])->findAll();

        if(isset($response['existing_code']) AND is_array($response['existing_code']) AND count($response['existing_code']) > 0){
            $response['save_code'] = $EmailCodeModel->save($data_save);
            $response['email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_save, $template);
        }else{
            $response['save_code'] = FALSE;
            $response['email_sent'] = FALSE;
        }

		return $this->respond($response);
    }
    function generateEmailCode() 
    {
        $number = mt_rand(0, 999999);
        $formattedNumber = str_pad($number, 6, '0', STR_PAD_LEFT);
        
        while (substr($formattedNumber, 0, 3) === '000') {
            $number = mt_rand(0, 999999);
            $formattedNumber = str_pad($number, 6, '0', STR_PAD_LEFT);
        }
        
        return $formattedNumber;
    }    
    public function check_email()
    {
		$SubscribersModel = model('SubscribersModel');
		$data = $this->request->getPost();

        $response['success'] = FALSE;
        
        $email = $data['email'];
        $email_check = $SubscribersModel->where('email', $email)->first();
        if($email_check){
            $response['success'] = TRUE;
        }

        return $this->respond($response);
    }
    public function verify_email_code()
    {
		$EmailCodeModel = new EmailCodeModel();
		$data = $this->request->getPost();
        
        $response['success'] = FALSE;
        // return $this->respond($response);

        $used_email_code = $EmailCodeModel->where(['email' => $data['email'], 'code' => $data['code'], 'code_used' => 1])->findAll();
        $existing_email_code = $EmailCodeModel->where(['email' => $data['email'], 'code' => $data['code'], 'code_used' => 0])->orderBy('date', 'DESC')->first();
        
        $response['codes'] = $existing_email_code;

        if(count($used_email_code) > 0){
            $response['msg'] = 'This code is already used';
            return $this->respond($response);
        }else if(isset($existing_email_code)){
            $response['success'] = TRUE;
            $EmailCodeModel->save(['id' => $existing_email_code['id'], 'code_used' => 1]);
            session()->set('code_verified', 'verified');
        }

        return $this->respond($response);
    }
    // public function subscriber_to_teacher()
    // {
	// 	$stripe_model = new StripeModel();
	// 	$email_model = new EmailModel();
    //     $teachers_model = model('TeachersModel');
    //     $NotificationsModel = model('NotificationsModel');
	// 	$request = service('request');
    //     $data = $this->request->getPost();

	// 	$validation =  \Config\Services::validation();
	// 	$rules = $this->model->validationRules;
    //     // echo $data['address']['country'];
    //     // die();
    //     if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //         $rules['ssn']    = [
    //             'label'  => 'SSN Number',
    //             'rules'  => 'required|min_length[4]',
    //         ];
    //         unset($data['address']['city']);
    //         unset($data['address']['line1']);
    //         unset($data['address']['postal_code']);
    //         unset($data['id_number']);
    //         unset($data['phone']);
    //         unset($data['account_holder_name']);
    //         unset($data['account_number']);
    //     }else{
    //         $rules['address.city']    = [
    //             'label'  => 'City',
    //             'rules'  => 'required|min_length[2]',
    //         ];
    //         $rules['address.postal_code']    = [
    //             'label'  => 'ZIP code',
    //             'rules'  => 'required|min_length[3]',
    //         ];
    //         $rules['address.line1']    = [
    //             'label'  => 'Address',
    //             'rules'  => 'required|min_length[3]',
    //         ];
    //         $rules['id_number']    = [
    //             'label'  => 'ID Number',
    //             'rules'  => 'required|numeric|min_length[4]',
    //         ];
    //         $rules['phone']    = [
    //             'label'  => 'Phone Number',
    //             'rules'  => 'required|min_length[7]',
    //         ];
    //         $rules['account_holder_name']    = [
    //             'label'  => 'Account Holder Name',
    //             'rules'  => 'required|min_length[7]',
    //         ];
    //         $rules['account_number']    = [
    //             'label'  => 'Account Number',
    //             'rules'  => 'required',
    //         ];
    //         // $rules['routing_number']    = [
    //         //     'label'  => 'Routing Number',
    //         //     'rules'  => 'required',
    //         // ];
    //         unset($data['ssn']);
    //     }
    //     $rules['address.country']    = [
    //         'label'  => 'Country',
    //         'rules'  => 'required',
    //     ];
    //     $rules['day']    = [
    //         'label'  => 'Day of birth',
    //         'rules'  => 'required|numeric',
    //     ];
    //     $rules['month']    = [
    //         'label'  => 'Month of birth',
    //         'rules'  => 'required',
    //     ];
    //     $rules['year']    = [
    //         'label'  => 'Year of birth',
    //         'rules'  => 'required|numeric',
    //     ];
    //     $messages = $this->model->validationMessages;
	// 	$data = $this->request->getPost();
	// 	$response['data'] = $data;
	// 	$response['rules'] = $rules;

	// 	$validation->reset();
	// 	$validation->setRules($rules);
	// 	if (!$validation->run($data))
	// 	{
	// 		$response['message'] = implode('</br>', $validation->getErrors());
    //         $response['json'] = $validation->getErrors();
	// 		return $this->respond($response);
	// 	}
	// 	else
	// 	{
    //         if(isset($logged_user['stripe_customer'])){
    //             $data['stripe_customer'] = $logged_user['stripe_customer'];
    //         }else{
    //             $customer = $stripe_model->create_customer($data);
    //             if (!$customer['success']){
    //                 return $this->respond($customer);
    //             }
    //             $data['stripe_customer'] = $customer['customer'];
    //         }

	// 		$files = $this->request->getFiles();
	// 		$response['files'] = $files;
	// 		if (isset($files['document_back']) AND $files['document_back']->isValid()){
	// 			$file = $files['document_back'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $document_back = 'uploads/document/' . $name;
    //             $document_back_file = $stripe_model->upload_document($document_back, 'identity_document');
    //             $data['document_back'] = $document_back_file['id'];
	// 		}
	// 		if (isset($files['document_front']) AND $files['document_front']->isValid()){
	// 			$file = $files['document_front'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $document_front = 'uploads/document/' . $name;
    //             $document_front_file = $stripe_model->upload_document($document_front, 'identity_document');
    //             $data['document_front'] = $document_front_file['id'];
	// 		}
	// 		if (isset($files['additional_document_back']) AND $files['additional_document_back']->isValid()){
	// 			$file = $files['additional_document_back'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $additional_document_back = 'uploads/document/' . $name;
    //             $additional_document_back_file = $stripe_model->upload_document($additional_document_back, 'identity_document');
    //             $data['additional_document_back'] = $additional_document_back_file['id'];
	// 		}
	// 		if (isset($files['additional_document_front']) AND $files['additional_document_front']->isValid()){
	// 			$file = $files['additional_document_front'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $additional_document_front = 'uploads/document/' . $name;
    //             $additional_document_front_file = $stripe_model->upload_document($additional_document_front, 'identity_document');
    //             $data['additional_document_front'] = $additional_document_front_file['id'];
	// 		}

    //         $slug = slugify($data['firstname']) . '-' . slugify($data['lastname']);

    //         $months = array(
    //             'January'   => 1,
    //             'February'  => 2,
    //             'March'     => 3,
    //             'April'     => 4,
    //             'May'       => 5,
    //             'June'      => 6,
    //             'July'      => 7,
    //             'August'    => 8,
    //             'September' => 9,
    //             'October'   => 10,
    //             'November'  => 11,
    //             'December'  => 12
    //         );
    //         if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //             $teacher_strpie_account_data = array(
    //                 'email' => $data['email'],
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'ssn_last_4' => $data['ssn'],
    //                 'day' => $data['day'],
    //                 'address' => $data['address'],
    //                 'month' => $months[$data['month']],
    //                 'year' => $data['year'],
    //                 'date' => $data['date'],
    //                 'ip' => $data['ip'],
    //                 'teacher_url' => base_url() . '/teachers/' . $slug,
    //             );
    //         }else{
    //             $teacher_strpie_account_data = array(
    //                 'email' => $data['email'],
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'id_number' => $data['id_number'],
    //                 // 'card' => $data['card'],
    //                 'address' => $data['address'],
    //                 'phone' => $data['phone'],
    //                 'day' => $data['day'],
    //                 'month' => $months[$data['month']],
    //                 'year' => $data['year'],
    //                 'date' => $data['date'],
    //                 'account_number' => $data['account_number'],
    //                 'account_holder_name' => $data['account_holder_name'],
    //                 'routing_number' => ((isset($data['routing_number']) AND $data['routing_number'] != '') ? $data['routing_number'] : NULL),
    //                 'ip' => $data['ip'],
    //                 'teacher_url' => base_url() . '/teachers/' . $slug,
    //             );
    //         }
    //         $response['teacher_strpie_account_data'] = $teacher_strpie_account_data;
    //         $response['account'] = $stripe_model->create_account($teacher_strpie_account_data);
	// 		if (!$response['account']['success'])
	// 		{
	// 			return $this->respond($response['account']);
	// 		}
    //         // create teacher
    //         $teacher = $teachers_model->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $data['email'] . "'")->getFirstRow('array');
    //         if(!isset($teacher['id'])){
    //             $teacher_data = array(
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'email' => $data['email'],
    //                 'slug' => $slug,
    //                 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', 'lagreeod')))),
    //                 'status' => 1,
    //                 'country' => $data['address']['country'],
    //                 'content' => $data['firstname'] . ' ' . $data['lastname'] . ' is a Lagree teacher'
    //             );
    //             // save teacher stripe account ID acct_....
    //             $response['new_teacher_saved'] = $teachers_model->save($teacher_data);
    //             $new_id = $teachers_model->getInsertID();
    //             session()->set('teacher', $new_id);
    //         }else{
    //             session()->set('teacher', $teacher['id']);
    //         }
    //         // save teacher stripe account ID acct_....
    //         if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //             $teacher_account = array(
    //                 'id' => session('teacher'),
    //                 'stripe_account' => $response['account']['account']['id']
    //             );
    //         }else{
    //             $teacher_account = array(
    //                 'id' => session('teacher'),
    //                 'stripe_account' => $response['account']['account']['id'],
    //                 'stripe_account_card_added' => 1
    //             );
    //         };
    //         $response['teacher_account_updated'] = $teachers_model->save($teacher_account);

	// 		$response['success'] = $this->model->save($data);
    //         $response['message'] = 'Welcome to LagreeOD';
	// 		$user_id = $this->model->getInsertID();
	// 		$sent = $email_model->send_verification_link($user_id);
	// 		$response['return_url'] = base_url() . '/account/classes';
	// 		session()->set('user', $user_id);
    //         $notification_data = [
    //             'content'   => 'Welcome to LagreeOD',
    //             'author'    => 'system',
    //             'subscriber_id' => $user_id,
    //             'type' => 'welcome_od',
    //             'date'      => date('Y-m-d H:i:s')
    //         ];
    //         $response['notification_saved'] = $NotificationsModel->save($notification_data);
	// 	}
	// 	//echo json_encode($response);
	// 	return $this->respond($response);
    // }
    // public function subscriber_to_teacher_logged()
    // {
	// 	$stripe_model = new StripeModel();
	// 	$email_model = new EmailModel();
    //     $teachers_model = model('TeachersModel');
	// 	$request = service('request');
    //     $data = $this->request->getPost();

	// 	$validation =  \Config\Services::validation();
	// 	$rules = $this->model->validationRules;
    //     // echo $data['address']['country'];
    //     // die();
    //     if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //         $rules['ssn']    = [
    //             'label'  => 'SSN Number',
    //             'rules'  => 'required|min_length[4]',
    //         ];
    //         unset($data['address']['city']);
    //         unset($data['address']['line1']);
    //         unset($data['address']['postal_code']);
    //         unset($data['id_number']);
    //         unset($data['phone']);
    //         unset($data['account_holder_name']);
    //         unset($data['account_number']);
    //         unset($data['routing_number']);
    //     }else{
    //         $rules['address.city']    = [
    //             'label'  => 'City',
    //             'rules'  => 'required|min_length[2]',
    //         ];
    //         $rules['address.postal_code']    = [
    //             'label'  => 'ZIP code',
    //             'rules'  => 'required|min_length[3]',
    //         ];
    //         $rules['address.line1']    = [
    //             'label'  => 'Address',
    //             'rules'  => 'required|min_length[3]',
    //         ];
    //         $rules['id_number']    = [
    //             'label'  => 'ID Number',
    //             'rules'  => 'required|numeric|min_length[4]',
    //         ];
    //         $rules['phone']    = [
    //             'label'  => 'Phone Number',
    //             'rules'  => 'required|min_length[7]',
    //         ];
    //         $rules['account_holder_name']    = [
    //             'label'  => 'Account Holder Name',
    //             'rules'  => 'required|min_length[7]',
    //         ];
    //         $rules['account_number']    = [
    //             'label'  => 'Account Number',
    //             'rules'  => 'required',
    //         ];
    //         $rules['routing_number']    = [
    //             'label'  => 'Routing Number',
    //             'rules'  => 'required',
    //         ];
    //         unset($data['ssn']);
    //     }
    //     $rules['address.country']    = [
    //         'label'  => 'Country',
    //         'rules'  => 'required',
    //     ];
    //     $rules['day']    = [
    //         'label'  => 'Day of birth',
    //         'rules'  => 'required|numeric',
    //     ];
    //     $rules['month']    = [
    //         'label'  => 'Month of birth',
    //         'rules'  => 'required',
    //     ];
    //     $rules['year']    = [
    //         'label'  => 'Year of birth',
    //         'rules'  => 'required|numeric',
    //     ];
    //     $messages = $this->model->validationMessages;
	// 	$data = $this->request->getPost();
	// 	$response['data'] = $data;

    //     if(isset($data['stripe_customer']) AND $data['stripe_customer'] !=''){
    //         unset($rules['password']);
    //         unset($rules['email']);
    //         unset($rules['firstname']);
    //         unset($rules['lasttname']);
    //     }
	// 	$response['rules'] = $rules;

	// 	$validation->reset();
	// 	$validation->setRules($rules);
	// 	if (!$validation->run($data))
	// 	{
	// 		$response['message'] = implode('</br>', $validation->getErrors());
    //         $response['json'] = $validation->getErrors();
	// 		return $this->respond($response);
	// 	}
	// 	else
	// 	{
	// 		$response['return_url'] = base_url() . '/account/classes';

	// 		$files = $this->request->getFiles();
	// 		$response['files'] = $files;
	// 		if (isset($files['document_back']) AND $files['document_back']->isValid()){
	// 			$file = $files['document_back'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $document_back = 'uploads/document/' . $name;
    //             $document_back_file = $stripe_model->upload_document($document_back, 'identity_document');
    //             $data['document_back'] = $document_back_file['id'];
	// 		}
	// 		if (isset($files['document_front']) AND $files['document_front']->isValid()){
	// 			$file = $files['document_front'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $document_front = 'uploads/document/' . $name;
    //             $document_front_file = $stripe_model->upload_document($document_front, 'identity_document');
    //             $data['document_front'] = $document_front_file['id'];
	// 		}
	// 		if (isset($files['additional_document_back']) AND $files['additional_document_back']->isValid()){
	// 			$file = $files['additional_document_back'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $additional_document_back = 'uploads/document/' . $name;
    //             $additional_document_back_file = $stripe_model->upload_document($additional_document_back, 'identity_document');
    //             $data['additional_document_back'] = $additional_document_back_file['id'];
	// 		}
	// 		if (isset($files['additional_document_front']) AND $files['additional_document_front']->isValid()){
	// 			$file = $files['additional_document_front'];
	// 			$response['ClientExtension'] = $file->getClientExtension();
	// 			$response['guessExtension'] = $file->guessExtension();
	// 			$response['getClientMimeType'] = $file->getClientMimeType();
	// 			$response['getMimeType'] = $file->getMimeType();
	// 			$name = $file->getRandomName();
    //             $file->move(ROOTPATH . 'public/uploads/document', $name);
	// 			\Config\Services::image()
	// 				->withFile(ROOTPATH . 'public/uploads/document/' . $name)
	// 				->resize(1200, 800, true, 'width')
	// 				->save(ROOTPATH . 'public/uploads/document/' . $name, 90);
    //             $additional_document_front = 'uploads/document/' . $name;
    //             $additional_document_front_file = $stripe_model->upload_document($additional_document_front, 'identity_document');
    //             $data['additional_document_front'] = $additional_document_front_file['id'];
	// 		}

    //         $slug = slugify($data['firstname']) . '-' . slugify($data['lastname']);

    //         $months = array(
    //             'January'   => 1,
    //             'February'  => 2,
    //             'March'     => 3,
    //             'April'     => 4,
    //             'May'       => 5,
    //             'June'      => 6,
    //             'July'      => 7,
    //             'August'    => 8,
    //             'September' => 9,
    //             'October'   => 10,
    //             'November'  => 11,
    //             'December'  => 12
    //         );
    //         if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //             $teacher_strpie_account_data = array(
    //                 'email' => $data['email'],
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'ssn_last_4' => $data['ssn'],
    //                 'day' => $data['day'],
    //                 'address' => $data['address'],
    //                 'month' => $months[$data['month']],
    //                 'year' => $data['year'],
    //                 'date' => $data['date'],
    //                 'ip' => $data['ip'],
    //                 'teacher_url' => base_url() . '/teachers/' . $slug,
    //             );
    //         }else{
    //             $teacher_strpie_account_data = array(
    //                 'email' => $data['email'],
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'id_number' => $data['id_number'],
    //                 // 'card' => $data['card'],
    //                 'address' => $data['address'],
    //                 'phone' => $data['phone'],
    //                 'day' => $data['day'],
    //                 'month' => $months[$data['month']],
    //                 'year' => $data['year'],
    //                 'date' => $data['date'],
    //                 'account_number' => $data['account_number'],
    //                 'account_holder_name' => $data['account_holder_name'],
    //                 'routing_number' => $data['routing_number'],
    //                 'ip' => $data['ip'],
    //                 'teacher_url' => base_url() . '/teachers/' . $slug,
    //             );
    //         }
    //         $response['teacher_strpie_account_data'] = $teacher_strpie_account_data;
    //         $response['account'] = $stripe_model->create_account($teacher_strpie_account_data);
	// 		if (!$response['account']['success'])
	// 		{
	// 			return $this->respond($response['account']);
	// 		}
    //         // create teacher
    //         $teacher = $teachers_model->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $data['email'] . "'")->getFirstRow('array');
    //         if(!isset($teacher['id'])){
    //             $teacher_data = array(
    //                 'firstname' => $data['firstname'],
    //                 'lastname' => $data['lastname'],
    //                 'email' => $data['email'],
    //                 'slug' => $slug,
    //                 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', 'lagreeod')))),
    //                 'status' => 1,
    //                 'country' => $data['address']['country'],
    //                 'content' => $data['firstname'] . ' ' . $data['lastname'] . ' is a Lagree teacher'
    //             );
    //             // save teacher stripe account ID acct_....
    //             $response['new_teacher_saved'] = $teachers_model->save($teacher_data);
    //             $new_id = $teachers_model->getInsertID();
    //             session()->set('teacher', $new_id);
    //         }else{
    //             session()->set('teacher', $teacher['id']);
    //         }
    //         // save teacher stripe account ID acct_....
    //         if(isset($data['address']['country']) AND $data['address']['country'] == 'US'){
    //             $teacher_account = array(
    //                 'id' => session('teacher'),
    //                 'stripe_account' => $response['account']['account']['id']
    //             );
    //         }else{
    //             $teacher_account = array(
    //                 'id' => session('teacher'),
    //                 'stripe_account' => $response['account']['account']['id'],
    //                 'stripe_account_card_added' => 1
    //             );
    //         };
    //         $response['teacher_account_updated'] = $teachers_model->save($teacher_account);

	// 		$response['success'] = $this->model->save($data);
    //         $response['message'] = 'Welcome to LagreeOD';
	// 		$user_id = $this->model->getInsertID();
	// 		$sent = $email_model->send_verification_link($user_id);
	// 		$response['return_url'] = base_url() . '/account/classes';
	// 		// session()->set('user', $user_id);
	// 	}
	// 	//echo json_encode($response);
	// 	return $this->respond($response);
    // }
    public function buy_class()
    {
		$stripe_model = new StripeModel();
		$email_model = new EmailModel();
        $SubscribersClasses = model('SubscribersClasses');
        $NotificationsModel = model('NotificationsModel');

		$logged_user = $this->user;
		$request = service('request');

        $validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
		$rules['card.name'] = [
            'label'  => 'Name on card',
            'rules'  => 'required',
        ];
		$rules['card.number'] = [
            'label'  => 'Card number',
            'rules'  => 'required|numeric',
        ];
		$rules['card.exp_month'] = [
            'label'  => 'Month',
            'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
        ];
		$rules['card.exp_year'] = [
            'label'  => 'Year',
            'rules'  => 'required|numeric|exact_length[2]',
        ];
		$rules['card.cvc'] = [
            'label'  => 'CVC',
            'rules'  => 'required|numeric',
        ];
		$rules['price'] = [
            'label'  => 'Payment option',
            'rules'  => 'required',
        ];
		// $messages = $this->model->validationMessages;
        $data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

        // if(isset($data['stripe_customer']) AND $data['stripe_customer'] !=''){
        //     unset($rules['password']);
        //     unset($rules['email']);
        // }
		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
			$token = $stripe_model->create_card_token($data['card']);
			if (!$token['success'])
			{
				return $this->respond($token);
			}
			$customer = $stripe_model->add_card_to_customer($this->user['stripe_customer'], $token['token']);
			if (!$customer['success'])
			{
				return $this->respond($customer);
			}

            // $coupon
            // $coupon = $data['coupon'];

            // $response['token'] = $token;
            // $response['customer'] = $customer;

            $class = $this->class_info($data['class_id']);
            $seller = $this->teacher_info($data['seller_id']);
            $user_from_teacher = $this->subscriber_from_teacher_info($seller['email']);
            $response['seller'] = $seller;

            $response['bought_class'] = $stripe_model->create_charge($data['buyer_id'], $seller, $class, $data['stripe_customer'], $this->prices['Buy Class'], $customer['default_source']);
            if($response['bought_class']['charge']['status'] != 'succeeded')
            {
				return $this->respond($response['bought_class']);
			}else{

                if(isset($seller['email']) AND $seller['email'] != ''){
                    $to = $seller['email'];
                    $reply = '<EMAIL>';
                    $subject = 'Lagree On Demand - Someone bought your class';
                    $dataEmail = [
                        'fullname' => $seller['firstname'],
                        'email' => $seller['email'],
                        'class_title' => $data['class_title']
                    ];
                    $template = 'front/email_templates/email-to-seller';
                    $response['email_to_seller'] = $email_model->send_template($to, $reply, $subject, $dataEmail, $template);

                    $notification_seller_data = array(
                        'content'   => 'Someone has bought your class <span class="text-underline">' . $data['class_title'] . '</span>.',
                        'author'    => 'system',
                        'link'      => base_url() . '/account/classes/',
                        'subscriber_id'    => isset($user_from_teacher['id']) ? $user_from_teacher['id'] : 0,
                        'type' => 'class_bought_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_seller_saved'] = $NotificationsModel->save($notification_seller_data);
                }
                // create transfer
                $response['transfer_data'] = [
                    'amount' => round(($data['price'] * $this->prices['Teachers cut']), 2) * 100,
                    'destination' => $seller['stripe_account'],
                    'transfer_group' => date('F Y'),
                    'description' => 'Payout for class ' . $class['title']
                ];
                $response['transfer_funds'] = $stripe_model->create_transfer($response['transfer_data']);

                $data_save = [
                    "class_id" => $data['class_id'],
                    "subscriber_id" => $data['buyer_id'],
                    "seller_id" => $data['seller_id'],
                    "price" => $data['price'],
                    "seller_earning" => round($data['price'] * $this->prices['Teachers cut'], 2),
                    "date" => date('Y-m-d H:i:s'),
                    "stripe_charge" => $response['bought_class']['charge']['id'],
                    "stripe_transfer" => isset($response['transfer_funds']['transfer']['id']) ? $response['transfer_funds']['transfer']['id'] : ''
                ];
                $response['data_save'] = $data_save;
                $response['success'] = $SubscribersClasses->save($data_save);
            }

            $response['class_url'] = "/classes/" . $class['slug'];
        }
		return $this->respond($response);
    }
    public function rent_class()
    {
		$stripe_model = new StripeModel();
		$email_model = new EmailModel();
        $SubscribersClasses = model('SubscribersClasses');

		$logged_user = $this->user;
		$request = service('request');

        $validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
		$rules['card.name']    = [
				'label'  => 'Name on card',
				'rules'  => 'required',
			];
		$rules['card.number']    = [
				'label'  => 'Card number',
				'rules'  => 'required|numeric',
			];
		$rules['card.exp_month']    = [
				'label'  => 'Month',
				'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
			];
		$rules['card.exp_year']    = [
				'label'  => 'Year',
				'rules'  => 'required|numeric|exact_length[2]',
			];
		$rules['card.cvc']    = [
				'label'  => 'CVC',
				'rules'  => 'required|numeric',
			];
		// $messages = $this->model->validationMessages;
        $data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

        // if(isset($data['stripe_customer']) AND $data['stripe_customer'] !=''){
        //     unset($rules['password']);
        //     unset($rules['email']);
        // }
		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
			$token = $stripe_model->create_card_token($data['card']);
			if (!$token['success'])
			{
				return $this->respond($token);
			}
			$customer = $stripe_model->add_card_to_customer($this->user['stripe_customer'], $token['token']);
			if (!$customer['success'])
			{
				return $this->respond($customer);
			}

            // $coupon
            // $coupon = $data['coupon'];

            // $response['token'] = $token;
            // $response['customer'] = $customer;

            $class = $this->class_info($data['class_id']);
            $seller = $this->teacher_info($data['seller_id']);
            $response['seller'] = $seller;

            $response['bought_class'] = $stripe_model->create_charge($data['buyer_id'], $seller, $class, $data['stripe_customer'], $this->prices['Rent Class'], $customer['default_source']);
            if($response['bought_class']['charge']['status'] != 'succeeded')
            {
				return $this->respond($response['bought_class']);
			}else{

                if(isset($seller['email']) AND $seller['email'] != ''){
                    $to = $seller['email'];
                    $reply = '<EMAIL>';
                    $subject = 'Lagree On Demand - Someone rented your class';
                    $dataEmail = [
                        'fullname' => $seller['firstname'],
                        'email' => $seller['email'],
                        'duration' => date('m/d/Y H:i:s', strtotime('+ 1 days')),
                        'class_title' => $data['class_title']
                    ];
                    $template = 'front/email_templates/email-to-seller-rent';
                    $response['email_to_seller'] = $email_model->send_template($to, $reply, $subject, $dataEmail, $template);
                }
                if(isset($data['email']) AND $data['email'] != ''){
                    $to = $data['email'];
                    $reply = '<EMAIL>';
                    $subject = 'Lagree On Demand - You just rented a ' . $data['class_title'];
                    $dataEmail = [
                        'fullname' => $data['firstname'],
                        'email' => $data['email'],
                        'duration' => date('m/d/Y H:i:s', strtotime('+ 1 days')),
                        'class_title' => $data['class_title']
                    ];
                    $template = 'front/email_templates/email-to-buyer-rent';
                    $response['email_to_buyer'] = $email_model->send_template($to, $reply, $subject, $dataEmail, $template);
                }
                // create transfer
                $response['transfer_data'] = [
                    'amount' => round(($data['price'] * $this->prices['Teachers cut']), 2) * 100,
                    'destination' => $seller['stripe_account'],
                    'transfer_group' => date('F Y'),
                    'description' => 'Payout for class ' . $class['title']
                ];
                $response['transfer_funds'] = $stripe_model->create_transfer($response['transfer_data']);

                $data_save = [
                    "class_id" => $data['class_id'],
                    "subscriber_id" => $data['buyer_id'],
                    "seller_id" => $data['seller_id'],
                    "price" => $data['price'],
                    "seller_earning" => round($data['price'] * $this->prices['Teachers cut'], 2),
                    "date" => date('Y-m-d H:i:s'),
                    "stripe_charge" => $response['bought_class']['charge']['id'],
                    "stripe_transfer" => isset($response['transfer_funds']['transfer']['id']) ? $response['transfer_funds']['transfer']['id'] : '',
                    "purchase_type" => 'rent'
                ];
                $response['data_save'] = $data_save;
                $response['success'] = $SubscribersClasses->save($data_save);
            }

            $response['class_url'] = "/classes/" . $class['slug'];
        }
		return $this->respond($response);
    }
    public function request_payout()
    {
		$stripe_model = new StripeModel();
		$email_model = new EmailModel();
        $SubscribersClasses = model('SubscribersClasses');
        $TeachersPayoutsModel = model('TeachersPayoutsModel');
		$teacherModel = model('TeachersModel');
        $NotificationsModel = model('NotificationsModel');
		$logged_user = $this->user;
		$request = service('request');

        $teacher = $this->teacher_info(session('teacher'));

        $validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
		$rules['amount']    = [
            'label'  => 'Payout amount',
            'rules'  => 'required',
        ];
        if($teacher['stripe_account_card_added'] != 1){
            $rules['card.name']    = [
                'label'  => 'Name on card',
                'rules'  => 'required',
            ];
            $rules['card.number']    = [
                'label'  => 'Card number',
                'rules'  => 'required|numeric',
            ];
            $rules['card.exp_month']    = [
                'label'  => 'Month',
                'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
            ];
            $rules['card.exp_year']    = [
                'label'  => 'Year',
                'rules'  => 'required|numeric|exact_length[2]',
            ];
            $rules['card.cvc']    = [
                'label'  => 'CVC',
                'rules'  => 'required|numeric',
            ];
        }
		// $messages = $this->model->validationMessages;
        $data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

        // if(isset($data['stripe_customer']) AND $data['stripe_customer'] !=''){
        //     unset($rules['password']);
        //     unset($rules['email']);
        // }
		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
           if($teacher['stripe_account_card_added'] != 1){
                $data['card']['currency'] = 'usd';
                $token = $stripe_model->create_card_token($data['card']);
                if (!$token['success'])
                {
                    return $this->respond($token);
                }

                //update account with payment source
                $response['account'] = $stripe_model->update_account($teacher['stripe_account'], $token['token']);
                if (!$response['account']['success'])
                {
                    return $this->respond($response['account']);
                }else{
                    // save payout
                    $data_teacher_save = [
                        "id" => $teacher['id'],
                        "stripe_account_card_added" => 1
                    ];
                    // print_r($data_teacher_save);
                    // die();
                    $response['data_teacher_card_save'] = $teacherModel->save($data_teacher_save);
                }
            }

            // create payout
            $currency = $teacher['country'] == 'US' ? 'usd' : 'eur';
			$response['payout'] = $stripe_model->create_payout($teacher, ($data['amount'] * 100), $currency);
			if (!$response['payout']['success'])
			{
				return $this->respond($response['payout']);
			}else{
                $to = $teacher['email'];
                $reply = '<EMAIL>';
                $subject = 'Lagree On Demand - Payout request';
                $dataEmail = [
                    'firstname' => $teacher['firstname'],
                    'amount' => $data['amount']
                ];
                $template = 'front/email_templates/payout-request';
                $response['payout_request_email'] = $email_model->send_template($to, $reply, $subject, $dataEmail, $template);

                $notification_seller_data = array(
                    'content'       => 'Your payout request is received',
                    'author'        => 'system',
                    'subscriber_id' => session('user'),
                    'type'          => 'payout_request_notif',
                    'date'          => date('Y-m-d H:i:s')
                );
                $response['notification_seller_saved'] = $NotificationsModel->save($notification_seller_data);

                // save payout
                $data_payout_save = [
                    "teacher_id" => $teacher['id'],
                    "amount" => $data['amount'],
                    "date" => date('Y-m-d H:i:s'),
                    "stripe_payout" => isset($response['payout']['payout']['id']) ? $response['payout']['payout']['id'] : ''
                ];
                $response['data_payout_save'] = $TeachersPayoutsModel->save($data_payout_save);
                $response['success'] = TRUE;
            }

            // $response['class_url'] = "/classes/" . $class['slug'];
        }
		return $this->respond($response);
    }
    public function activate($created_at = NULL, $user_id = NULL)
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $subscriber = $this->model->where(['MD5(created_at)' => $created_at, 'MD5(id)' => $user_id, 'status' => 1])->first();
		if ($subscriber)
		{
			$subscriber['status'] = 2;
			$response['success'] = $this->model->save($subscriber);
			// echo $subscriber['firstname'] . ' ' . $subscriber['lastname'] . " Subscriber's Email is confirmed";
            $data['current'] = $subscriber;
            $data['current']['image'] = base_url() . 'images/classes1.jpg';
            $data['current']['seo_title'] = 'Collections | Lagree On Demand';
            $data['current']['seo_description'] = 'Lagree On Demand Collections';
            $data['current']['seo_keywords'] = 'Lagree On Demand Collections';

            $data['current']['title'] = "Dear " . $subscriber['firstname'] . ', your email address is now confirmed.';
            $data['current']['content'] = "Enjoy using and practicing on Lagree On Demand platform";
            $data['current']['button'] = '<a href="/classes" class="btn btn-tall btn-wide btn-border white-bg black" title="Classes">Classes</a>';
		}
		else
		{
            $data['current']['image'] = base_url() . 'images/classes1.jpg';
            $data['current']['seo_title'] = 'Collections | Lagree On Demand';
            $data['current']['seo_description'] = 'Lagree On Demand Collections';
            $data['current']['seo_keywords'] = 'Lagree On Demand Collections';

            $data['current']['title'] = "We have a problem confirming your email address.";
            $data['current']['content'] = "If you need help with your registration, please go to contact page and send us a ticket.";
            $data['current']['button'] = '<a href="/classes" class="btn btn-tall btn-wide btn-border white-bg black" title="Contact Us">Contact Us</a>';
		}
        echo view('front/pages/message_view', $data);
    }
    public function thankyou()
    {

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['current']['image'] = base_url() . 'images/classes1.jpg';
        $data['current']['seo_title'] = 'Thank you | Lagree On Demand';
        $data['current']['seo_description'] = 'Lagree On Demand Thank you';
        $data['current']['seo_keywords'] = 'Lagree On Demand Thank you';

        $data['current']['title'] = "Message Has Been Sent!";
        $data['current']['content'] = "Thank you for contacting our support. We will be in touch with you shortly.";
        $data['current']['button'] = '<a href="/" class="btn btn-tall btn-wide btn-border white-bg black" title="Homepage">Homepage</a>';

        echo view('front/pages/message_view', $data);
    }
    public function sendMail()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = model('EmailModel');

        $rules    = [
            'contact_fullname'     => [
                'label'  => 'Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'contact_email'     => [
                'label'  => 'Email',
                'rules'  => 'required|valid_email',
                'errors' => [
                    'is_unique' => 'That email has already been taken. Please choose another.',
                ],
            ],
            'contact_phone'     => [
                'label'  => 'Phone',
                'rules'  => 'required',
            ],
            'contact_message'     => [
                'label'  => 'Message',
                'rules'  => 'required|min_length[2]',
            ]
        ];
        $response['success'] = FALSE;
        $s1 = session("broj1");
        $s2 = session("broj2");


		$validation->reset();
		$validation->setRules($rules);
        if(($s1 + $s2) == $data['sum']){
            if (!$validation->run($data)){
                $response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
                $response['json'] = $validation->getErrors();
            }else{
                if($data['name'] == ''){
                    $to = $this->settings['main_email'];
                    $reply = $data['contact_email'];
                    $subject = 'Lagree On Demand - Message from Contact form';
                    $email_data = [
                        'fullname' => $data['contact_fullname'],
                        'email' => $data['contact_email'],
                        'phone' => $data['contact_phone'],
                        'message' => $data['contact_message'],
                    ];
                    $template = 'front/email_templates/contact-form';
                    $response = $email_model->send_template($to, $reply, $subject, $email_data, $template);
                }else{
                    $response['success'] = TRUE;
                }
            }
        }else{
            $response['message'] = 'Wrong result. Please sum these two numbers correctly.';
        }
        return $this->respond($response);
    }
    public function saveGiveaway()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $GiveawayModel = model('GiveawayModel');
        // $email_model = model('EmailModel');

        $response['success'] = FALSE;
        $s1 = session("broj1");
        $s2 = session("broj2");

		$rules = $GiveawayModel->validationRules;
        $validation->reset();
		$validation->setRules($rules);

        if(($s1 + $s2) == $data['sum']){
            if (!$validation->run($data)){
                $response['message'] = 'Please fill all the fields';
                // $response['message'] = 'Form submission  failed: <br /><br />' . implode('<br />', $validation->getErrors());
                $response['json'] = $validation->getErrors();
            }else{
                if($data['name'] == ''){
                    $save_data = [
                        'firstname' => $data['firstname'],
                        'lastname' => $data['lastname'],
                        'email' => $data['email'],
                        // 'message' => $data['message'],
                    ];

                    $response['success'] = $GiveawayModel->save($save_data);
                    // $to = $this->settings['main_email'];
                    // $reply = $data['contact_email'];
                    // $subject = 'Lagree On Demand - Message from Raffle form';
                    // $email_data = [
                    //     'firstname' => $data['firstname'],
                    //     'lastname' => $data['lastname'],
                    //     'email' => $data['email'],
                    //     // 'message' => $data['message'],
                    // ];
                    // $template = 'front/email_templates/raffle-form';
                    // $response = $email_model->send_template($to, $reply, $subject, $email_data, $template);
                }else{
                    $response['success'] = TRUE;
                }
            }
        }else{
            $response['message'] = 'Please sum these two numbers correctly.';
        }
        return $this->respond($response);
    }
    public function send_student_form()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = model('EmailModel');

        $rules    = [
            'student_firstname'     => [
                'label'  => 'First Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'student_lastname'     => [
                'label'  => 'Last Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'student_location'     => [
                'label'  => 'Location',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'student_email'     => [
                'label'  => 'Email',
                'rules'  => 'required|valid_email',
            ],
            'student_phone'     => [
                'label'  => 'Phone',
                'rules'  => 'required',
            ],
            'experience'     => [
                'label'  => 'Level of Experience',
                'rules'  => 'required|min_length[2]',
            ],
            'student_month'     => [
                'label'  => 'Month',
                'rules'  => 'required',
            ],
            'student_day'     => [
                'label'  => 'Day',
                'rules'  => 'required',
            ],
            'student_year'     => [
                'label'  => 'Year',
                'rules'  => 'required',
            ],
        ];
        $response['success'] = FALSE;
        $s1 = session("broj1");
        $s2 = session("broj2");


		$validation->reset();
		$validation->setRules($rules);
        if(($s1 + $s2) == $data['sum']){
            if (!$validation->run($data)){
                $response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
                $response['json'] = $validation->getErrors();
            }else{
                // if($data['name'] == ''){
                    $to = $this->settings['main_email'];
                    $reply = NULL;
                    $subject = 'Lagree On Demand - Join Lagree - Model form';
                    $email_data = [
                        'firstname' => $data['student_firstname'],
                        'lastname' => $data['student_lastname'],
                        'location' => $data['student_location'],
                        'phone' => $data['student_phone'],
                        'email' => $data['student_email'],
                        'experience' => $data['experience'],
                        'month' => $data['student_month'],
                        'day' => $data['student_day'],
                        'year' => $data['student_year'],
                    ];
                    $template = 'front/email_templates/student-form';
                    $response = $email_model->send_template($to, $reply, $subject, $email_data, $template);
                // }else{
                    $response['success'] = TRUE;
                // }
            }
        }else{
            $response['message'] = 'Wrong result. Please sum these two numbers correctly.';
        }
        return $this->respond($response);
    }
    public function send_teacher_form()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = model('EmailModel');

        $rules    = [
            'teacher_firstname'     => [
                'label'  => 'First Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'teacher_lastname'     => [
                'label'  => 'Last Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'teacher_location'     => [
                'label'  => 'Location',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'teacher_email'     => [
                'label'  => 'Email',
                'rules'  => 'required|valid_email',
            ],
            'teacher_phone'     => [
                'label'  => 'Phone',
                'rules'  => 'required',
            ],
            'certification'     => [
                'label'  => 'Certification Level',
                'rules'  => 'required|min_length[2]',
            ],
            'teacher_month'     => [
                'label'  => 'Month',
                'rules'  => 'required',
            ],
            'teacher_day'     => [
                'label'  => 'Day',
                'rules'  => 'required',
            ],
            'teacher_year'     => [
                'label'  => 'Year',
                'rules'  => 'required',
            ],
        ];
        $response['success'] = FALSE;
        $s1 = session("broj1");
        $s2 = session("broj2");


		$validation->reset();
		$validation->setRules($rules);
        if(($s1 + $s2) == $data['sum']){
            if (!$validation->run($data)){
                $response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
                $response['json'] = $validation->getErrors();
            }else{
                // if($data['name'] == ''){
                    $to = $this->settings['main_email'];
                    $reply = NULL;
                    $subject = 'Lagree On Demand - Join Lagree - Teacher form';
                    $email_data = [
                        'firstname' => $data['teacher_firstname'],
                        'lastname' => $data['teacher_lastname'],
                        'location' => $data['teacher_location'],
                        'phone' => $data['teacher_phone'],
                        'email' => $data['teacher_email'],
                        'certification' => $data['certification'],
                        'month' => $data['teacher_month'],
                        'day' => $data['teacher_day'],
                        'year' => $data['teacher_year'],
                    ];
                    $template = 'front/email_templates/teacher-form';
                    $response = $email_model->send_template($to, $reply, $subject, $email_data, $template);
                // }else{
                    $response['success'] = TRUE;
                // }
            }
        }else{
            $response['message'] = 'Wrong result. Please sum these two numbers correctly.';
        }
        return $this->respond($response);
    }
    public function sendMailModel()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = model('EmailModel');

        $rules    = [
            'fullname'     => [
                'label'  => 'Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'model'     => [
                'label'  => 'Equipment Model',
                'rules'  => 'required',
            ],
            'shipping_address'     => [
                'label'  => 'Shipping Address',
                'rules'  => 'required',
            ],
            'email'     => [
                'label'  => 'Email',
                'rules'  => 'required|valid_email',
            ],
            'message'     => [
                'label'  => 'Note',
                'rules'  => 'required|min_length[2]',
            ]
        ];
        $response['success'] = FALSE;

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			$response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
            if($data['name'] == ''){
                $to = $data['send_to'];
                // $to = $this->settings['main_email'];
                $reply = $data['email'];
                $subject = 'Lagree On Demand - Mega purchase inquire form';
                $data = [
                    'fullname' => $data['fullname'],
                    'email' => $data['email'],
                    'model' => $data['model'],
                    'shipping_address' => $data['shipping_address'],
                    'message' => $data['message'],
                ];
                $template = 'front/email_templates/model-purchase-form';
                $response = $email_model->send_template($to, $reply, $subject, $data, $template);
                $response['success'] = TRUE;
            }
        }
        return $this->respond($response);
    }
    public function sendHelpEmail()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = model('EmailModel');

        $rules    = [
            'customers_fullname'     => [
                'label'  => 'Name',
                'rules'  => 'required|alpha_numeric_space|min_length[2]',
            ],
            'customers_eml'     => [
                'label'  => 'Email',
                'rules'  => 'required|valid_email',
            ],
            'customers_message'     => [
                'label'  => 'Message',
                'rules'  => 'required',
            ]
        ];
        $response['success'] = FALSE;

        $s1 = session("broj1");
        $s2 = session("broj2");

		$validation->reset();
		$validation->setRules($rules);
        if(($s1 + $s2) == $data['sum']){
            if (!$validation->run($data)){
                $response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
                $response['json'] = $validation->getErrors();
            }else{
                if($data['name'] == ''){
                    // $to = '<EMAIL>';
                    $to = $this->settings['main_email'];
                    $reply = $data['customers_eml'];
                    $subject = "Lagree On Demand - Customer Support question from " . $data['customers_fullname'];
                    $data = [
                        'customers_fullname' => $data['customers_fullname'],
                        'customers_eml' => $data['customers_eml'],
                        'customers_message' => $data['customers_message'],
                    ];
                    $template = 'front/email_templates/help-form';
                    $response['success'] = $email_model->send_template($to, $reply, $subject, $data, $template);
                }else{
                    $response['success'] = TRUE;
                }
            }
        }else{
            $response['msg'] = 'Wrong result. Please sum these two numbers correctly.';
        }
        return $this->respond($response);
    }
    public function test_email(){
        $email_model = model('EmailModel');
		// $data = array(
		// 	'fullname' => 'Nikola Mitrovic',
		// 	'email' => '<EMAIL>',
		// 	'phone' => '************',
		// 	'message' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Tenetur rerum consequatur ad deserunt! Esse quisquam veritatis magnam. Ipsum nesciunt dolorem ipsam delectus tenetur nam ratione cupiditate reiciendis nobis, amet deleniti?',
		// );

        //$to = $email->SMTPUser;
        // $to = '<EMAIL>';
        // $subject = 'TEST Subject';
        // $message = 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Tenetur rerum consequatur ad deserunt! Esse quisquam veritatis magnam. Ipsum nesciunt dolorem ipsam delectus tenetur nam ratione cupiditate reiciendis nobis, amet deleniti?';

        // $fullname = $data['fullname'];
        // $email = $data['email'];
        // $phone = $data['phone'];
        // $message = $data['message'];
        // $msg = "
        //     This email is sent from test page: <br>
        //     Name: $fullname<br>
        //     Email: $email<br>
        //     Phone: $phone<br><br>
        //     Message: $message<br>
        // ";

        // $data = [
        //     'fullname' => 'TEST TEST',
        //     'email' => '<EMAIL>',
        //     'phone' => '555555555',
        //     'message' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Tenetur rerum consequatur ad deserunt! Esse quisquam veritatis magnam. Ipsum nesciunt dolorem ipsam delectus tenetur nam ratione cupiditate reiciendis nobis, amet deleniti!',
        // ];
        // $to = '<EMAIL>';
        // $reply = $data['email'];
        // $subject = 'TEST Message from Lagree On Demand';
        // $template = 'front/email_templates/contact-form';
        // $response = $email_model->send_template($to, $reply, $subject, $data, $template);

        // echo $response ? 'Email successfully sent' : 'Email NOT sent';

        $emailService = \Config\Services::email();

        $data = [];
        $to = '<EMAIL>';
        $reply = '<EMAIL>';
        $subject = 'Testing recap email template';
        $template = 'front/email_templates/recap-email';
        $response = $email_model->send_template($to, $reply, $subject, $data, $template);

        echo $response ? 'Email successfully sent' : 'Email NOT sent';

        // $to = '<EMAIL>';
        // $emailService->setTo($to);
        // $emailService->setFrom('<EMAIL>', 'Testing recap email template...');

        // $emailService->setSubject('Accountable RECAP email');
        // $emailService->setMessage($msg);

        // if ($emailService->send()){
        //     echo 'Email successfully sent';
        // }else{
        //     $data = $emailService->printDebugger(['headers']);
        //     print_r($data);
        // }
    }
    public function reset_reg(){
        session()->remove('subscribe_info');
        session()->remove('code_verified');
    }

    public function failed(){

        $BlacklistModel = new BlacklistModel();
        $endpoint_secret = 'whsec_fKyqHVR7vro0cEchXParPvmgC1pNZBU6';
        // Stripe::setApiKey($_ENV['api_key_live']);

        \Stripe\Stripe::setApiKey($_ENV['api_key_live']);
        // Retrieve the request's body and parse it as JSON
        $input = @file_get_contents('php://input');
        $event = null;

        // Log the full webhook payload
        log_message('info', 'Stripe Webhook Payload: ' . $input);

        // Check if the signature header is present
        if (!isset($_SERVER['HTTP_STRIPE_SIGNATURE'])) {
            log_message('error', 'Stripe signature header missing');
            return $this->response->setStatusCode(400, 'Signature header missing');
        }

        $signature = $_SERVER['HTTP_STRIPE_SIGNATURE'];

        try {
            // Verify the signature and construct the event
            $event = \Stripe\Webhook::constructEvent(
                $input, $signature, $endpoint_secret
            );
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            log_message('error', 'Invalid payload');
            return $this->response->setStatusCode(400, 'Invalid payload');
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            log_message('error', 'Invalid signature');
            return $this->response->setStatusCode(400, 'Invalid signature');
        }

        // Handle the event
        switch ($event->type) {
            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object; // contains the Stripe PaymentIntent object
                $customerId = $paymentIntent->customer; // Customer ID from PaymentIntent

                // Fetch customer details
                $customer = \Stripe\Customer::retrieve($customerId);

                // Log the customer's email address
                $outcomeType = isset($paymentIntent->charges->data[0]->outcome->type) ? $paymentIntent->charges->data[0]->outcome->type : 'N/A';

                log_message('error', 'PaymentIntent failed: ' . $outcomeType);
                log_message('error', 'Customer Email: ' . $customer->email);

                if(isset($customer->email) AND $customer->email != '' AND isset($outcomeType) AND $outcomeType == 'blocked') {
                    $blocked = $BlacklistModel->where(['email' => $customer->email])->first();
                    if($blocked == NULL){
                        $BlacklistModel->save([
                            'email' => $customer->email,
                            'stripe_status' => $outcomeType,
                        ]);
                    }
                }

                // Handle the failed payment here
                break;

            // Add more cases for other event types you want to handle
            default:
                log_message('info', 'Unhandled event type: ' . $event->type);
                return $this->response->setStatusCode(400, 'Event type not handled');
        }

        return $this->response->setStatusCode(200);
    }

    private function _block_user_sucuri($ip){
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://waf.sucuri.net/api?v2',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query(array(
                'k' => '65930be04c04428df9ecfbc2edb4320d',
                's' => '256dbe7089152876286ccf33b7cf818f',
                'a' => 'blocklist_ip',
                'ip' => $ip, // Replace IP_ADDRESS with the actual IP address you want to block
                'duration' => '1000000'
            ))
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            return curl_error($curl);
        } else {
            return $response;
        }

        curl_close($curl);
    }
    public function allow_user_sucuri($ip){
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://waf.sucuri.net/api?v2',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query(array(
                'k' => '65930be04c04428df9ecfbc2edb4320d',
                's' => '256dbe7089152876286ccf33b7cf818f',
                'a' => 'delete_blocklist_ip',
                'ip' => $ip, // Replace IP_ADDRESS with the actual IP address you want to block
                'duration' => '1000000'
            ))
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            return curl_error($curl);
        } else {
            return $response;
        }

        curl_close($curl);
    }
}