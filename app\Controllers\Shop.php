<?php namespace App\Controllers;

use Shopify\Clients\Rest;
use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Shop extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ShopModel');
        $this->controller = 'shop';

        $this->shopify_config = array(
			'api_key' => $_ENV['shopify_api_key'],
			'password' => $_ENV['shopify_password'],
			'shopUrl' => $_ENV['shopify_ShopUrl']
		);

	}

    public function index()
    {
        $data['menu'] = $this->menu;
        $data['footer'] = $this->footer;
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        $data['nums'] = create_session_nums();

        $data['current'] = $this->model->where(['template' => 'homepage'])->first();
        // $data['all_sliders'] = $this->model->query("SELECT * FROM slider WHERE deleted_at IS NULL AND status = 0 ORDER BY sort ASC")->getResultArray();

		echo view('front/shop/index_view', $data);
    }

    public function category($collection = '')
    {
        // $data['footer'] = $this->footer;
        // $data['menu'] = $this->menu;
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        $data['nums'] = create_session_nums();

        $meta_titles = [
            'equipment' => "Equipment and Machines by Lagree Fitness | Lagree Fitness",
            'accessories' => "Fitness Equipment Accessories for Lagree Fitness Equipment | Lagree Fitness",
            'whats-new' => "What's New | New Lagree Fitness Equipment, Products, and Machines",
            'lagree-to-go' => "To-Go Exercise Equipment by Lagree Fitness | Travel-Friendly Lagree Fitness Equiloment",
            'bundles' => "Bundles by Lagree Fitness | Lagree Fitness Fitness Equipment Bundles",
            'apparel' => "Apparel by Lagree Fitness | Buy Lagree Fitness Apparel",
            'gift-cards' => "Lagree Fitness Gift Cards | Purchase a Lagree Fitness Gift Card",
            'events' => "Events by Lagree Fitness | Lagree Fitness Micro Events",
            'deals' => "Deals by Lagree Fitness | Lagree Fitness Micro Deals",
            'equipment_micro' => "Micro Machines by Lagree Fitness | Lagree Fitness",
            'equipment_mini' => "Mini Machines by Lagree Fitness | Lagree Fitness",
            'equipment_mega' => "Mega Machines by Lagree Fitness | Lagree Fitness",
            'equipment_evo' => "Evo Machines by Lagree Fitness | Lagree Fitness",
            'accessories_micro' => "Micro Equipment Accessories for Lagree Fitness Equipment | Lagree Fitness",
            'accessories_mini' => "Mini Equipment Accessories for Lagree Fitness Equipment | Lagree Fitness",
            'accessories_mega' => "Mega Equipment Accessories for Lagree Fitness Equipment | Lagree Fitness",
            'accessories_evo' => "EVO Equipment Accessories for Lagree Fitness Equipment | Lagree Fitness"
        ];
        $meta_desc = [
            'equipment' => "Shop Lagree Fitness equipment and transform your body. Our revolutionary low-impact, high-intensity exercise machines will change your body like never before!",
            'accessories' => "Shop accessories for Lagree Fitness equipment and make your Micro, Mini, Megaformer, or EVO exactly what you want! We revolutionize fitness for you!",
            'whats-new' => "Shop all new Lagree Fitness products, machines, and equipment today! Like our method, we never stop, so you can always expect something new!",
            'lagree-to-go' => "Shop travel-friendly Lagree Fitness equipment and take the Lagree Shakes anywhere you go! Never miss a workout again with our Lagree To-Go fitness equipment!",
            'bundles' => "Save big when you purchase Lagree Fitness bundles and take your workout anywhere you go! Choose from a variety of fitness equipment bundles by Lagree Fitness!",
            'apparel' => "Shop our high-quality Lagree Fitness apparel and rep your favorite fitness brand everywhere you go! Choose from fitness apparel, sweatsuits, and so much more!",
            'gift-cards' => "Purchase a Lagree Fitness gift card for your loved one and give them the gift that keeps on giving! Gift cards by Lagree Fitness are redeemable online!",
            'events' => "Join our Lagree Fitness events, including the Micro events, and see what the fun is all about! Get trained by Master Lagree trainers and Sebastien Lagree himself!",
            'deals' => "Save big when you purchase Lagree Fitness deals and take your workout anywhere you go! Choose from a variety of fitness equipment deals by Lagree Fitness!",
            'equipment_micro' => "Shop Lagree Fitness Micro equipment and transform your body. Our revolutionary low-impact, high-intensity exercise machines will change your body like never before!",
            'equipment_mini' => "Shop Lagree Fitness Mini equipment and transform your body. Our revolutionary low-impact, high-intensity exercise machines will change your body like never before!",
            'equipment_mega' => "Shop Lagree Fitness Mega equipment and transform your body. Our revolutionary low-impact, high-intensity exercise machines will change your body like never before!",
            'equipment_evo' => "Shop Lagree Fitness EVO equipment and transform your body. Our revolutionary low-impact, high-intensity exercise machines will change your body like never before!",
            'accessories_micro' =>  "Shop Micro accessories for Lagree Fitness equipment and make your Micro exactly what you want! We revolutionize fitness for you!",
            'accessories_mini' =>   "Shop Mini accessories for Lagree Fitness equipment and make your Mini exactly what you want! We revolutionize fitness for you!",
            'accessories_mega' =>   "Shop Megaformer accessories for Lagree Fitness equipment and make your Megaformer exactly what you want! We revolutionize fitness for you!",
            'accessories_evo' =>    "Shop EVO accessories for Lagree Fitness equipment and make your EVO exactly what you want! We revolutionize fitness for you!s"
        ];

		$data['current']['image'] = base_url() . 'img/lf-img159.jpg';
		$data['current']['seo_title'] = $meta_titles[$collection];
		$data['current']['seo_description'] = $meta_desc[$collection];

        return view('front/pages/shop_view', $data);
    }

    public function slug($slug = '')
    {
        // $data['footer'] = $this->footer;
        // $data['menu'] = $this->menu;
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        $LagreeProducts = model('LagreeProducts');
        $data['nums'] = create_session_nums();

        $data['product'] = $LagreeProducts->single_product_slug($slug);

		$data['current']['image'] = $data['product']['image'];
		$data['current']['seo_title'] = $data['product']['title'] . ' - $' . $data['product']['price'];
		$data['current']['seo_description'] = "Lagree Fitness";

        $data['mega'] = [
            '5046117400714', // M2 Megaformer - RESTORED
            '5046147350666', // M3S Megaformer - RESTORED
            '5046140665994', // M3 Megaformer - RESTORED
            '5046151905418', // M3K Megaformer - RESTORED
            '5046159835274', // M3X Megaformer - RESTORED
            '5043260293258', // M3 Megaformer - NEW
            '5043496157322', // M3S Megaformer - NEW
            '5043561988234', // EVO Megaformer - NEW
            '5043501793418', // M3K Megaformer - NEW
            '5043552649354', // M3X Megaformer - NEW
            '7602086477975', // M3K+ Megaformer - NEW (AFFIRM/SHOP PAY NOT AVAILABLE FOR THIS ITEM)
            '7847237582999', // THE RAMP
        ];
        // echo "<pre>";
        // print_r($data['product']);
        // die();

        return view('front/shop/shop-single_view', $data);
    }

    public function cart()
    {
        // $data['footer'] = $this->footer;
        // $data['menu'] = $this->menu;
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        // $data['controller'] = $this->controller;
        $shop_model = model('ShopModel');
        $data['nums'] = create_session_nums();

        if(NULL === session('cart')){
            return redirect()->to('/shop');
        }else{
            $cart = json_decode(session('cart'), TRUE);
            if(!isset($cart['items'])){
                return redirect()->to('/shop');
            }
        }

		// $data['current']['image'] = '';
		$data['current']['seo_title'] = 'Lagree Shop - Cart';
		$data['current']['seo_description'] = 'Lagree Shop - Cart';

        return view('front/shop/cart_view', $data);
    }

    function show_all_products()
    {

        $ShopifyProductsModel = model('ShopifyProductsModel');
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $api_url = 'https://' . $config['ApiKey'] . ':' . $config['Password'] . '@' . $config['ShopUrl'];
        $products_obj_url = $api_url . '/admin/api/2021-10/collections/177644667018/products.json?limit=250'; // admin/api/2021-10/products/5042742788234.json
        // $products_obj_url = $api_url . '/admin/products.json?limit=20&page='.($i+1);
        $products_content = @file_get_contents( $products_obj_url );
        $products_json = json_decode( $products_content, true );

        if(isset($products_json['products'])){
            foreach($products_json['products'] as $key => $single){
                $product = $ShopifyProductsModel->single_product_api($single['id']);

                if(strpos($single['tags'], 'no_shop') === FALSE AND $single['status'] == 'active'){
                    echo json_encode($product['product']['options']) . '<br>';
                    echo json_encode($product['product']['variants']) . '<br>';
                    echo '<br><br>';
                }
            }
        }

        // if(isset($products_json['products'])){
        //     foreach($products_json['products'] as $key => $single){
        //         echo json_encode($single['options']) . '<br>';
        //         echo json_encode($single['variants']) . '<br>';
        //         echo '<br><br>';
        //     }
        // }
        echo '<pre>';
        print_r($products_json);
        die();

    }
    function get_products()
    {
        $LagreeProducts = model('LagreeProducts');
        $request = service('request');
        $data = $request->getPost();

        $products['all_products'] = $LagreeProducts->all_shop_product($data['collection'], $data['start'], $data['limit'], $data['order']);
        $products['collection'] = $data['collection'];

        $response['success'] = FALSE;
        if(NULL != $products['all_products'] AND count($products['all_products']) > 0){
            $response['html'] = view('front/shop/shop-items-ajax_view', $products);
            $response['count'] = count($products['all_products']);
            $response['success'] = TRUE;
        }

		return $this->respond($response);
    }

    public function get_shopify_collection($id = 0)
    {
        $shopify_model = model('ShopifyModel');
        if($id != 0){
            $data['shopify'] = $shopify_model->single_collection($id);
            if($data['shopify']['success']){
                foreach($data['shopify']['products'] as $key => $product){
                    $data['collection_products'][] = $shopify_model->single_product($product['id']);
                }
                $response['success'] = TRUE;
            }
            $response['html'] = view('front/shop/shop_collection_products_view', $data);
        }else{
            $response['success'] = FALSE;
        }

		return $this->respond($response);
    }
    public function check_code()
	{
        $codesModel = model('CodesModel');
        $request = service('request');
        $data = $request->getPost();

		$response['success'] = TRUE;
		$response['code_info'] = $codesModel->check_code($data['code']);

        return $this->respond($response);
	}
    function empty_cart()
    {
		session()->remove('cart');
        $response['success'] = TRUE;
        return $this->respond($response);
    }
    function add_to_cart()
    {
        $request = service('request');
        $data = $request->getPost();
        $already_added = false;

        if(NULL !== session('cart') OR session('cart') != 0){
            $cart = json_decode(session('cart'), TRUE);
            foreach($cart['items'] as $key => $single){
                $response['items'][$key] = $single;
                if($data['product_id'] == $single['product_id']){
                    if($data['variant_id'] == $single['variant_id']){
                        $already_added = TRUE;
                        $response['items'][$key]['qty'] = $single['qty'] + $data['qty'];
                    }
                }
            }
            if(!$already_added){
                $response['items'][count($cart['items'])] = $data;
                $response['count'] = count($cart['items'])+1;
            }else{
                $response['count'] = count($cart['items']);
            }
            session()->set('cart', json_encode($response));
        }else{
            $response['count'] = 1;
            $response['items'][0] = $data;
            session()->set('cart', json_encode($response));
        }

		return $this->respond($response);
    }
    function remove_from_cart()
    {
        $request = service('request');
        $data = $request->getPost();

        $cart = json_decode(session('cart'), TRUE);

        $response['total'] = 0;
        foreach($cart['items'] as $key => $single){
            if($data['variant_id'] != $single['variant_id']){
                $response['items'][$key] = $single;
                $response['total'] += ($single['price'] * $single['qty']);
            }
        }
        if(isset($response['items'])){
            $response['count'] = count($response['items']);
            $response['type'] = 'REMOVE TO CART';
            session()->set('cart', json_encode($response));
        }else{
            session()->remove('cart');
            $response['count'] = 0;
        }

        return $this->respond($response);
    }
    function update_cart()
    {
        $request = service('request');
        $data = $request->getPost();

        $session = json_decode(session('cart'), TRUE);
        $cart = json_decode($data['items'], TRUE);

        $response['total'] = 0;
        foreach($session['items'] as $key => $single){
            $response['items'][$key] = $single;
            foreach($cart as $key2 => $single2){
                if($single['variant_id'] == $single2['variant']){
                    $response['items'][$key]['qty'] = $single2['qty'];
                    $response['total'] += ($single['price'] * $single2['qty']);
                }
            }
        }
        if(isset($response['items'])){
            $response['count'] = count($response['items']);
            session()->set('cart', json_encode($response));
        }else{
            session()->remove('cart');
            $response['count'] = 0;
        }

		return $this->respond($response);
    }
    function search()
    {
        // $data['footer'] = $this->footer;
        // $data['menu'] = $this->menu;
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        $data['nums'] = create_session_nums();
        $request = service('request');
        $post = $request->getPost();

        if(!isset($post['search_term'])){
            return redirect()->to('/shop');
        }
        $keyword = $post['search_term'];
        $data['search_term'] = $post['search_term'];
        $data['search_results']['all_products'] = $this->model->query("SELECT *
                                                                        FROM products_shopify
                                                                        WHERE collection_id = 'ALL'
                                                                        AND status = 'active'
                                                                        AND (
                                                                            LOWER(title) LIKE '%" . strtolower($keyword) . "%' OR
                                                                            LOWER(handle) LIKE '%" . strtolower($keyword) . "%' OR
                                                                            LOWER(description) LIKE '%" . strtolower($keyword) . "%'
                                                                        )
                                                                    ")->getResultArray();
        $data['search_results']['collection'] = "search";

		$data['current']['seo_title'] = "Lagree Fitness Search - $keyword";
		$data['current']['seo_description'] = "Lagree Fitness Search - $keyword";

        return view('front/shop/search_view', $data);
    }
}