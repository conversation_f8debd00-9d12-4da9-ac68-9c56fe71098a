<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Notifications extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('NotificationsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        $data['all_notifications'] = $this->model->all_notifications_admin(0, session('per_page'));
        $data['notifications_count'] = $this->model->where(['author' => 'admin'])->countAllResults();
        $data['page'] = 1;

        echo view('admin/notifications/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['sort_by'] = "Date Added";
        $data['all_notifications'] = $this->model->all_notifications_admin(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['notifications_count'] = $this->model->where(['author' => 'admin'])->countAllResults();
        $data['page'] = $page;

        echo view('admin/notifications/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_notifications'] = $this->model->all_notifications_admin(0, session('per_page'), $data['search_term']);
        $data['notifications_count'] = $this->model->like('content', $data['search_term'])->where(['author' => 'admin'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/notifications/index_view', $data);
    }

    public function sort_by($type = 'notifications.date', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $data['all_notifications'] = $this->model->all_notifications_admin(0, session('per_page'), NULL, ($type. " " . $direction));
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['classes_count'] = count($data['all_notifications']);
        $types = array(
            "notifications.created_atdesc" => "Date Added",
            "notifications.codeasc" => "Ascending",
            "notifications.codedesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;

        echo view('admin/notifications/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['current'] = $this->model->current($edit_id);
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/notifications');
        }
        $data['users'] = $this->model->query("SELECT * FROM subscribers WHERE deleted_at IS NULL")->getResultArray();

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/notifications/edit_view', $data);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';

            if(isset($data['date']) AND Time::createFromFormat("m/d/Y H:i:s" , $data['date'], 'America/Los_Angeles'))
            {
                $tmp = Time::createFromFormat("m/d/Y H:i:s" , $data['date'], 'America/Los_Angeles');
                $data['date'] = $tmp->toDateTimeString('Y-m-d H:i:s');
            }

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}