<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\StripeModel;
use App\Models\SubscribersModel;

class CheckDuplicateSubscriptions extends BaseCommand
{
    protected $group       = 'Stripe';
    protected $name        = 'stripe:check-duplicates';
    protected $description = 'Check for duplicate Stripe subscriptions and optionally fix them';

    protected $usage = 'stripe:check-duplicates [options]';
    protected $arguments = [];
    protected $options = [
        '--fix' => 'Automatically fix duplicates by cancelling older subscriptions',
        '--customer' => 'Check specific customer ID',
        '--subscription' => 'Check specific subscription ID',
        '--price-analysis' => 'Analyze price IDs and their usage'
    ];

    public function run(array $params)
    {
        $stripe_model = new StripeModel();
        $subscribers_model = new SubscribersModel();

        CLI::write('Checking for duplicate Stripe subscriptions...', 'yellow');

        $fix_mode = CLI::getOption('fix');
        $specific_customer = CLI::getOption('customer');
        $specific_subscription = CLI::getOption('subscription');
        $price_analysis = CLI::getOption('price-analysis');

        try {
            if ($price_analysis) {
                $this->analyzePriceIds($stripe_model);
            } elseif ($specific_subscription) {
                $this->checkSpecificSubscription($stripe_model, $specific_subscription);
            } elseif ($specific_customer) {
                $this->checkSpecificCustomer($stripe_model, $subscribers_model, $specific_customer, $fix_mode);
            } else {
                $this->checkAllCustomers($stripe_model, $subscribers_model, $fix_mode);
            }

        } catch (\Exception $e) {
            CLI::error('Error: ' . $e->getMessage());
            return;
        }

        CLI::write('Done!', 'green');
    }

    private function checkSpecificSubscription($stripe_model, $subscription_id)
    {
        CLI::write("Checking subscription: {$subscription_id}", 'cyan');

        try {
            $stripe = new \Stripe\StripeClient(
                $stripe_model->stripe_config['api_key']
            );

            $subscription = $stripe->subscriptions->retrieve($subscription_id);
            
            CLI::write("Subscription Details:", 'yellow');
            CLI::write("  ID: {$subscription->id}");
            CLI::write("  Customer: {$subscription->customer}");
            CLI::write("  Status: {$subscription->status}");
            CLI::write("  Created: " . date('Y-m-d H:i:s', $subscription->created));
            CLI::write("  Current Period: " . date('Y-m-d H:i:s', $subscription->current_period_start) . ' to ' . date('Y-m-d H:i:s', $subscription->current_period_end));

            // Check for other subscriptions for this customer
            $active_subs = $stripe_model->get_customer_active_subscriptions($subscription->customer);
            
            if ($active_subs['success'] && $active_subs['count'] > 1) {
                CLI::write("⚠️  DUPLICATE FOUND! Customer has {$active_subs['count']} active subscriptions:", 'red');
                foreach ($active_subs['subscriptions'] as $sub) {
                    CLI::write("    - {$sub->id} (created: " . date('Y-m-d H:i:s', $sub->created) . ")");
                }
            } else {
                CLI::write("✅ No duplicates found for this customer", 'green');
            }

        } catch (\Stripe\Exception\InvalidRequestException $e) {
            CLI::error("Subscription not found: " . $e->getMessage());
        }
    }

    private function checkSpecificCustomer($stripe_model, $subscribers_model, $customer_id, $fix_mode)
    {
        CLI::write("Checking customer: {$customer_id}", 'cyan');

        // Get subscriber info
        $subscriber = $subscribers_model->where('stripe_customer', $customer_id)->first();
        if ($subscriber) {
            CLI::write("Subscriber: {$subscriber['email']} (ID: {$subscriber['id']})");
        }

        // Check active subscriptions
        $active_subs = $stripe_model->get_customer_active_subscriptions($customer_id);
        
        if (!$active_subs['success']) {
            CLI::error("Failed to retrieve subscriptions: " . $active_subs['message']);
            return;
        }

        CLI::write("Active subscriptions: {$active_subs['count']}");

        if ($active_subs['count'] > 1) {
            CLI::write("⚠️  DUPLICATES FOUND!", 'red');
            
            foreach ($active_subs['subscriptions'] as $sub) {
                CLI::write("  - {$sub->id} (created: " . date('Y-m-d H:i:s', $sub->created) . ", status: {$sub->status})");
            }

            if ($fix_mode) {
                $this->fixCustomerDuplicates($stripe_model, $subscribers_model, $customer_id, $active_subs['subscriptions']);
            } else {
                CLI::write("Use --fix flag to automatically resolve duplicates", 'yellow');
            }
        } else {
            CLI::write("✅ No duplicates found", 'green');
        }
    }

    private function checkAllCustomers($stripe_model, $subscribers_model, $fix_mode)
    {
        CLI::write("Scanning all customers for duplicates...", 'cyan');

        $subscribers = $subscribers_model->where('stripe_customer IS NOT NULL')->findAll();
        $total_checked = 0;
        $duplicates_found = 0;

        foreach ($subscribers as $subscriber) {
            $total_checked++;
            
            if ($total_checked % 10 == 0) {
                CLI::showProgress($total_checked, count($subscribers));
            }

            $active_subs = $stripe_model->get_customer_active_subscriptions($subscriber['stripe_customer']);
            
            if ($active_subs['success'] && $active_subs['count'] > 1) {
                $duplicates_found++;
                
                CLI::write("\n⚠️  DUPLICATE #{$duplicates_found}: {$subscriber['email']} (Customer: {$subscriber['stripe_customer']})", 'red');
                CLI::write("   Active subscriptions: {$active_subs['count']}");
                
                foreach ($active_subs['subscriptions'] as $sub) {
                    CLI::write("     - {$sub->id} (created: " . date('Y-m-d H:i:s', $sub->created) . ")");
                }

                if ($fix_mode) {
                    $this->fixCustomerDuplicates($stripe_model, $subscribers_model, $subscriber['stripe_customer'], $active_subs['subscriptions']);
                }
            }
        }

        CLI::write("\nScan complete!", 'green');
        CLI::write("Total customers checked: {$total_checked}");
        CLI::write("Customers with duplicates: {$duplicates_found}");

        if ($duplicates_found > 0 && !$fix_mode) {
            CLI::write("Use --fix flag to automatically resolve duplicates", 'yellow');
        }
    }

    private function fixCustomerDuplicates($stripe_model, $subscribers_model, $customer_id, $subscriptions)
    {
        CLI::write("🔧 Fixing duplicates for customer: {$customer_id}", 'yellow');

        // Keep the newest subscription, cancel the rest
        $newest_subscription = null;
        $newest_created = 0;

        foreach ($subscriptions as $sub) {
            if ($sub->created > $newest_created) {
                $newest_created = $sub->created;
                $newest_subscription = $sub;
            }
        }

        $cancelled_count = 0;
        foreach ($subscriptions as $sub) {
            if ($sub->id !== $newest_subscription->id) {
                try {
                    $cancel_result = $stripe_model->cancel_subscription($sub->id);
                    if ($cancel_result['success']) {
                        CLI::write("   ✅ Cancelled: {$sub->id}", 'green');
                        $cancelled_count++;
                    } else {
                        CLI::write("   ❌ Failed to cancel: {$sub->id} - " . $cancel_result['message'], 'red');
                    }
                } catch (\Exception $e) {
                    CLI::write("   ❌ Error cancelling {$sub->id}: " . $e->getMessage(), 'red');
                }
            }
        }

        // Update subscriber record
        $subscriber = $subscribers_model->where('stripe_customer', $customer_id)->first();
        if ($subscriber) {
            $subscribers_model->save([
                'id' => $subscriber['id'],
                'stripe_subscription' => $newest_subscription->id,
                'subscription_status' => 'active'
            ]);
            CLI::write("   ✅ Updated subscriber record to use: {$newest_subscription->id}", 'green');
        }

        CLI::write("   Fixed! Cancelled {$cancelled_count} duplicate subscriptions, kept: {$newest_subscription->id}", 'green');
    }

    private function analyzePriceIds($stripe_model)
    {
        CLI::write("Analyzing Price IDs and their usage...", 'cyan');

        try {
            $stripe = new \Stripe\StripeClient(
                $stripe_model->stripe_config['api_key']
            );

            // Common price IDs from your examples
            $price_ids = [
                'price_1KFFPGL6EaNAw2awJpDD8rTi',
                'price_1KJR2XL6EaNAw2awZrAvJzID'
            ];

            foreach ($price_ids as $price_id) {
                CLI::write("\n--- Analyzing Price ID: {$price_id} ---", 'yellow');

                try {
                    $price = $stripe->prices->retrieve($price_id);

                    CLI::write("Price Details:");
                    CLI::write("  Amount: $" . ($price->unit_amount / 100));
                    CLI::write("  Currency: {$price->currency}");
                    CLI::write("  Interval: {$price->recurring->interval}");
                    CLI::write("  Interval Count: {$price->recurring->interval_count}");
                    CLI::write("  Active: " . ($price->active ? 'Yes' : 'No'));

                    // Get subscriptions using this price
                    $subscriptions = $stripe->subscriptions->all([
                        'price' => $price_id,
                        'limit' => 100
                    ]);

                    CLI::write("  Total Subscriptions: " . count($subscriptions->data));

                    $active_count = 0;
                    $customers_with_this_price = [];

                    foreach ($subscriptions->data as $sub) {
                        if ($sub->status === 'active') {
                            $active_count++;
                            $customers_with_this_price[] = $sub->customer;
                        }
                    }

                    CLI::write("  Active Subscriptions: {$active_count}");

                    // Check for customers with multiple subscriptions using this price
                    $customer_counts = array_count_values($customers_with_this_price);
                    $duplicates_with_this_price = array_filter($customer_counts, function($count) {
                        return $count > 1;
                    });

                    if (!empty($duplicates_with_this_price)) {
                        CLI::write("  ⚠️  Customers with multiple active subscriptions for this price:", 'red');
                        foreach ($duplicates_with_this_price as $customer_id => $count) {
                            CLI::write("    - {$customer_id}: {$count} subscriptions");
                        }
                    } else {
                        CLI::write("  ✅ No duplicate subscriptions found for this price", 'green');
                    }

                } catch (\Stripe\Exception\InvalidRequestException $e) {
                    CLI::write("  ❌ Price not found: " . $e->getMessage(), 'red');
                }
            }

            // Overall analysis
            CLI::write("\n--- Overall Duplicate Analysis ---", 'yellow');

            // Get all active subscriptions and group by customer
            $all_active_subs = $stripe->subscriptions->all([
                'status' => 'active',
                'limit' => 100
            ]);

            $customer_subscription_counts = [];
            foreach ($all_active_subs->data as $sub) {
                $customer_subscription_counts[$sub->customer][] = $sub;
            }

            $total_duplicates = 0;
            foreach ($customer_subscription_counts as $customer_id => $subs) {
                if (count($subs) > 1) {
                    $total_duplicates++;
                    CLI::write("Customer {$customer_id} has " . count($subs) . " active subscriptions:");
                    foreach ($subs as $sub) {
                        $price_id = $sub->items->data[0]->price->id ?? 'unknown';
                        CLI::write("  - {$sub->id} (price: {$price_id}, created: " . date('Y-m-d H:i:s', $sub->created) . ")");
                    }
                }
            }

            CLI::write("\nTotal customers with duplicate subscriptions: {$total_duplicates}", $total_duplicates > 0 ? 'red' : 'green');

        } catch (\Exception $e) {
            CLI::error("Error during analysis: " . $e->getMessage());
        }
    }
}
