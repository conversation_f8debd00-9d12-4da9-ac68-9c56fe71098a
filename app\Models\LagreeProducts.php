<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class LagreeProducts extends Model
{
    // protected $DBGroup = 'lfproducts'; // CRM database
    protected $table = 'products_shopify';
	// protected $allowedFields = ['title', 'slug', 'audio'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    // public function all_studios(){
    //     $data = $this->query("SELECT *
    //                         FROM lag_studios
    //                         ORDER BY id asc
    //                     ")->getResultArray();
    //     return $data;
    // }

    function all_shop_product($collection = 'ALL', $start = 0, $limit = 12, $order = 'price ASC')
	{
        $data = $this->query("SELECT * FROM products_shopify WHERE collection_id = '" . $collection . "' AND status = 'active' AND price > 0 AND title NOT LIKE '%inventory%' ORDER BY " . $order . " LIMIT " . $start . ", " . $limit)->getResultArray();

        return $data;
	}

    function single_product_slug($slug = NULL)
	{

        $result = $this->where(["handle" => $slug, "collection_id" => "ALL", "status" => "active"])->first();
        if($result == NULL){
            $result = $this->where(["product_id" => $slug, "collection_id" => "ALL", "status" => "active"])->first();
        }

        return $result;
	}

	protected function prepare_data(array $data)
	{
		return $data;
	}
}