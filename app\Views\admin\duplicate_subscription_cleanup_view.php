<?= $this->extend('admin/templates/layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Duplicate Subscription Cleanup Tool</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button id="find-duplicates" class="btn btn-primary">Find Duplicate Subscriptions</button>
                            <button id="generate-report" class="btn btn-info ml-2">Generate Report</button>
                        </div>
                    </div>
                    
                    <div id="loading" class="text-center" style="display: none;">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>Scanning for duplicate subscriptions...</p>
                    </div>
                    
                    <div id="results" style="display: none;">
                        <div class="alert alert-info">
                            <strong>Scan Results:</strong>
                            <ul id="summary-list"></ul>
                        </div>
                        
                        <div id="duplicates-table-container">
                            <table class="table table-striped" id="duplicates-table">
                                <thead>
                                    <tr>
                                        <th>Email</th>
                                        <th>Customer ID</th>
                                        <th>Subscription Count</th>
                                        <th>Subscriptions</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="duplicates-tbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for subscription details -->
<div class="modal fade" id="subscriptionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Subscription Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="subscription-details">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="cleanup-btn">Cleanup Duplicates</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentCustomerId = null;
    
    $('#find-duplicates').click(function() {
        findDuplicates();
    });
    
    $('#generate-report').click(function() {
        generateReport();
    });
    
    function findDuplicates() {
        $('#loading').show();
        $('#results').hide();
        
        $.ajax({
            url: '<?= base_url('admin/duplicate_subscription_cleanup/find_duplicates') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                $('#loading').hide();
                
                if (response.success) {
                    displayResults(response);
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                $('#loading').hide();
                alert('An error occurred while scanning for duplicates.');
            }
        });
    }
    
    function displayResults(response) {
        $('#summary-list').html(
            '<li>Total customers checked: ' + response.total_checked + '</li>' +
            '<li>Customers with duplicates: ' + response.total_duplicates + '</li>'
        );
        
        let tbody = $('#duplicates-tbody');
        tbody.empty();
        
        response.duplicates.forEach(function(duplicate) {
            let subscriptionsList = duplicate.subscriptions.map(function(sub) {
                return sub.id + ' (' + sub.plan_interval + ', created: ' + sub.created + ')';
            }).join('<br>');
            
            let row = '<tr>' +
                '<td>' + duplicate.email + '</td>' +
                '<td>' + duplicate.stripe_customer + '</td>' +
                '<td>' + duplicate.subscription_count + '</td>' +
                '<td>' + subscriptionsList + '</td>' +
                '<td>' +
                    '<button class="btn btn-sm btn-info view-details" data-customer="' + duplicate.stripe_customer + '">View Details</button>' +
                '</td>' +
            '</tr>';
            
            tbody.append(row);
        });
        
        $('#results').show();
    }
    
    $(document).on('click', '.view-details', function() {
        currentCustomerId = $(this).data('customer');
        loadSubscriptionDetails(currentCustomerId);
    });
    
    function loadSubscriptionDetails(customerId) {
        $.ajax({
            url: '<?= base_url('admin/duplicate_subscription_cleanup/get_customer_subscriptions') ?>',
            method: 'GET',
            data: { customer_id: customerId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    displaySubscriptionDetails(response.subscriptions);
                    $('#subscriptionModal').modal('show');
                } else {
                    alert('Error: ' + response.message);
                }
            }
        });
    }
    
    function displaySubscriptionDetails(subscriptions) {
        let html = '<div class="table-responsive">' +
            '<table class="table table-sm">' +
            '<thead>' +
                '<tr>' +
                    '<th>Select</th>' +
                    '<th>Subscription ID</th>' +
                    '<th>Status</th>' +
                    '<th>Created</th>' +
                    '<th>Current Period</th>' +
                    '<th>Plan</th>' +
                    '<th>Amount</th>' +
                '</tr>' +
            '</thead>' +
            '<tbody>';
        
        subscriptions.forEach(function(sub, index) {
            html += '<tr>' +
                '<td><input type="radio" name="keep_subscription" value="' + sub.id + '" ' + (index === 0 ? 'checked' : '') + '></td>' +
                '<td>' + sub.id + '</td>' +
                '<td>' + sub.status + '</td>' +
                '<td>' + sub.created + '</td>' +
                '<td>' + sub.current_period_start + ' to ' + sub.current_period_end + '</td>' +
                '<td>' + sub.plan_interval + '</td>' +
                '<td>$' + sub.plan_amount + '</td>' +
            '</tr>';
        });
        
        html += '</tbody></table></div>' +
            '<p class="text-muted"><strong>Instructions:</strong> Select the subscription you want to keep. All other subscriptions will be cancelled.</p>';
        
        $('#subscription-details').html(html);
    }
    
    $('#cleanup-btn').click(function() {
        let keepSubscription = $('input[name="keep_subscription"]:checked').val();
        
        if (!keepSubscription) {
            alert('Please select a subscription to keep.');
            return;
        }
        
        if (!confirm('Are you sure you want to cancel all other subscriptions for this customer? This action cannot be undone.')) {
            return;
        }
        
        $.ajax({
            url: '<?= base_url('admin/duplicate_subscription_cleanup/cleanup_duplicates') ?>',
            method: 'POST',
            data: {
                customer_id: currentCustomerId,
                keep_subscription: keepSubscription
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('Success: ' + response.message);
                    $('#subscriptionModal').modal('hide');
                    findDuplicates(); // Refresh the results
                } else {
                    alert('Error: ' + response.message);
                }
            }
        });
    });
    
    function generateReport() {
        $.ajax({
            url: '<?= base_url('admin/duplicate_subscription_cleanup/generate_report') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Create downloadable report
                    let reportData = JSON.stringify(response.report, null, 2);
                    let blob = new Blob([reportData], { type: 'application/json' });
                    let url = window.URL.createObjectURL(blob);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = 'duplicate_subscriptions_report_' + new Date().toISOString().slice(0, 10) + '.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                } else {
                    alert('Error generating report: ' + response.message);
                }
            }
        });
    }
});
</script>

<?= $this->endSection() ?>
