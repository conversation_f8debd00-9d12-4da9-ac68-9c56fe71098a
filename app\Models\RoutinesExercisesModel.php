<?php namespace App\Models;

use CodeIgniter\Model;

class RoutinesExercisesModel extends Model
{
    protected $table = 'routines_selected_exercises';
	protected $allowedFields = ['routine_id', 'routine_selected_exercises', 'date', 'orientation', 'duration', 'springs', 'springs_count', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}