<?php namespace App\Models;

use CodeIgniter\Model;

class ClassesModel extends Model
{
    protected $table = 'classes';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'video_preview', 'body_parts', 'video', 'video_thumb', 'video_encrypted_path', 'duration', 'content', 'teacher', 'difficulty', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'notification_sent', 'type', 'reason', 'audio', 'teacher_saved', 'routine_duration','micro_friendly', 'based_on_lagree', 'published_at', 'scheduled_title'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
        // 'content'     => 'required',
        // 'video'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        // 'body_parts'     => 'required',
        // 'accessories'     => 'required',
        // 'springs'     => 'required',
        // 'teacher'     => 'required',
    ];
    protected $validationMessages = [
        'slug' => [
            'required'  => 'The Page URL field is required.',
            'is_unique' => 'The Page URL field must be unique!'
        ]
    ];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_classes($start = 0, $limit = 10, $search_term = "0", $order = "classes.created_at DESC", $status = "0,1"){
        $status_sql = ($status != NULL) ? " AND classes.status IN (" . $status . ")" : " AND classes.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            video_state.video_time as video_state,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_classes,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_classes_search($start = 0, $limit = 10, $search_term = "0"){
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(classes.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.id,classes.image,classes.title,classes.slug,classes.duration,classes.video_thumb,
                            IF((SELECT count(id) FROM classes_rate WHERE class_id = classes.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                            (SELECT count(rate > 3) as rate FROM classes_rate WHERE class_id = classes.id GROUP BY class_id) as likeCount,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            -- video_state.video_time as video_state,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS machines
                            FROM classes
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            -- LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            AND classes.status = 0
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY classes.created_at DESC
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_lagree_classes($start = 0, $limit = 10, $search_term = "0", $order = "classes.created_at DESC", $status = "0"){
        $status_sql = ($status != NULL) ? " AND classes.status IN (" . $status . ")" : " AND classes.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function(&$word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_classes,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_buy_rent_classes($start = 0, $limit = 10, $search_term = "0", $order = "classes.created_at DESC", $status = "0"){
        $status_sql = ($status != NULL) ? " AND classes.status IN (" . $status . ")" : " AND classes.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function(&$word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_classes,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 1
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function subscribers_active_classes($teacher_id){
        // $TeachersModel = model('TeachersModel');
        // $teacher = $TeachersModel->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $user_email . "'")->getFirstRow('array');

        if(isset($teacher_id)){
            $data = $this->query("SELECT classes.*,
                                COALESCE(ss.max_sale,0) AS sales,
                                COALESCE(rr.max_rent,0) AS rents,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                COALESCE(s.sum_price,0) AS classEarned,
                                CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                                difficulty.title as diff,
                                teachers.slug  as teach_slug,
                                teachers.id  as teach_id,
                                video_state.video_time as video_state,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as max_rent FROM subscribers_classes WHERE purchase_type = 'rent' GROUP BY class_id) rr on rr.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, count(*) as max_sale FROM subscribers_classes WHERE purchase_type IS NULL GROUP BY class_id) ss on ss.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes WHERE subscribers_classes.seller_id = " . $teacher_id . " GROUP BY class_id) s on s.class_id = classes.id
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                                LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                                LEFT JOIN difficulty on difficulty.id = classes.difficulty
                                LEFT JOIN teachers on teachers.id = classes.teacher
                                LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 0
                                AND classes.type = 1
                                AND classes.teacher = " . $teacher_id ."
                                GROUP BY classes.id
                                ORDER BY classes.created_at DESC
                            ")->getResultArray();
        }else{
            $data = [];
        }
        return $data;
    }

    public function subscribers_rejected_classes($teacher_id){
        // $TeachersModel = model('TeachersModel');
        // $teacher = $TeachersModel->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $user_email . "'")->getFirstRow('array');

        if(isset($teacher_id)){
            $data = $this->query("SELECT classes.*,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                COALESCE(s.sum_price,0) AS classEarned,
                                CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                                difficulty.title as diff,
                                teachers.slug  as teach_slug,
                                teachers.id  as teach_id,
                                video_state.video_time as video_state,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes WHERE subscribers_classes.seller_id = " . $teacher_id . " GROUP BY class_id) s on s.class_id = classes.id
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                                LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN difficulty on difficulty.id = classes.difficulty
                                LEFT JOIN teachers on teachers.id = classes.teacher
                                LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 3
                                AND classes.type = 1
                                AND classes.teacher = " . $teacher_id ."
                                GROUP BY classes.id
                                ORDER BY classes.created_at DESC
                            ")->getResultArray();
        }else{
            $data = [];
        }
        return $data;
    }

    public function subscribers_pending_classes($teacher_id = 0){
        // $TeachersModel = model('TeachersModel');
        // $teacher = $TeachersModel->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $user_email . "'")->getFirstRow('array');

        if($teacher_id != 0){
            $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 1
                            AND classes.status IN (2,3)
                            AND classes.teacher = " . $teacher_id ."
                            GROUP BY classes.id
                            ORDER BY classes.created_at DESC
                        ")->getResultArray();
        }else{
            $data = [];
        }
        return $data;
    }

    public function all_pending($start = 0, $limit = 10, $search_term = "0", $order = "classes.created_at DESC"){
        $admin = session('admin') ? ' AND classes.status = 2 ' : '';
        // $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function(&$word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            " . $admin . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                        ")->getResultArray();
        return $data;
    }

    public function all_buy_rent_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 1
                            AND classes.teacher != " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_micro_classes($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                                ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes_machine.class_machine = 1
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function mini_mini_pro_classes($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            IF(subscribers_classes.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                SELECT * FROM (
                                    SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                                ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 0
                            AND classes_machine.class_machine IN (3,4)
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function mini_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 0
                            AND classes_machine.class_machine = 3
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();

        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function mini_pro_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 0
                            AND classes_machine.class_machine = 4
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();

        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function all_micro_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 0
                            AND classes_machine.class_machine = 1
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();
            
            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function all_micro_pro_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.type = 0
                            AND classes_machine.class_machine = 14
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();
            
            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function all_mega_classes($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes_machine.class_machine = 2
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_mega_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            AND classes.status = 0
                            AND classes_machine.class_machine = 2
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function all_mega_pro_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            AND classes.status = 0
                            AND classes_machine.class_machine = 13
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function evo_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            AND classes.status = 0
                            AND classes_machine.class_machine IN (10,11)
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function abwheel_classes_home($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.type = 0
                            AND classes.status = 0
                            AND classes_machine.class_machine = 5
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        foreach($data as $key => $single){
            $purchased = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)")->getRowArray();
            $rented = $this->query("SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()")->getRowArray();
            $watched = $this->query("SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0))->getRowArray();

            if(!empty($purchased)){ if($single['id'] == $purchased['class_id']){ $data[$key]['purchased'] = 1;}; }else{ $data[$key]['purchased'] = 0;};
            if(!empty($rented)){ if($single['id'] == $rented['class_id']){ $data[$key]['rented'] = 1; }; }else{ $data[$key]['rented'] = 0;};
            if(!empty($watched)){ if($single['id'] == $watched['class_id']){ $data[$key]['watched'] = 1; }; }else{ $data[$key]['watched'] = 0;};
        }

        return $data;
    }

    public function all_mini_classes($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            IF(subscribers_classes.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes_machine.class_machine = 3
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function filter_classes($data){
        $limit_size =  ($data['limit'] != 0) ? " LIMIT " . $data['start'] . ", " . $data['limit'] : "";
		$sql_add = '';
		$join = '';
        $sql_selector = ' ';

		//WHERE
        $real_durations = [
            '0' => 'c.duration < 600',
            '1' => '(c.duration > 600 AND c.duration < 1200)',
            '2' => '(c.duration > 1200 AND c.duration < 1800)',
            '3' => '(c.duration > 1800 AND c.duration < 2400)',
            '4' => '(c.duration > 2400 AND c.duration < 3000)',
            '5' => '(c.duration > 3000 AND c.duration < 3600)',
            '6' => 'c.duration > 3600'
        ];
        if( (isset($data['machine']) AND $data['machine'] != '') OR
            (isset($data['accessories']) AND $data['accessories'] != '') OR
            (isset($data['body_parts']) AND $data['body_parts'] != '') OR
            (isset($data['tempo']) AND $data['tempo'] != '') OR
            (isset($data['teacher']) AND $data['teacher'] != '') OR
            (isset($data['language']) AND $data['language'] != '') OR
            (isset($data['difficulty']) AND $data['difficulty'] != '') OR
            (isset($data['duration']) AND $data['duration'] != '')
        ){
            $sql_add .=  "WHERE c.deleted_at IS NULL AND c.status = 0 AND c.type = 0 ";
            if(isset($data['machine']) AND $data['machine'] != ''){
                $sql_add .= " AND cm.class_machine IN (" . ((isset($data['machine']) AND is_array($data['machine'])) ? implode(',', $data['machine']) : 0) . ") ";
            }
            if(isset($data['accessories']) AND $data['accessories'] != ''){
                $sql_add .= " AND ca.class_accessories IN (" . ((isset($data['accessories']) AND is_array($data['accessories'])) ? implode(',', $data['accessories']) : 0) . ") ";
                $join .= ' LEFT JOIN classes_accessories ca ON ca.class_id = c.id ';
            }
            if(isset($data['body_parts']) AND $data['body_parts'] != ''){
                $sql_add .= " AND cbp.class_body_parts IN (" . ((isset($data['body_parts']) AND !empty($data['body_parts']) AND is_array($data['body_parts'])) ? implode(',', $data['body_parts']) : 0) . ") ";
                $join .= ' LEFT JOIN classes_body_parts cbp ON cbp.class_id = c.id ';
            }
            if(isset($data['tempo']) AND $data['tempo'] != ''){
                $sql_add .= " AND ct.class_tempo IN (" . ((isset($data['tempo']) AND !empty($data['tempo']) AND is_array($data['tempo'])) ? implode(',', $data['tempo']) : 0) . ") ";
                // $join .= ' LEFT JOIN classes_tempo ON classes_tempo.class_id = classes.id ';
            }
            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $sql_add .= " AND t.id IN (" . ((isset($data['teacher']) AND !empty($data['teacher']) AND is_array($data['teacher'])) ? implode(',', $data['teacher']) : 0) . ") ";
            }
            if(isset($data['language']) AND $data['language'] != ''){
                $sql_add .=  " AND c.language IN (" . ((isset($data['language']) AND !empty($data['language']) AND is_array($data['language'])) ? implode(',', $data['language']) : 0) . ")";
                $join .= ' LEFT JOIN languages l on l.id = c.language ';
            }
            if(isset($data['difficulty']) AND $data['difficulty'] != ''){
                $sql_add .=  " AND c.difficulty IN (" . ((isset($data['difficulty']) AND !empty($data['difficulty']) AND is_array($data['difficulty'])) ? implode(',', $data['difficulty']) : 0) . ")";
            }
            if(isset($data['duration']) AND $data['duration'] != ''){
                $sql_add .=  " AND (";
                if(is_array($data['duration'])){
                    foreach($data['duration'] as $key => $value){
                        $sql_add .= $real_durations[$value] . '' . ((count($data['duration']) == ($key+1)) ? '' : ' OR ');
                    }
                    $sql_add .= ") AND c.duration != '' AND c.duration != 'NaN' AND c.duration IS NOT NULL ";
                }else{
                    $sql_add .= $real_durations[$data['duration']] . ")";
                }
            }
        }else{
            $sql_add .=  " WHERE c.deleted_at IS NULL AND c.status = 0 AND c.type = 0 ";
        }
        if($data['order'] == 'countView desc'){
            $join = ' LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = c.id ';
            $sql_selector .= ' COALESCE(x.cnt,0) AS countView, ';
        }else{
            $data['order'] = str_replace('classes', 'c', $data['order']);
        }

        // $sql = "SELECT c.id, c.image, c.video_thumb, c.video_preview, c.status, c.type, c.title, c.slug, c.created_at, 
        //                             CASE 
        //                                 WHEN c.duration REGEXP '^[0-9]+$' THEN c.duration
        //                                 ELSE 0
        //                             END AS duration,
        //                             t.slug as teach_slug,
        //                             IF(cr.user_id IS NOT NULL, 1, 0) as liked,
        //                             cs.likeCount,
        //                             d.title as diff,
        //                             " . $sql_selector . "
        //                             CONCAT(t.firstname, ' ', t.lastname) as teach,
        //                             GROUP_CONCAT(DISTINCT m.title SEPARATOR ', ') AS all_class_machines,
        //                             GROUP_CONCAT(DISTINCT tm.title ORDER BY tm.sort ASC SEPARATOR ', ') AS all_class_tempo,
        //                             vs.video_time as video_state,                                     
        //                             sw.watched
        //                             FROM classes c
        //                             " . $join . "
        //                             LEFT JOIN difficulty d ON d.id = c.difficulty
        //                             LEFT JOIN teachers t ON t.id = c.teacher
        //                             LEFT JOIN classes_machine cm ON cm.class_id = c.id
        //                             LEFT JOIN machines m ON m.id = cm.class_machine
        //                             LEFT JOIN classes_tempo ct ON ct.class_id = c.id
        //                             LEFT JOIN tempo tm ON tm.id = ct.class_tempo
        //                             LEFT JOIN (SELECT class_id, COUNT(rate > 3) as likeCount FROM classes_rate GROUP BY class_id) cs ON cs.class_id = c.id
        //                             LEFT JOIN (SELECT class_id, 1 as watched FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") sw ON sw.class_id = c.id
        //                             LEFT JOIN (SELECT class_id, user_id FROM classes_rate WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") cr ON cr.class_id = c.id
        //                             LEFT JOIN video_state vs ON (vs.video_id = c.slug AND vs.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND vs.video_type = 'classes')
        //                             " . $sql_add . "
        //                             GROUP BY c.id
        //                             ORDER BY " . $data['order'] . "
        //                             " . $limit_size;

        // echo '<pre>';
        // print_r($sql);
        // die();
                                            

        $result = $this->query("SELECT c.id, c.image, c.video_thumb, c.video_preview, c.status, c.type, c.title, c.slug, c.created_at, c.micro_friendly, c.based_on_lagree, 
                                    CASE 
                                        WHEN c.duration REGEXP '^[0-9]+$' THEN c.duration
                                        ELSE 0
                                    END AS duration,
                                    t.slug as teach_slug,
                                    IF(cr.user_id IS NOT NULL, 1, 0) as liked,
                                    cs.likeCount,
                                    d.title as diff,
                                    " . $sql_selector . "
                                    CONCAT(t.firstname, ' ', t.lastname) as teach,
                                    GROUP_CONCAT(DISTINCT m.title SEPARATOR ', ') AS all_class_machines,
                                    GROUP_CONCAT(DISTINCT tm.title ORDER BY tm.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                    vs.video_time as video_state,                                     
                                    sw.watched
                                    FROM classes c
                                    " . $join . "
                                    LEFT JOIN difficulty d ON d.id = c.difficulty
                                    LEFT JOIN teachers t ON t.id = c.teacher
                                    LEFT JOIN classes_machine cm ON cm.class_id = c.id
                                    LEFT JOIN machines m ON m.id = cm.class_machine
                                    LEFT JOIN classes_tempo ct ON ct.class_id = c.id
                                    LEFT JOIN tempo tm ON tm.id = ct.class_tempo
                                    LEFT JOIN (SELECT class_id, COUNT(rate > 3) as likeCount FROM classes_rate GROUP BY class_id) cs ON cs.class_id = c.id
                                    LEFT JOIN (SELECT class_id, 1 as watched FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") sw ON sw.class_id = c.id
                                    LEFT JOIN (SELECT class_id, user_id FROM classes_rate WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") cr ON cr.class_id = c.id
                                    LEFT JOIN video_state vs ON (vs.video_id = c.slug AND vs.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND vs.video_type = 'classes')
                                    " . $sql_add . "
                                    GROUP BY c.id
                                    ORDER BY " . $data['order'] . "
                                    " . $limit_size . "
                                ")->getResultArray();


        // $result = "SELECT classes.*,
        //                             IF((SELECT count(id) FROM classes_rate WHERE class_id = classes.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
        //                             (SELECT count(rate > 3) as rate FROM classes_rate WHERE class_id = classes.id GROUP BY class_id) as likeCount,
        //                             difficulty.title as diff,
        //                             teachers.slug  as teach_slug,
        //                             -- video_state.video_time as video_state,
        //                             " . $sql_selector . "
        //                             CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                             GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
        //                             GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
        //                             IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
        //                                     ) as classes_purchased
        //                             ), 1, 0) as purchased,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
        //                                     ) as classes_rented
        //                             ), 1, 0) as rented,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
        //                                     ) as classes_watched
        //                             ), 1, 0) as watched
        //                             FROM classes
        //                             " . $join . "
        //                             LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
        //                             LEFT JOIN machines ON machines.id = classes_machine.class_machine
        //                             LEFT JOIN subscribers_classes ON subscribers_classes.class_id = classes.id
        //                             LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
        //                             LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
        //                             LEFT JOIN teachers on teachers.id = classes.teacher
        //                             LEFT JOIN difficulty on difficulty.id = classes.difficulty
        //                             -- LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
        //                             " . $sql_add . "
        //                             GROUP BY classes.id
        //                             ORDER BY " . $data['order'] . "
        //                             " . $limit_size . "
        //                         ";
        //                         echo '<pre>';
        //                         print_r($result);
        //                         die();
                                
        return $result;
    }

    public function filter_buy_rent($data){

        $limit_size =  ($data['limit'] != 0) ? " LIMIT " . $data['start'] . ", " . $data['limit'] : "";
		$sql_add = '';

		//WHERE
        $real_durations = [
            '0' => 'classes.duration < 600',
            '1' => '(classes.duration > 600 AND classes.duration < 1500)',
            '2' => 'classes.duration > 1500'
        ];
        if( (isset($data['machine']) AND $data['machine'] != '') OR
            (isset($data['accessories']) AND $data['accessories'] != '') OR
            (isset($data['body_parts']) AND $data['body_parts'] != '') OR
            (isset($data['tempo']) AND $data['tempo'] != '') OR
            (isset($data['teacher']) AND $data['teacher'] != '') OR
            (isset($data['language']) AND $data['language'] != '') OR
            (isset($data['difficulty']) AND $data['difficulty'] != '') OR
            (isset($data['duration']) AND $data['duration'] != '')
        ){
            $sql_add .=  "WHERE classes.deleted_at IS NULL AND classes.status = 0 AND classes.type = 1 ";
            if(isset($data['machine']) AND $data['machine'] != ''){
                $sql_add .= " AND classes_machine.class_machine IN (" . ((isset($data['machine']) AND is_array($data['machine'])) ? implode(',', $data['machine']) : 0) . ") ";
            }
            if(isset($data['accessories']) AND $data['accessories'] != ''){
                $sql_add .= " AND classes_accessories.class_accessories IN (" . ((isset($data['accessories']) AND is_array($data['accessories'])) ? implode(',', $data['accessories']) : 0) . ") ";
            }
            if(isset($data['body_parts']) AND $data['body_parts'] != ''){
                $sql_add .= " AND classes_body_parts.class_body_parts IN (" . ((isset($data['body_parts']) AND !empty($data['body_parts']) AND is_array($data['body_parts'])) ? implode(',', $data['body_parts']) : 0) . ") ";
            }
            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $sql_add .= " AND teachers.id IN (" . ((isset($data['teacher']) AND !empty($data['teacher']) AND is_array($data['teacher'])) ? implode(',', $data['teacher']) : 0) . ") ";
            }
            if(isset($data['language']) AND $data['language'] != ''){
                $sql_add .=  " AND classes.language IN (" . ((isset($data['language']) AND !empty($data['language']) AND is_array($data['language'])) ? implode(',', $data['language']) : 0) . ")";
            }
            if(isset($data['difficulty']) AND $data['difficulty'] != ''){
                $sql_add .=  " AND classes.difficulty IN (" . ((isset($data['difficulty']) AND !empty($data['difficulty']) AND is_array($data['difficulty'])) ? implode(',', $data['difficulty']) : 0) . ")";
            }
            if(isset($data['duration']) AND $data['duration'] != ''){
                $sql_add .=  " AND (";
                if(is_array($data['duration'])){
                    foreach($data['duration'] as $key => $value){
                        $sql_add .= $real_durations[$value] . '' . ((count($data['duration']) == ($key+1)) ? '' : ' OR ');
                    }
                    $sql_add .= ") AND classes.duration != '' AND classes.duration != 'NaN' AND classes.duration IS NOT NULL";
                }else{
                    $sql_add .= $real_durations[$data['duration']] . ")";
                }
            }
        }else{
            $sql_add .=  "WHERE classes.deleted_at IS NULL AND classes.status = 0 AND classes.type = 1 ";
        }
        $result = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug,
                                    DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                                    COALESCE(x.cnt,0) AS countView,
                                    COALESCE(y.rate,0) AS classRate,
                                    CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                                    video_state.video_time as video_state,
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                    GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                    IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                                    IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                                    IF(classes.id IN (
                                            SELECT * FROM (
                                                    SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                            ) as classes_purchased
                                    ), 1, 0) as purchased,
                                    IF(classes.id IN (
                                            SELECT * FROM (
                                                    SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                            ) as classes_rented
                                    ), 1, 0) as rented,
                                    IF(classes.id IN (
                                            SELECT * FROM (
                                                    SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                            ) as classes_watched
                                    ), 1, 0) as watched
                                    FROM classes
                                    LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                    LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                    LEFT JOIN classes_accessories ON classes_accessories.class_id = classes.id
                                    LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                    LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                                    LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                                    LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                                    LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                    LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                                    LEFT JOIN subscribers_classes ON subscribers_classes.class_id = classes.id
                                    LEFT JOIN difficulty on difficulty.id = classes.difficulty
                                    LEFT JOIN languages on languages.id = classes.language
                                    LEFT JOIN teachers on teachers.id = classes.teacher
                                    LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                                    " . $sql_add . "
                                    GROUP BY classes.id
                                    ORDER BY " . $data['order'] . "
                                    " . $limit_size . "
                                ")->getResultArray();
        return $result;
    }

    public function filter_classes_admin($start = 0, $limit = 10, $search_term = NULL, $order = "classes.created_at DESC", $filter_data = NULL){

        $sql_add = '';

		//WHERE
        // echo "<pre>";
        // var_dump($filter_data);
        // die();
        // $search =  ($search_term != NULL AND $search_term != "0") ? "AND classes.title LIKE '%$search_term%'" : "";

        if($search_term != "0"){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        $sql_add .=  "WHERE classes.deleted_at IS NULL \nAND classes.status = 0 \nAND classes.type = 0 ";

        if($filter_data != NULL){
            $real_durations = [
                '0' => 'classes.duration < 600',
                '1' => '(classes.duration > 600 AND classes.duration < 1200)',
                '2' => '(classes.duration > 1200 AND classes.duration < 1800)',
                '3' => '(classes.duration > 1800 AND classes.duration < 2400)',
                '4' => '(classes.duration > 2400 AND classes.duration < 3000)',
                '5' => '(classes.duration > 3000 AND classes.duration < 3600)',
                '6' => 'classes.duration > 3600'
            ];
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= " \nAND classes_machine.class_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= " \nAND classes_body_parts.class_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= " \nAND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  " \nAND classes.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  " \nAND classes.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            $durations = json_decode($filter_data['duration'][0], TRUE);
            if(is_array($durations) AND count($durations) > 0){
                $sql_add .=  " \nAND (";
                foreach($durations as $key => $value){
                    $sql_add .= $real_durations[$value] . '' . ((count($durations) == ($key+1)) ? '' : ' OR ');
                }
                $sql_add .= ") \nAND classes.duration != '' \nAND classes.duration != 'NaN' \nAND classes.duration IS NOT NULL ";
            }

            // if(isset($filter_data['duration']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['duration']))) != ""){
            //     $sql_add .=  " AND (classes.duration ";
            //     $sql_add .=  count($filter_data['duration']) > 0 ? str_replace(['"',"'"], "", str_replace("[", "", str_replace("]","", implode(' OR classes.duration ', json_decode($filter_data['duration'][0]))))) . ")" : "< 0)";
            // }
        }

        // $result_query = "SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at,
        //                     COALESCE(x.cnt,0) AS countView,
        //                     COALESCE(y.rate,0) AS classRate,
        //                     -- COALESCE(s.sum_price,0) AS classEarned,
        //                     CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                     difficulty.title as diff,
        //                     teachers.slug  as teach_slug,
        //                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
        //                     FROM classes
        //                     LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
        //                     LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
        //                     -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
        //                     LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
        //                     LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
        //                     LEFT JOIN machines ON machines.id = classes_machine.class_machine
        //                     LEFT JOIN difficulty on difficulty.id = classes.difficulty
        //                     LEFT JOIN languages on languages.id = classes.language
        //                     LEFT JOIN teachers on teachers.id = classes.teacher
        //                     " . $sql_add . "
        //                     " . $search . "
        //                     GROUP BY classes.id
        //                     ORDER BY " . $order . "
        //                     LIMIT " . $start . ", " . $limit . "
        //                 ";
        // echo '<pre>';
        // print_r($filter_data);
        // echo '<br>';
        // echo '<br>';
        // echo '<br>';
        // print_r($result_query);
        // die();
                        

        $result = $this->query("SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at, classes.updated_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            -- COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }

    public function filter_classes_no_admin($start = 0, $limit = 10, $search_term = NULL, $order = "classes.created_at DESC", $filter_data = NULL){

        $sql_add = '';

		//WHERE
        // echo "<pre>";
        // var_dump($filter_data);
        // die();
        $search =  ($search_term != NULL AND $search_term != "0") ? "AND classes.title LIKE '%$search_term%'" : "";

        $sql_add .=  "WHERE classes.deleted_at IS NULL AND classes.status IN (0,1) AND classes.type = 0 ";

        if($filter_data != NULL){
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= " AND classes_machine.class_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= " AND classes_body_parts.class_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= " AND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  " AND classes.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  " AND classes.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            if(isset($filter_data['duration']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['duration']))) != ""){
                $sql_add .=  " AND (classes.duration ";
                $sql_add .=  count($filter_data['duration']) > 0 ? str_replace(['"',"'"], "", str_replace("[", "", str_replace("]","", implode(' OR classes.duration ', json_decode($filter_data['duration'][0]))))) . ")" : "< 0)";
            }
        }

        $result_query = "SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.created_at, classes.published_at, classes.scheduled_title, classes.updated_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            -- COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            AND teacher = " . session('admin') ."
                            GROUP BY classes.id
                            ORDER BY status DESC
                            LIMIT " . $start . ", " . $limit . "
                        ";

        $result = $this->query("SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at, classes.updated_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            -- COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            AND teacher = " . session('admin') ."
                            GROUP BY classes.id
                            ORDER BY status DESC
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }

    public function filter_scheduled_recordings_admin($start = 0, $limit = 10, $search_term = NULL, $order = "classes.created_at DESC", $filter_data = NULL, $show_todays = NULL, $teacher_id = NULL){

        $sql_add = '';

		//WHERE
        // echo "<pre>";
        // var_dump($filter_data);
        // die();
        // $search =  ($search_term != NULL AND $search_term != "0") ? "AND classes.title LIKE '%$search_term%'" : "";

        if($search_term != "0"){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        $sql_add .=  "WHERE classes.deleted_at IS NULL \nAND classes.status = 1 \nAND classes.type = 0 ";

        if($show_todays != NULL){
            $sql_add .= " \nAND classes.title LIKE CONCAT('%', DATE_FORMAT(CURDATE(), '%m/%d/%Y'), '%') ";
        }

        if($teacher_id != NULL AND $teacher_id != 0){
            $sql_add .= " \nAND classes.teacher = " . $teacher_id . " ";
        }

        if($filter_data != NULL){
            $real_durations = [
                '0' => 'classes.duration < 600',
                '1' => '(classes.duration > 600 AND classes.duration < 1200)',
                '2' => '(classes.duration > 1200 AND classes.duration < 1800)',
                '3' => '(classes.duration > 1800 AND classes.duration < 2400)',
                '4' => '(classes.duration > 2400 AND classes.duration < 3000)',
                '5' => '(classes.duration > 3000 AND classes.duration < 3600)',
                '6' => 'classes.duration > 3600'
            ];
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= " \nAND classes_machine.class_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= " \nAND classes_body_parts.class_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= " \nAND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  " \nAND classes.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  " \nAND classes.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            $durations = json_decode($filter_data['duration'][0], TRUE);
            if(is_array($durations) AND count($durations) > 0){
                $sql_add .=  " \nAND (";
                foreach($durations as $key => $value){
                    $sql_add .= $real_durations[$value] . '' . ((count($durations) == ($key+1)) ? '' : ' OR ');
                }
                $sql_add .= ") \nAND classes.duration != '' \nAND classes.duration != 'NaN' \nAND classes.duration IS NOT NULL ";
            }

            // if(isset($filter_data['duration']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['duration']))) != ""){
            //     $sql_add .=  " AND (classes.duration ";
            //     $sql_add .=  count($filter_data['duration']) > 0 ? str_replace(['"',"'"], "", str_replace("[", "", str_replace("]","", implode(' OR classes.duration ', json_decode($filter_data['duration'][0]))))) . ")" : "< 0)";
            // }
        }

        // $result_query = "SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at,
        //                     COALESCE(x.cnt,0) AS countView,
        //                     COALESCE(y.rate,0) AS classRate,
        //                     -- COALESCE(s.sum_price,0) AS classEarned,
        //                     CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                     difficulty.title as diff,
        //                     teachers.slug  as teach_slug,
        //                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
        //                     FROM classes
        //                     LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
        //                     LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
        //                     -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
        //                     LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
        //                     LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
        //                     LEFT JOIN machines ON machines.id = classes_machine.class_machine
        //                     LEFT JOIN difficulty on difficulty.id = classes.difficulty
        //                     LEFT JOIN languages on languages.id = classes.language
        //                     LEFT JOIN teachers on teachers.id = classes.teacher
        //                     " . $sql_add . "
        //                     " . $search . "
        //                     GROUP BY classes.id
        //                     ORDER BY " . $order . "
        //                     LIMIT " . $start . ", " . $limit . "
        //                 ";
        // echo '<pre>';
        // print_r($filter_data);
        // echo '<br>';
        // echo '<br>';
        // echo '<br>';
        // print_r($result_query);
        // die();
                        

        $result = $this->query("SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at, classes.updated_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            -- COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }

    public function filter_scheduled_recordings_no_admin($start = 0, $limit = 10, $search_term = NULL, $order = "classes.created_at DESC", $filter_data = NULL, $show_todays = NULL, $teacher_id = NULL){

        $sql_add = '';

		//WHERE
        // echo "<pre>";
        // var_dump($filter_data);
        // die();
        $search =  ($search_term != NULL AND $search_term != "0") ? "AND classes.title LIKE '%$search_term%'" : "";

        $sql_add .=  "WHERE classes.deleted_at IS NULL AND classes.status = 1 AND classes.type = 0 ";

        if($show_todays != NULL){
            $sql_add .= " \nAND classes.title LIKE CONCAT('%', DATE_FORMAT(CURDATE(), '%m/%d/%Y'), '%') ";
        }

        if($teacher_id != NULL){
            $sql_add .= " \nAND classes.teacher = " . $teacher_id . " ";
        }

        if($filter_data != NULL){
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= " AND classes_machine.class_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= " AND classes_body_parts.class_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= " AND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  " AND classes.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  " AND classes.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            if(isset($filter_data['duration']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['duration']))) != ""){
                $sql_add .=  " AND (classes.duration ";
                $sql_add .=  count($filter_data['duration']) > 0 ? str_replace(['"',"'"], "", str_replace("[", "", str_replace("]","", implode(' OR classes.duration ', json_decode($filter_data['duration'][0]))))) . ")" : "< 0)";
            }
        }

        // $result_query = "SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.created_at, classes.published_at, classes.scheduled_title,
        //                     COALESCE(x.cnt,0) AS countView,
        //                     COALESCE(y.rate,0) AS classRate,
        //                     -- COALESCE(s.sum_price,0) AS classEarned,
        //                     CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                     difficulty.title as diff,
        //                     teachers.slug  as teach_slug,
        //                     teachers.id  as teach_id,
        //                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
        //                     FROM classes
        //                     LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
        //                     LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
        //                     -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
        //                     LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
        //                     LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
        //                     LEFT JOIN machines ON machines.id = classes_machine.class_machine
        //                     LEFT JOIN difficulty on difficulty.id = classes.difficulty
        //                     LEFT JOIN languages on languages.id = classes.language
        //                     LEFT JOIN teachers on teachers.id = classes.teacher
        //                     " . $sql_add . "
        //                     " . $search . "
        //                     AND teacher = " . session('admin') ."
        //                     GROUP BY classes.id
        //                     ORDER BY " . $order . "
        //                     LIMIT " . $start . ", " . $limit . "
        //                 ";

        $result = $this->query("SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at, classes.updated_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            -- COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            -- LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            AND teacher = " . session('admin') ."
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }

    public function filter_buy_rent_admin($start = 0, $limit = 10, $search_term = NULL, $order = "classes.created_at DESC", $filter_data = NULL){

        $sql_add = '';

        //WHERE
        // echo "<pre>";
        // var_dump($filter_data);
        // die();
        $search =  ($search_term != NULL AND $search_term != "0") ? "AND classes.title LIKE '%$search_term%'" : "";

        $sql_add .=  "WHERE classes.deleted_at IS NULL AND classes.status IN (0,1) AND classes.type = 1 ";

        if($filter_data != NULL){
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= " AND classes_machine.class_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= " AND classes_body_parts.class_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= " AND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  " AND classes.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  " AND classes.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            if(isset($filter_data['duration']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['duration']))) != ""){
                $sql_add .=  " AND (classes.duration ";
                $sql_add .=  count($filter_data['duration']) > 0 ? str_replace(['"',"'"], "", str_replace("[", "", str_replace("]","", implode(' OR classes.duration ', json_decode($filter_data['duration'][0]))))) . ")" : "< 0)";
            }
        }

        $result_query = "SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ";

        $result = $this->query("SELECT classes.id, classes.image, classes.video_thumb, classes.status, classes.type, classes.title, classes.duration, classes.published_at, classes.scheduled_title, classes.created_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, SUM(seller_earning) as sum_price FROM subscribers_classes GROUP BY class_id) s on s.class_id = classes.id
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN languages on languages.id = classes.language
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }

    public function similar_classes($start = 0, $limit = 0, $search_term = NULL, $order = "classes.updated_at desc", $id = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT classes.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate, difficulty.title as diff,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.id != " . $id . "
                            " . $search . "
                            GROUP BY classes.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug, teachers.email as teach_email, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_full_name, teachers.image  as teach_image,
                                COALESCE(x.cnt,0) AS countView,
                                IF((SELECT count(id) FROM subscribers_favs WHERE class_id = classes.id AND type = 'classes' AND subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as added_to_watch_later,
                                IF((SELECT count(id) FROM classes_rate WHERE class_id = classes.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                (SELECT count(rate > 3) as rate FROM classes_rate WHERE class_id = classes.id GROUP BY class_id) as likeCount,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                teachers.id  as teach_id,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT body_parts.id SEPARATOR ', ') AS all_body_parts_ids,
                                GROUP_CONCAT(DISTINCT accessories.id SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                GROUP_CONCAT(DISTINCT tensions.title ORDER BY tensions.sort ASC SEPARATOR ', ') AS all_class_tensions,
                                GROUP_CONCAT(DISTINCT machines.id SEPARATOR ', ') AS all_class_machines
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x ON x.class_id = classes.id
                                LEFT JOIN classes_accessories ON  classes_accessories.class_id = classes.id
                                LEFT JOIN accessories ON accessories.id = classes_accessories.class_accessories
                                LEFT JOIN classes_tensions ON  classes_tensions.class_id = classes.id
                                LEFT JOIN tensions ON tensions.id = classes_tensions.class_tensions
                                LEFT JOIN classes_springs ON  classes_springs.class_id = classes.id
                                LEFT JOIN springs ON springs.id = classes_springs.class_springs
                                LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                                LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                                LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                                LEFT JOIN body_parts ON body_parts.id = classes_body_parts.class_body_parts
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 0
                                AND classes.slug = '" . $slug . "'
                            ")->getRowArray();

        if(!empty($data['all_class_accessories'])){
            $data['accessories'] = $this->query("SELECT * FROM accessories WHERE id IN (" . $data['all_class_accessories'] . ")")->getResultArray();
            $data['count_accessories'] = count(explode(',', $data['all_class_accessories']));
        }else{
            $data['accessories'] = [];
            $data['count_accessories'] = 0;
        }
        if(!empty($data['all_class_machines'])){
            $data['machines'] = $this->query("SELECT * FROM machines WHERE id IN (" . $data['all_class_machines'] . ")")->getResultArray();
            $data['count_machines'] = count(explode(',', $data['all_class_machines']));
        }else{
            $data['machines'] = [];
            $data['count_machines'] = 0;
        }
            
        if(!empty($data) AND isset($data['teach_id'])) {
            $data['teacher_studios'] = $this->query("SELECT * FROM studios WHERE user_id = " . (isset($data['teach_id']) ? $data['teach_id'] : 0) . " AND deleted_at IS NULL")->getResultArray();
        }
        return $data;
    }

    public function class_info($id = ''){
        $data = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug, teachers.email as teach_email, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_full_name, teachers.image  as teach_image,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                teachers.id  as teach_id,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT custom_accessories.title SEPARATOR ', ') AS all_custom_class_accessories,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_class_accessories_shopify,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                                GROUP_CONCAT(DISTINCT tensions.title ORDER BY tensions.sort ASC SEPARATOR ', ') AS all_class_tensions,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_class_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x ON x.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y ON y.class_id = classes.id
                                LEFT JOIN classes_accessories ON  classes_accessories.class_id = classes.id
                                LEFT JOIN accessories ON accessories.id = classes_accessories.class_accessories
                                LEFT JOIN classes_custom_accessories ON classes_custom_accessories.class_id = classes.id
                                LEFT JOIN custom_accessories ON custom_accessories.id = classes_custom_accessories.class_custom_accessories
                                LEFT JOIN classes_tensions ON  classes_tensions.class_id = classes.id
                                LEFT JOIN tensions ON tensions.id = classes_tensions.class_tensions
                                LEFT JOIN classes_springs ON  classes_springs.class_id = classes.id
                                LEFT JOIN springs ON springs.id = classes_springs.class_springs
                                LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                                LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                                LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                                LEFT JOIN body_parts ON body_parts.id = classes_body_parts.class_body_parts
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE classes.deleted_at IS NULL
                                AND classes.id = '" . $id . "'
                            ")->getRowArray();
                            
        return $data;
    }

    public function cron(){
        $data = $this->query("SELECT classes.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teacher_name
                                FROM classes
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 0
                                AND classes.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

    function prev_next($id = 0)	{
		$result = array('prev' => NULL, 'next' => NULL,);

		if($id != 0){
            $tmp = $this->query("SELECT * FROM classes WHERE status = 0 AND deleted_at IS NULL ORDER BY created_at desc")->getResultArray();

			if(count($tmp) > 1)
			{
				$total = count($tmp);
				$index_list = array_column($tmp, 'id');
				$index_id = array_search($id, array_column($tmp, 'id'));
				if($index_id !== FALSE){
					if($index_id < $total - 1){
						$result['next'] = $this->where(['id' => $index_list[$index_id + 1]])->first();
					}else{
						$result['next'] = $this->where(['id' => $index_list[0]])->first();
					}

					if($index_id > 0){
						$result['prev'] = $this->where(['id' => $index_list[$index_id - 1]])->first();
					}else{
						$result['prev'] = $this->where(['id' => $index_list[$total - 1]])->first();
					}
				}
			}
		}

        // echo "<pre>";
        // var_dump($result);
        // die();

		return $result;
	}

    public function exercises_for_class($class_id = 0){
        $exercises_model = model('ExercisesModel');
        $data = [];
        if($class_id != 0){
            $res = $exercises_model->query("SELECT * FROM classes_selected_exercises WHERE class_id = " . $class_id . " ORDER BY sort asc")->getResultArray();

            if(!empty($res)){
                foreach($res as $single){
                    if($single['class_selected_exercises'] != 0){
                        $data_check = $exercises_model->query("SELECT exercises.*, 
                                            COALESCE(x.cnt,0) AS countView, 
                                            COALESCE(y.rate,0) AS classRate, 
                                            difficulty.title as diff, 
                                            GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                            GROUP_CONCAT(DISTINCT body_parts.id SEPARATOR ', ') AS all_body_parts_ids,
                                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids,
                                            classes_selected_exercises.id as csc_id, 
                                            classes_selected_exercises.orientation as orientation, 
                                            classes_selected_exercises.duration as custom_duration, 
                                            'exercises_class' AS class, 
                                            SUM(classes_selected_exercises.duration) AS total_duration, 
                                            classes_selected_exercises.sort as sorting
                                            FROM exercises
                                            LEFT OUTER JOIN (SELECT exercise_id, count(*) AS cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) AS rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                                            LEFT OUTER JOIN difficulty ON difficulty.id = exercises.difficulty
                                            LEFT OUTER JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                            LEFT OUTER JOIN machines ON machines.id = exercises_machine.exercise_machine
                                            LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                                            LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                                            LEFT OUTER JOIN classes_selected_exercises ON (classes_selected_exercises.class_selected_exercises = exercises.id AND classes_selected_exercises.class_id = " . $class_id . ")
                                            WHERE exercises.deleted_at IS NULL
                                            AND exercises.status = 0
                                            AND exercises.id = " . $single['class_selected_exercises'] . "
                                            GROUP BY exercises.id
                                            ORDER BY classes_selected_exercises.sort asc
                                        ")->getRowArray();
                        if(!empty($data_check)){
                            $data[] = $data_check;
                        }
                    }else{
                        $data[] = [
                            'id' => $single['id'],
                            'title' => "Transition: " . $single['duration'] . " seconds",
                            'class' => "exercises_class",
                            'csc_sort' => $single['sort'],
                            'csc_id' => $single['id'],
                            'duration' => $single['duration'],
                            'transition' => 1
                        ];
                    }
                }
            }
        }
        // echo '<pre>';
        
		return $data;
    }

    public function exercises_for_single_class($class_id = 0){
        $exercises_model = model('ExercisesModel');

        if($class_id != 0){
            $res = $exercises_model->query("SELECT * FROM classes_selected_exercises WHERE class_id = " . $class_id . " ORDER BY sort asc")->getResultArray();

            if(!empty($res)){
                foreach($res as $single){
                    $calendar_events = $exercises_model->query("SELECT * FROM calendar_events WHERE class_id = " . $single['class_id'] . " AND calendar_events.deleted_at IS NULL")->getRowArray();
        
                    $total_duration = $exercises_model->query("SELECT SUM(duration) as total FROM classes_selected_exercises WHERE class_id = " . $single['class_id'] . "")->getRowArray();
                    
                    if($single['class_selected_exercises'] != 0){
                        $exercise = $exercises_model->query("SELECT exercises.id, exercises.title, difficulty.title as diff, 'exercises_class' AS class, 
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, 
                                    " . $single['sort'] . " as csc_sort, 
                                    " . $single['id'] . " as csc_id, 
                                    '" . $single['springs'] . "' as springs, 
                                    '" . $single['springs_count'] . "' as springs_count, 
                                    '" . $single['orientation'] . "' as orientation,                                     
                                    0 as transition,
                                    " . $single['duration'] . " as custom_duration, 
                                    " . $single['duration'] . " as duration, 
                                    '" . (isset($calendar_events['time']) ? $calendar_events['time'] : '') . "' as time, 
                                    '" . (isset($calendar_events['date']) ? $calendar_events['date'] : '') . "' as date, 
                                    CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(teachers.firstname != '', teachers.firstname, 'Model is missing'))) as teacher_model, 
                                    '" . duration_standard($total_duration['total']) . "' as total_duration
                                    FROM exercises
                                    LEFT OUTER JOIN teachers ON teachers.id = " . ($calendar_events != NULL ? $calendar_events['teacher_id'] : 0) . "
                                    LEFT OUTER JOIN models ON models.id = " . ($calendar_events != NULL ? $calendar_events['model_id'] : 0) . "
                                    LEFT OUTER JOIN teachers t2 ON t2.id = " . ($calendar_events != NULL ? $calendar_events['teacher_as_model_id'] : 0) . "
                                    LEFT OUTER JOIN difficulty on difficulty.id = exercises.difficulty
                                    LEFT OUTER JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                    LEFT OUTER JOIN machines ON machines.id = exercises_machine.exercise_machine
                                    WHERE exercises.deleted_at IS NULL
                                    AND exercises.status = 0
                                    AND exercises.id = " . $single['class_selected_exercises'] . "
                                    GROUP BY exercises.id
                                    ORDER BY csc_sort asc
                                ")->getRowArray();
                        if($exercise != NULL){
                            $data[] = $exercise;
                        }
                    }else{
                        $data[] = [
                            'id' => $single['id'],
                            'title' => "Transition: " . $single['duration'] . " seconds",
                            'class' => "exercises_class",
                            'csc_sort' => $single['sort'],
                            'csc_id' => $single['id'],
                            'duration' => $single['duration'],
                            'custom_duration' => $single['duration'],
                            'transition' => 1
                        ];
                    }
                }
                // die();

            }else{
                $data = [];
            }
        }else{
            $data = [];
        }       
		return $data;
    }

    public function single_exercise_for_single_class($class_id = 0, $id = 0){
        $exercises_model = model('ExercisesModel');

        if($class_id != 0 && $id != 0){
            $res = $exercises_model->query("SELECT * FROM classes_selected_exercises WHERE class_id = " . $class_id . " AND id = " . $id . " ORDER BY sort asc")->getResultArray();

            if(!empty($res)){
                foreach($res as $single){
                    $calendar_events = $exercises_model->query("SELECT * FROM calendar_events WHERE class_id = " . $single['class_id'] . " AND calendar_events.deleted_at IS NULL")->getRowArray();
        
                    if($single['class_selected_exercises'] != 0){
                        $exercise = $exercises_model->query("SELECT exercises.id, exercises.title, difficulty.title as diff, 'exercises_class' AS class, 
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, 
                                    " . $single['sort'] . " as csc_sort, 
                                    " . $single['id'] . " as csc_id, 
                                    '" . $single['springs'] . "' as springs, 
                                    '" . $single['springs_count'] . "' as springs_count, 
                                    '" . $single['orientation'] . "' as orientation,                                     
                                    0 as transition,
                                    " . $single['duration'] . " as custom_duration, 
                                    " . $single['duration'] . " as duration
                                    FROM exercises
                                    LEFT OUTER JOIN difficulty on difficulty.id = exercises.difficulty
                                    LEFT OUTER JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                    LEFT OUTER JOIN machines ON machines.id = exercises_machine.exercise_machine
                                    WHERE exercises.deleted_at IS NULL
                                    AND exercises.status = 0
                                    AND exercises.id = " . $single['class_selected_exercises'] . "
                                    GROUP BY exercises.id
                                    ORDER BY csc_sort asc
                                ")->getRowArray();
                        if($exercise != NULL){
                            $data[] = $exercise;
                        }
                    }else{
                        $data[] = [
                            'id' => $single['id'],
                            'title' => "Transition: " . $single['duration'] . " seconds",
                            'class' => "exercises_class",
                            'csc_sort' => $single['sort'],
                            'csc_id' => $single['id'],
                            'duration' => $single['duration'],
                            'transition' => 1
                        ];
                    }
                }
            }else{
                $data = [];
            }
        }else{
            $data = [];
        }       
		return $data;
    }

    public function load_ajax_exercises($start = 0, $class_id = 0){
        $exercises_model = model('ExercisesModel');
        $data['selected_exercises_for_selection'] = $this->exercises_for_class($class_id);

        $data['all_exercises'] = $exercises_model->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, classes_selected_exercises.id as csc_id, classes_selected_exercises.orientation as orientation, classes_selected_exercises.duration as custom_duration, 'exercises_class' AS class, SUM(classes_selected_exercises.duration) AS total_duration
                                            FROM exercises
                                            LEFT OUTER JOIN (SELECT exercise_id, count(*) AS cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) AS rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                                            LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                            LEFT JOIN classes_selected_exercises ON (classes_selected_exercises.class_selected_exercises = exercises.id AND classes_selected_exercises.class_id = " . $class_id . ")
                                            WHERE exercises.deleted_at IS NULL
                                            AND exercises.status = 0
                                            GROUP BY exercises.id
                                            ORDER BY classes_selected_exercises.sort asc
                                            LIMIT " . $start .", 30
                                        ")->getResultArray();

        return $data;
    }

	protected function prepare_data(array $data){
		return $data;
	}

}