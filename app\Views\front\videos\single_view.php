<?php
    // NEW template
    $url = $current['video'];
    $path = parse_url($url, PHP_URL_PATH);
    $name_file = basename($path);
    $name = str_replace('.mp4', '', $name_file);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<link rel="stylesheet" type="text/css" href="css/mvp.css" />
<link rel="stylesheet" type="text/css" href="css/flat.css?118" />
<link href="css/videojs.custom.css" rel="stylesheet">
</head>
<body class="collection-page">
<style>
    [data-tooltip]::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 13px;
        z-index: 1111;
        left: 100%;
        background: #000;
        color: #fff;
        max-width: 200px;
        opacity: 0;
        pointer-events: none;
        white-space: nowrap;
        font-size: 10px;
        padding: 5px 10px;
        line-height: 1.4;
        transition: all 0.25s ease-in-out 0s;
    }
    [data-tooltip][data-tooltip-position]::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 13px;
        z-index: 1111;
        left: auto;
        right: 100%;
        background: #000;
        color: #fff;
        max-width: 200px;
        opacity: 0;
        pointer-events: none;
        white-space: nowrap;
        font-size: 10px;
        padding: 5px 10px;
        line-height: 1.4;
        transition: all 0.25s ease-in-out 0s;
    }
    .mvp-btn.mvp-btn-volume-off svg, .mvp-music-toggle.mvp-contr-btn.muted svg {
        fill: rgba(255, 255, 255, 0.4) !important;
    }
    .mvp-btn.mvp-btn-volume-off, .mvp-music-toggle.mvp-contr-btn.muted {
        opacity: 1 !important;
    }
    [data-tooltip] {
        position: relative;
    }
    [data-tooltip]:hover::before {
        opacity: 1;
    }
    .video-row {
        max-width: 1030px;
        margin: auto;
    }
    .single-class-new-row:last-of-type {
        border-bottom: 1px solid #F0F0F0;
    }
    .single-class-new-row {
        display: flex;
        width: 100%;
        align-items: center;
        padding: 5px 0px;
        border: none;
        border-top: 1px solid #F0F0F0;
        margin-bottom: 0;
    }
    textarea {
        resize: none !important;
    }
    .single-exercise .image-overlay:not(.no-overlay)::before {
        opacity: 0.3;
    }
    .gap40 {
        gap: 40px;
    }
    @media (max-width: 480px){
        .img-panel-content {
            padding: 0 2vw;
        }
        .music-with-control {
            top: 10px;
            left: -5px;
        }
        .phone-button.play_music_button {
            margin-right: 15px;
        }
        .phone-button.play_voice_button {
            margin: 15px;
        }
        .phone-buttons {
            margin-top: 0;
            gap: 0;
            padding: 0;
        }
        .exercises-container-overhidden.mt-4 {
            margin: 0 -16px 22px;
            height: 125px;
            overflow: hidden;
        }
    }
</style>
<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="px-0 pt-0 classesroot">
        <div class="container-fluid black-bg position-relative">
            <span class="class-next">
                <a href="/classes/<?php echo $prev_next['next']['slug']; ?>"><img src="images/right-class-arrow.svg" alt="" class="img-fluid" /></a>
            </span>
            <span class="class-prev">
                <a href="/classes/<?php echo $prev_next['prev']['slug']; ?>"><img src="images/left-class-arrow.svg" alt="" class="img-fluid" /></a>
            </span>
            <div class="row video-row">
                <div class="col-12">
                    <?php
                    if(isset($logged_user)){
                        if((NULL !== session('subscription') AND session('subscription') == 'active')){
                    ?>
                        <?php if(isset($current['audio']) AND $current['audio'] != ""){ ?>
                            <span class="music-with-control">MUSIC CONTROL</span>
                        <?php } ?>
                        <div id="lod-video" style="display: none;">
                            <div class="mvp-player-wrap">
                                <div class="mvp-player-holder">
                                    <div class="mvp-media-holder"></div>
                                    <div class="mvp-unmute-toggle">Enable volume</div>
                                    <div class="mvp-player-loader" style="background: url(<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>) no-repeat top center / 100% auto;"></div>

                                    <div class="mvp-live-note">
                                        <div class="mvp-live-note-inner">
                                            <div class="mvp-live-note-icon"></div>
                                            <div class="mvp-live-note-title">LIVE</div>
                                        </div>
                                    </div>
                                    <div class="mvp-big-play">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#333"/>
                                        </svg>
                                    </div>
                                    <div class="mvp-player-controls">
                                        <div class="mvp-player-controls-bottom">
                                        <div class="mvp-player-controls-bottom-left">
                                                <div class="mvp-skip-backward-toggle mvp-contr-btn">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="rewind" d="M14.115,19.886a7.411,7.411,0,1,0-5.671-13.1l2.92,2.518-8.02,1.947.381-8.464L6.433,5.095A9.894,9.894,0,0,1,11.745,2.64,10,10,0,1,1,4.994,18.4l2.053-1.587A7.363,7.363,0,0,0,14.115,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-playback-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-play">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
                                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#fff"/>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-pause">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20">
                                                            <g id="pause" transform="translate(-240.764 -1653)">
                                                                <path id="Path_1312" data-name="Path 1312" d="M0,0H6V20H0Z" transform="translate(240.764 1653)" fill="#fff"/>
                                                                <path id="Path_1313" data-name="Path 1313" d="M0,0H6V20H0Z" transform="translate(249.764 1653)" fill="#fff"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="mvp-skip-forward-toggle mvp-contr-btn">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="Path_1030" data-name="Path 1030" d="M12.349,19.886a7.411,7.411,0,1,1,5.671-13.1L15.1,9.306l8.02,1.947-.381-8.464L20.03,5.095A9.894,9.894,0,0,0,14.719,2.64,10,10,0,1,0,21.469,18.4l-2.053-1.587A7.363,7.363,0,0,1,12.349,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-volume-wrapper mvp-contr-btn">
                                                    <div class="mvp-volume-toggle mvp-contr-btn" data-tooltip="Voice-over On/Off" data-tooltip-position="left">
                                                        <div class="mvp-btn mvp-btn-volume-up">
                                                            <!-- <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                            </svg> -->
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-down">
                                                            <!-- <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                            </svg> -->
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-off">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                            <!-- <svg aria-hidden="true" focusable="false" role="img" viewBox="0 0 640 512"><path d="M633.82 458.1l-69-53.33C592.42 360.8 608 309.68 608 256c0-95.33-47.73-183.58-127.65-236.03-11.17-7.33-26.18-4.24-33.51 6.95-7.34 11.17-4.22 26.18 6.95 33.51 66.27 43.49 105.82 116.6 105.82 195.58 0 42.78-11.96 83.59-33.22 119.06l-38.12-29.46C503.49 318.68 512 288.06 512 256c0-63.09-32.06-122.09-85.77-156.16-11.19-7.09-26.03-3.8-33.12 7.41-7.09 11.2-3.78 26.03 7.41 33.13C440.27 165.59 464 209.44 464 256c0 21.21-5.03 41.57-14.2 59.88l-39.56-30.58c3.38-9.35 5.76-19.07 5.76-29.3 0-31.88-17.53-61.33-45.77-76.88-11.58-6.33-26.19-2.16-32.61 9.45-6.39 11.61-2.16 26.2 9.45 32.61 11.76 6.46 19.12 18.18 20.4 31.06L288 190.82V88.02c0-21.46-25.96-31.98-40.97-16.97l-49.71 49.7L45.47 3.37C38.49-2.05 28.43-.8 23.01 6.18L3.37 31.45C-2.05 38.42-.8 48.47 6.18 53.9l588.36 454.73c6.98 5.43 17.03 4.17 22.46-2.81l19.64-25.27c5.41-6.97 4.16-17.02-2.82-22.45zM32 184v144c0 13.25 10.74 24 24 24h102.06l88.97 88.95c15.03 15.03 40.97 4.47 40.97-16.97V352.6L43.76 163.84C36.86 168.05 32 175.32 32 184z"></path></svg> -->
                                                        </div>
                                                    </div>
                                                    <div class="mvp-volume-seekbar">
                                                        <div class="mvp-volume-bg">
                                                            <div class="mvp-volume-level"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
                                                <div class="mvp-music-toggle mvp-contr-btn muted" data-tooltip="Music On/Off">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="39.5" height="60" viewBox="0 0 39.5 60">
                                                        <path id="music_note_FILL0_wght400_GRAD0_opsz48" d="M24.65,66a12.379,12.379,0,0,1-12.5-12.5A12.379,12.379,0,0,1,24.65,41a12.5,12.5,0,0,1,4.208.667A10.835,10.835,0,0,1,32.15,43.5V6h19.5V17.25H37.15V53.5A12.379,12.379,0,0,1,24.65,66Z" transform="translate(-12.15 -6)"/>
                                                    </svg>
                                                </div>
                                                <?php } ?>
                                                <!-- <div class="mvp-media-time-current"></div> -->
                                            </div>
                                            <div class="mvp-seekbar">
                                                <div class="mvp-seekbar-wrap">
                                                    <div class="mvp-progress-bg">
                                                        <div class="mvp-load-level"></div>
                                                        <div class="mvp-progress-level"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mvp-player-controls-bottom-right">
                                                <!-- <div class="mvp-media-time-total"></div> -->
                                                <div class="mvp-cast-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-cast-off" data-tooltip="Play on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-cast-on" data-tooltip="Stop playing on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>

                                                <div class="mvp-airplay-toggle mvp-contr-btn" data-tooltip="AirPlay">
                                                    <svg id="cast" xmlns="http://www.w3.org/2000/svg" width="27.5" height="20" viewBox="0 0 27.5 20">
                                                        <path id="Path_932" data-name="Path 932" d="M6,18V34.25h7.348L14.6,33H7.25V19.25h25V33H24.9l1.25,1.25H33.5V18Z" transform="translate(-6 -18)" fill="#fff"/>
                                                        <g id="Group_4129" data-name="Group 4129" transform="translate(6.25 12.5)">
                                                            <path id="Path_933" data-name="Path 933" d="M26,65.5H41L33.5,58Z" transform="translate(-26 -58)" fill="#fff"/>
                                                        </g>
                                                    </svg>
                                                </div>

                                                <div class="mvp-fullscreen-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-fullscreen" data-tooltip="Fullscreen" data-tooltip-position="left">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-normal" data-tooltip="Exit Fullscreen" data-tooltip-position="left">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- end mvp-player-holder -->
                            </div><!-- end mvp-player-wrap -->
                            <div id="mvp-playlist-list">
                                <div class="playlist-video">
                                    <div class="mvp-playlist-item"
                                        data-type="video"
                                        data-path='[{"quality": "HD", "mp4": "<?php echo (isset($current['video']) AND $current['video'] != '') ? base_url() . '/video.php?name=' . $current['slug'] . '-' . $name : ''; ?>"}]'
                                        data-poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>"
                                        data-share="<?php echo current_url(); ?>"
                                        data-preview-seek="auto"
                                        data-title="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>">
                                    </div>
                                </div>
                            </div>
                            <!-- base_url() . '/video.php?name=' . $name -->
                            <div class="mvp-preview-seek-wrap">
                                <div class="mvp-preview-seek-inner"></div>
                                <div class="mvp-preview-seek-info"></div>
                            </div>
                        </div>
                        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
                            <!--   -->
                            <audio id="lod_music_audio" muted controls src="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>" style="width: 100%;position: absolute; top: 0; left: 0; opacity: 0; pointer-events: none;"></audio>
                            <div class="phone-buttons mt-2 flex aic jcsb" style="display: none">
                                <div class="phone-button play_voice_button active">VOICEOVER: ON</div>
                                <div class="phone-button play_music_button">MUSIC: OFF</div>
                            </div>
                        <?php } ?>

                        <?php
                            }else{
                        ?>
                            <div class="img-panel">
                                <div class="image-overlay h100"><img src="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" alt="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" class="img-fluid" /></div>
                                <div class="img-panel-content aic">
                                    <a href="/subscribe" class="flex aic white semibold f-14 bold text-uppercase" title="SUBSCRIBE OR LOGIN TO WATCH"><img src="images/lock-dark.svg" alt="" class="img-fluid mr-1 unlock-icon"> SUBSCRIBE OR LOGIN TO WATCH</a>
                                </div>
                            </div>

                        <?php
                            }
                        ?>
                    <?php
                    }else{
                    ?>
                        <div class="img-panel">
                            <div class="image-overlay h100"><img src="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" alt="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" class="img-fluid" /></div>
                            <div class="img-panel-content aic">
                                <a href="/subscribe" class="flex aic white f-18 bold text-uppercase" title="SUBSCRIBE OR LOGIN TO WATCH" data-popup="login-popup">
                                    SUBSCRIBE OR LOGIN TO WATCH
                                </a>
                            </div>
                            <img src="images/lock-dark.svg" alt="" class="img-fluid unlock-icon abs-bottom-down" />
                        </div>
                    <?php
                    }
                    ?>
                </div>
            </div>
        </div>
        <div class="container850">
            <div class="row pt-5 mt-05 pt-mob-4">
                <div class="col-12 pr-mob-1">
                    <!-- <h1 class="h2 mb-3 mobile"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?></h1> -->
                    <div class="row mb-5 pb-05 mb-mob-4">
                        <div class="col-12">
                            <div class="mobile-flex-vertical">
                                <div class="w100">
                                    <h1 class="mb-0 single-class-title"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?></h1>
                                    <div class="flex aic jcsb mt-15 mt-mob-1">
                                        <div class="flex aic">
                                            <div class="f-12 media-desc with-comma line-height-small">
                                                with <span class="d-inline-block ml-05 line-height-small"> <a href="teachers/<?php echo (isset($current['teach_slug']) AND $current['teach_slug'] != '') ? $current['teach_slug'] : ''; ?>" class="link link-midGray midGray text-underline line-height-small"><?php echo (isset($current['teach']) AND $current['teach'] != '') ? $current['teach'] : ''; ?></a></span>
                                                <?php echo (isset($current['duration']) AND $current['duration'] != '') ? '<span class="d-inline-block line-height-small">'. only_minutes($current['duration']) . ' min</span>' : ''; ?>
                                                <?php echo (isset($current['diff']) AND $current['diff'] != '') ? '<span class="d-inline-block line-height-small">' . $current['diff'] . '</span>' : ''; ?>
                                                <?php // if(isset($logged_user)){ ?>
                                                <span class="d-inline-block line-height-small link link-black" data-anchor="#comments"><span class="comments_no line-height-small"><?php echo $count_comments; ?></span> Comments</span>
                                                <?php // } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="mt-0 mb-0">
                    <div class="row">
                        <div class="col-12">
                            <div class="flex aic class-svc">
                            <?php
                                    if(isset($logged_user)){
                                ?>
                                <a href="javascript:;" class="class-box-small flex aic jcc medium f-10 playlistbox" onclick="read_playlists_popup($(this))" data-popup="add-to-playlist-popup" data-class_id="<?php echo $current['id']; ?>" data-class_type="xxxxxxxxx" data-playlist_id="0" style="white-space: nowrap">
                                    <img src="images/add-to-playlist.svg" alt="" class="img-fluid mr-1" style="height: 10px;" />
                                    PLAYLIST
                                </a>
                                <?php
                                    }
                                ?>

                                <?php if(isset($logged_user)){ ?>

                                     <a href="javascript:;" class=" class-box-small flex aic jcc medium f-10" data-popup="share-popup" title="Share"><img src="images/share-icon.svg" alt="" class="img-fluid mr-05" width="16" height="11" style="filter: invert(1)"> SHARE</a>
                                <?php
                                    }else{
                                ?>
                                <a href="javascript:;" class="class-box-small flex aic jcc medium f-10 playlistbox" data-popup="login-popup" style="white-space: nowrap">
                                    <img src="images/add-to-playlist.svg" alt="" class="img-fluid mr-1" style="height: 10px;" />
                                    PLAYLIST
                                </a>

                                    <a href="javascript:;" class="class-box-small flex aic jcc link medium" data-popup="share-popup" title="Share"><img src="images/share-icon.svg" alt="" class="img-fluid mr-05" width="16" height="11" style="filter: invert(1)"> SHARE</a>
                                <?php } ?>

                                <?php
                                    if(isset($logged_user)){
                                        if(
                                            (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
                                            OR
                                            (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
                                            OR
                                            (isset($own) AND count($own) > 0 AND $current['type'] == 1)
                                        ){
                                ?>
                                        <a href="javascript:;" class="class-box-small f-12 medium f-10-mob" data-popup="rate-popup" title="Rate class">
                                <?php
                                        }else{
                                ?>
                                        <span class="class-box-small f-10 medium">
                                <?php
                                        }
                                    }else{
                                ?>
                                    <span class="class-box-small f-10 medium">
                                <?php
                                    }
                                ?>
                                    <span class="flex aic jcc red f-10 medium h100">
                                        <i class="icon-small-star mr-05"></i>
                                        <?php  echo (!isset($logged_user)) ? 'RATING' : 'RATE'; ?>
                                        <?php
                                            if(isset($logged_user)){
                                                if(
                                                    (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
                                                    OR
                                                    (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
                                                    OR
                                                    (isset($own) AND count($own) > 0 AND $current['type'] == 1)
                                                ){
                                        ?> (<?php
                                                }
                                            }
                                        echo (isset($current['classRate']) AND $current['classRate'] != '') ? number_format($current['classRate'], 1) : '';
                                            if(isset($logged_user)){
                                                if(
                                                    (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
                                                    OR
                                                    (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
                                                    OR
                                                    (isset($own) AND count($own) > 0 AND $current['type'] == 1)
                                                ){
                                        ?>)<?php
                                                }
                                            }
                                        ?>
                                    </span>
                                <?php
                                    if(isset($logged_user)){
                                        if(
                                            (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
                                            OR
                                            (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
                                            OR
                                            (isset($own) AND count($own) > 0 AND $current['type'] == 1)
                                        ){
                                ?>
                                </a>
                                <?php
                                        }else{
                                ?>
                                </span>
                                <?php
                                        }
                                    }else{
                                ?>
                                </span>
                                <?php
                                    }
                                ?>

                                <a class="class-box-small flex aic jcc" href="javascript:;" data-anchor="#gotocomments"><i class="icon-small-star mr-05"></i> COMMENTS (<span class="comments_no line-height-small"><?php echo $count_comments; ?></span>)</a>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="mobile">
                        <hr class="mt-0 mb-0 mb-mob-4 mt-mob-0">
                        <h3 class="f-14 bold line-height-small">EXERCISES IN CLASS (<?php // echo count($class_exercises); ?>)</h3>
                        <?php // if(count($class_exercises) > 0){ ?>
                        <div class="exercises-container-overhidden mt-4">
                            <div class="exercises-container">
                                <div class="exercises-container-scroll">
                                    <?php
                                    // $c=0;
                                    // foreach($class_exercises as $single){
                                    // $c++;
                                    ?>
                                    <a href="exercises/<?php // echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container single-exercise <?php // echo $c > 5 ? 'hide' : ''; ?>" style="margin-right: 15px">
                                        <div class="image-overlay">
                                            <div class="single-exercise-image mr-0">
                                                <img src="<?php // echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ''; ?>" alt="" class="img-fluid" />
                                                <span class="play-button"><span></span></span>
                                            </div>
                                        </div>
                                        <div class="single-exercise-title">
                                            <p class="f-10 normal mt-1" style="line-height: 20px;"><?php // echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></p>
                                        </div>
                                    </a>
                                    <?php
                                    // echo ($c > 5) ? '<p class="f-10 bold link link-black" onclick="$(this).hide();$(\'.single-exercise\').removeClass(\'hide\');">SHOW ALL</p>' : '';
                                    // }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php
                        // }else{
                        ?>
                            <p class="mt-2 f-12 line-height-small mb-mob-4">The list is empty.</p>

                        <?php
                        // }
                        ?>
                    </div> -->
                    <?php if((isset($current['all_body_parts']) AND $current['all_body_parts'] != '') OR (isset($current['all_class_springs']) AND $current['all_class_springs'] != '')){ ?>
                    <hr class="mt-0 mb-0 mb-mob-4 mt-mob-0">
                    <div class="row mt-55 mb-55 mt-mob-4 mb-mob-4">
                        <div class="col-12 flex flex-column gap40">
                            <?php if(isset($current['all_body_parts']) AND $current['all_body_parts'] != ''){ ?>
                            <div class="class-body-parts">
                                <h3 class="f-12 semibold line-height-small">BODY PARTS</h3>
                                <p class="f-12 line-height-small"><?php echo (isset($current['all_body_parts']) AND $current['all_body_parts'] != '') ? $current['all_body_parts'] : ''; ?></p>
                            </div>
                            <?php } ?>
                            <?php if(isset($current['all_class_springs']) AND $current['all_class_springs'] != ''){ ?>
                            <div class="class-body-parts">
                                <h3 class="f-12 semibold line-height-small">SPRING LOAD</h3>
                                <p class="f-12 line-height-small"><?php echo (isset($current['all_class_springs']) AND $current['all_class_springs'] != '') ? $current['all_class_springs'] : ''; ?></p>
                                <a href="https://youtu.be/UFjKewf-aXY" data-fancybox="" class="explvideo">Explanation (video)</a>
                            </div>
                            <?php } ?>
                            <?php if(isset($current['all_class_tensions']) AND $current['all_class_tensions'] != ''){ ?>
                            <div class="class-body-parts">
                                <h3 class="f-12 semibold line-height-small">BUNGEE TENSION</h3>
                                <p class="f-12 line-height-small"><?php echo (isset($current['all_class_tensions']) AND $current['all_class_tensions'] != '') ? $current['all_class_tensions'] : ''; ?></p>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                    <?php } ?>
                    <?php if((isset($shopify_machines) AND count($shopify_machines) > 0) OR (isset($shopify_accessories) AND count($shopify_accessories) > 0)){ ?>
                    <hr class="mt-0 mb-0" />
                    <div class="row my-5 pt-05 pb-05 mt-mob-4 mb-mob-4">
                        <div class="col-12 flex-vertical">
                            <h3 class="f-14 semibold mb-5 pb-05 mb-mob-4 line-height-small">EQUIPMENT IN THIS VIDEO</h3>
                            <?php if(isset($shopify_machines) AND count($shopify_machines) > 0){ ?>
                                <?php foreach($shopify_machines as $key => $single){ ?>
                                    <div class="single-class-new-row">
                                        <span class="class-new-row-image"><img src="<?php echo $single['product']['image']['src']; ?>" alt="" class="img-fluid" width="70" /></span>
                                        <span class="flex flex-column jcc">
                                            <span class="f-14 f-12-mob line-height-small medium"><?php echo ($shopify_machines_titles[$key] == 'Microformer') ? 'The Micro' : $shopify_machines_titles[$key]; ?></span>
                                            <span class="f-1 line-height-small flex midGray hide"><?php echo ($shopify_machines_titles[$key] == 'Microformer') ? "STARTING AT" : ''; ?> $<?php echo number_format($single['product']['variants'][0]['price'], 0) ?></span>
                                        </span>
                                        <a href="<?php echo base_url(); ?>/shop/<?php echo $single['product']['handle']; ?>" class="btn btn-xs btn-border ml-auto h30" title="BUY NOW" target="_blank">BUY</a>
                                    </div>
                                <?php } ?>
                            <?php } ?>
                            <?php if(isset($shopify_accessories) AND count($shopify_accessories) > 0){ ?>
                                <!-- <h3 class="f-1 bold line-height-small my-3">ACCESSORIES USED:</h3> -->
                                <?php foreach($shopify_accessories as $key => $single){ ?>
                                    <div class="single-class-new-row">
                                        <span class="class-new-row-image"><img src="<?php echo $single['product']['image']['src']; ?>" alt="" class="img-fluid" width="70" /></span>
                                        <span class="flex flex-column jcc">
                                            <span class="f-14 f-12-mob line-height-small medium"><?php echo $single['product']['title']; ?></span>
                                            <span class="f-1 line-height-small flex midGray hide">$<?php echo number_format($single['product']['variants'][0]['price'], 0) ?></span>
                                        </span>
                                        <a href="<?php echo base_url(); ?>/shop/<?php echo $single['product']['handle']; ?>" class="btn btn-xs btn-border ml-auto h30" title="BUY NOW" target="_blank">BUY</a>
                                    </div>
                                <?php } ?>
                            <?php } ?>
                        </div>
                    </div>
                    <?php } ?>
                    <!-- <hr class="mt-0 mb-0" /> -->
                    <div class="row mb-5 pb-05 mb-mob-4" id="comments">
                        <div id="gotocomments" class="col-12">
                            <h3 class="f-14 semibold line-height-small">COMMENTS (<span class="comments_no line-height-small"><?php echo $count_comments; ?></span>)</h3>
                        </div>
                    </div>
                    <hr class="mt-0 mb-0" />
                    <div class="row mt-5 mt-mob-4">
                        <div class="col-12">
                            <form action="comments/save" method="POST" class="comments-form main-comment-form">
                                <div class="textarea"><textarea name="comment" id="comment" class="comments-field" placeholder="Your comment..." onclick="$(this).parent().next().show();"></textarea></div>
                                <div class="form-options" style="display: none;">
                                    <div class="flex aic jcsb">
                                        <div class="checkbox">
                                            <input type="checkbox" name="notify_replay" class="" id="notify_replay">
                                            <label for="notify_replay" class="f-10 black normal pl-3">Notify me on new replies</label>
                                        </div>
                                        <div class="buttons">
                                            <input type="hidden" name="type" value="videos">
                                            <button type="button" class="btn btn-sm white-bg midGray f-12 f-10-mob bold h40" onclick="$(this).closest('.form-options').hide();$(this).closest('.comments-form').find('#comment').val('');">Cancel</button>
                                            <button type="submit" class="btn btn-sm black-bg white f-12 f-10-mob bold h40">SEND</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class="comments-container">
<?php
if(!empty($comments) AND $count_comments > 0){
    foreach($comments as $single){
?>
                                <div class="single-comment-container" id="comment_<?php echo $single['id']; ?>">
                                    <div class="single-comment" data-id="<?php echo $single['id']; ?>" data-parent-id="0">
                                        <div class="avatar40">
                                            <?php if($single['user_image'] != ''){ ?>
                                                <img src="<?php echo $single['user_image']; ?>" alt="user image" class="img-fluid" />
                                            <?php }else{ ?>
                                                <span class="initials f-12 white semibold no-border black-bg"><?php echo $single['user_initials']; ?></span>
                                            <?php } ?>
                                        </div>
                                        <div class="single-comment-info w100">
                                            <div class="comment-user"><span class="semibold f-12"><?php echo $single['user_name']; ?></span>, <span class="midGray light f-12"><?php echo (isset($single['date']) AND $single['date'] != '') ? time_ago(strtotime($single['date'])) : ''; ?></span></div>
                                            <p class="comment-message"><?php echo nl2br($single['message']); ?></p>
                                            <div class="single-comment-action">
                                                <div class="flex aic">
                                                    <?php if(!empty($single['reply']) AND count($single['reply']) > 0){ ?>
                                                    <a href="javascript:;" title="hide replies" class="link link-blue blue f-12 semibold hide-reply flex aic mr-1" onclick="show_hide_reply($(this))">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20" style="width: 6px;transform: translateY(1px) rotate(151deg);" class="img-fluid mr-1 reply-icon">
                                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#1264FF"/>
                                                        </svg>
                                                        <span class="show_hide">Hide</span> <span class="reply_no mx-05"><?php echo $single['count_replys']; ?></span> replies
                                                    </a>
                                                    <?php } ?>
                                                    <a href="javascript:;" class="link link-midGray midGray f-12 semibold no-underline ls-50" title="Reply comment" <?php echo isset($logged_user) ? '' : 'data-popup="login-popup"'; ?> onclick="<?php echo isset($logged_user) ? 'insert_form($(this))' : ''; ?>"><?php echo isset($logged_user) ? 'Reply' : 'Log in to reply'; ?></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
        <?php
        if(!empty($single['reply']) AND count($single['reply']) > 0){
        ?>
                                   <div class="reply-comments">
        <?php
            foreach($single['reply'] as $single2){
        ?>
                                        <div class="single-comment" id="comment_<?php echo $single2['id']; ?>" data-id="<?php echo $single2['id']; ?>" data-parent-id="<?php echo $single['id']; ?>">
                                            <div class="avatar25">
                                            <?php if($single2['user_image'] != ''){ ?>
                                                <img src="<?php echo $single2['user_image']; ?>" alt="user image" class="img-fluid" />
                                            <?php }else{ ?>
                                                <span class="initials f-10 white no-border black-bg"><?php echo $single2['user_initials']; ?></span>
                                            <?php } ?>
                                            </div>
                                            <div class="single-comment-info w100">
                                                <div class="comment-user"><span class="semibold f-12"><?php echo $single2['user_name']; ?></span>, <span class="midGray light f-12"><?php echo (isset($single2['date']) AND $single2['date'] != '') ? time_ago(strtotime($single2['date'])) : ''; ?></span></div>
                                                <p class="comment-message"><?php echo nl2br($single2['message']); ?></p>
                                                <div class="single-comment-action">
                                                    <div class="flex aic">
                                                        <a href="javascript:;" class="link link-midGray midGray f-12 semibold no-underline ls-50" title="Reply comment" <?php echo isset($logged_user) ? '' : 'data-popup="login-popup"'; ?> onclick="<?php echo isset($logged_user) ? 'insert_form($(this))' : ''; ?>"><?php echo isset($logged_user) ? 'Reply' : 'Log in to reply'; ?></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                        <?php
                        if(!empty($single2['reply']) AND count($single2['reply']) > 0){
                        ?>
                                        <div class="reply-comments2">
                        <?php
                            foreach($single2['reply'] as $single3){
                        ?>
                                            <div class="single-comment" id="comment_<?php echo $single3['id']; ?>" data-id="<?php echo $single3['id']; ?>" data-parent-id="<?php echo $single2['id']; ?>">
                                                <div class="avatar25">
                                                <?php if($single2['user_image'] != ''){ ?>
                                                    <img src="<?php echo $single3['user_image']; ?>" alt="user image" class="img-fluid" />
                                                <?php }else{ ?>
                                                    <span class="initials f-10 white no-border black-bg"><?php echo $single3['user_initials']; ?></span>
                                                <?php } ?>
                                                </div>
                                                <div class="single-comment-info w100">
                                                    <div class="comment-user"><span class="semibold f-12"><?php echo $single3['user_name']; ?></span>, <span class="midGray light f-12"><?php echo (isset($single3['date']) AND $single3['date'] != '') ? time_ago(strtotime($single3['date'])) : ''; ?></span></div>
                                                    <p class="comment-message"><?php echo nl2br($single3['message']); ?></p>
                                                    <div class="single-comment-action">
                                                        <div class="flex aic">
                                                            <a href="javascript:;" class="link link-midGray midGray f-12 semibold no-underline ls-50" title="Reply comment" <?php echo isset($logged_user) ? '' : 'data-popup="login-popup"'; ?> onclick="<?php echo isset($logged_user) ? 'insert_form($(this))' : ''; ?>"><?php echo isset($logged_user) ? 'Reply' : 'Log in to reply'; ?></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                        <?php
                            }
                        ?>
                                        </div>
                        <?php
                        }
                        ?>
        <?php
            }
        ?>
                                    </div>
        <?php
        }
        ?>
                                </div>
<?php
    }
}
?>
                            </div>
                        </div>
                    </div>
                    <?php if(empty($logged_user)){ ?>
                    <hr class="mt-0 mb-5" />
                    <p class="f-12">Please <a href="javascript:;" class="link link-black black text-underline" data-popup="login-popup">login</a> or <a href="/subscribe"  class="link link-black black text-underline">subscribe</a> to comment.</p>
                    <?php
                    }
                    ?>
                </div>
                <!-- <div class="col-4 pl-mob-3 pl-4 desktop">
                    <h3 class="f-12 bold line-height-small">EXERCISES IN CLASS (<?php // echo count($class_exercises); ?>)</h3>
                    <?php // if(count($class_exercises) > 0){ ?>
                    <div class="exercises-container mt-3">
                        <?php
                        // $c=0;
                        // foreach($class_exercises as $single){
                        // $c++;
                        ?>
                        <a href="exercises/<?php // echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container single-exercise <?php // echo $c > 5 ? 'hide' : ''; ?>">
                            <div class="image-overlay" style="margin-right: 15px">
                                <div class="single-exercise-image mr-0">
                                    <img src="<?php // echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $current['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                                    <span class="play-button"><span></span></span>
                                </div>
                            </div>
                            <div class="single-exercise-title">
                                <p class="f-10 normal" style="line-height: 20px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></p>
                            </div>
                        </a>
                        <?php
                        // echo ($c == 5) ? '<p class="f-10 bold link link-black" onclick="$(this).hide();$(\'.single-exercise\').removeClass(\'hide\');">SHOW ALL</p>' : '';
                        // }
                        ?>
                    </div>
                    <?php
                    // }else{
                    ?>
                        <p class="mt-3 f-12 line-height-small">The list is empty.</p>

                    <?php
                    // }
                    ?>

                </div> -->
            </div>
        </div>
    </section>
</main>

<template id="reply-form-template">
    <form action="comments/save" method="POST" class="comments-form mt-2">
        <div class="textarea">
            <textarea name="comment" id="comment" class="comments-field" placeholder="Your comment..."></textarea>
        </div>
        <div class="form-options">
            <div class="flex aic jcr">
                <div class="buttons">
                    <input type="hidden" name="type" value="videos">
                    <button type="button" class="link link-midGray midGray f-12 f-10-mob semibold no-underline ls-50" style="background: #fff !important" onclick="$(this).closest('form').remove()">Cancel</button>
                    <button type="button" onclick="submit_form($(this).closest('form'))" class="link link-black black f-12 f-10-mob semibold ml-2 no-underline ls-50" style="background: #fff !important">Send</button>
                </div>
            </div>
        </div>
    </form>
</template>
<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/textarea.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var user_id = <?php echo (isset($logged_user)) ? $logged_user['id'] : 0; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
var video_state_id = <?php echo isset($current['video_state']) ? $current['video_state']['id'] : 'null'; ?>;

autosize(document.querySelectorAll('textarea'));

var form_template = $('#reply-form-template').html();
function insert_form(xx){
    console.log(xx.closest('.single-comment-action').find('form').length);
    if(xx.closest('.single-comment-action').find('form').length == 0){
        xx.parent().parent().append(form_template);
    }
    autosize(document.querySelectorAll('textarea'));
    $('textarea').on('keyup', function(){
        if($(this).height() > 40){
            $(this).parent().css({"padding-bottom":"25px"});
        }else{
            $(this).parent().css({"padding-bottom":"8px"});
        }
    });

}
$('textarea').on('keyup', function(){
    if($(this).height() > 40){
        $(this).parent().css({"padding-bottom":"25px"});
    }else{
        $(this).parent().css({"padding-bottom":"8px"});
    }
});
function show_hide_reply(xx){
    xx.closest('.single-comment-container').find('.reply-comments').slideToggle();
    if(xx.closest('.single-comment-container').find('.show_hide').text() == 'Hide'){
        xx.closest('.single-comment-container').find('.show_hide').text('Show');
        xx.closest('.single-comment-container').find('.reply-icon').css({"transform": "translateY(-1px) rotate(330deg)"});
    }else{
        xx.closest('.single-comment-container').find('.reply-icon').css({"transform": "translateY(1px) rotate(150deg)"});
        xx.closest('.single-comment-container').find('.show_hide').text('Hide');
    }
}
const textarea = document.querySelector('textarea');
textarea.onkeypress = (event) => {
  const keyCode = event.keyCode;
  if (keyCode === 13) {
    $('#comment').css({"padding-bottom":"25px"});
  }
}
setTimeout(function(){
    to_timezone();
},1000);
</script>
<?php
if(isset($logged_user)){
    if(NULL !== session('subscription') AND session('subscription') == 'active'){
?>
<script type="text/javascript" src="js/new.js"></script>
<script type="text/javascript">
<?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
audio = document.getElementById('lod_music_audio');
<?php } ?>
var video = document.getElementById("lod-video");
var player = $("#lod-video").mvp({
    sourcePath: "",
    instanceName: "player1",
    activePlaylist: ".playlist-video",
    activeItem: 0,
    volume: 1,
    // playerRatio: 1,
    usePlayer: false,
    autoPlay: false,
    autoPlayAfterFirst: true,
    randomPlay: false,
    loopingOn: true,
    preload: true,
    mediaEndAction: 'poster',
    useMobileNativePlayer: false,
    showPosterOnPause: false,
    hideQualityMenuOnSingleQuality: true,
    aspectRatio: 1, // 1
    facebookAppId: "",
    playlistOpened: false,
    useKeyboardNavigationForPlayback: true,
    truncatePlaylistDescription: true,
    rightClickContextMenu: "custom",
    playlistItemContent: "thumb,title, description,duration",
    elementsVisibilityArr: [
        {
            width: 500, elements: [ "play", "next", "seekbar", "fullscreen", "volume"]
        }
    ],
    skin: "flat-light",
    playlistPosition: "no-playlist",
    playerType: "normal",
    playlistScrollType: "perfect-scrollbar",
    useSearchBar: false
});
player.on("mediaEnd", function(e, data){
    $('.mvp-poster-holder').removeClass('hidden_poster');
});
$('.mvp-big-play').on('click', function(){
    $('.music-with-control').hide();
});
<?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
player.on('mediaStart', function(e, data){
    console.log('PLAYER START');
    // setTimeout(function(){
        $('.mvp-player-loader').css('background','none');
    // }, 300);
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.volume = 0.1;
    audio.play();
    <?php } ?>
});
player.on('mediaPlay', function(e, data){
    console.log('PLAYER MEDIA PLAY');
    console.log(data.instance.getCurrentTime());
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.currentTime = data.instance.getCurrentTime();
    audio.volume = 0.1;
    audio.play();
    <?php } ?>
    $('.music-with-control').hide();
});
player.on('seeked', function(e, data){
    console.log('PLAYER ONSEEKED');
    console.log(data.instance.getCurrentTime());
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.currentTime = data.instance.getCurrentTime();
    <?php } ?>
});
player.on('mediaStop mediaPause', function(e, data){
    console.log('PLAYER STOP/PAUSE');
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.pause();
    <?php } ?>
    // $('.music-with-control').show();
});
$('.mvp-music-toggle').on('click', function(){
    console.log('MUSIC CLICKED');
    // player.toggleMute();
    $('.mvp-music-toggle').toggleClass('muted');
    if($('.play_music_button').text() == 'music: on'){
        $('.play_music_button').text('music: off');
    }else{
        $('.play_music_button').text('music: on');
    };
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.currentTime = player.getCurrentTime();
    audio.muted = !audio.muted;
    <?php } ?>
});
$('.mvp-btn.mvp-btn-volume-up').on('click', function(){
    console.log('VOICE OFF');
    // player.toggleMute();
    if($('.play_voice_button').text() == 'VOICEOVER: ON'){
        $('.play_voice_button').text('VOICEOVER: OFF').removeClass('active');
    }else{
        $('.play_voice_button').text('VOICEOVER: ON').addClass('active');
    };
});
$('.mvp-btn.mvp-btn-volume-off').on('click', function(){
    console.log('VOICE ON');
    // player.toggleMute();
    if($('.play_voice_button').text() == 'VOICEOVER: ON'){
        $('.play_voice_button').text('VOICEOVER: OFF').removeClass('active');
    }else{
        $('.play_voice_button').text('VOICEOVER: ON').addClass('active');
    };
});
<?php } ?>
player.on('mediaPlay', function(e, data){
    console.log("START SAVING VIDEO STATE each 3s");
    setInterval(function () {
        // localStorage.setItem("<?php // echo $current['slug']; ?>", document.querySelector(".mvp-media").currentTime);
        // save_video_state(video_state_id, '<?php echo $current['slug']; ?>', <?php echo isset($logged_user) ? $logged_user['id'] : 0; ?>, document.querySelector(".mvp-media").currentTime);
    }, 3000);
});

video.addEventListener('ended', () => {
    console.log('video ENDED');
    video.pause();
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.pause();
    <?php } ?>
    video.currentTime = 0;
});
function iOS() {
  return [
    'iPad Simulator',
    'iPhone Simulator',
    'iPod Simulator',
    'iPad',
    'iPhone',
    'iPod'
  ].includes(navigator.platform)
  // iPad on iOS 13 detection
  || (navigator.userAgent.includes("Mac") && "ontouchend" in document)
}
if(iOS()){
    $('#lod-video').html('<video id="native-video" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" controls style="width: 100%;object-fit: cover"><source src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"></source></video>')
    $('.phone-buttons').show();
    console.log('IPHONE');
    var native_video = document.getElementById("native-video");
    console.log('NATIVE LOADED');
    native_video.addEventListener('seeked', () => {
        console.log('video SEEKED');
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.currentTime = native_video.currentTime;
        <?php } ?>
    });
    native_video.onplay = function() {
        console.log('video PLAY');
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.volume = 0.1;
        audio.play();
        <?php } ?>
        $('.music-with-control').hide();
    };
    native_video.onpause = function() {
        console.log('video pause');
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.pause();
        <?php } ?>
        // $('.music-with-control').show();
    };
    $('.play_music_button').on('click touch', function(){
        console.log('MUSIC CLICKED');
        if($('.play_music_button').text() == 'MUSIC: ON'){
            $('.play_music_button').text('MUSIC: OFF').removeClass('active');
        }else{
            $('.play_music_button').text('MUSIC: ON').addClass('active');
        };
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.currentTime = native_video.currentTime;
        audio.muted = !audio.muted;
        audio.volume = 0.1;
        <?php } ?>
    });
    $('.play_voice_button').on('click touch', function(){
        console.log('VOICE CLICKED');
        if($('.play_voice_button').text() == 'VOICEOVER: ON'){
            $('.play_voice_button').text('VOICEOVER: OFF').removeClass('active');
        }else{
            $('.play_voice_button').text('VOICEOVER: ON').addClass('active');
        };
        native_video.muted = !native_video.muted;
    });
}else{
    console.log('PLAYER LOADED');
    var video_seek = document.querySelector(".mvp-seekbar");
    video_seek.addEventListener('click touch', function(){
        console.log('video seek');
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.currentTime = player.getCurrentTime();
        audio.volume = 0.1;
        <?php } ?>
    });
    $('.play_music_button').on('click touch', function(){
        console.log('MUSIC CLICKED');
        $('.mvp-music-toggle').toggleClass('muted');
        if($(this).text() == 'music: on'){
            $(this).text('music: off');
        }else{
            $(this).text('music: on');
        };
        <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
        audio.currentTime = player.getCurrentTime();
        audio.muted = !audio.muted;
        audio.volume = 0.1;
        <?php } ?>
    });
    $('.play_voice_button').on('click touch', function(){
        console.log('VOICE CLICKED');
        if($(this).text() == 'VOICEOVER: ON'){
            $(this).text('VOICEOVER: OFF');
        }else{
            $(this).text('VOICEOVER: ON');
        };
        player.toggleMute();
    });
}
var video = document.getElementById("lod-video");
video.addEventListener('click', function(){
    console.log('video clicked');
    $('.mvp-poster-holder').addClass('hidden_poster');
    mark_as_watched('classes', <?php echo isset($logged_user) ? $logged_user['id'] : 0; ?>, <?php echo $current['id']; ?>);
    <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
    audio.currentTime = player.getCurrentTime();
    <?php } ?>
});
$('#lod-video').on('mouseleave', function(){
    $('.mvp-player-controls').removeClass('mvp-player-controls-visible');
});
$('#lod-video').on('mouseenter', function(){
    $('.mvp-player-controls').addClass('mvp-player-controls-visible');
});
</script>
<?php
    }
}
?>
<script>
/* COMMENTS FORM */
function submit_form(form){
	console.log('comments-form-ajax submit');
	// e.preventDefault();
	// var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    var parent = form.closest('.single-comment').data('id');
    var type = 'videos';
    var message = form.find('.comments-field').val();
    button.addClass('btn--loading');

    console.log("class_id: " + class_id);
    console.log("parent: " + parent);
    console.log("message: " + message);
    console.log("user_id: " + user_id);

    $.ajax({
        type: "POST",
        url: url,
        data: {
            class_id,
            parent,
            message,
            type,
            user_id
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Your comment has been sent on approval', 'success', 2500, 1);
                // button.removeClass('btn--loading');
                form.remove();
                // form.trigger('reset');
                // window.location = '/thank-you'
            }else{
                console.log('NO SUCCESS');
                app_msg("Something went wrong", 'danger', 2500, 1);
                form.remove();
                // $.each(data.json, function(key, val){
                //     $('.contact-form [name=' + key + "]").addClass('error');
                // });
                // button.removeClass('btn--loading');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            form.remove();
            app_msg('Something went wrong! Please try again', 'danger', 2500, 1);
            // button.removeClass('btn--loading');
        }
    });
};
// var item = localStorage.getItem('<?php // echo $current['slug']; ?>');
<?php if(isset($current['video_state']['video_time']) AND (int)($current['video_state']['video_time']) > 0){ ?>
    var item = <?php echo $current['video_state']['video_time']; ?>;
    console.log('CURRENT TIME: ' + item);
    $(document).ready(function(){
        $(".mvp-playlist-item").attr('data-start', item);
    });
<?php } ?>
window.onunload = function () {
    var curr = document.querySelector(".mvp-media").currentTime;
    // localStorage.setItem("<?php // echo $current['slug']; ?>", (curr == 'undefined' ? 0 : curr));
    // save_video_state(video_state_id, '<?php echo $current['slug']; ?>', <?php echo isset($logged_user) ? $logged_user['id'] : 0; ?>, curr);
}
/* function save_video_state(id = '', video_id = '', user_id = 0, video_time = 0, video_type = 'videos'){
    console.log("START VIDEO STATE SAVE");

    $.ajax({
        type: 'POST',
        url: 'classes/save_video_state',
        data: {
            id,
            user_id,
            video_id,
            video_time,
            video_type
        },
        dataType: 'json',
        success: function (data) {
            // console.log(data);
            // console.log('SUCCESS');
            // console.log("VIDEO STATE SAVED");
            if(data.inserted_id){
                video_state_id = data.inserted_id;
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
} */
</script>
</body>
</html>