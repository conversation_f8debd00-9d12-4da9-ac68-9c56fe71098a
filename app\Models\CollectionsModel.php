<?php namespace App\Models;

use CodeIgniter\Model;

class CollectionsModel extends Model
{
    protected $table = 'collections';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'cover_image', 'mob_cover_image', 'content', 'short_desc', 'difficulty', 'subscription_type', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'sort', 'notification_sent'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]|is_unique[collections.title,id,{id}]',
        'slug'        => 'required|alpha_dash|is_unique[collections.slug,id,{id}]',
        // 'image'     => 'required',
        //'short_desc'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	// public function add_gallery($page_id = 0, $gallery = array())
	// {
	// 	$response['success'] = FALSE;
	// 	$db      = \Config\Database::connect();
	// 	$builder = $db->table('collections_galleries');
	// 	$builder->delete(['page_id' => $page_id]);
	// 	if (count($gallery) > 0)
	// 	{
	// 		$builder->insertBatch($gallery);
	// 	}

	// 	/*
	// 	if (count($users) == 1)
	// 	{
	// 		$response['user_id'] = $users[0]['id'];
	// 		$response['success'] = TRUE;
	// 	}
	// 	else
	// 	{
	// 		$response['error'] = 'Bad username or password.';
	// 	}*/
	// 	return $response;
	// }

	// public function get_gallery($page_id = 0)
	// {
	// 	$response['success'] = FALSE;
	// 	$db      = \Config\Database::connect();
	// 	$builder = $db->table('collections_galleries');
	// 	$response =  $builder->getWhere(['page_id' => $page_id])->getResultArray();
	// 	return $response;
	// }

    public function all_collections($start = 0, $limit = 0, $search_term = NULL, $order = "sort, collections.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND collections.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT collections.*, difficulty.title as diff
                            -- COALESCE(x.cnt,0) AS classesCount,
                            -- COALESCE(y.cnty,0) AS howtoCount
                            FROM collections
                            -- LEFT OUTER JOIN (SELECT collections_id, collection_selected_classes, count(*) as cnt FROM collections_selected_classes WHERE collection_selected_classes != 0 GROUP BY collections_id) x ON x.collections_id = collections.id
                            -- LEFT OUTER JOIN (SELECT collections_id, collection_selected_howto, count(*) as cnty FROM collections_selected_howto WHERE collection_selected_howto != 0 GROUP BY collections_id) y ON y.collections_id = collections.id
                            LEFT JOIN difficulty on difficulty.id = collections.difficulty
                            WHERE collections.deleted_at IS NULL
                            " . $search . "
                            GROUP BY collections.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        if($data != NULL AND count($data) > 0){
            foreach($data as $key => $single){
                $data[$key]['classesCount'] = count($this->classes_for_collection($single['id']));
            }
        }

        return $data;
    }

    public function single_collection($field = 'id', $value = NULL)
    {
        $data = [];
        if($value != NULL){
            $res = $this->query("SELECT collections.*, difficulty.title as diff,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM collections
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_classes, count(*) as cnt FROM collections_selected_classes WHERE collection_selected_classes != 0 GROUP BY collections_id) x ON x.collections_id = collections.id
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_howto, count(*) as cnty FROM collections_selected_howto WHERE collection_selected_howto != 0 GROUP BY collections_id) y ON y.collections_id = collections.id
                            LEFT JOIN difficulty on difficulty.id = collections.difficulty
                            WHERE collections.deleted_at IS NULL
                            AND " . $field . " = '" . $value . "'
                        ")->getResultArray();
            if(count($res) > 0){
                $data = $res[0];
            }
        }
        return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT collections.*, difficulty.title as diff,
                                        GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_collections_machines,
                                        GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_collections_machines_short
                                        FROM collections
                                        LEFT JOIN collections_machine ON collections_machine.collections_id = collections.id
                                        LEFT JOIN machines ON machines.id = collections_machine.collection_machine
                                        LEFT JOIN difficulty ON difficulty.id = collections.difficulty
                                        WHERE collections.deleted_at IS NULL
                                        AND collections.slug = '" . $slug . "'
                                    ")->getRowArray();
        return $data;
    }

    public function classes_for_collection($collection_id = 0){
        $classes_model = model('ClassesModel');

        if($collection_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT collection_selected_classes AS id FROM collections_selected_classes WHERE collections_id = " . $collection_id . "");
            $res = $query->getResultArray();

            $query1 = $db->query("SELECT collection_selected_howto AS id FROM collections_selected_howto WHERE collections_id = " . $collection_id . "");
            $res1 = $query1->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }

            $d = "";
            if(!empty($res1)){
                foreach($res1 as $single){ $d .= $single['id'] . ','; }
                $ids_videos = substr($d, 0, -1);
            }else{
                $ids_videos = 0;
            }

            $data = $classes_model->query("
                                            SELECT * FROM (
                                                (
                                                    SELECT classes.id, classes.title, classes.video_preview, classes.teacher, 'classes' as type, classes.slug, classes.image, classes.video_thumb, classes.duration, classes.status, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach, teachers.slug AS teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, collections_selected_classes.id AS csc_id, collections_selected_classes.sort AS sort, 'class_class' AS class, IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, 'classes' as slug_type,
                                                    video_state.video_time as video_state,
                                                    IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                                                    IF(classes.id IN (
                                                        SELECT * FROM (
                                                                SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                        ) as subquery
                                                    ), 1, 0) as purchased,
                                                    IF(classes.id IN (
                                                            SELECT * FROM (
                                                                    SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                            ) as subquery2
                                                    ), 1, 0) as watched
                                                    FROM classes
                                                    LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                                    LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                                    LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                                    LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                                                    LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                                    LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                                    LEFT JOIN teachers ON teachers.id = classes.teacher
                                                    LEFT JOIN collections_selected_classes ON collections_selected_classes.collection_selected_classes = classes.id
                                                    LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                                                    WHERE classes.deleted_at IS NULL
                                                    AND classes.status = 0
                                                    AND classes.id IN (" . $ids . ")
                                                    GROUP BY classes.id
                                                )
                                                UNION
                                                (
                                                    SELECT howto.id, howto.title, howto.video_preview, howto.teacher, 'videos' as type, howto.slug, howto.image, howto.video_thumb, howto.duration, howto.status, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach, teachers.slug AS teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, collections_selected_howto.id AS csc_id, collections_selected_howto.sort AS sort, 'howto_class' AS class, IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, 'videos' as slug_type,
                                                    video_state.video_time as video_state,
                                                    IF(howto.id IN (0), 1, 0) as watched,
                                                    0 as purchased,
                                                    0 as own
                                                    FROM howto
                                                    LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                                                    LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                                                    LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                                    LEFT JOIN subscribers_favs ON subscribers_favs.class_id = howto.id
                                                    LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                                    LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                                    LEFT JOIN teachers ON teachers.id = howto.teacher
                                                    LEFT JOIN collections_selected_howto ON collections_selected_howto.collection_selected_howto = howto.id
                                                    LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                                                    WHERE howto.deleted_at IS NULL
                                                    AND howto.status = 0
                                                    AND howto.id IN (" . $ids_videos . ")
                                                    GROUP BY howto.id
                                                )
                                            ) AS i
                                            ORDER BY sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }
    public function howto_for_collection($collection_id = 0){
        $howto_model = model('HowtoModel');

        if($collection_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT collection_selected_howto AS id FROM collections_selected_howto WHERE collections_id = " . $collection_id . "");
            $res = $query->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }
            $data = $howto_model->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach, teachers.slug as teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, collections_selected_howto.id as csc_id, video_state.video_time as video_state
                                            FROM howto
                                            LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                                            LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                            LEFT JOIN teachers ON teachers.id = howto.teacher
                                            LEFT JOIN collections_selected_howto ON collections_selected_howto.collection_selected_howto = howto.id
                                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                                            WHERE howto.deleted_at IS NULL
                                            AND howto.status = 0
                                            AND howto.id IN (" . $ids . ")
                                            GROUP BY howto.id
                                            ORDER BY collections_selected_howto.sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }
    public function cron(){
        $data = $this->query("SELECT collection.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teach_name
                                FROM collection
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE collection.deleted_at IS NULL
                                AND collection.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}
