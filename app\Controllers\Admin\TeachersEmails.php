<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class TeachersEmails extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('TeachersEmailModel');
	}

    public function index()
    {
		$this->all();
    }

    public function all($page = 1, $sort = 'created_at DESC')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['data'] = $this->request->getPost();
        if(isset($data['data']['search_term'])){
            $data['search_term'] = $data['data']['search_term'];
        }else{
            $data['search_term'] = '';
        }
        $data['all_teachers_emails'] = $this->model->all_teachers_emails(($page * session('per_page')) - session('per_page'), session('per_page'), $data['search_term'], $sort);
        
        $teachers_count = $this->model->all_teachers_emails(0, 10000, $data['search_term']);
        $data['teachers_count'] = count($teachers_count);
        $data['sort_by'] = $sort;
        $data['page'] = $page;

        echo view('admin/teachers_emails/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments; 
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_teachers_emails'] = $this->model->query("SELECT  teachers_emails.*
                                        FROM  teachers_emails
                                        WHERE  teachers_emails.deleted_at IS NULL
                                        AND (teachers_emails.description LIKE '%" . $data['search_term'] . "%' OR teachers_emails.subject LIKE '%" . $data['search_term'] . "%')
                                        ORDER BY teachers_emails.created_at desc")->getResultArray();
        $data['teachers_emails_count'] = $this->model->like('subject', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/teachers_emails/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
		$TeachersModel = model('TeachersModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }else if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            if($edit_id != $data['logged_user']['id']){
                return redirect()->to('admin/classes');
            }
        }

        $data['all_teachers'] = $TeachersModel->all_certified_teachers();
		$data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/teachers_emails');
        };

		return view('admin/teachers_emails/edit_view', $data);
    }

    public function save()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');

        $validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
            $response['teachers_emails_sent'] = FALSE;
            $tt = $TeachersModel->query("SELECT email FROM teachers WHERE id IN (". $data['teachers_id'] . ")")->getResultArray();
            $teachers_to = '';
            
            $end = end($tt);
            foreach ($tt as $key => $single) {
                if($end != $single){
                    $teachers_to .= $single['email'] . ',';
                }else{
                    $teachers_to .= $single['email'];
                }
            }            

            if($teachers_to != ''){
                $subject = $data['subject'];
                $data_template = [
                    'msg' => $data['description'],
                ];
                $template = 'front/email_templates/teachers-email';
                // $to = '<EMAIL>';
                $to = $teachers_to;
                $cc = $data['teachers_cc'];
                $response['teachers_emails_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template, $cc);
            }
    
            $save_data = $data;
            $save_data['status'] = $response['teachers_emails_sent'] ? 0 : 1;
    
			$response['success'] = $this->model->save($save_data);
			$response['errors'] = $this->model->errors();

            if($response['success']){
                $response['message'] = 'Emails sent successfully';
            }else{
                $response['message'] = 'Server error. Please try again';
            }
			$response['inserted_id'] = (isset($data['id']) AND $data['id']) > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}