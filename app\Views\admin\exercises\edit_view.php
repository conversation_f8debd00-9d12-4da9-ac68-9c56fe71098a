<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.custom-toggle-checkbox {
    cursor: pointer;
    font-family: 'Graphik';
    font-size: 16px;
    padding-left: 35px;
    position: relative;
    user-select: none;
}
 

.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
    position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 18px !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
    z-index: 99999999;
}
#main_form {
	margin: 0 auto 60px;
}
.input-container.dropdown-button.has-error {
	border: 1px solid red;
}

/*Dropdown checkboxes*/ 
.dropdown-container {position: relative;max-width: 500px;}
.dropdown-label {font-size:14px; color:#969696;}
.dropdown-label:after {content: ""; width:8px; height:5px; background: url(/admin_assets_new/images/triangle-down.svg) no-repeat center center/cover; position: absolute; right: 15px; top: 50%; margin-top: -3px;}
/*.dropdown-container.is-active .dropdown-label:after {content: "\25B2";}*/
.dropdown-button {cursor: pointer;  border: 1px solid #f0f0f0; background: white; display: flex; flex-flow: row wrap;}
.dropdown-quantity {flex: 1;display: flex; flex-flow: row wrap; flex-basis: 100%; flex-wrap: nowrap; white-space: nowrap;}
.dropdown-list {position: absolute; overflow-y: auto; z-index: 9999999; top: 55px; width: 100%; max-height: 250px; padding: 15px 20px 0 20px; border: 1px solid #ddd !important; border-top: 0; background: white; display: none; max-width: 500px; box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);}
.dropdown-container.is-active .dropdown-list {display: block; border-radius:10px;}
.dropdown-list input[type="search"] {padding: 5px; display: block; width: 100%;}
.dropdown-list ul {padding: 0; padding-top: 10px; list-style: none;}
.dropdown-list li {padding: 0.24em 0;}
input[type="checkbox"] {margin-right: 5px;}
.dropdown-container .is-hidden { display: none; }
.hidebtn {font-size:10px; font-weight:600; border:1px solid #000; padding:7px 20px; float:right; cursor:pointer; margin-top: -5px;} 
.hidebtn:hover {color:#fff; background:#000;} 
.add-desc {display:none;}
.showdiv {display:flex !important;}

/*Disabled input if Please select machine first displayed*/
.machine_first {margin-top: 10px; color:#DB1818 !important;}
.machinefirst-box {display:block !important;}
.dropdown-container.machinefirst-box.disabled-input .input-container.noselect {pointer-events: none; cursor: default; background:#f8f8f8;}
/*end*/


@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
#main_form {margin-bottom: 20px !important;}
}
</style>
 
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5 mb-4">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <?php if(isset($current['status']) AND $current['status'] == 2){ ?>
                    <h1 class="h3">Exercise Review</h1>
                    <span class="btn btn-xs yellow-bg white f-1 ml-2" style="min-height: 25px;">Pending</span>
                    <a href="admin/exercises" class="btn btn-border white-bg black ml-auto" title="Cancel">Cancel</a>
                <?php }else{ ?>
                    <h1 class="h3"><?php echo isset($current['id']) ? '' : 'Upload' ?> Exercise <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                    <span class="btn btn-xs status-orange f-12 btnadmin mr-auto" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                    <a href="admin/exercises" class="btn btn-border white-bg black ml-2" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
                <?php } ?>
            </div>
            <form action="admin/exercises/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc mb-1 upload-zone" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 400px;"' : '' ?>>
                <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                <span class="f-16 semibold mb-1">DRAG AND DROP VIDEO HERE</span> 
                <span class="f-14 midGray video_choose">or <u>select a file</u></span>
                </div>
                <div class="video_placeholder">
                    <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                </div>
                <span id="video-is-uploading"></span>
                <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                <span id="progress-bar-status-show"></span>
                <span id="toshow" style="display: none;"></span>
            </form>

            <div class="flex aic jcsb mt-1 mb-2">
                <span id="remove_video" class="link link-red red text-underline f-12 mb-1" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                <!-- <div class="duration-container ml-auto">
                    <div class="flex aic jcr f-14 no-wrap" hidden>
                        <p class="mr-1">Duration</p>
                        <input type="text" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                        <span class="duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                    </div>
                </div> -->
                <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
            </div>
            <hr class="mt-4 mb-4">
        </div>

        <!-- <div class="container flex-vertical aic video_thumbs" <?php // echo ((isset($current['image']) AND $current['image'] != '') OR $current['id'] == NULL) ? 'style="display: none"' : ''; ?>>
            <h3 class="mb-3">Video Thumbnail</h3>
            <div class="row">
                <div class="col-12 flex aic">
                    <div class="upload-image">
                        <img src="<?php // echo !isset($current['video_thumb']) ? 'admin_assets_new/images/upload-icon.svg' : $current['video_thumb']; ?>" alt="" class="image_preview video_thumb <?php // echo !isset($current['video_thumb']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo !isset($current['video_thumb']) ? 1 : 1; ?>">
                    </div>
                    <div class="midGray f-14">
                        <div class="flex flex-column mb-2 image_options">
                            <a href="javascript:;" class="link link-midGray midGray text-underline remove_thumb mb-2" <?php // echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? '' : 'style="display: none"'; ?>>Remove</a>
                            <a href="javascript:;" class="btn btn-sm f-14 red-bg white choose_php_thumb" onclick="get_thumbs($(this))" title="">Select thumbnail from video</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="thumbs-container" style="display: none;">
                <hr class="my-4">
                <div class="row">
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail1" id="thumbnail1" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail2" id="thumbnail2" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail3" id="thumbnail3" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                </div>
            </div>
            <hr class="my-4">
        </div> -->
        <form action="admin/exercises/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom exercises_form mb-4 pb-05" id="main_form">
            <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
            <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>
            <h5 class="flex aic jcsb mb-4 f-14 semibold">CUSTOM THUMBNAIL <a class="btn btn-xs f-10 btn-border hidebtn hidethumb">SHOW</a></h5>
            <div class="" style="display: none;">
            <div class="image_container flex aic thumb-box mb-5">
                <div class="upload-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                    </div>

                </div>
            </div>
                </div>
            <hr class="mt-0 mb-45">
            <div class="row mb-2 mb-mob-3">
                <div class="col-12">
                    <h5 class="mb-45 f-14 semibold">EXERCISE INFO</h5>
                    <h5 class="mb-1 f-11">NAME</h5>
                    <div class="input-container" id="title_container" style="position: relative;">
                        <input type="text" name="title" class="line-input black" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>

                    <h5 class="mb-1 f-11">AKA</h5>
                    <div class="input-container" id="aka_container" style="position: relative;">
                        <input type="text" name="aka" class="line-input black" placeholder="Enter" value="<?php echo isset($current['aka']) ? $current['aka'] : '' ?>" />
                    </div>
                </div>
            </div>
            <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" />
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-45 mt-05 f-14 semibold top-border pt-45 mt-mob-0">DESCRIPTION</h5>
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input class_description" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">500</span></p>
                    </div>
                    <div class="input-container mb-1" id="content_container"><!-- CHECKBOX TO DISPLAY NEW DESCRIPTION FIELD -->
                        <input type="checkbox" id="check-desc" name="check-desc" <?php echo (isset($current['additional_desc']) AND $current['additional_desc'] != '') ? 'checked' : '' ?>>
                        <label for="check-desc" id="label-adddesc" class="f-14">Add additional description</label>
                    </div>
                    <div class="input-container add-desc mt-6 mb-1" id="content_container" <?php echo (isset($current['additional_desc']) AND $current['additional_desc'] != '') ? 'style="display: block"' : '' ?>><!-- NEW DESCRIPTION FIELD TO CONNECT WITH DATABASE -->
                    <h5 class="mb-1 f-11">ADDITIONAL DESCRIPTION</h5>
                        <textarea type="text" name="additional_desc" class="line-input class_description-add" onkeyup="$('.words_count2').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500" placeholder="Enter"><?php echo isset($current['additional_desc']) ? $current['additional_desc'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count2">500</span></p>
                    </div>
                </div>
            </div>
            <hr class="mt-4 mt-mob-15 mb-45">
            <h5 class="mb-45 f-14 semibold">ADDITIONAL INFO</h5>
            <h5 class="mb-1 f-11">MACHINE *</h5>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="machine_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'Selected (' . count($current_machines) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <div class="checkbox mb-15">
                        <label class="f-12 custom-toggle-checkbox select_all_micros">Select all Micros</label>
                    </div>
                    <div class="checkbox mb-15">
                        <label class="f-12 custom-toggle-checkbox select_all_minis">Select all Minis</label>
                    </div>
                    <div class="checkbox mb-15">
                        <label class="f-12 custom-toggle-checkbox select_all_megas">Select all Megas</label>
                    </div>
                <?php
                $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
                $list_machines = array();
                foreach($machines as $single){
                    $list_machines[$single['id']] = $single['title'];
                ?>
                    <div class="checkbox mb-15">
                        <input type="checkbox" class="available_machines" name="machine[]" data-name="<?php echo $single['title']; ?>" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    if(count($curr_machines)){
                        foreach($machines as $single){
                            foreach($curr_machines as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="machine_select<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>

            <div class="row mb-2 pb-05">
                <div class="col-12">
                    <h5 class="mb-1 f-11">ACCESSORIES REQUIRED</h5>
                    <?php $curr_accessories = (isset($current_accessories) AND $current_accessories != '') ? $current_accessories : array(); ?>
                    <div class="dropdown-container machinefirst-box dropdown-wrap">
                        <div id="accessories_container" class="input-container mb-0 dropdown-button noselect">
                            <div class="dropdown-label <?php echo (isset($curr_accessories) AND $curr_accessories != '' AND count($curr_accessories) > 0) ? 'black' : ''; ?>"><?php echo (isset($curr_accessories) AND $curr_accessories != '' AND count($curr_accessories) > 0) ? 'Selected (' . count($curr_accessories) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                        <?php
                        foreach($accessories as $single){
                        ?>
                            <div class="checkbox mb-15" data-name="<?php echo $single['title']; ?>" data-machine="<?php echo $single['machine']; ?>">
                                <input type="checkbox" <?php if($single['id'] == 41){ ?>onchange="$(this).is(':checked') ? $('.bungee_tension').show() : $('.bungee_tension').hide()"<?php } ?> <?php if($single['id'] == 41){ ?>onchange="$(this).is(':checked') ? $('.bungee_tension').show() : $('.bungee_tension').hide()"<?php } ?> class="" name="accessories[]" data-name="<?php echo $single['title']; ?>" id="accessories<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                <label for="accessories<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                            </div>
                        <?php
                        }
                        ?>
                        </div>
                        <div class="dropdown-quantity">
                        <?php 
                        if(count($curr_accessories)){
                            foreach($accessories as $single){
                                foreach($curr_accessories as $single2){
                                    if($single['id'] == $single2){
                        ?>
                        <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="accessories<?php echo $single['id']; ?>">×</span></span>
                        <?php 
                                    }
                                }
                            }
                        }
                        ?>                    
                        </div>
                    </div>
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>                
            </div>

            <div class="row mb-2 pb-05">
                <div class="col-12">
                     <h5 class="mb-1 f-11">SPRING LOAD <!--<span class="link midGray f-12 normal select_all text-capitalize">Select All</span>--></h5>
                    <?php $curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array(); ?>

                    <div class="dropdown-container machinefirst-box dropdown-wrap">
                        <div id="springs_container" class="input-container mb-0 dropdown-button noselect">
                            <div class="dropdown-label <?php echo (isset($curr_springs) AND $curr_springs != '' AND count($curr_springs) > 0) ? 'black' : ''; ?>"><?php echo (isset($curr_springs) AND $curr_springs != '' AND count($curr_springs) > 0) ? 'Selected (' . count($curr_springs) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                            <div class="checkbox mb-15 all_mega_springs" style="display: none;">
                                <label class="f-12 custom-toggle-checkbox select_all_springs">Select all</label>
                            </div>
                            <div class="checkbox mb-15 all_mega_springs" style="display: none;">
                                <label class="f-12 custom-toggle-checkbox select_all_heavy_springs">Select all heavy</label>
                            </div>
                            <div class="checkbox mb-15 all_mega_springs" style="display: none;">
                                <label class="f-12 custom-toggle-checkbox select_all_light_springs">Select all light</label>
                            </div>
                            <?php
                            foreach($springs as $single){
                            ?>
                            <div class="checkbox mb-15" data-machine="<?php echo $single['machine']; ?>">
                                <input type="checkbox" class="" name="springs[]" data-name="<?php echo $single['title']; ?>" id="springs<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                <label for="springs<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                            </div>
                            <?php
                            }
                            ?>
                        </div>
                        <div class="dropdown-quantity">
                            <?php 
                            if(count($curr_springs)){
                                foreach($springs as $single){
                                    foreach($curr_springs as $single2){
                                        if($single['id'] == $single2){
                            ?>
                            <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="springs<?php echo $single['id']; ?>">×</span></span>
                            <?php 
                                        }
                                    }
                                }
                            }
                            ?>                    
                        </div>
                    </div>
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>
            </div>

            <div class="row mb-2 pb-05">
                <div class="col-12">
                     <h5 class="mb-1 f-11">TEMPO COUNT <!--<span class="link midGray f-12 normal select_all text-capitalize">Select All</span>--></h5>
                    <div class="dropdown-container machinefirst-box dropdown-wrap">
                        <div id="tempo_container" class="input-container mb-0 dropdown-button noselect">
                            <?php $curr_tempo = (isset($current_tempo) AND $current_tempo != '') ? $current_tempo : array(); ?>
                            <div class="dropdown-label <?php echo (isset($curr_tempo) AND $curr_tempo != '' AND count($curr_tempo) > 0) ? 'black' : ''; ?>"><?php echo (isset($curr_tempo) AND $curr_tempo != '' AND count($curr_tempo) > 0) ? 'Selected (' . count($curr_tempo) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                            <?php
                            // $curr_tempo = (isset($current_tempo) AND $current_tempo != '') ? $current_tempo : array();
                            foreach($tempo as $single){
                            ?>
                            <div class="checkbox mb-15" data-machine="<?php echo $single['machine']; ?>" data-id="<?php echo $single['id']; ?>">
                                <input type="checkbox" class="" name="tempo[]" data-name="<?php echo $single['title']; ?>" id="tempo<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_tempo) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                <label for="tempo<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                            </div>
                            <?php
                            }
                            ?>
                        </div>
                        <div class="dropdown-quantity">
                            <?php 
                            if(count($curr_tempo)){
                                foreach($tempo as $single){
                                    foreach($curr_tempo as $single2){
                                        if($single['id'] == $single2){
                            ?>
                            <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="tempo<?php echo $single['id']; ?>">×</span></span>
                            <?php 
                                        }
                                    }
                                }
                            }
                            ?>                    
                        </div>
                    </div> 
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>
            </div> 

            <h5 class="mb-1 f-11">DIFFICULTY *</h5>
            <?php
                $diff = [];
                foreach($difficulty as $single) {
                    $diff[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="difficulty_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['difficulty']) AND $current['difficulty'] != '') ? 'black' : ''; ?>"><?php echo (isset($current['difficulty']) AND $current['difficulty'] != '') ? $diff[$current['difficulty']] : 'Select'; ?></div>
                </div>
                <div class="col-6 dropdown-list">
                <?php
                $current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
                foreach($difficulty as $single){
                ?>
                    <div class="checkbox mb-15">
                        <input type="radio" class="" name="difficulty" data-name="<?php echo $single['title']; ?>" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    if($current_difficulty > 0){
                        foreach($difficulty as $single){
                            if($single['id'] == $current_difficulty){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="difficulty<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
 
            <h5 class="mb-1 f-11">EXERCISE TYPE</h5>
            <?php
                $exec_type = [];
                foreach($exercise_type as $single) {
                    $exec_type[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="exercise_type_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_exercise_type) AND $current_exercise_type != '' AND count($current_exercise_type) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_exercise_type) AND $current_exercise_type != '' AND count($current_exercise_type) > 0) ? 'Selected (' . count($current_exercise_type) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                <?php
                foreach($exercise_type as $single){
                ?>
                    <div class="checkbox mb-15">
                        <input type="radio" class="" name="exercise_type[]" data-name="<?php echo $single['title']; ?>" id="exercise_type<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_exercise_type) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="exercise_type<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    foreach($exercise_type as $single){
                        foreach($current_exercise_type as $single2){
                            if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="exercise_type<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
 
            <h5 class="mb-1 f-11">BODY POSITION</h5>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="body_position_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_body_position) AND $current_body_position != '' AND count($current_body_position) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_body_position) AND $current_body_position != '' AND count($current_body_position) > 0) ? 'Selected (' . count($current_body_position) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    foreach($body_position as $key => $single){
                    ?>
                    <div class="checkbox w100 mb-15">
                        <input type="checkbox" class="" name="body_position[]" data-name="<?php echo $single['title']; ?>" id="body_position_<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_body_position) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="body_position_<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    if(count($current_body_position)){
                        foreach($body_position as $single){
                            foreach($current_body_position as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="body_position_<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
            
            <h5 class="mb-1 f-11">DIRECTION</h5>
            <?php
                $direc = [];
                foreach($direction as $single) {
                    $exec_type[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="direction_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_direction) AND $current_direction != '' AND count($current_direction) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_direction) AND $current_direction != '' AND count($current_direction) > 0) ? 'Selected (' . count($current_direction) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                <?php
                foreach($direction as $single){
                ?>
                    <div class="checkbox mb-15">
                        <input type="radio" class="" name="direction[]" data-name="<?php echo $single['title']; ?>" id="direction<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_direction) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="direction<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    foreach($direction as $single){
                        foreach($current_direction as $single2){
                            if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="direction<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
 
            <h5 class="mb-1 f-11">TERMINOLOGY</h5>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="terminology_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_terminology) AND $current_terminology != '' AND count($current_terminology) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_terminology) AND $current_terminology != '' AND count($current_terminology) > 0) ? 'Selected (' . count($current_terminology) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <div class="checkbox mb-15">
                        <label class="f-12 custom-toggle-checkbox select_3_terminology">Select Green, Golden, and Silver</label>
                    </div>

                    <?php
                    foreach($terminology as $key => $single){
                    ?>
                    <div class="checkbox w100 mb-15">
                        <input type="checkbox" class="" name="terminology[]" data-name="<?php echo $single['title']; ?>" id="terminology_<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_terminology) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="terminology_<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    if(count($current_terminology)){
                        foreach($terminology as $single){
                            foreach($current_terminology as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="terminology_<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                    ?>                    
                </div>
            </div> 
            
            <h5 class="mb-1 f-11">TRAVEL DISTANCE</h5>
            <?php
                $direc = [];
                foreach($range_of_motion as $single) {
                    $range[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="range_of_motion_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_range_of_motion) AND $current_range_of_motion != '' AND count($current_range_of_motion) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_range_of_motion) AND $current_range_of_motion != '' AND count($current_range_of_motion) > 0) ? 'Selected (' . count($current_direction) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                <?php
                foreach($range_of_motion as $single){
                ?>
                    <div class="checkbox mb-15">
                        <input type="radio" class="" name="range_of_motion[]" data-name="<?php echo $single['title']; ?>" id="range_of_motion<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_range_of_motion) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="range_of_motion<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    foreach($range_of_motion as $single){
                        foreach($current_range_of_motion as $single2){
                            if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="range_of_motion<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
 
            <h5 class="mb-1 f-11">TENSION</h5>
            <?php
                $direc = [];
                foreach($tension as $single) {
                    $ten[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-2 pb-05 dropdown-wrap">
                <div id="tension_container" class="input-container mb-0 dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_tension) AND $current_tension != '' AND count($current_tension) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_tension) AND $current_tension != '' AND count($current_tension) > 0) ? 'Selected (' . count($current_direction) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                <?php
                foreach($tension as $single){
                ?>
                    <div class="checkbox mb-15">
                        <input type="radio" class="" name="tension[]" data-name="<?php echo $single['title']; ?>" id="tension<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $current_tension) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="tension<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
                <div class="dropdown-quantity">
                    <?php 
                    foreach($tension as $single){
                        foreach($current_tension as $single2){
                            if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="tension<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>
 
            <hr class="mt-45 mt-mob-25 mb-45">            
            <div class="row">
                <div class="col-12 bodyparts-wrapper">
                    <h5 class="mb-45 f-14 semibold">BODY PARTS</h5>
                    <div class="top-border pb-05 flex aic gap-3 gap-mob-2 pt-2">
                        <div class="checkbox mb-15 mb-mob-0" id="body_parts_container">
                            <input type="checkbox" class="" id="full_body">
                            <label for="full_body" class="f-14">Full Body</label>
                        </div>
                        <!--<div class="checkbox mb-15 mb-mob-0" id="body_parts_container">
                            <input type="checkbox" class="" id="upper_body">
                            <label for="upper_body" class="f-14">Upper Body</label>
                        </div>
                        <div class="checkbox mb-15 mb-mob-0" id="body_parts_container">
                            <input type="checkbox" class="" id="lower_body">
                            <label for="lower_body" class="f-14">Lower Body</label>
                        </div>-->
                        <div class="checkbox mb-15 mb-mob-0" id="body_parts_container">
                            <input type="checkbox" class="" id="bundle_2">
                            <label for="bundle_2" class="f-14">Upper Body</label>
                        </div>
                        <div class="checkbox mb-15 mb-mob-0" id="body_parts_container">
                            <input type="checkbox" class="" id="bundle_1">
                            <label for="bundle_1" class="f-14">Lower Body</label>
                        </div>
                    </div>
                    <hr class="mt-0 mt-mob-25 mb-5">
                </div>
                <div class="col-12 flex bodyparts-wrapper mb-3 pb-05">
                    <div class="mr-150">
<?php
$curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '') ? $current_body_parts : array();
?>
                        <!-- <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts16" <?php echo in_array(16, $curr_body_parts) ? 'checked' : '' ?> value="16">
                            <label for="body_parts16" class="f-14">Full body</label>
                        </div> -->

<?php
$c=0;
foreach($body_parts as $single){
    if($single['id'] != 16){
        $c++;
?>
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="body_parts_items" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>" data-group="<?php echo $single['parts_group']; ?>">
                            <label for="body_parts<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
        if($c == 6){
            echo '</div><div class="mr-150">';
            $c = 0;
        }
    }
}
?>

                    </div>
                </div>
            </div>
            <hr class="mt-0 mt-mob-25 mb-5">

            <div class="row">
                <div class="col-12">
                    <div class="uploading" style="display: none;">Uploading video. Please wait...</div>
                    <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                    <input type="hidden" name="type" id="type" value="<?php echo isset($current['type']) ? $current['type'] : 0 ?>">
                    <input type="hidden" name="prev_status" id="prev_status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <!-- <input type="hidden" name="created_at" id="created_at" value="<?php // echo isset($current['created_at']) ? $current['created_at'] : 0 ?>"> -->
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="video_encrypted_path" id="video_encrypted_path" value="<?php echo isset($current['video_encrypted_path']) ? $current['video_encrypted_path'] : 0 ?>">
                    <input type="hidden" name="video_preview" id="video_preview" value="<?php echo isset($current['video_preview']) ? $current['video_preview'] : '' ?>">

                    <div class="approve-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? '' : 'style="display: none;"';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">APPROVE AND PUBLISH EXERCISE</button>
                        <button type="button" class="btn btn-wide btn-tall btn-border white-bg black ml-2" data-popup="reject-video" onclick="save_status(3);$('[name=teacher_id]').val($('[name=teacher]:checked').val());">REJECT</button>
                    </div>
    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
        <div class="default-buttons flex aic">
            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
            <a href="/admin/exercises" class="cancel-link ml-2" title="Cancel">Cancel</a>
            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="exercises" data-popup="delete-popup" title="Cancel">DELETE EXERCISE</a>
        </div>
    <?php }else{ ?>
        <div class="default-buttons flex aic">
            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
            <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
            <a href="/admin/exercises" class="cancel-link" title="Cancel">Cancel</a>
        </div>
    <?php } ?>
                    <?php if(isset($prev) OR isset($next)){ ?>
                    <div class="bottom-fixed-buttons" <?php echo ($current['status'] == 2) ? 'style="display: none;"' : '';?>>
                        <?php if(isset($prev)){ ?>
                            <a href="admin/exercises/edit/<?php echo $prev['id']; ?>" class="link link-black black text-underline f-14 mr-2">Previous Exercise</a>
                        <?php } ?>
                        <div class="flex aic jcc">
                        <?php if(($finder_search_term != NULL OR $finder_machine != NULL) AND session('came_from') != NULL AND session('came_from') == 'finder'){ ?>
                            <p class="f-12 p-1 midgray lightGray-bg">Finder (<?php echo $finder_search_term != '' ? 'Search Term: <b>' . $finder_search_term . '</b>' : ''; ?><?php echo ($finder_search_term != '' ? ', ' : '') . 'Machine: <b>' . $list_machines[$finder_machine] . '</b>'; ?>) <a href="javascript:;" class="discard_order link link-midGray text-underline ml-1" onclick="discard_order()" title="">Discard</a></p>
                            <?php } ?>
                            <button type="submit" class="btn btn-xs red-bg white px-3 py-1" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                        </div>
                        <?php if(isset($next)){ ?>
                            <a href="admin/exercises/edit/<?php echo $next['id']; ?>" class="link link-black black text-underline f-14">Next Exercise</a>
                        <?php } ?>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/exercises.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var exercise_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var statuss = 0;
<?php if(isset($current['duration']) AND ($current['duration'] == '' OR $current['duration'] == 'NaN' OR is_numeric($current['duration'])) AND isset($current['id']) AND $current['id'] > 0){ ?>
    setTimeout(function(){
        $('#duration_val').val(($('video').get(0).duration).toFixed(0));
    }, 4500);
<?php } ?>
$('.custom-toggle-checkbox').on('click', function(){
    $(this).toggleClass('checked');
});
$('.select_3_terminology').on('click', function(){
    if($(this).hasClass('checked')){
        $('#terminology_4').attr('checked', 'checked').change();
        $('#terminology_5').attr('checked', 'checked').change();
        $('#terminology_6').attr('checked', 'checked').change();
    }else{
        $('#terminology_4').attr('checked', false).change();
        $('#terminology_5').attr('checked', false).change();
        $('#terminology_6').attr('checked', false).change();
    }
});
$('.select_all_micros').on('click', function(){
    if($(this).hasClass('checked')){
        $('#machine_select1').attr('checked', 'checked').change();
        $('#machine_select14').attr('checked', 'checked').change();
    }else{
        $('#machine_select1').attr('checked', false).change();
        $('#machine_select14').attr('checked', false).change();
    }
});
$('.select_all_minis').on('click', function(){
    if($(this).hasClass('checked')){
        $('#machine_select3').attr('checked', 'checked').change();
        $('#machine_select4').attr('checked', 'checked').change();
    }else{
        $('#machine_select3').attr('checked', false).change();
        $('#machine_select4').attr('checked', false).change();
    }
});
$('.select_all_springs').on('click', function(){
    if($(this).hasClass('checked')){
        $('#springs4').attr('checked', 'checked').change();
        $('#springs5').attr('checked', 'checked').change();
        $('#springs6').attr('checked', 'checked').change();
        $('#springs7').attr('checked', 'checked').change();
        $('#springs8').attr('checked', 'checked').change();
    }else{
        $('#springs4').attr('checked', false).change();
        $('#springs5').attr('checked', false).change();
        $('#springs6').attr('checked', false).change();
        $('#springs7').attr('checked', false).change();
        $('#springs8').attr('checked', false).change();
    }
    $('.select_all_light_springs').removeClass('checked');
    $('.select_all_heavy_springs').removeClass('checked');
});
$('.select_all_light_springs').on('click', function(){
    if($(this).hasClass('checked')){
        $('#springs5').attr('checked', 'checked').change();
        $('#springs7').attr('checked', 'checked').change();
        $('#springs8').attr('checked', 'checked').change();
        $('#springs4').attr('checked', false).change();
        $('#springs6').attr('checked', false).change();
    }else{
        $('#springs5').attr('checked', false).change();
        $('#springs7').attr('checked', false).change();
        $('#springs8').attr('checked', false).change();
        $('#springs4').attr('checked', false).change();
        $('#springs6').attr('checked', false).change();
    }
    $('.select_all_springs').removeClass('checked');
    $('.select_all_heavy_springs').removeClass('checked');

});
$('.select_all_heavy_springs').on('click', function(){
    if($(this).hasClass('checked')){
        $('#springs4').attr('checked', 'checked').change();
        $('#springs6').attr('checked', 'checked').change();
        $('#springs5').attr('checked', false).change();
        $('#springs7').attr('checked', false).change();
        $('#springs8').attr('checked', false).change();
    }else{
        $('#springs4').attr('checked', false).change();
        $('#springs6').attr('checked', false).change();
        $('#springs5').attr('checked', false).change();
        $('#springs7').attr('checked', false).change();
        $('#springs8').attr('checked', false).change();
    }
    $('.select_all_springs').removeClass('checked');
    $('.select_all_light_springs').removeClass('checked');
});
$('.select_all_megas').on('click', function(){
    if($(this).hasClass('checked')){
        $('#machine_select2').attr('checked', 'checked').change();
        $('#machine_select13').attr('checked', 'checked').change();
        $('#machine_select6').attr('checked', 'checked').change();
        $('#machine_select10').attr('checked', 'checked').change();
        $('#machine_select11').attr('checked', 'checked').change();
        $('.all_mega_springs').show();
    }else{
        $('#machine_select2').attr('checked', false).change();
        $('#machine_select13').attr('checked', false).change();
        $('#machine_select6').attr('checked', false).change();
        $('#machine_select10').attr('checked', false).change();
        $('#machine_select11').attr('checked', false).change();
        $('.all_mega_springs').hide();
    }
});
$('.remove_tag').on('click', function(){
    var id = $(this).data('uncheck');
    var container = $(this).closest('.dropdown-container');
    
    container.find('input#' + id).attr('checked', false).prop('checked', false);
    $(this).closest('.dropdown-sel').remove();
    
    setTimeout(function(){
        var num = container.find('input:checked').length;
        console.log(num);
        if(num > 0){
            container.find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
        }else{
            container.find('.dropdown-label').removeClass('black').text('Select');
        }
    }, 50);
    if($('[data-uncheck*="machine_select"]').length == 0){
        reset_machine_dependencies();
    }
});
$(document).on('click', '.remove_tag', function(){
    var id = $(this).data('uncheck');
    var container = $(this).closest('.dropdown-container');

    container.find('input#' + id).attr('checked', false).prop('checked', false);
    $(this).closest('.dropdown-sel').remove();
    
    setTimeout(function(){
        var num = container.find('input:checked').length;
        console.log(num);
        if(num > 0){
            container.find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
        }else{
            container.find('.dropdown-label').removeClass('black').text('Select');
        }
    }, 50);
    if($('[data-uncheck*="machine_select"]').length == 0){
        reset_machine_dependencies();
    }
});
$('#bundle_1').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('#bundle_2').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('.body_parts_items[value="27"]').prop('checked', true);
        $('.body_parts_items[value="7"]').prop('checked', true);
        $('.body_parts_items[value="8"]').prop('checked', true);
        $('.body_parts_items[value="9"]').prop('checked', true);
        $('.body_parts_items[value="10"]').prop('checked', true);
        $('.body_parts_items[value="11"]').prop('checked', true);
        $('.body_parts_items[value="12"]').prop('checked', true);
        $('.body_parts_items[value="13"]').prop('checked', true);
        $('.body_parts_items[value="26"]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});
$('#bundle_2').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('#bundle_1').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){        
        $('.body_parts_items[value="1"]').prop('checked', true);
        $('.body_parts_items[value="2"]').prop('checked', true);
        $('.body_parts_items[value="3"]').prop('checked', true);
        $('.body_parts_items[value="4"]').prop('checked', true);
        $('.body_parts_items[value="5"]').prop('checked', true);
        $('.body_parts_items[value="7"]').prop('checked', true);
        $('.body_parts_items[value="11"]').prop('checked', true);
        $('.body_parts_items[value="12"]').prop('checked', true);
        $('.body_parts_items[value="14"]').prop('checked', true);
        $('.body_parts_items[value="15"]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});
$('#lower_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('#bundle_1').prop('checked', false);
    $('#bundle_2').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="2"]').prop('checked', true);
    }else{
        $('[data-group="2"]').prop('checked', false);
    }
});
$('#upper_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('#bundle_1').prop('checked', false);
    $('#bundle_2').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="1"]').prop('checked', true);
    }else{
        $('[data-group="1"]').prop('checked', false);
    }
});
$('#full_body').on('change', function(e){
    if($(this).is(':checked')){
        $('#lower_body').prop('checked', false);
        $('#upper_body').prop('checked', false);
        $('#bundle_1').prop('checked', false);
        $('#bundle_2').prop('checked', false);
        $('[data-group]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});

function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
$('#reject_class').on('submit', function (e) {
	console.log('reject_class submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
	var formData = form.serialize();
    button.addClass('btn--loading');
	$.ajax({
		type: "POST",
		url: url,
		data: formData,
		dataType: "json",
		success: function(data) {
			console.log(data);
			if (data.success) {
				console.log('SUCCESS');
                app_msg('Video rejected');
                close_all();
                <?php if(isset($pending) AND is_array($pending) AND count($pending) > 1){ ?>
                    window.location = '/admin/exercises/pending';
                <?php }else{ ?>
                    window.location = '/admin/exercises';
                <?php } ?>

                button.removeClass('btn--loading');
			} else {
				console.log('NO SUCCESS');
                app_msg('Something went wrong. Please try again', 'danger');
                button.removeClass('btn--loading');
			}
		},
		error: function(result) {
			console.log('ERROR WITH PHP');
			console.log(result);
            app_msg('Server problem', 'danger');
            button.removeClass('btn--loading');
		}
	});
});
function get_duration(){
    $('#duration').val(($('video').get(0).duration).toFixed(0));
    $('#duration_val').val(($('video').get(0).duration).toFixed(0));
    console.log('DURATION LOADED');
}
function discard_order(){
    $.ajax({
        type: 'POST',
        url: 'admin/exercises/discard_order',
        data: 'neki_data',
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                window.location.reload();
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
</script>


<script>//Checkboxes inside dropdwon
const $dropdown = $('.dropdown-container'); // Cache all;

function UI_dropdown() {

  const $this = $(this);
  const $btn = $('.dropdown-button', this);
  const $list = $('.dropdown-list', this);
  const $li = $('li', this);
  const $search = $('.dropdown-search', this);
  const $ckb = $(':checkbox, :radio', this);
  const $qty = $('.dropdown-quantity', this);


  $btn.on('click', function() {
    $dropdown.not($this).removeClass('is-active'); // Close other
    $this.toggleClass('is-active'); // Toggle this
  });

  $search.on('input', function() {
    const val = $(this).val().trim();
    const rgx = new RegExp(val, 'i');
    $li.each(function() {
      const name = $(this).text().trim();
      $(this).toggleClass('is-hidden', !rgx.test(name));
    });
  });

  $ckb.on('change', function(elem) {
    const names = $ckb.get().filter(el => el.checked).map(el => {
      return `<span class="dropdown-sel">${el.dataset.name.trim()}<span class="remove_tag" data-uncheck="${el.id.trim()}">×</span></span>`;
    });
    var num = $(elem.target).closest('.dropdown-container').find(':checked').length;
    setTimeout(function(){
        if(num > 0){
            $(elem.target).closest('.dropdown-container').find('.input-container').removeClass('has-error');
            $(elem.target).closest('.dropdown-container').find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
        }else{
            $(elem.target).closest('.dropdown-container').find('.input-container').addClass('has-error');
            $(elem.target).closest('.dropdown-container').find('.dropdown-label').removeClass('black').text('Select');
        }
    }, 50);
    $qty.html(names.join(''));
    select_machine();
  });
}

$dropdown.each(UI_dropdown); // Apply logic to all dropdowns

// Dropdown - Close opened 
$(document).on('click', function(ev) {
  const $targ = $(ev.target).closest('.dropdown-container');
  if (!$targ.length) $dropdown.filter('.is-active').removeClass('is-active');
});
</script>

<script>//**************SHOW-HIDE***************
$(document).ready(function() {  //ADDITIONAL DESCRIPTION TEXTAREA
    $('#check-desc').click(function() {
        $('.add-desc').toggle();
    });   
    $('.hidethumb').click(function() { //CUSTOM THUMBNAIL SECTION
        $('.thumb-box').toggleClass('showdiv');
    });   
});
$('.hidethumb').click(function() { //CUSTOM THUMBNAIL SECTION
    $(this).parent().next().slideToggle();
    $(this).text() == 'SHOW' ? $(this).text('HIDE') : $(this).text('SHOW');
});
</script>
</body>
</html>
