<?php
if(isset($invoice['invoice']['lines']['data'][1])){
    if(abs($invoice['invoice']['lines']['data'][1]['amount']) == 9999){
        $plan = 'Annual';
    }else if(abs($invoice['invoice']['lines']['data'][1]['amount']) == 999){
        $plan = 'Monthly';
    }else if(abs($invoice['invoice']['lines']['data'][1]['amount']) == 399){
        $plan = 'Weekly';
    }
}else{
    if(abs($invoice['invoice']['lines']['data'][0]['amount']) == 9999){
        $plan = 'Annual';
    }else if(abs($invoice['invoice']['lines']['data'][0]['amount']) == 999){
        $plan = 'Monthly';
    }else if(abs($invoice['invoice']['lines']['data'][0]['amount']) == 399){
        $plan = 'Weekly';
    }
}
?>
<meta charset="UTF-8">
<title></title>
<body>
<table style="width: 100%;font-family: Helvetica" bgcolor="#fff" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td>
            <table style="width: 700px;margin: 0 auto 20px" border="0" cellpadding="0" cellspacing="0">
                <tr>
                    <td colspan="2" style="padding: 20px 0; text-align: center">
                        <img src="https://www.lagreeod.com/images/email-logo.png" alt="" class="img-fluid" width="250" />
                    </td>
                </tr>
                <tr>
                    <td colspan=2>
                        <table style="width: 100%" bgcolor="#FFFFFF" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="padding: 20px 40px 40px;text-align: center">
                                    <h1 style="font-size: 22px;font-weight: bold;margin-bottom: 30px;">Receipt <span style="color: #871B1E">#<?php echo $invoice['invoice']['number']; ?></span> from Lagree On Demand</h1>
                                    <p style="margin-bottom: 60px; font-size: 18px;">Thanks for using Lagree On Demand. Below is your receipt.</p>
                                    <p style="width: 500px; border-bottom: 1px solid #E5E5E5; margin: 60px auto 40px"></p>
                                    <table style="width: 100%;margin: 0 auto 40px;font-size: 14px;" border="0" cellpadding="0" cellspacing="0">
                                        <tr>
                                            <td style="width: 40%;padding: 5px"><strong>Subscription plan</strong></td>
                                            <td style="width: 60%;padding: 5px">
                                                <?php echo $plan ?>
                                                 -
                                                $<?php echo (abs($invoice['invoice']['lines']['data'][0]['amount']) / 100); ?><?php echo $plan == 'Monthly' ? '/mo' : ''; ?>
                                            </td>
                                        </tr>
                                        <?php foreach($invoice['invoice']['lines']['data'] as $amount){ ?>
                                        <tr>
                                            <td style="width: 40%;padding: 5px"><strong>Amount</strong></td>
                                            <td style="width: 60%;padding: 5px">
                                                <?php echo $amount['amount'] < 0 ? '-$' . abs($amount['amount'] / 100) : '$' . abs($amount['amount'] / 100); ?><?php echo $plan == 'Monthly' ? '/mo' : ''; ?><br>
                                                <small style="color: #888"><?php echo $amount['description']; ?></small>
                                            </td>
                                        </tr>
                                        <?php } ?>
                                        <tr>
                                            <td style="width: 40%;padding: 5px"><strong>Transaction date</strong></td>
                                            <td style="width: 60%;padding: 5px"><?php echo date('m/d/Y', $invoice['invoice']['created']); ?></td>
                                        </tr>
                                        <?php if(isset($charge)){ ?>
                                        <tr>
                                            <td style="width: 40%;padding: 5px"><strong>Payment Method</strong></td>
                                            <td style="width: 60%;padding: 5px;text-transform: uppercase"><?php echo isset($charge) ? $charge['charge']['payment_method_details']['card']['network'] . ' - ' : ''; ?><?php echo isset($charge) ? $charge['charge']['payment_method_details']['card']['last4'] : ''; ?></td>
                                        </tr>
                                        <?php } ?>
                                    </table>
                                    <table style="background: #F8F8F8;width: 100%;padding: 15px;line-height: 1.6;margin: 0 auto 40px;font-size: 14px;" cellspacing="0" cellpadding="0" border="0">
                                        <tbody>
                                            <tr>
                                                <td style="width: 80%;padding: 5px;line-height: 1.5;">
                                                    <?php echo $plan; ?> Subscription for Lagree On Demand<br>
                                                    <small style="font-size: 11px;display: block;margin-top: 5px;">
                                                        PERIOD: <?php echo date('m/d/Y', $invoice['invoice']['lines']['data'][0]['period']['start']); ?> - <?php echo date('m/d/Y', $invoice['invoice']['lines']['data'][0]['period']['end']); ?>
                                                    </small>
                                                </td>
                                                <td style="width: 20%;padding: 5px;text-align: right;">
                                                    <strong><span style="color: #871B1E;font-size: 16px;">
                                                    <?php echo $invoice['invoice']['total'] < 0 ? '-$' . number_format(abs($invoice['invoice']['total'] / 100), 2) : '$' . number_format(abs($invoice['invoice']['total'] / 100), 2); ?></span></strong>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <p style="text-align: left;font-size: 12px;line-height: 20px">Next payment will be made on: <?php echo date('m/d/Y', $invoice['invoice']['lines']['data'][0]['period']['end']); ?>.</p>
                                    <!-- <p style="text-align: left;font-size: 12px;line-height: 20px">Please find your invoice attached (in PDF format) to this email.</p> -->
                                    <p style="width: 500px; border-bottom: 1px solid #E5E5E5; margin: 60px auto 40px"></p>
                                    <!-- <p style="font-size: 12px;line-height: 20px">If you need help with your subscription, please <a href="<?php echo base_url() . '/contact-us'; ?>" style="color: #871B1E">click here</a> to contact us.</p> -->
                                    <p style="width: 500px; border-bottom: 1px solid #E5E5E5; margin: 40px auto 40px"></p>
                                    <p style="font-size: 12px;color: #969696;line-height: 20px">Lagree On Demand is a part of Lagree Fitness family, based on Lagree Method, one of the fastest growing and hottest workouts in the fitness industry.</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>