<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class LiveEvents extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('LiveEventsModel');
        $controller = 'liveevents';
	}

    public function index()
    {
        $LiveEventsSubscribersModel = model('LiveEventsSubscribersModel');
        $liveevents_views_model = model('LiveEventsViewModel');
        $collections_model = model('CollectionsModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['next_liveevent'] = $this->model->incoming_liveevents(0,1);
        // if(isset($data['next_liveevent']) AND isset($data['next_liveevent']['all_class_machines'])){
        //     print_r($data['next_liveevent']['all_class_machines']);
        //     die();
        // }
        $data['is_past'] = FALSE;

        $data['signed_users'] = 0;
        if(count($data['next_liveevent']) > 0){
            $data['signed_users'] = $LiveEventsSubscribersModel->where('liveevents_id', $data['next_liveevent'][0]['id'])->countAllResults();
            $data['signed_for_event'] = $LiveEventsSubscribersModel->where(["liveevents_id" => $data['next_liveevent'][0]['id'], "subscriber_id" => $data['logged_user']['id']])->first();
        }
        if(count($data['next_liveevent']) <= 1 OR $data['next_liveevent'][0]['id'] == NULL){
            $data['next_liveevent'] = $this->model->past_liveevents(0,1);
            $data['is_past'] = TRUE;
        }
        $data['past_liveevents'] = $this->model->past_liveevents();

		$data['current']['image'] = base_url() . 'images/liveevents1.jpg';
		$data['current']['seo_title'] = 'Live Micro and Mega Events';
		$data['current']['seo_description'] = 'Get access to Micro and Mega live events powered by Lagree Fitness.';
		echo view('front/liveevents/index_view', $data);
    }

    public function slug($slug = '')
    {
        helper('text');
        $LiveEventsViewModel = model('LiveEventsViewModel');
        $LiveEventsRate_model = model('LiveEventsRateModel');
		$data['logged_user'] = $this->user;
        // if(!isset($data['logged_user'])){
        //     return redirect()->to('/subscribe');
        // }
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['id'] = $this->get_id_from_slug('liveevents', $slug);
        $save_pageview = array('liveevents_id' => $data['id'], 'date' => date('Y-m-d'));
        $saved = $LiveEventsViewModel->save($save_pageview);

        $data['current'] = $this->model->current($slug);

        $data['past_liveevents'] = $this->model->past_liveevents();

        $data['rated'] = $LiveEventsRate_model->where(['liveevents_id' => $data['current']['id'], 'user_id' => $data['logged_user']['id']])->find();

        // if(isset($data['current']['time']) AND $data['current']['time'] != ''){
        //     $exp_time = explode(":", $data['current']['time']);
        //     $time = $exp_time[0]*60*60;
        //     $data['time'] = $exp_time[0]*60*60;
        // }
        // if(isset($data['current']['date']) AND $data['current']['date'] != ''){
        //     $data['event_time'] = date('Y-m-d H:i:s', strtotime($data['current']['date']) + $time);
        // }
        // $data['current_time'] = date('Y-m-d H:i:s');

        // $data['started'] = $data['current_time'] < $data['event_time'] ? FALSE : TRUE;

        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/liveevents/single_view', $data);
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('liveevents_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($save_favs)->delete();
        }

		return $this->respond($response);
    }

    public function subscribe_to_event()
    {
        $liveevents_subscribers_model = model('LiveEventsSubscribersModel');
		$request = service('request');
        $data = $request->getPost();
        $save_subscriber = array('liveevents_id' => $data['liveevents_id'], 'subscriber_id' => $data['subscriber_id'], 'date' => date('Y-m-d'));

        $response['signed_event'] = $liveevents_subscribers_model->where(["liveevents_id" => $data['liveevents_id'], "subscriber_id" => $data['subscriber_id']])->first();

        $response['success'] = FALSE;
        if(empty($response['signed_event'])){
            $response['success'] = $liveevents_subscribers_model->save($save_subscriber);
        }else{
            $response['success'] = FALSE;
            $response['msg'] = 'Already signed for this event';
        }
        $response['data'] = $data;

		return $this->respond($response);
    }

    public function rate_class()
    {
        $LiveEventsRate_model = model('LiveEventsRateModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('liveevents_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['favs'] = $LiveEventsRate_model->where(["liveevents_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $LiveEventsRate_model->save($save_rate);
            $response['success'] = TRUE;
        }

		return $this->respond($response);
    }

    public function get_id_from_slug($table, $slug = 0)
	{
        $currentModel = model($table . 'Model');

		$response = $currentModel->where(['slug' => $slug])->first();

        return $response['id'];
	}
}