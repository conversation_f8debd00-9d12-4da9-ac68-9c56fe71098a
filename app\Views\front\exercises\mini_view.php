<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="exercises-page" onload="filter_exercises()">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="p-10">
        <div class="container-fluid">
            <div class="row">
                <div class="col-6">
                    <h2 class="mb-4 h2">Mini Exercises</h2>
                    <h3 class="f-16 light">We have made the core exercises for your machine(s), follow these instructions and enjoy exercising on your own!</h3>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 px-100">
        <div class="container-fluid">
            <div class="row aic flex-row">
                <div class="col-12 text-right">
                    <div class="dropdown d-inline-block">
                        <span class="f-12 midGray desktop-inline">Sort by:</span>
                        <span class="f-12 dropdown-button" data-dropdown>Date Added <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by" data-sort="exercises.created_at/desc" title="">Date Added</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by" data-sort="exercises.title/asc" title="">Ascending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by" data-sort="exercises.title/desc" title="">Descending</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by" data-sort="countView/desc" title="">Popularity</a></li>
                            <li><a href="javascript:;" class="link link-darkGray darkGray sort_by" data-sort="classRate/desc" title="">Best Rated</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <hr class="my-5 mobile">
        <div class="container-fluid mt-5">
            <div class="row big-big-gap products_list"></div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="midGray load_more">Loading More...</h3>
                </div>
            </div>
        </div>
    </section>
</main>

<input type="hidden" name="type[]" class="select-class-type">
<input type="hidden" name="machine[]" class="select-machines" value="[3]">
<input type="hidden" name="duration[]" class="select-duration" >
<input type="hidden" name="difficulty[]" class="select-difficulty">
<input type="hidden" name="language[]" class="select-language">
<!-- <input type="hidden" name="teacher[]" class="select-teacher"> -->
<input type="hidden" name="body_parts[]" class="select-body_parts">

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
<script src="js/exercises.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>