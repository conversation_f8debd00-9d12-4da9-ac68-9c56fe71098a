<?php namespace App\Models;

use CodeIgniter\Model;

class ClassesRateModel extends Model
{
    protected $table = 'classes_rate';
	protected $allowedFields = ['class_id', 'date', 'rate', 'user_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}