<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" />
</head>
<body class="account-page logged dashboard-page">

<?php echo view('front/templates/header.php'); ?>
<main id="site-root">
    <div class="announcement_bar" style="background: <?php echo $settings['announcement_color']; ?>; display: none;">
        <?php echo $settings['announcement_bar']; ?>
        <span class="close_announcement">×</span>
    </div>
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white semibold text-uppercase pb-1 mb-05 mb-mob-0">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
        <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                            DASHBOARD
                        </h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12 py-4 py-mob-2 flex aic jcsb border px-4 px-mob-2 radius-10 radius-mob-6">
                    <div class="flex flex-column">
                        <p class="f-14 normal desktop ">Ask Sebastien directly anything related to Lagree method.</p>
                        <p class="f-12 normal mobile">Ask Sebastien directly.</p>
                    </div>
                    <p><a href="/asksebastien" class="btn black-bg white f-12 h-42 semibold" title="ASK SEB">ASK SEB</a></p>
                </div>
            </div>

           <!-- <div class="row ail nowrap">
<?php
if($featured_videos[0]['title'] != '' OR $featured_videos[1]['title'] != '' OR $featured_videos[2]['title'] != ''){
?>
                <div class="col-12 py-5 bottom-border" style="width: 100%">
                    <h3 class="mb-5 f-14 semibold text-uppercase line-height-small mb-mob-4">FEATURED VIDEOS</h3>
                    <div class="row big-gap featured-row pt-05">
<?php
foreach($featured_videos as $key => $row){
    if(isset($row['title'])){
?>
                        <div class="col-4">
                            <div class="single-video-item mb-0 mb-mob-1 single-ajax-class <?php echo $row['type'] == 'classes' ? 'classes' : 'videos'; ?>">
                                <a href="<?php echo $row['type'] == 'classes' ? 'classes' : 'videos'; ?>/<?php echo (isset($row['slug']) AND $row['slug'] != '') ? $row['slug'] : ''; ?>" class="">
                                    <div class="video-container">
                                        <span class="play-button"><span></span></span>
                                        <div class="image-overlay h100" <?php echo ((isset($row['video_preview']) AND $row['video_preview'] != "")) ? 'style="background: url(' . ((isset($row['image']) AND $row['image'] != '') ? $row['image'] : ((isset($row['video_thumb']) AND $row['video_thumb'] != '') ? $row['video_thumb'] : '')) . ') no-repeat center center / cover"' : ''; ?> <?php echo (isset($row['watched']) AND $row['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?>>
                                            <img src="<?php echo (isset($row['image']) AND $row['image'] != '') ? $row['image'] : ((isset($row['video_thumb']) AND $row['video_thumb'] != '') ? $row['video_thumb'] : ''); ?>" alt="<?php echo (isset($row['title']) AND $row['title'] != '') ? $row['title'] : ''; ?>" class="img-fluid" />
                                        </div>
                                    </div>
                                    <p class="f-12 text-left"><?php echo (isset($row['title']) AND $row['title'] != '') ? $row['title'] : ''; ?></p>
                                </a>
                            </div>
                        </div>
<?php
    }
}
?>
                    </div>
                </div>
<?php
}
?>
                </div>-->
                <div class="row">
                    <div class="col-12">
                    <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">MY STATISTICS</h3>
                        <div class="dashboard-panel border px-4 px-mob-2 py-3 py-mob-15 radius-10 radius-mob-6">
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Total Class Views</span>
                                <span class="lh-30 midGray"><?php echo $my_views; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Last Month</span>
                                <span class="lh-30 midGray"><?php echo $my_views_last_month; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">This Week (<?php echo date("m/d", strtotime("-1 week")); ?> - <?php echo date("m/d"); ?>)</span>
                                <span class="lh-30 midGray"><?php echo $my_views_this_week; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Today</span>
                                <span class="lh-30 midGray"><?php echo $my_views_today; ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                    <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">PLATFORM STATISTICS</h3>
                        <div class="dashboard-panel border px-4 px-mob-2 py-3 py-mob-15 radius-10 radius-mob-6">
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Classes</span>
                                <span class="lh-30 midGray"><?php echo $all_classes; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Exercises</span>
                                <span class="lh-30 midGray"><?php echo $all_exercises; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Collections</span>
                                <span class="lh-30 midGray"><?php echo $all_collections; ?></span>
                            </div>
                            <div class="flex aic jcsb f-14 f-12-mob dashboard-panel-item">
                                <span class="lh-30">Live Events</span>
                                <span class="lh-30 midGray"><?php echo $all_liveevents; ?></span>
                            </div>
                        </div>
                    </div>
                 </div>
            </div>
        </div>
        <div class="mobilespacer mobile" style="height:50px;"></div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/money-input.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/cookie.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/swiper-bundle.min.js"></script>
<?php
if($settings['announcement_show'] == 1){
    // if(!isset($_COOKIE[$settings['announcement_cookie']])) {
?>
<script>
    $('.close_announcement').on('click', function(){
        createCookie('<?php echo $settings['announcement_cookie']; ?>', 'opened', 1);
        $('.announcement_bar').hide();
    });
    console.log(readCookie('<?php echo $settings['announcement_cookie']; ?>'));
    if(readCookie('<?php echo $settings['announcement_cookie']; ?>') === null){
        document.querySelector('.announcement_bar').style.display = 'block';
        console.log('RADI COOKIE');
    }
</script>
<?php
    // }
}
?>
</body>
</html>