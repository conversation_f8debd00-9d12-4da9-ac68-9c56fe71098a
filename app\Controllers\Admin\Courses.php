<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Courses extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CoursesModel');

        $db = \Config\Database::connect();
		$this->machines = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT course_machine, count(*) AS cnt FROM courses_videos_machine GROUP BY course_machine) x ON x.course_machine = machines.id
                                            HAVING countMachine > 0
                                      ')->getResultArray();
		$this->body_parts = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT course_body_parts, count(*) AS cnt FROM courses_videos_body_parts GROUP BY course_body_parts) x ON x.course_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                            ORDER BY body_parts.title asc
                                        ')->getResultArray();
        $this->all_teachers = $db->query('SELECT *, COALESCE(x.cnt,0) AS countCourses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM courses WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            HAVING countCourses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();

	}

    public function index()
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['courses_sort'] = $this->courses_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = $this->machines;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('courses', $search_term);
            if($search_term == ""){
                $this->set_search('courses', '0');
                return redirect()->to('/admin/courses');
            }
        }else if(session('courses_search') !== "" AND session('courses_search') !== "0"){
            $search_term = session('courses_search');
        }else{
            $search_term = "0";
        };


        $data['page'] = 1;
        $page = 1;
        $data['search_term'] = $search_term;
        if(session('courses_sort') != ''){
            $ss = sort_name(session('courses_sort'));
            $sss = session('courses_sort');
        }else{
            $ss = sort_name('courses.title desc');
            $sss = 'courses.title desc';
        }
        $data['sort_by'] = $ss;
        $data['all_courses'] = $this->model->all_courses(($page * session('courses_per_page')) - session('courses_per_page'), session('courses_per_page'), $search_term, $sss);
        $data['courses_count'] = count($this->model->all_courses(0, 10000, $search_term, $sss));

        echo view('admin/courses/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['courses_sort'] = $this->courses_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = $this->machines;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('courses', $search_term);
            if($search_term == ""){
                $this->set_search('courses', '0');
                return redirect()->to('/admin/courses/page/' . $page);
            }
        }else if(session('courses_search') !== "" AND session('courses_search') !== 0){
            $search_term = session('courses_search');
        }else{
            $search_term = 0;
        };

        if(!empty($post)){
            // $this->set_filter($post);
            $session->set('courses_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('courses_filter');
            // $session->set('courses_filter', '');
        }

        $data['search_term'] = $search_term;
        $data['sort_by'] = "Date Added";
        $data['all_courses'] = $this->model->filter_courses_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, 'courses.created_at', session('courses_filter'));
        $data['courses_count'] = count($this->model->filter_courses_admin(0, 10000, $search_term, 'courses.created_at', session('courses_filter')));
        $data['page'] = $page;

        echo view('admin/courses/filter_view', $data);
    }

    public function filter($page = 1)
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['courses_sort'] = $this->courses_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = $this->machines;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        // $data['order'] =  isset($post['order']) ? $post['order'] : 'courses.created_at';
        // $data['search'] =  isset($post['search']) ? $post['search'] : '';

        // echo "<pre>";
        // print_r($post);
        // die();

        if(isset($post['search_term'])){
            $search_term = $post['search_term'];
            $this->set_search('courses', $search_term);
            if($search_term == ""){
                $this->set_search('courses', '0');
                return redirect()->to('/admin/courses/filter/');
            }
        }else if(session('courses_search') !== "" AND session('courses_search') !== 0){
            $search_term = session('courses_search');
        }else{
            $search_term = 0;
        };

        $data['filter'] = $post;

        if(isset($post)){
            $session->set('courses_filter', $post);
        }

        // $search_term = isset($_GET['search_term']) ? $_GET['search_term'] : NULL;
        $data['search_term'] = $search_term;
        $data['sort_by'] = "Date Added";
        $data['order'] =  'courses.created_at';
        $data['all_courses'] = $this->model->filter_courses_admin(($page * session('courses_per_page')) - session('courses_per_page'), session('courses_per_page'), $data['search_term'] == 0 ? NULL : $data['search_term'], $data['order'], $data['filter']);
        $data['courses_count'] = count($this->model->filter_courses_admin(0, 10000, $search_term, 'courses.created_at', session('courses_filter')));
        $data['page'] = $page;

        // echo '<pre>';
        // print_r($data);
        // echo '</pre>';
        echo view('admin/courses/filter_view', $data);
    }

    public function datatable()
    {
		$post = $this->request->getPost();
		$draw = $post['draw'];
		$row = $post['start'];

		$rowperpage = $post['length']; // Rows display per page
		$columnIndex = $post['order'][0]['column']; // Column index
		$columnName = $post['columns'][$columnIndex]['data']; // Column name
		$columnSortOrder = $post['order'][0]['dir']; // asc or desc
		$searchValue = $post['search']['value']; // Search value


		$db      = \Config\Database::connect();
		$sql = "SELECT
			courses.*,
			IF(courses.deleted_at IS NULL, 0, 1) as deleted
			FROM courses
			WHERE 1 ";
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecords = count($data);

		if (session()->has('filter_table'))
		{
			$filter = session()->filter_table;
			if(isset($filter['vreme']) AND $filter['vreme'] <> ''){
				$sql .= " AND users.created_at > '" . $filter['vreme'] . "'";
			}
			if(isset($filter['vrsta_potvrde']) AND $filter['vrsta_potvrde'] <> ''){
				$sql .= " AND users.vrsta_potvrde = '" . $filter['vrsta_potvrde'] . "'";
			}
		}
		if($searchValue != ''){
			$sql .= " AND (
				courses.id LIKE '%" . $searchValue . "%'
				OR courses.title LIKE '%" . $searchValue . "%'
				) ";
		}
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecordwithFilter = count($data);
		$sql .= " ORDER BY " . $columnName . " " . $columnSortOrder;
		$sql .= " LIMIT " . $row . "," . $rowperpage;
		$query = $db->query($sql);
		$data_final = $query->getResult();
		$response = array(
		  "mrnj" => $sql,
		  "draw" => intval($draw),
		  "iTotalRecords" => $totalRecords,
		  "iTotalDisplayRecords" => $totalRecordwithFilter,
		  "aaData" => $data_final
		);
		echo json_encode($response, JSON_PRETTY_PRINT);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['courses_sort'] = $this->courses_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();
		$current_machines = $db->query("SELECT * FROM courses_machine WHERE course_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['course_machine'] ;
            }
        }else{
            $data['current_machines'] = array();
        }
		$current_body_parts = $db->query("SELECT * FROM courses_videos_body_parts WHERE course_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['course_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = array();
        }
		$current_accessories = $db->query("SELECT * FROM courses_videos_accessories WHERE course_id = " . $edit_id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['course_accessories'] ;
            }
        }else{
            $data['current_accessories'] = array();
        }
		$current_springs = $db->query("SELECT * FROM courses_videos_springs WHERE course_id = " . $edit_id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['course_springs'] ;
            }
        }else{
            $data['current_springs'] = array();
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/courses');
        };
        $data['all_courses'] = $this->model->all_courses(0, 0, session('courses_search'), 'courses.created_at', "0, 1");
        // $data['all_exercises'] = $exercises_model->all_exercises(0,0);
        $data['all_course_videos'] = $this->model->all_course_videos($edit_id);

        $total = count($data['all_courses']);
        $index_list = array_column($data['all_courses'], 'id');
        $index_id = array_search($edit_id, array_column($data['all_courses'], 'id'));
        if($index_id !== FALSE)
        {
            if($index_id < $total - 1){
                $data['prev'] = $this->model->where('id', $index_list[$index_id + 1])->first();
            }else{
                $data['prev'] = $this->model->where('id', $index_list[0])->first();
            }
            if($index_id > 0){
                $data['next'] = $this->model->where('id', $index_list[$index_id - 1])->first();
            }else{
                $data['next'] = $this->model->where('id', $index_list[$total - 1])->first();
            }
        }

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/courses/edit_view', $data);
    }

    public function edit_bulk()
    {
        $data = $this->request->getPost();
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // echo '<pre>';
        // print_r(explode(',', ($data['ids'])));
        // echo '</pre>';
        // die();
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
        $data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
        $data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
        $data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();

        $ids = explode(',', ($data['ids']));
        foreach($ids as $key => $id){
            $data['current_courses'][] = $this->model->where(['id' => $id])->first();
            $current_machines = $db->query("SELECT * FROM courses_videos_machine WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_machines)){
                foreach($current_machines as $k => $single){
                    $data['current_machines'][$id][] = $single['course_machine'] ;
                }
            }else{
                $data['current_machines'][$id] = array();
            }
            $current_body_parts = $db->query("SELECT * FROM courses_videos_body_parts WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_body_parts)){
                foreach($current_body_parts as $k => $single){
                    $data['current_body_parts'][$id][] = $single['course_body_parts'] ;
                }
            }else{
                $data['current_body_parts'][$id] = array();
            }
            $current_accessories = $db->query("SELECT * FROM courses_videos_accessories WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_accessories)){
                foreach($current_accessories as $k => $single){
                    $data['current_accessories'][$id][] = $single['course_accessories'] ;
                }
            }else{
                $data['current_accessories'][$id] = array();
            }
            $current_tensions = $db->query("SELECT * FROM courses_tensions WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_tensions)){
                foreach($current_tensions as $k => $single){
                    $data['current_tensions'][$id][] = $single['course_tensions'] ;
                }
            }else{
                $data['current_tensions'][$id] = array();
            }
            $current_springs = $db->query("SELECT * FROM courses_videos_springs WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_springs)){
                foreach($current_springs as $k => $single){
                    $data['current_springs'][$id][] = $single['course_springs'] ;
                }
            }else{
                $data['current_springs'][$id] = array();
            }
            $current_tempo = $db->query("SELECT * FROM courses_tempo WHERE course_id = " . $id)->getResultArray();
            if(!empty($current_tempo)){
                foreach($current_tempo as $k => $single){
                    $data['current_tempo'][$id][] = $single['course_tempo'] ;
                }
            }else{
                $data['current_tempo'][$id] = array();
            }
        }

		// echo '<pre>';
		// print_r($data);
		// die();

		return view('admin/courses/multi_edit_view', $data);
    }

    public function multi()
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		return view('admin/courses/multi_view', $data);
    }

    public function clear_search()
    {
        $this->set_search('courses', "0");
		return redirect('admin/courses');
    }

    public function save_batch()
    {
		$data = $this->request->getPost();
        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('courses');
                foreach($single_field as $k => $v){
                    $fields[$k][$key] = $v;
                }
            }
        }
        if (count($fields) > 0){
            $builder->insertBatch($fields);
        }
		return redirect('admin/courses');
    }

    public function save_video_item()
    {
        $CoursesVideosModel = model('CoursesVideosModel');
		$request = service('request');
        $data = $request->getPost();

        // $save_video = [
        //     'class_id' => $data['class'],
        //     'user_id' => $data['user'],
        //     'date' => date('Y-m-d')
        // ];

        $response['success'] = $CoursesVideosModel->save($data);
        $response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $CoursesVideosModel->getInsertID();
        $response['new_item'] = $data['id'] > 0 ? FALSE : TRUE;

        !isset($data['accessories']) ? $data['accessories'] = [] : '';
        !isset($data['springs']) ? $data['springs'] = [] : '';
        !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
        !isset($data['machine']) ? $data['machine'] = [] : '';

        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('courses_videos_' . $key);
                $builder->delete(['course_id' => $data['id']]);
                $fields = array();
                foreach($single_field as $k => $v){
                    $fields[] = array(
                        'course_id' => $response['inserted_id'],
                        'course_' . $key => $v,
                        'date' => date('Y-m-d'),
                    );
                }
                if (count($fields) > 0){
                    $builder->insertBatch($fields);
                }
            }
        }

        if($response['success']){
            $response['course_video_item'] = $this->get_course_video_item($response['inserted_id']);
        }

		return $this->respond($response);
    }


    public function save()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $SubscribersModel = model('SubscribersModel');
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$data = $this->request->getPost();
		$validation->reset();
        if($data['status'] == 1){
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[courses.slug,id,{id}]',
            ];
        }else{
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[courses.slug,id,{id}]',
                // 'machine'       => 'required',
                'teacher'       => 'required',
            ];
        }
        $response['rules'] = $rules;
        $validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Course successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
                $file->move(ROOTPATH . 'public/uploads/courses', $name);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/courses/' . $name)
				// 	->resize(750, 410, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/courses/' . $name, 90);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/courses/' . $name)
				// 	->resize(510, 300, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/courses/thumb_' . $name, 100);

                $data['image'] = 'uploads/courses/' . $name;
                // $data['image_small'] = 'uploads/courses/thumb_' . $name;
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/courses', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/courses/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/courses/' . $name, 98);
				$data['cover_image'] = 'uploads/courses/' . $name;
			}

            if(isset($data['image_removed']) AND $data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if(isset($data['cover_image_removed']) AND $data['cover_image_removed'] == 1){
                $data['cover_image'] = "";
            }
            unset($data['cover_image_removed']);

			$response['validation'] = $validation->getErrors();
			if($this->model->save($data)){
                $response['success'] = TRUE;
            }else{
                $response['success'] = $this->model->errors();
            }
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('courses_' . $key);
                    $builder->delete(['course_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'course_id' => $response['inserted_id'],
                            'course_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

            // if($data['id'] == 0 OR $data['prev_status'] != 2){
            //     $notification_data = array(
            //         'content'   => '<span course="text-underline">' . $data['title'] . '</span> course is uploaded.',
            //         'link'      => base_url() . '/courses/' . $data['slug'],
            //         'author'    => 'system',
            //         'date'    => date('Y-m-d H:i:s')
            //     );
            //     $response['notification_saved'] = $NotificationsModel->save($notification_data);
            // }

		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function get_course_video_item_ajax($id = 0, $course_id = 0)
    {
        $teachers_model = model('TeachersModel');

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();
		$current_machines = $db->query("SELECT * FROM courses_videos_machine WHERE course_id = " . $id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['course_machine'] ;
            }
        }else{
            $data['current_machines'] = array();
        }
		$current_body_parts = $db->query("SELECT * FROM courses_videos_body_parts WHERE course_id = " . $id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['course_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = array();
        }
		$current_accessories = $db->query("SELECT * FROM courses_videos_accessories WHERE course_id = " . $id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['course_accessories'] ;
            }
        }else{
            $data['current_accessories'] = array();
        }
		$current_springs = $db->query("SELECT * FROM courses_videos_springs WHERE course_id = " . $id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['course_springs'] ;
            }
        }else{
            $data['current_springs'] = array();
        }

        $CoursesVideosModel = model('CoursesVideosModel');
        if($id != 0){
            $result = $CoursesVideosModel->where(['id' => $id])->first();
            if($result['type'] == 'video'){
                $data['course'] = $result;
                $result['html'] = view('admin/courses/video_upload_view', $data);
            }
        }else{
            $data['course'] = [];
            $data['course']['course_id'] = $course_id;
            $result['html'] = view('admin/courses/video_upload_view', $data);
        }

        return $this->respond($result);
    }

    public function get_course_video_item($id = 0){
        $CoursesVideosModel = model('CoursesVideosModel');
        if($id != 0){
            $result = $CoursesVideosModel->where(['id' => $id])->first();
        }

        return $result;
    }

    public function save_video_course()
    {
        $CoursesVideosModel = model('CoursesVideosModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['success'] = $CoursesVideosModel->save($data);
        $result['course_video_id'] = $CoursesVideosModel->getInsertID();
        $result['inserted_id'] = $data['id'] > 0 ? $data['id'] : $CoursesVideosModel->getInsertID();

        if($result['success']){
            $result['course_video_item'] = $this->get_course_video_item($data['course_video_id']);
        }

		return $this->respond($result);
    }

    public function sort_courses_videos_table()
    {
        $CoursesVideosModel = model('CoursesVideosModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                $CoursesVideosModel->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

}