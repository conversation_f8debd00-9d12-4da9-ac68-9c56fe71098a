<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Machines extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('MachinesModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_machiness'] = $this->model->query("SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC")->getResultArray();
        $data['draft_machiness'] = $this->model->query("SELECT * FROM machines WHERE deleted_at IS NULL AND status = 1")->getResultArray();

        $data['machiness_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Joined";
        $data['page'] = 1;

        echo view('admin/machines/index_view', $data);
    }

    public function deleted($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $current = $this->model->query("SELECT * FROM machines WHERE deleted_at IS NULL AND status = 1 AND id = " . $edit_id)->getResultArray();
		$data['current'] = $current[0];

        echo view('admin/machines/deleted_view', $data);
    }
    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function edit($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/machines');
        }

		return view('admin/machines/edit_view', $data);
    }
    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Slide successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/machines', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/machines/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/machines/' . $name, 98);
				$data['image'] = 'uploads/machines/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/machines', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/machines/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/machines/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/machines/' . $name;
			}

            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if($data['mob_cover_image_removed'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}