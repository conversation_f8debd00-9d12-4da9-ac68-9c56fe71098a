<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class BuyRent extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ClassesModel');
    }

    public function index()
    {
        $collections_model = model('CollectionsModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $teachers_model = model('TeachersModel');
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                            HAVING countMachine > 0
                                      ')->getResultArray();
        foreach($data['machines'] as $key => $value) {
            $data['accessories'][$key]['machine_name'] = $value['short_name'];
            $data['accessories'][$key]['id'] = $value['id'];
            $data['accessories'][$key]['count_accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
            $data['accessories'][$key]['accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
        }

		$data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['languages'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM languages
                                            LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status = 0
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();

        $data['featured_collections'] = $collections_model->all_collections(0, 2);
        $data['all_classes'] = $this->model->all_buy_rent_classes(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Buy or Rent Micro, Mini, and Megaformer Classes | Lagree On Demand';
		$data['current']['seo_description'] = "Change your body from the comfort of your home or on the go with Lagree Micro, Mini, and Megaformer classes available online 24/7! Get Lagree On Demand today!";
		echo view('front/classes/buy_rent_view', $data);
    }

    public function megaformer()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $db = \Config\Database::connect();
		$data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status = 0
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();
        $data['all_classes'] = $this->model->all_mega_classes(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Megaformer Classes | Megaformer Classes On Demand';
		$data['current']['seo_description'] = "Ready to transform your body? Check our Lagree On Demand's collection of Megaformer classes and maximize your machine's full potential!";
		echo view('front/classes/mega_view', $data);
    }

    public function microformer()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $db = \Config\Database::connect();
		$data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status = 0
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();
        $data['all_classes'] = $this->model->all_micro_classes(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Micro Classes | Lagree Micro Classes On Demand';
		$data['current']['seo_description'] = "Ready to maximize your Lagree Micro's full potential? Check out our library of online Micro classes and start changing your body today!";
		echo view('front/classes/micro_view', $data);
    }

    public function mini()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $db = \Config\Database::connect();
		$data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status = 0
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();
        $data['all_classes'] = $this->model->all_mini_classes(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Mini Classes | Lagree Mini Classes On Demand';
		$data['current']['seo_description'] = "Maximize your Lagree Mini's full potential with Mini classes on Lagree On Demand. View our Mini classes and start creating the body you've always wanted.";
		echo view('front/classes/mini_view', $data);
    }

    public function slug($slug = '')
    {
        helper('text');
        $classes_views_model = model('ClassesViewModel');
        $ClassesRate_model = model('ClassesRateModel');
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $SubscribersClasses = model('SubscribersClasses');
        $shopify_model = model('ShopifyModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['current'] = $this->model->current($slug);
        $data['nums'] = create_session_nums();

        // $data['id'] = $this->get_id_from_slug('classes', $slug);
        $save_pageview = array('class_id' => $data['current']['id'], 'date' => date('Y-m-d'));
        $saved = $classes_views_model->save($save_pageview);

        $data['in_favs'] = $SubscribersFavs_model->where(['class_id' => $data['current']['id'], 'subscriber_id' => $data['logged_user']['id']])->find();
        $data['bought'] = $SubscribersClasses->where(['class_id' => $data['current']['id'], 'subscriber_id' => $data['logged_user']['id'], 'purchase_type' => NULL])->find();
        $data['rented'] = $SubscribersClasses->query("SELECT *, DATE_ADD(MAX(date), INTERVAL 1 DAY) as expiry_rent_date FROM subscribers_classes  WHERE class_id = " . $data['current']['id'] . " AND subscriber_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND purchase_type = 'rent' AND DATE_ADD(date, INTERVAL 1 DAY) > CURDATE()")->getResultArray();
        $data['own'] = $this->model->where(['id' => $data['current']['id'],'teacher' => (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0)])->find();
        $data['similar_classes'] = $this->model->similar_classes(0, 3, NULL, 'created_at DESC', $data['current']['id']);

        $data['rated'] = $ClassesRate_model->where(['class_id' => $data['current']['id'], 'user_id' => $data['logged_user']['id']])->find();

        if($data['current']['all_class_machines_shopify'] != NULL){
            $shopify_machines = explode(',', $data['current']['all_class_machines_shopify']);
            $data['shopify_machines_titles'] = explode(',', $data['current']['all_class_machines']);
            foreach($shopify_machines as $product){
                if($product != ''){
                    $data['shopify_machines'][] = $shopify_model->single_product($product);
                }
            }
        }



        if($data['current']['all_class_accessories_shopify'] != NULL){
            $shopify_accessories = explode(',', $data['current']['all_class_accessories_shopify']);
            $data['shopify_accessories_titles'] = explode(',', $data['current']['all_class_accessories']);
            foreach($shopify_accessories as $key => $product){
                if($product != ''){
                    $data['shopify_accessories'][] = $shopify_model->single_product($product);
                }
            }
        }
        $data['current']['content'] = 'Whether you’re at home, the gym, your hotel, or small apartment, you can stream Lagree On Demand on any device.';
        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
        if($data['current']['id'] == NULL){
            return redirect()->to('/');
        }
		return view('front/classes/single_view', $data);
    }
    public function filter()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'classes.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Mini Classes | Lagree Mini Classes On Demand';
		$data['current']['seo_description'] = "Maximize your Lagree Mini's full potential with Mini classes on Lagree On Demand. View our Mini classes and start creating the body you've always wanted.";
		$data['current']['seo_keywords'] = 'Lagree On Demand Classes';

        $data['all_classes'] = $this->model->filter_buy_rent($filter_data);

		$response['view'] = view('front/classes/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_classes']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));
        $remove_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user']);

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
        }

		return $this->respond($response);
    }
    public function mark_as_watched()
    {
        $subscribersWatched_model = model('SubscribersWatchedModel');
		$request = service('request');
        $data = $request->getPost();
        $save_watched = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $subscribersWatched_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $subscribersWatched_model->save($save_watched);
        }

		return $this->respond($response);
    }

    public function rate_class()
    {
        $ClassesRate_model = model('ClassesRateModel');
        $NotificationsModel = model('NotificationsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('class_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['favs'] = $ClassesRate_model->where(["class_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $ClassesRate_model->save($save_rate);
            $response['success'] = TRUE;
        }
        $class = $this->class_info($data['class']);
        if($class['type'] == 1){
            $seller = $this->teacher_info($class['teacher']);
            $user_from_teacher = $this->subscriber_from_teacher_info($seller['email']);


            if(isset($user_from_teacher['id'])){
                $notification_data = array(
                    'content'   => 'Someone has rated your class <span class="text-underline">' . $class['title'] . '</span>.',
                    'link'      => base_url() . '/account/classes',
                    'author'    => 'system',
                    'subscriber_id'    => isset($user_from_teacher['id']) ? $user_from_teacher['id'] : 0,
                    'type' => 'class_rated_notif',
                    'date'    => date('Y-m-d H:i:s')
                );
                $response['notification_saved'] = $NotificationsModel->save($notification_data);
            }
        }

		return $this->respond($response);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
        $rules = [
            'title'         => 'required|min_length[2]',
            'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
            'video'         => 'required',
            'machine'       => 'required',
            'difficulty'    => 'required',
            'teacher'       => 'required',
        ];
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/classes', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/' . $name, 98);
				$data['image'] = 'uploads/classes/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('classes_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function sess()
    {
        $seller = $this->teacher_info(21);

        // session()->remove('per_page');
        echo '<pre>';
        var_dump($seller);
        echo round((4.99 * 0.7), 2) * 100;
        echo '</pre>';
    }
}