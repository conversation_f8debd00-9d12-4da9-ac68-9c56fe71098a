<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.upload-image .image_preview.no-img.has_image.has_icon {
	width: 96px !important;
	height: 96px !important;
}
.msg-result:not(:last-of-type) {
	border-bottom: 1px solid #f0f0f0 !important;
}
.avatar25 {
	width: 25px;
	height: 25px;
	border-radius: 30px;
	overflow: hidden;
	position: relative;
	min-width: 25px;
}
.search-form.show .seach-input, .search-form:hover .seach-input, .search-form:active .seach-input, .search-form .seach-input:focus, .search-form .seach-input:active {
	width: 350px;
	color: #000;
}
.ajax-results {
	position: absolute;
	top: 15px;
	width: 350px;
	right: 0;
	padding: 20px 0 0 0px;
	border: 1px solid #f0f0f0;
	background: #fff;
	z-index: 111;
	box-shadow: 0 6px 20px 0 rgba(0,0,0,0.1);
	display: none;
	max-height: 70vh;
	overflow: auto;
}
.msg-result:hover {
	background: #f9f9f9;
	cursor: pointer;
}
.f-11 {
	font-size: 11px !important;
}
.search-form {
	z-index: 1112;
	background: #fff;
}
.search-form .search-button {
	z-index: 113;
}
</style>
 

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">
                    ASK SEBASTIEN
                </h1>
                <!-- <div class="search-container">
                    <input type="text" placeholder="Search..." style="width: 350px;font-size: 14px;position: relative;z-index: 111111111;" class="line-input border small ml-auto px-1 search-messages">
                    <div class="ajax-results"></div>
                </div> -->
                <div class="search-container">
                    <form action="javascript:;" method="GET" class="search-form <?php echo (isset($search_term) AND $search_term != '0') ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input search-messages f-14" autocomplete="off" value="<?php echo (isset($search_term) AND $search_term != "0") ? $search_term : ''; ?>" style="position: relative;z-index: 112;">
                        <?php if(isset($search_term) AND $search_term != "0"){ ?>
                        <a href="admin/classes/clear_search" class="clear_search" style="font-size: 18px;right: 40px;">×</a>
                        <?php } ?>
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        <div class="ajax-results"></div>
                    </form>
                </div>
            </div>
            <hr class="mt-0 mb-6">
            <div class="row ail big-gap" style="flex-wrap: nowrap">
                <div class="col settingsmenu ask-names">
                    <ul class="">
<?php foreach($all_conversations as $single){ ?>
                        <li class="single_conversation" data-id="<?php echo $single['id']; ?>">
                            <span class="single-conversation flex aic jcsb border radius-10 <?php echo $single['countMessages'] > 0 ? 'new_msg' : ''; ?>">
                                <span class="receiver_name text-uppercase f-10 <?php echo $single['countMessages'] > 0 ? 'bold' : ''; ?>"><?php echo $single['user_name']; ?></span>
                                <?php echo $single['countMessages'] > 0 ? '<span class="red-dot position-relative"></span>' : ''; ?>
                            </span>
                        </li>
<?php } ?>
                    </ul>
                    <div id="ask-load-more" class="btn black-bg white btn-xs">LOAD MORE</div>
                </div>
                <div class="col p-0 border radius-10" style="width: 100% !important">
                    <div class="conversation_with bottom-border" style="display: none;">
                        <div class="col-12 px-3">
                            <div class="ask-txt flex aic jcsb">
                                <h3 class="f-1 semibold flex aic chat_with"></h3>
                                <p class="text-right"><span class="btn link-normalRed normalRed f-12 hide_record" data-popup="hide-conversation-popup" data-id="0" data-table="conversations">Delete</span></p>
                            </div>
                        </div>
                    </div>
                    <div class="conversation-list select-conv">
                        <div class="flex aic jcc"><h4 class="f-14">Please select a conversation</h4></div>
                    </div>
                    <div class="conversation-write" style="display: none;">
                        <form action="admin/conversations/new_message" class="" id="send_message" method="POST">
                            <div class="insert-question borderNew radius-10">
                                <textarea name="message" id="message" class="no-border" placeholder="Enter"></textarea>
                            </div>
                            <input type="hidden" name="sender_id" value="0" />
                            <input type="hidden" name="receiver_id" value="0" />
                            <input type="hidden" name="conversation_id" value="0" />
                            <input type="hidden" name="type" value="text" />
                            <div class="flex aic jcsb mt-3">
                                <div class="flex aic gap-1">
                                    <a href="javascript:;" class="btn btn-badge-40" id="add_img_to_chat" data-popup="add-msg-img-popup" title=""><img src="admin_assets_new/images/upload-img-icon.svg" alt="" class="img-fluid" /></a>
                                    <a href="javascript:;" class="btn btn-badge-40" id="add_file_to_chat" data-popup="add-msg-file-popup" title=""><img src="admin_assets_new/images/upload-file-icon.svg" alt="" class="img-fluid" /></a>
                                    <a href="javascript:;" class="btn btn-badge-40" id="add_url_to_chat" data-popup="add-msg-url-popup" title=""><img src="admin_assets_new/images/upload-link-icon.svg" alt="" class="img-fluid" /></a>
                                </div>
                                <button type="submit" class="btn black-bg white">SEND</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/conversations.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var conversation_user_id = 0;
var conversation_id = 0;
$(document).on('keypress', function(event){
    console.log('PRESS');
    console.log(event.keyCode);
    if ((event.keyCode == 10 || event.keyCode == 13) && event.ctrlKey){
        $('#send_message').trigger('submit');
    }
});
var cc = 0;
var chat_with = "";

$('.search-form').on('submit', function(e){
    e.preventDefault();
    doneTyping();
});
$(document).ready(function(){
    $('.single_conversation').on('click', function(){
        cc = 0;
        chat_with = $(this).find('.receiver_name').html();
        conversation_id = $(this).data('id');
        load_conversations(conversation_id, 1);
        $('.conversation-write').show();
        $('.conversation_with').show();
        $('#send_message').trigger('reset');
        $('.hide_record').attr('data-id', conversation_id);
        setTimeout(function(){
            window.scroll({ top: $('.conversation-list').height() - 200 });
        }, 250);
        var adminTimer = setInterval(() => {
            load_conversations(conversation_id);
            cc++;
            console.log(cc++);
            if(cc > 60){clearInterval(adminTimer);}
        }, 10000);
    });
});
$('html, body').on('click', function(){
    $('.ajax-results').hide();
});
$('.search-messages').on('click focus', function(e){
    e.stopPropagation();
    if($(this).val().length != 0){
        $('.ajax-results').show();
    }
});
var typingTimer;                //timer identifier
var doneTypingInterval = 800;  //time in ms, 0.8 seconds
$(document).on("keyup", ".search-messages", function() {
	clearTimeout(typingTimer);
    typingTimer = setTimeout(doneTyping, doneTypingInterval);
	if($(this).val().length > 0){
        $('.search-form').addClass('show');
    }else{
        $('.search-form').removeClass('show');
    }
	if($(this).val() == ""){
		$('.ajax-results').hide();
	}
});

// $('.search-messages').on('keyup', function(){
function doneTyping () {
    var search_term = $('.search-messages').val();

    if(search_term.length > 2){
        $.ajax({
            type: 'POST',
            url: 'admin/conversations/search_message',
            data: {
                search_term: search_term
            },
            dataType: 'json',
            success: function (data) {
                console.log('Success');
                console.log(data);
                if(data.conversation.length){
                    $('.ajax-results').html(data.html).show();
                }else{
                    $('.ajax-results').html('<span class="f-1 d-block text-center py-1">No result</span>').show();
                }
            },
            error: function (request, status, error) {
                alert('Error');
            }
        });

    }
};
$(document).on('click', '.msg-result', function(){
    conversation_id = $(this).data('id');
    msg_id = $(this).data('message_id');
    chat_with = $(this).find('.message strong').text();
    load_conversations(conversation_id, 1, msg_id);
    $('.ajax-results').hide();
});
function noOfLineBreaks(){
    enteredText = $("#message").val();
    numberOfLineBreaks = (enteredText.match(/\n/g)||[]).length;

    return numberOfLineBreaks;
}
$("#message").on('keyup', function (e) {
    var lineBreaks = noOfLineBreaks();
    if(lineBreaks > 0) {
        console.log('has line breaks');
        $(this).parent().removeClass("pb-0");
    }else{
        console.log('no line breaks');
        $(this).parent().addClass("pb-0");
    }
});
</script>
<script>
 $(document).ready(function () {
            $(".single_conversation").hide();

            $(".single_conversation").slice(0, 10).show();
            if ($(".single_conversation:hidden").length != 0) {
                $("#ask-load-more").show();
            }else {
                $("#ask-load-more").hide();
            }
            $("#ask-load-more").on("click", function (e) {
                e.preventDefault();
                $(".single_conversation:hidden").slice(0, 10).slideDown();
                if ($(".single_conversation:hidden").length == 0) {
                    $("#ask-load-more").hide().fadOut("slow");
                }
            });
        })

    </script>
</body>
</html>