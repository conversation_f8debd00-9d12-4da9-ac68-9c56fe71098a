<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Cron extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
    }

    public function index()
    {
        $email_model = model('EmailModel');
        $classes_model = model('ClassesModel');
        $collections_model = model('CollectionsModel');
        $liveevents_model = model('LiveEventsModel');
        $teachers_model = model('TeachersModel');

        $data['teachers'] = $teachers_model->findAll();

        $data['user_new_class'] = $this->model->where(['email_new_class' => 1])->findAll();
        $data['user_new_collection'] = $this->model->where(['email_new_collection' => 1])->findAll();
        $data['user_new_liveevent'] = $this->model->where(['email_new_live' => 1])->findAll();

        $data['class'] = $classes_model->cron();
        $data['collection'] = $collections_model->where('notification_sent', 0)->orderBy('created_at', 'desc')->first();
        $data['liveevent'] = $liveevents_model->where('notification_sent', 0)->orderBy('created_at', 'desc')->first();

        $class_to_mark = array('id' => $data['class']['id'], 'notification_sent' => 1);
        $collection_to_mark = array('id' => $data['collection']['id'], 'notification_sent' => 1);
        $event_to_mark = array('id' => $data['liveevent']['id'], 'notification_sent' => 1);

        if(isset($data['class'])){
            echo '<h4 style="font-weight: 400">Users email notification for new class - "<b>' . $data['class']['title'] . '</b>"</h4>';
            echo '<pre>';
            // var_dump($data['user_new_class']);
            // die();
            $subject = 'New Class Uploaded';
            $data_template = [
                'class_link' => base_url() . '/classes/' . $data['class']['slug'],
                'class_title' => $data['class']['title'],
                'class_teacher' => $data['class']['teacher_name']
            ];
            $template = 'front/email_templates/new-class';
            foreach($data['user_new_class'] as $single){
                echo $single['firstname'] . ' ' . $single['lastname'] . '<br>';
                $to = $single['email'];
                $response = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
            }
            echo '</pre>';
            $classes_model->save($class_to_mark);
        }else{
            echo 'No new classes with notification NOT sent<br>';
        }

        if(isset($data['collection'])){
            echo '<h4 style="font-weight: 400">Users email notification for new collection - "<b>' . $data['collection']['title'] . '</b>"</h4>';
            echo '<pre>';
            $subject = 'New Class Uploaded';
            $data_template = [
                'collection_link' => base_url() . '/collections/' . $data['collection']['slug'],
                'collection_title' => $data['collection']['title']
            ];
            $template = 'front/email_templates/new-collection';
            foreach($data['user_new_collection'] as $single){
                echo $single['firstname'] . ' ' . $single['lastname'] . '<br>';
                $to = $single['email'];
                $response = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
            }
            $collections_model->save($collection_to_mark);
            echo '</pre>';
        }else{
            echo 'No new collections with notification NOT sent<br>';
        }

        // if(isset($data['liveevent'])){
        //     echo '<h4 style="font-weight: 400">Users email notification for new live event - "<b>' . $data['liveevent']['title'] . '</b>"</h4>';
        //     echo '<pre>';
        //     $subject = 'New Class Uploaded';
        //     $data_template = [
        //         'collection_link' => base_url() . '/collections/' . $data['collection']['slug'],
        //         'collection_title' => $data['collection']['title']
        //     ];
        //     $template = 'front/email_templates/new-collection';
        //     foreach($data['user_new_liveevent'] as $single){
        //         echo $single['firstname'] . ' ' . $single['lastname'] . '<br>';
        //         $to = $single['email'];
        //         $response = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
        //     }
        //     $liveevents_model->save($event_to_mark);
        //     echo '</pre>';
        // }else{
        //     echo 'No new live events with notification NOT sent<br>';
        // }
        die();
    }

    function clearSessionFiles()
    {
        $listFilePath = WRITEPATH . "session/";
        $listFile = scandir(WRITEPATH . "session/");
        
        foreach($listFile as $file){
            if(!is_dir($listFilePath . $file)){
                if($file !== "index.html"){
                    unlink($listFilePath . $file);
                }
            }
        }
    }

    function clearLogFiles()
    {
        $listFilePath = WRITEPATH . "logs/";
        $listFile = scandir(WRITEPATH . "logs/");

        foreach($listFile as $file){
            if(!is_dir($listFilePath . $file)){
                if($file !== "index.html"){
                    unlink($listFilePath . $file);
                }
            }
        }
    } 
}