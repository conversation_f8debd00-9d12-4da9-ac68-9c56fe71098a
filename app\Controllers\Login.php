<?php namespace App\Controllers;

use App\Models\EmailModel;
use App\Models\SubscribersModel;
use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use Google_Client;

class Login extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		$this->model = new SubscribersModel();
		$this->user = $this->model->where(['id' => session("user")])->first();

        $settingsModel = model('SettingsModel');
		$this->settings = $settingsModel->where(['id' => 1])->first();

        helper("front");
    }

    public function validate_login()
    {
		$DevicesModel = model('DevicesModel');
		$SurveysModel = model('SurveysModel');
		$SurveysFinishedModel = model('SurveysFinishedModel');
		$request = service('request');

		$data = $request->getPost();
		session()->remove('user');
		session()->remove('teacher');
		session()->remove('subscription');
		session()->remove('subscription_info');
		session()->remove('subscription_plan');
		session()->remove('s1');
		session()->remove('s2');

		$response = $this->model->login_user($data);
        // login is OK, check devices
        if($response['success']){
            $existing_devices_count = $DevicesModel->user_devices($response['user_id']);
            $response['current_device'] = $DevicesModel->user_current_device($response['user_id'], $data);
            $response['active_survey'] = $SurveysModel->active();
            if($response['active_survey'] != NULL){
                session()->set('active_survey', $response['active_survey']['id']);
            }
            
            $ip = getenv('REMOTE_ADDR');
            $data['ip'] = $ip;
            $query = @unserialize(file_get_contents('http://ip-api.com/php/'.$ip));
            if($query && $query['status'] == 'success'){
                $data['city'] = $query['city'];
                $data['country'] = $query['country'];
                $data['zip'] = $query['zip'];
            }else{
                $data['city'] = '';
                $data['country'] = '';
                $data['zip'] = '';
            };

            $response['for_save'] = [
                'user_id' => $response['user_id'],
                'os_name' => $data['os_name'],
                'os_version' => $data['os_version'],
                'browser_name' => $data['browser_name'],
                'browser_version' => $data['browser_version'],
                'device_model' => $data['device_model'],
                'device_type' => $data['device_type'],
                'device_vendor' => $data['device_vendor'],
                'country' => $data['country'],
                'city' => $data['city'],
                'zip' => $data['zip'],
                'userAgent' => $data['userAgent'],
                'last_activity' => date('Y-m-d H:i:s')
            ];

            if(count($existing_devices_count) <= 10){
                if($response['current_device']['success']){
                    $response['saved_device_activity'] = $DevicesModel->save(['id' => $response['current_device']['device']['id'], 'last_activity' => date('Y-m-d H:i:s'), 'browser_version' => $data['browser_version']]);
                    // set SUCCESS login session
                    if($response['success']){
                        session()->set('user', $response['user_id']);
                        session()->set('subscription', $response['subscription']);
                        session()->set('subscription_info', $response['subscription_info']);
                        session()->set('subscription_plan', $response['subscription_plan']);
                        if($response['teacher'] != 0){
                            session()->set('teacher', $response['teacher']);
                        }
                        if($response['active_survey'] != NULL){
                            $response['finished_survey'] = $SurveysFinishedModel->where(['user_id' => $response['user_id'], 'survey_id' => $response['active_survey']['id']])->first();
                            if($response['finished_survey'] != NULL){
                                session()->set('finished_survey', TRUE);
                            }
                        }            
                    }
                }else if(count($existing_devices_count) < 10){
                    $response['saved_new_device'] = $DevicesModel->save($response['for_save']);
                    // set SUCCESS login session
                    if($response['success']){
                        session()->set('user', $response['user_id']);
                        session()->set('subscription', $response['subscription']);
                        session()->set('subscription_plan', $response['subscription_plan']);
                        if($response['teacher'] != 0){
                            session()->set('teacher', $response['teacher']);
                        }
                        if($response['active_survey'] != NULL){
                            $response['finished_survey'] = $SurveysFinishedModel->where(['user_id' => $response['user_id'], 'survey_id' => $response['active_survey']['id']])->first();
                            if($response['finished_survey'] != NULL){
                                session()->set('finished_survey', TRUE);
                            }
                        }            
                    }
                }else{
                    $response['success'] = FALSE;
                    $response['error'] = 'Maximum number of devices is reached.';
                }
            }else{
                $response['success'] = FALSE;
                $response['error'] = 'Maximum number of devices is reached.';
            };
        };
		$response['method'] = $request->getMethod();
		$response['request'] = $request;
		$response['return_url'] = $data['return_url'];

		//echo json_encode($response);
		return $this->respond($response);
    }

    public function logout()
    {
		session()->remove('user');
		session()->remove('teacher');
		session()->remove('subscription');
		session()->remove('subscription_info');
		session()->remove('subscription_plan');
		session()->remove('active_survey');
		session()->remove('finished_survey');
		$response['success'] = TRUE;
		return redirect('/');
    }

    public function direct_login()
    {
		session()->set('admin', 1);
    }

	public function validate_fb_id()
	{
		$request = service('request');
		$data = $request->getPost();
		$response['method'] = $request->getMethod();
		$response['request'] = $request;
		$response['data'] = $data;
		$response['success'] = FALSE;
		if (!isset($data['fb_id'])){
			$response['message'] = 'There is no valid Facebook account.';
			return $this->respond($response);
		}
		$user = $this->model->where(['fb_id' => $data['fb_id']])->findAll();
		if (count($user) == 1){
			$response['user_id'] = $user[0]['id'];
			session()->set('user', $user[0]['id']);
			$response['success'] = TRUE;
			$response['return_url'] = site_url('account');
		}else{
			$response['message'] = 'There is no user connected with this Facebook account.';
		}
		return $this->respond($response);
	}

	public function validate_google_id()
	{
		$request = service('request');
		$data = $request->getPost();
		$response['method'] = $request->getMethod();
		$response['request'] = $request;
		$response['data'] = $data;
		$response['success'] = FALSE;
		if (!isset($data['google_id'])){
			$response['message'] = 'There is no valid Google account.';
			return $this->respond($response);
		}
		$user = $this->model->where(['google_id' => $data['google_id']])->findAll();
		if (count($user) == 1){
			$response['user_id'] = $user[0]['id'];
			session()->set('user', $user[0]['id']);
			$response['success'] = TRUE;
			$response['return_url'] = site_url('account');
		}else{
			$response['message'] = 'There is no user connected with this Google account.';
		}
		return $this->respond($response);
	}

	public function validate_google_token()
	{
		$request = service('request');
		$data = $request->getPost();
		$response['method'] = $request->getMethod();
		$response['request'] = $request;
		$response['data'] = $data;
		$response['success'] = FALSE;
		if (empty($data['google_token'])){
			$response['message'] = 'There is no valid Google account.';
			return $this->respond($response);
		}
		//Check google token
		//$client = new Google_Client(['client_id' => '************-9l96j3rpv6468ai1ct59qiorgaba1j18.apps.googleusercontent.com']);  // Specify the CLIENT_ID of the app that accesses the backend
		$client = new Google_Client(['client_id' => '***********-32tphh0d0jl66bqsm38fvm6ghgrau1g7.apps.googleusercontent.com']);  // Specify the CLIENT_ID of the app that accesses the backend
		$payload = $client->verifyIdToken($data['google_token']);
		if ($payload) {
		  $userid = $payload['sub'];
		  // If request specified a G Suite domain:
		  //$domain = $payload['hd'];
			$user = $this->model->where(['google_token' => $data['google_token']])->findAll();
			if (count($user) == 1){
				$response['user_id'] = $user[0]['id'];
				session()->set('user', $user[0]['id']);
				$response['success'] = TRUE;
				$response['return_url'] = site_url('account');
			}else{
				$response['message'] = 'There is no user connected with this Google account.';
			}
		} else {
		  // Invalid ID token
		  $response['message'] = 'Invalid Google token.';
		}
		return $this->respond($response);
	}

    public function forgot_pass()
    {
		$email_model = new EmailModel();
		$request = service('request');
		$data = $request->getPost();

        $user = $this->model->where(['email' => $data['email']])->first();
        if ($user != NULL){
            // $response['user'] = $user;
            $to = $data['email'];
            $subject = 'Lagree On Demand - Forgot password';
            $data = [
                'reset_pass_link' => base_url() . '/login/new_password/' . md5($user['id'])
            ];
            $template = 'front/email_templates/reset-password';
            $response = $email_model->send_template($to, FALSE, $subject, $data, $template);
            // $response = $email_model->send_email($data);
        }else{
            $response['msg'] = 'We did not find any user with this email address. Please try again';
            $response['email'] = $data['email'];
            $response['user'] = $user;
        }

        return $this->respond($response);
    }
    
    public function new_password($id)
    {
        $data = $this->model->where(['MD5(id)' => $id])->first();
        $data['user'] = $data;
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = $this->_create_session_nums();

        $data['current']['image'] = base_url() . 'images/liveevents1.jpg';
		$data['current']['seo_title'] = 'New password';
		$data['current']['seo_description'] = 'Lagree On Demand New password';
		$data['current']['seo_keywords'] = 'Lagree On Demand New password';

		echo view('front/pages/new-pass_view', $data);
    }

    public function reset_pass()
    {
		$email_model = new EmailModel();
		$request = service('request');
		$data = $request->getPost();

        // $response['data'] = $data;
        $validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        unset($rules['firstname']);
        unset($rules['lastname']);
        unset($rules['email']);
		$validation->reset();
		$validation->setRules($rules);
        $response['success'] = FALSE;
        if (!$validation->run($data)){
			$response['message'] = implode('</br>', $validation->getErrors());
		}else{
            if (isset($data['password']) AND $data['password'] <> ''){
                $new_data['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))));
                $new_data['id'] = $data['id'];
            }
            $response['success'] = $this->model->save($new_data);
        }

        if($response['success']){
            $to = $data['email'];
            $subject = 'Lagree On Demand - Password changed';
            $data = [];
            $template = 'front/email_templates/password-changed';
            $response = $email_model->send_template($to, FALSE, $subject, $data, $template);
        }else{
            $response['email_sent'] = 'NOT SEND';
        }

        return $this->respond($response);
    }

	public function test_token()
	{
		$id_token = 'eyJhbGciOiJSUzI1NiIsImtpZCI6ImFkZDhjMGVlNjIzOTU0NGFmNTNmOTM3MTJhNTdiMmUyNmY5NDMzNTIiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MJIMsju2dy-VGuYM4nPkh-pIyLLLbkUdjvGHCatfEciY8aLcWXeZe6K3G7l_EL8C7aZkdibGYXvjWde8ZcM7IJCZBT5j74eQynATIsxjCLWuFmoi4n7U9snQD_ZCRVZPiVXmd4JkNxLvKnaF2uo0-YJpP2ZRYx-1NU1dooYoG8HysHB8LGWPBWcCQU2O_cN0z4PT8i_gJh5PKEIn54mWLt9b1UfuntugDwc5DFHxMWPMmlLWm2JcTT-QmHBByuQCamc0YBUK1i6J8g8m9Q8Sbp19NXdT02YsYgUqvtiQLo-KBlZ58MSmjPbr62eyoArTiJxukSSyq7aD8Tc3q4vUog';
		$client = new Google_Client(['client_id' => '************-9l96j3rpv6468ai1ct59qiorgaba1j18.apps.googleusercontent.com']);  // Specify the CLIENT_ID of the app that accesses the backend
		$payload = $client->verifyIdToken($id_token);
		if ($payload) {
		  $userid = $payload['sub'];
		  // If request specified a G Suite domain:
		  //$domain = $payload['hd'];
		  echo $userid;
		  echo '<pre>';
		  var_dump($payload);
		} else {
		  // Invalid ID token
		  echo 'NE VALJA';
		}
	}

    private function _create_session_nums(){
        $result['broj1'] = rand(5,25);
        $result['broj2'] = rand(5,25);
        session()->set("broj1", $result['broj1']);
        session()->set("broj2", $result['broj2']);

        return $result;
	}

    public function session()
    {
		// if(session()->has('admin')){
		// 	 echo session("admin");
		// }
        echo '<pre>';
        // echo session("per_page");
        // echo '<br>';
		print_r(session()->get());
        echo '</pre>';
    }

    public function survey_session()
    {
        $SurveysModel = model('SurveysModel');
		$SurveysFinishedModel = model('SurveysFinishedModel');
        $response['active_survey'] = $SurveysModel->active();
        $response['user'] = session('user');

        echo '<pre>';
        print_r($response);
        
        if($response['active_survey'] != NULL){
            $response['finished_survey'] = $SurveysFinishedModel->where(['user_id' => session('user'), 'survey_id' => $response['active_survey']['id']])->first();
            if($response['finished_survey'] != NULL){
                echo '<br>-------------------------------<br>';
                print_r($response['finished_survey']);
            }else{
                echo '<br>-------------------------------<br>';
                echo 'NO FINISHED SURVEY';
            }
        }            

    }
}