<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Audio extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('AudioModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        $data['all_audio'] = $this->model->all_audio(0, session('per_page'));
        $data['audio_count'] = $this->model->countAllResults();
        $data['page'] = 1;

        echo view('admin/audio/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        $data['all_audio'] = $this->model->all_audio(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['audio_count'] = $this->model->countAllResults();
        $data['page'] = $page;

        echo view('admin/audio/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_audio'] = $this->model->all_audio(0, session('per_page'), $data['search_term']);
        $data['audio_count'] = $this->model->like('code', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/audio/index_view', $data);
    }

    public function sort_by($type = 'audio.created_at', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $data['all_audio'] = $this->model->all_audio(0, session('per_page'), NULL, ($type. " " . $direction));
        $data['audio_count'] = $this->model->countAllResults();
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['classes_count'] = count($data['all_audio']);
        $types = array(
            "audio.created_atdesc" => "Date Added",
            "audio.titleasc" => "Ascending",
            "audio.titledesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;

        echo view('admin/audio/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['current'] = $this->model->current($edit_id);

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/audio/edit_view', $data);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Audio successfully saved';

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}