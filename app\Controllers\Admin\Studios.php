<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Studios extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('StudiosModel');
        $this->super_admin = session('super_admin') != NULL ? session('super_admin') : 0;
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        if($this->super_admin > 0){
            $data['all_studios'] = $this->model->all_studios(0, session('per_page'));
            $data['studios_count'] = $this->model->countAllResults();
        }else{
            $data['all_studios'] = $this->model->all_user_studios(0, session('per_page'));
            $data['studios_count'] = $this->model->where(['user_id' => session('admin')])->countAllResults();
        }
        $data['page'] = 1;

        echo view('admin/studios/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['sort_by'] = "Date Added";
        if($this->super_admin > 0){
            $data['all_studios'] = $this->model->all_studios(($page * session('per_page')) - session('per_page'), session('per_page'));
            $data['studios_count'] = $this->model->countAllResults();
        }else{
            $data['all_studios'] = $this->model->all_user_studios(($page * session('per_page')) - session('per_page'), session('per_page'));
            $data['studios_count'] = $this->model->where(['user_id' => session('admin')])->countAllResults();
        }
        $data['page'] = $page;

        echo view('admin/studios/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        if($this->super_admin > 0){
            $data['all_studios'] = $this->model->all_studios(0, session('per_page'), $data['search_term']);
            $data['studios_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        }else{
            $data['all_studios'] = $this->model->all_user_studios(0, session('per_page'), $data['search_term']);
            $data['studios_count'] = $this->model->like('title', $data['search_term'])->where(['user_id' => session('admin')])->countAllResults();
        }
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/studios/index_view', $data);
    }

    public function sort_by($type = 'studios.created_at', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($this->super_admin > 0){
            $data['all_studios'] = $this->model->all_studios(0, session('per_page'), NULL, ($type. " " . $direction));
            $data['studios_count'] = $this->model->countAllResults();
        }else{
            $data['all_studios'] = $this->model->all_user_studios(0, session('per_page'), NULL, ($type. " " . $direction));
            $data['studios_count'] = $this->model->where(['user_id' => session('admin')])->countAllResults();
        }
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['studios_count'] = count($data['all_studios']);
        $types = array(
            "studios.created_atdesc" => "Date Added",
            "studios.titleasc" => "Ascending",
            "studios.titledesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;

        echo view('admin/studios/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['current'] = $this->model->current($edit_id);

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/studios/edit_view', $data);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Studios successfully saved';

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function save_status()
    {
        $data = $this->request->getPost();

        $save_data = [
            'id' => $data['id'],
            'status' => $data['status'] == 1 ? 0 : 1
        ];
        $response['success'] = $this->model->save($save_data);

		return $this->respond($response);
    }
}