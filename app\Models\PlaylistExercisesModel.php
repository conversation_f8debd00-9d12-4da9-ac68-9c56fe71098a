<?php namespace App\Models;

use CodeIgniter\Model;

class PlaylistExercisesModel extends Model
{
    protected $table = 'playlists_selected_exercises';
	protected $allowedFields = ['playlists_id', 'playlist_selected_exercises', 'date', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}