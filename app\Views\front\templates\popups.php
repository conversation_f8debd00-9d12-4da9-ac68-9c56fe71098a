<style>
.reg-buttons .btn:hover {
    color: #fff !important;
}
</style>
<div id="overlay" class="overlay">
    <div class="applying_filters"></div>
    <div class="overlay-blur"></div>
    <div class="popup p-0 delete-popup">
        <!--<span class="close_page close">×</span>-->
        <div class="popup-header pt-4 mt-05 pb-2 no-border">
            <p class="f-14 medium text-transf-none text-center lh-25 cancel-subs-txt">Are you sure you want to delete this item?</p>
        </div>
        <div class="popup-body pb-5">
            <div class="py2 text-center">
                <a href="javascript:;" class="btn red-bg white py-0 mr-1 close delete_item h-42" data-delete-id="0" data-delete-table="no" title="Delete">Delete</a>
                <a href="javascript:;" class="btn btn-border white-bg py-0 black close mb-0 h-42" title="Cancel">Cancel</a>
            </div>
        </div>
    </div>
    <div class="popup p-0 delete-device-popup mob-middle">
        <div class="popup-header pt-4 mt-05 pb-2 no-border">
            <p class="f-14 medium text-transf-none text-center lh-25 cancel-subs-txt">Are you sure you want to delete this device?<br><span class="textRed normal f-10">This operation cannot be undone.</span></p>
        </div>
        <div class="popup-body pb-5">
            <div class="py2 text-center">
                <a href="javascript:;" class="btn red-bg white py-0 mr-1 close delete_device_item h-42" data-delete-id="0" data-delete-table="Devices" title="Delete">Delete</a>
                <a href="javascript:;" class="btn btn-border white-bg py-0 black close mb-0 h-42" title="Cancel">Cancel</a>
            </div>
        </div>
    </div>
    <div class="popup p-0 delete-card-popup mob-middle">
        <div class="popup-header pt-4 mt-05 pb-2 no-border">
            <p class="f-14 medium text-transf-none text-center lh-25 cancel-subs-txt">Are you sure you want to delete this card?</p>
        </div>
        <div class="popup-body pt-05 pb-5">
            <div class="w100 flex aic jcc card_placeholder"></div>
            <div class="py2 text-center">
                <a href="javascript:;" class="btn red-bg white py-0 mr-1 close delete_card_item h-42">Delete</a>
                <a href="javascript:;" class="btn btn-border white-bg py-0 black close mb-0 h-42" title="Cancel">Cancel</a>
            </div>
        </div>
    </div>
    <div class="popup p-0 cancel-subscribtion-popup mob-middle">
        <!--<span class="close_page close pt-2">×</span>-->
        <div class="popup-header pt-4 mt-05 pb-2 no-border">
            <p class="f-14 medium text-transf-none text-center lh-25 cancel-subs-txt">Are you sure you want to cancel your subscription?<br><span class="textRed normal f-10">This operation cannot be undone.</span></p>
        </div>
        <div class="popup-body pb-5">
            <div class="py2 text-center subsc-delete-buttons">
                <!-- <a href="javascript:;" class="btn btn-border white-bg black close" title="Cancel">Cancel</a> -->
                <form id="cancel_subscription" action="account/cancel_subscription" method="post" class="subscribe-form">
                    <input type="hidden" name="stripe_subscription" value="<?php echo isset($logged_user['stripe_subscription']) ? $logged_user['stripe_subscription'] : 0; ?>">
                    <button type="submit" class="btn close delete-subsc" title="Cancel Subscription">CONFIRM</button>
                </form>
                <button type="submit" class="btn goback-subsc close" title="Go Back">GO BACK</button>
            </div>
        </div>
    </div>
    <div class="popup login-popup">
        <form action="login/validate_login" method="post" class="login-form" id="login-form">
            <div class="p-0">
                <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
                <div class="">
                    <h2>Login</h2>
                    <hr class="popup-divider showmob">
                    <div class="row-vertical">
                        <div class="col-12" style="display: none;">
                            <p class="darkRed f-12 login-error mb-2"></p>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-15">
                                <input type="text" name="email" class="line-input" id="email" placeholder="Email">
                                <!--<span class="input-label">Email</span>-->
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 account-showpass">
                            <div class="input-container">
                                <span class="reveal_password">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                    <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                </svg>
                                </span>
                                <input type="password" name="password" class="line-input" id="password" placeholder="Password">
                                <!--<span class="input-label">Password</span>-->
                                <span id="Password_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 mt-3 pt-05 mt-mob-2 mb-mob-0 flex aic jcsb mob-flex-col">
                            <input type="hidden" name="return_url" value="<?php echo current_url(); ?>">

                            <input type="hidden" name="os_name" id="os_name" />
                            <input type="hidden" name="os_version" id="os_version" />
                            <input type="hidden" name="browser_name" id="browser_name" />
                            <input type="hidden" name="browser_version" id="browser_version" />
                            <input type="hidden" name="device_model" id="device_model" />
                            <input type="hidden" name="device_type" id="device_type" />
                            <input type="hidden" name="device_vendor" id="device_vendor" />
                            <input type="hidden" name="userAgent" id="userAgent" />

                            <button type="submit" class="btn black-bg white">Log In</button>
                            <a href="javascript:;" class="link link-midGray midGray f-12" title="Forgot Password?" data-popup="forgot-popup">Forgot Password?</a>
                        </div>
                        <!--<div class="col-12 text-center">
                            <p class="midGray mb-3 light">You can also…</p>
                        </div>-->
                        <div class="col-12 text-center">
                        <hr class="mt-4 mb-25">
                            <div class="row small-gap">
                                <!-- <div class="col-12 mb-2 text-center">
                                    <a id="fb_login" href="javascript:;" class="btn fb-bg-color f-14 white w100 mb-2 mb-mob-0" title="Connect with Facebook">Connect with Facebook</a>
                                </div> -->
                                <!-- <div class="col-12 text-center">
                                    <a id="google_login" href="javascript:;" class="btn btn-border bg-white f-14 w100 mb-2" title="Connect with Apple">Connect with Google</a>
                                </div> -->
                                <div class="col-12 text-center reg-buttons mt-mob-2">
                                    <!-- <a href="javascript:;" class="btn btn-border black mb-2 w100" title="CREATE A FREE ACCOUNT" data-popup="register-popup">CREATE A FREE ACCOUNT</a> -->
                                    <a href="/subscribe" class="f-14" title="Subscribe today, cancel any-time"><u>Subscribe today, cancel any-time</u></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popup forgot-popup">
        <form action="login/forgot_pass" method="post" class="forgot-form" id="forgot-form">
            <div class="p-0">
                <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
                <div class="">
                    <h2>Forgot Password?</h2>
                    <hr class="popup-divider showmob mt-3">
                    <div class="row-vertical">
                        <div class="col-12">
                            <p class="f-12 lh-25">Enter the email address that you used to register. We'll send you an email with a link to reset your password.</p>
                            <div class="input-container">
                                <input type="text" name="email" class="line-input" id="forgot_email" placeholder="Email">
                                <!--<span class="input-label">Email</span>-->
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 mt-1 flex aic jcc">
                            <button type="submit" class="btn black-bg white">Reset password</button>
                        </div>
                        <hr class="mt-4 mb-3 mt-3 mt-mob-0">
                        <div class="col-12 flex aic jcc mt-mob-2">
                            <p class="normal f-14 my-0">If you still need help, <a href="/contact-us" class="link link-black black text-underline" title="contact us">contact us</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popup mega-inquire-popup">
        <form action="register/sendMailModel" method="post" class="model-form" id="model-form">
            <input type="text" name="name" style="position: absolute; top: 1px;left: 1px;width: 1px;height: 1px;opacity: 0;pointer-events: none">
            <div class="p-0">
                <span class="close_page close">×</span>
                <div class="">
                    <h4 class="f-24 mb-4">CONTACT US</h4>
                    <div class="row-vertical">
                        <div class="col-12">
                            <div class="input-container mb-15">
                                <input type="text" name="fullname" class="line-input" id="fullname" placeholder="Name">
                                <!--<span class="input-label">Full Name</span>-->
                                <span id="Name_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-15">
                                <input type="text" name="model" class="line-input main-mega-inquire-model" id="model" placeholder="Model">
                                <span class="input-label" style="line-height: 15px;">Equipment Model You are Interested in Purchasing</span>
                                <span id="model_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-15">
                                <input type="text" name="shipping_address" class="line-input" id="shipping_address" placeholder="Shipping address">
                                <!--<span class="input-label">Shipping address</span>-->
                                <span id="shipping_address_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-15">
                                <input type="text" name="email" class="line-input" id="email" placeholder="Email">
                                <!--<span class="input-label">Email</span>-->
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container">
                                <textarea type="text" name="message" class="line-input" placeholder="Message" style="min-height: 150px;margin-top: 20px;"></textarea>
                                <!--<span class="input-label">Note</span>-->
                                <span id="Message_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 mt-2 flex aic jcsb">
                            <!-- <input type="hidden" name="return_url" value="<?php echo current_url(); ?>"> -->
                            <!-- <input type="hidden" name="send_to" value="<EMAIL>"> -->
                            <input type="hidden" name="send_to" class="email_send_to" value="<EMAIL>">
                            <button type="submit" class="btn black-bg white f-16">SEND</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popup need-help-popup p-0">
        <form action="register/sendHelpEmail" method="post" class="help-form" id="help-form">
            <input type="text" name="name" style="position: absolute; top: 1px;left: 1px;width: 1px;height: 1px;opacity: 0;pointer-events: none">
            <div class="p-0">
                <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
                <h2>CUSTOMER SUPPORT</h2>
                <hr class="popup-divider">
                <div class="cust-supp-popupwrap">
                    <div class="row-vertical">
                        <div class="col-12">
                            <div class="input-container mb-2 mb-mob-15">
                                <input type="text" name="customers_fullname" required class="line-input px-2 f-14" placeholder="Name" />
                                <!--<span class="input-label">Name</span>-->
                                <span id="Name_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-2 mb-mob-15">
                                <input type="text" name="customers_eml" required class="line-input px-2 f-14" placeholder="Email" />
                                <!--<span class="input-label">Email</span>-->
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container mb-2">
                                <textarea name="customers_message" required class="line-input p-2 f-14" placeholder="Message" style="max-height: 150px !important;min-height: 150px;"></textarea>
                                <!--<span class="input-label">Message</span>-->
                                <span id="Message_error" class="input-error"></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex areyouhuman-box">
                        <div class="line-height-normal f-12 humantxt flex aic">
                            <span class="blockondesk">Are you human
                            <input type="text" name="s1" id="s1" value="" style="width: 20px;padding: 0;border: 0;display: inline;pointer-events: none;" readonly>+&nbsp;<span class="s2"></span>&nbsp;=&nbsp;</span>
                        </div>
                        <div class="humanenter">
                            <input type="text" name="sum" required class="line-input px-2 f-14" placeholder="Enter" />
                        </div>
                    </div>
                    <div class="row">
                    <hr class="my-2">
                        <div class="col-12 mt-1 flex aic jcsb">
                            <button type="submit" class="btn black-bg white w100">SUBMIT</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popup add-credit-card p-0">
        <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
        <div class="popup-header">
            <p class="f-14 bold pl-3 lh-small">Add Credit Card</p>
        </div>
        <form id="add_card" action="account/add_card" method="post" class="add-card-form">
            <div class="p-0">
                <div class="pt-3">
                    <!-- <h2 class="light pb-2 mb-5">Add Credit Card</h2> -->
                    <div class="row-vertical px-3">
                        <div class="col-12">
                            <div class="input-container mb-2">
                                <input type="text" name="card[name]" class="line-input h-46" id="name" placeholder="Name on Card">
                                <!--<span class="input-label">Name on Card</span>-->
                                <span id="Name_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 flex">
                            <div class="input-container credit-card-inputs">
                                <input type="text" name="card[number]" class="line-input h-46" id="add-card-number" placeholder="Card number">
                                <!--<span class="input-label">Card number</span>-->
                                <span id="card-number_error" class="input-error"></span>
                            </div>
                            <div style="width: 60px;" class="input-container ml-1">
                                <input type="text" name="card[exp_month]" class="line-input add-credit-card-date h-46" id="add-card-month" placeholder="MM" maxlength="2">
                                <!--<span class="input-label">MM</span>-->
                                <span id="card-number_error" class="input-error"></span>
                            </div>
                            <div style="width: 60px;" class="input-container ml-1">
                                <input type="text" name="card[exp_year]" class="line-input add-credit-card-date h-46" id="add-card-year" placeholder="YY" maxlength="2">
                                <!--<span class="input-label">YY</span>-->
                                <span id="card-number_error" class="input-error"></span>
                            </div>
                            <div style="width: 60px" class="input-container ml-1">
                                <input type="text" name="card[cvc]" class="line-input text-center h-46" id="add-card-cvc" placeholder="CVC" data-tempmail="90978" maxlength="3">
                                <!--<span class="input-label">CVC</span>-->
                                <span id="card-number_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 mt-mob-1">
                            <button type="submit" class="btn black-bg white w100 mb-0">Add new card</button>
                        </div>
                        <hr class="mt-3 mb-15">
                        <div class="col-12 mb-2 flex aic jcc">
                            <p class="f-14">If you need help, <a href="/contact-us" class="link link-black black text-underline" title="contact us">contact us</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div class="popup register-popup">
        <form id="register_popup" action="register/validate_popup" method="post" class="register-form" id="register-form">
            <div class="p-0">
                <span class="close_page close">×</span>
                <div class="">
                    <h2 class="light pb-2 mb-5">Register</h2>
                    <div class="row-vertical">
                        <div class="col-12">
                            <div class="input-container">
                                <input type="text" name="firstname" class="line-input" id="register_firstname" placeholder="First name">
                                <span class="input-label">First name</span>
                                <span id="Firstname_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container">
                                <input type="text" name="lastname" class="line-input" id="register_lastname" placeholder="Last name">
                                <span class="input-label">Last name</span>
                                <span id="Lastname_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container">
                                <input type="text" name="email" class="line-input" id="register_email" placeholder="Email">
                                <span class="input-label">Email</span>
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="input-container">
                                <span class="reveal_password">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                    <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                 </svg>
                                </span>
                                <input type="password" name="password" class="line-input" id="register_password" placeholder="Password">
                                <span class="input-label">Password</span>
                                <span id="Password_error" class="input-error"></span>
                            </div>
                        </div>
                        <div class="col-12 mt-2 flex aic jcc">
                            <button type="submit" class="btn black-bg white f-16">Start Using Your Account</button>
                        </div>
                        <hr class="mt-5 mb-3">
                        <div class="col-12 mt-2 flex aic jcc">
                            <a href="javascript:;" class="link link-black black light text-underline" title="Already have an account?" data-popup="login-popup">Already have an account?</a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div> -->
    <div class="popup p-0 share-popup">
        <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
        <div class="popup-header">
            <p class="f-14 semibold px-3 line-height-small">SHARE</p>
        </div>
        <div class="popup-body pb-0">
            <div class="share-social">
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo current_url(); ?>&amp;t=<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" onclick="javascript:window.open(this.href, '', 'menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600');return false;" class="share-item" title="facebook">
                    FACEBOOK
                </a>
                <a href="https://twitter.com/share?url=<?php echo current_url(); ?>&via=LagreeOnDemand&text=<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" onclick="javascript:window.open(this.href, '', 'menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600');return false;" class="share-item" title="twitter">
                   X.COM
                </a>
                <a href="mailto:?subject=<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>&body=<?php echo (isset($current['content']) AND $current['content'] != '') ? word_limiter($current['content'], 50) : ''; ?> - Visit <?php echo current_url(); ?>" class="share-item" title="email">
                    EMAIL
                </a>
            </div>
        </div>
        <div class="pt-1 mt-05 pb-3 text-center">
            <p class="f-12 midGray px-3">
              <span class="share-url mb-2 d-block"><?php echo current_url(); ?></span> 
              <span class="btn black-bg white h-40 share-url-copy cursor" onclick="copyToClipboard('<?php echo current_url(); ?>')">Copy link</span>
            </p>
        </div>
    </div>
    <div class="popup p-0 reason-popup">
        <span class="close_page close py-3">×</span>
        <div class="popup-header">
            <p class="f-14 bold text-center">Reason of Class Rejection</p>
        </div>
        <div class="popup-body py-5">
            <h3 class="f-16 semibold mb-2" style="display: none">Class “<span class="rejected-class-title">Full Body</span>” is rejected</h3>
            <p class="f-16 mb-2 midGray rejected-class-desc">desc</p>
        </div>
    </div>
    <div class="popup p-0 difficulty-explanation-popup">
        <span class="close_page close" style="padding: 25px;">×</span>
        <div class="popup-header py-2">
            <p class="f-14 bold text-center buy_class_header">DIFFICULTY LEVELS: EXPLANATION</p>
        </div>
        <div class="popup-body pb-0 py-3 px-4 mob-height" style="overflow-y: auto">
            <h5 class="f-14 medium mb-2 line-height-small">BEGINNER</h5>
            <ul>
                <li>Workout is done on the front of the machine only.</li>
                <li>Short sequences per body part.</li>
                <li>Exercises are not held long (no more than 2 minutes per leg for example)</li>
                <li>Progressions are short (one exercise + 1 variation only: For example, teach Elevator Lunge, followed by a Carriage Kick only, no pulse after).</li>
                <li>Transition time is not optimized /  slow transitions. Give them time to adapt.</li>
            </ul>
            <hr class="my-3">
            <h5 class="f-14 medium mb-2 line-height-small">INTERMEDIATE</h5>
            <ul>
                <li>Mix of front and back of the machines.</li>
                <li>Exercises are held longer.</li>
                <li>Longer sequences per body part.</li>
                <li>Progressions are longer.</li>
                <li>Short transitions.</li>
            </ul>
            <hr class="my-3">
            <h5 class="f-14 medium mb-2 line-height-small">ADVANCED</h5>
            <ul>
                <li>Long duration for all the exercises on the front.</li>
                <li>Long sequences per body part.</li>
                <li>Inclusion of advanced variations such as the deep lunge and deep pulse</li>
                <li>Combo exercises on front and on back.</li>
                <li>Long progressions.</li>
                <li>Super fast transitions.</li>
                <li>Inclusion of the spring with shroud.</li>
            </ul>
        </div>
    </div>
    <div class="popup p-0 buy-class">
        <span class="close_page close" style="padding: 25px;">×</span>
        <div class="popup-header py-2">
            <p class="f-14 bold text-center buy_class_header">Buy This Class</p>
        </div>
        <div class="popup-body pb-0 py-4">
            <form id="buy_class_form" action="register/buy_class" class="row" autocomplete="off">
                <input type="hidden" name="stripe_customer" value="<?php echo (isset($logged_user)) ? $logged_user['stripe_customer'] : '' ?>">
                <div class="col-12">
                    <h1 class="mb-2 h4 class_title">CLASS TITLE</h1>
                    <p class="f-14 normal lh-25 mb-5 buy_class_desc">Unlimited access</p>
                    <div class="panel big-padding for--loading p-0">
                        <div class="row w100 mb-5">
                            <div class="col-12 px-0">
                                <h4 class="line-height-small f-14">Payment Details</h4>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 flex">
                                <div class="input-container">
                                    <input type="text" name="card[name]" class="line-input" id="name" placeholder="Name on card">
                                    <!--<span class="input-label">Name on card</span>-->
                                    <span id="name_error" class="input-error"></span>
                                </div>
                            </div>
                            <div class="col-12 flex">
                                <div class="input-container mb-0">
                                    <input type="text" name="card[number]" class="line-input" id="card-number-new" maxlength="19" placeholder="Card number" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">Card number</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_month]" class="line-input card_month-new" id="card-month-year-new" placeholder="MM" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">MM</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_year]" class="line-input card_year-new" id="card-month-year-new" placeholder="YY" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">YY</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 60px" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[cvc]" class="line-input text-right card-cvc-new" id="card-cvc" placeholder="CVC" maxlength="3" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">CVC</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row w100 mt-5 mb-0 mt-mob-2">
                            <div class="col-12 px-0">
                                <!-- <h4 class="f-14 flex aic jcsb semibold">
                                    Single Class purchase
                                    <span class="ml-auto"><b>$<span class="subscription-price"><?php echo $_ENV['class_price']; ?></span></b></span>
                                </h4> -->
                                <div class="panel mb-2 subscription-option p-0 border selected">
                                    <div class="radio-button f-16 rtl w100">
                                        <input type="radio" name="price" id="buy-this-class" checked value="<?php echo $_ENV['class_price']; ?>" onchange="$('.subscription-option').removeClass('selected');$(this).closest('.subscription-option').addClass('selected');$('#buy_class_form').attr('action', 'register/buy_class');$('.buy_class_desc').text('Unlimited access');$('.buy_class_header').text('Buy This Class');$('.buy-rent-submit').text('BUY THIS CLASS');">
                                        <label for="buy-this-class" class="flex aic jcsb f-14 py-2 pl-2 pr-4 px-mob-1 mr-2"><b>BUY THIS CLASS </b> $<?php echo $_ENV['class_price']; ?></label>
                                    </div>
                                </div>
                                <div class="panel mb-0 subscription-option p-0 border">
                                    <div class="radio-button f-16 rtl w100">
                                        <input type="radio" name="price" id="rent-this-class" value="<?php echo $_ENV['class_rent']; ?>" onchange="$('.subscription-option').removeClass('selected');$(this).closest('.subscription-option').addClass('selected');$('#buy_class_form').attr('action', 'register/rent_class');$('.buy_class_desc').text('Rent this class for 24h');$('.buy_class_header').text('Rent This Class');$('.buy-rent-submit').text('RENT THIS CLASS');">
                                        <label for="rent-this-class" class="flex aic jcsb f-14 py-2 pl-2 pr-4 px-mob-1 mr-2"><b>RENT THIS CLASS </b> $<?php echo $_ENV['class_rent']; ?></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="mb-5 mt-3">
                        <div class="row w100">
                            <div class="col-12 flex aic jcc">
                                <input type="hidden" name="class_title" class="class_title_input">
                                <input type="hidden" name="class_id" class="class_id">
                                <input type="hidden" name="seller_id" class="seller_id">
                                <!-- <input type="hidden" name="price" class="price" value="<?php echo $_ENV['class_price']; ?>"> -->
                                <input type="hidden" name="buyer_id" id="buyer_id" value="<?php echo isset($logged_user) ? $logged_user['id'] : 0; ?>" />
                                <input type="hidden" name="firstname" id="subscriber_firstname" value="<?php echo isset($logged_user) ? $logged_user['firstname'] : ""; ?>" />
                                <input type="hidden" name="lastname" id="subscriber_lastname" value="<?php echo isset($logged_user) ? $logged_user['lastname'] : ""; ?>" />
                                <input type="hidden" name="email" id="subscriber_email" value="<?php echo isset($logged_user) ? $logged_user['email'] : ""; ?>" />
                                <input type="hidden" name="stripe_customer" id="stripe_customer" value="<?php echo isset($logged_user) ? $logged_user['stripe_customer'] : ""; ?>" />
                                <button type="submit" class="btn black-bg btn-tall btn-wide white buy-rent-submit">BUY THIS CLASS</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="popup p-0 payout-popup">
        <span class="close_page close" style="padding: 25px;">×</span>
        <div class="popup-header py-2">
            <p class="f-14 bold text-center">Request Payout</p>
        </div>
        <div class="popup-body pb-0 py-4">
            <form id="request_payout_form" action="register/request_payout" class="row" autocomplete="off">
                <div class="col-12">
                    <?php if(isset($teacher) AND $teacher['stripe_account_card_added'] != 1){ ?>
                    <p class="f-14 normal lh-25 mb-2 flex aic jcc">
                        <svg aria-hidden="true" class="SVGInline-svg SVGInline--cleaned-svg SVG-svg Icon-svg Icon--info-svg Icon--hoverable-svg Icon-color-svg Icon-color--gray-svg" height="12" width="12" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" style="width: 15px;height: 15px;margin-right: 15px;fill: #444;"><path d="M9 8a1 1 0 0 0-1-1H5.5a1 1 0 1 0 0 2H7v4a1 1 0 0 0 2 0zM4 0h8a4 4 0 0 1 4 4v8a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4zm4 5.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" fill-rule="evenodd"></path></svg>
                        Your card must be a debit card, NOT a credit card!
                    </p>
                    <?php }else{ ?>
                    <p class="f-14 normal lh-25 mb-2 flex aic jcc">
                        <svg aria-hidden="true" class="SVGInline-svg SVGInline--cleaned-svg SVG-svg Icon-svg Icon--info-svg Icon--hoverable-svg Icon-color-svg Icon-color--gray-svg" height="12" width="12" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" style="width: 15px;height: 15px;margin-right: 15px;fill: #444;"><path d="M9 8a1 1 0 0 0-1-1H5.5a1 1 0 1 0 0 2H7v4a1 1 0 0 0 2 0zM4 0h8a4 4 0 0 1 4 4v8a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4zm4 5.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" fill-rule="evenodd"></path></svg>
                        Minimum payout amount is <?php echo (isset($teacher) AND $teacher['country'] == 'US') ? '$' : ((isset($teacher) AND $teacher['country'] == 'CA') ? 'CAD' : ''); ?>50<?php echo (isset($teacher) AND $teacher['country'] != 'US' AND $teacher['country'] != 'CA') ? '€' : ''; ?>.
                    </p>
                    <?php } ?>
                    <div class="panel big-padding for--loading p-0">
                        <?php if(isset($teacher) AND $teacher['stripe_account_card_added'] != 1){ ?>
                        <div class="row w100 mb-5 mt-3">
                            <div class="col-12 px-0">
                                <h4 class="line-height-small f-14">Card Details</h4>
                            </div>
                        </div>
                        <?php } ?>
                        <div class="row">
                            <?php if(isset($teacher) AND $teacher['stripe_account_card_added'] != 1){ ?>
                            <div class="col-12 flex">
                                <div class="input-container">
                                    <input type="text" name="card[name]" class="line-input" id="name" placeholder="Name on card">
                                    <!--<span class="input-label">Name on card</span>-->
                                    <span id="name_error" class="input-error"></span>
                                </div>
                            </div>
                            <div class="col-12 flex">
                                <div class="input-container mb-0">
                                    <input type="text" name="card[number]" class="line-input" id="card-number-new2" maxlength="19" placeholder="Card number" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">Card number</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_month]" class="line-input card_month-new2" id="card-month-year-new2" placeholder="MM" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">MM</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_year]" class="line-input card_year-new2" id="card-month-year-new2" placeholder="YY" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">YY</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 60px" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[cvc]" class="line-input text-right card-cvc-new2" id="card-cvc2" placeholder="CVC" maxlength="3" onkeypress="return onlyNumberKey(event)">
                                    <!--<span class="input-label">CVC</span>-->
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                            </div>
                            <?php } ?>
                            <div class="col-12 flex mt-3">
                                <div class="p-2 lightGray-bg w100">
                                    <div class="input-container with-currency" data-currency="<?php echo (isset($teacher) AND $teacher['country'] == 'US') ? '$' : '€'; ?>">
                                        <input type="text" name="amount" class="line-input px-2" id="name" placeholder="Amount" data-type='currency'>
                                        <!-- <span class="input-label">Amount</span> -->
                                        <span id="amount_error" class="input-error"></span>
                                    </div>
                                </div>
                            </div>
                            <hr class="my-5">
                        </div>
                        <div class="row w100">
                            <div class="col-12 flex aic jcc">
                                <button type="submit" class="btn black-bg btn-tall btn-wide white">SEND</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="popup p-0 rate-popup">
        <span class="close_page close py-3"><img src="images/close-grey-icon.svg"></span>
        <div class="popup-header">
            <p class="f-14 bold text-center"><?php echo empty($rated) ? 'Love This Class? Rate It!' : 'Class Rated'; ?></p>
        </div>
        <div class="popup-body">
            <div class="rate-class <?php echo (!empty($rated)) ? 'no-border mb-0 pb-1 no-rate' : '' ?>">
                <span class="rate-value <?php echo (!empty($rated) AND $rated[0]['rate'] == '1') ? 'active' : ''; ?>">1</span>
                <span class="rate-value <?php echo (!empty($rated) AND $rated[0]['rate'] == '2') ? 'active' : ''; ?>">2</span>
                <span class="rate-value <?php echo (!empty($rated) AND $rated[0]['rate'] == '3') ? 'active' : ''; ?>">3</span>
                <span class="rate-value <?php echo (!empty($rated) AND $rated[0]['rate'] == '4') ? 'active' : ''; ?>">4</span>
                <span class="rate-value <?php echo (!empty($rated) AND $rated[0]['rate'] == '5') ? 'active' : ''; ?>">5</span>
            </div>
            <div class="py2 text-center">
                <input type="hidden" name="type" id="class_type" value="<?php echo service('uri')->getSegment(1); ?>">
                <input type="hidden" name="class_rate_val" id="class_rate_val">
                <?php if(empty($rated)){ ?>
                    <a href="javascript:;" class="btn darkGray-bg white rate_class close" data-class-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-user-id="<?php echo (isset($logged_user['id']) AND $logged_user['id'] != '') ? $logged_user['id'] : 0; ?>" title="Rate">Rate</a>
                <?php } ?>
            </div>
        </div>
    </div>
    <div class="popup p-0 add-to-playlist-popup">
        <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
        <div class="popup-header">
            <p class="f-14 bold text-left ml-3">add to playlist</p>
        </div>
        <div class="popup-body px-0 pb-0" style="overflow: hidden;">
            <div class="playlists-list bg--loading-small"></div>
            <div class="new_playlist_container p-3">
                <div class="input-container mb-3">
                    <input type="text" class="line-input f-14 playlist_name" placeholder="Playlist name">
                </div>
                <hr class="my-0">
                <span class="btn black-bg btn-sm white save_playlist w100 cursor mt-3 py-0">SAVE</span>
            </div>
        </div>
    </div>
    <div class="popup p-0 edit-playlist-popup">
        <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
        <h3 class="f-14 semibold text-left pl-3 pt-2 pb-2 pl-mob-2 mb-0 bottom-border">EDIT PLAYLIST</h3>
        <div class="popup-body px-3 pb-3">
            <form class="playlist-save" action="/playlists/save" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-12 new_playlist_container mt-3">
                        <div class="input-container mb-2">
                            <input type="text" name="title" maxlength="30" class="line-input f-14 edit_playlist_title make_slug" data-slug_target="#slug" placeholder="Playlist name">
                        </div>
                        <!--<h4 class="f-10 bold">DESCRIPTION</h4>
                        <div class="input-container mb-2">
                            <textarea type="text" name="content" maxlength="80" class="line-input f-14 edit_playlist_desc" placeholder="Enter (max 80 characters)" style="min-height: 100px;padding: 20px;"></textarea>
                        </div>
                        <hr class="my-3">
                        <div class="image_container flex aic">
                            <div class="upload-image mr-2" style="width: 120px">
                                <input type="file" name="image" id="image">
                                <img src="admin_assets_new/images/upload-icon.svg" alt="" class="image_preview no-img edit_playlist_image" style="opacity: 0.3">
                            </div>
                            <div class="image_container flex flex-column midGray f-14">
                                <div class="mb-0 image_options mb-mob-0" style="display: none;">
                                    <a href="javascript:;" class="link link-black black f-12 text-underline mb-1 replace_image flex aic">
                                        <img src="admin_assets_new/images/upload-icon.svg" width="10" alt="" class="img-fluid mr-05" />
                                        Replace Current</a>
                                </div>
                                <span class="f-10 line-height-normal">Max. file size is 2mb.<br>Desirable size: 600px x 600px.</span>
                            </div>
                        </div>-->
                        <!-- <hr class="my-3">
                        <div class="checkbox mb-2" id="private_container">
                            <input type="checkbox" class="edit_playlist_private" id="edit_private_playlist" onchange="$(this).is(':checked') ? $('#private').val(1) : $('#private').val(0)">
                            <label for="edit_private_playlist" class="f-12">Make playlist private</label>
                            <input type="hidden" name="private" id="private" value="0">
                        </div> -->
                        <hr class="my-3">
                    </div>
                    <div class="col-12 flex aic jcc">
                        <input type="hidden" name="id" value="0" class="edit_playlist_id" />
                        <input type="hidden" name="user_id" value="<?php echo session('user'); ?>" class="edit_playlist_user_id" />
                        <input type="hidden" name="slug" id="slug" value="0" class="edit_playlist_slug" />
                        <button type="submit" class="btn black-bg white w100 cursor mt-0 create_add_button">SAVE</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="popup p-0 mini-popup hub-popup">
        <span class="close_page close py-3">×</span>
        <div class="popup-body p-0">
            <div class="row-vertical aic">
                <p class="text-center pb-2">Lagree HUB<sup>TM</sup> - Switch platform</p>
            </div>
            <div class="footer-hub-items">
                <div class="px-0">
                    <a href="https://www.lagreefitness.com/" target="_blank" class="footer-hub-item top-border" title="lagree fitness hub">
                        LAGREE FITNESS
                    </a>
                </div>
                <div class="px-0">
                    <a class="footer-hub-item" title="lagree fitness">
                        <span style="color: #969696">LAGREE ON DEMAND</span>
                    </a>
                </div>
                <div class="px-0">
                    <a href="https://www.shopmaximumfitness.com/" class="footer-hub-item" target="_blank" title="lagree shop hub">
                        LAGREE SHOP
                    </a>
                </div>
                <div class="px-0">
                    <a href="https://www.lagreeacademy.com/" class="footer-hub-item" target="_blank" title="lagree shop hub">
                        LAGREE ACADEMY
                    </a>
                </div>
                <!-- <div class="px-0">
                    <a class="footer-hub-item" target="_blank" title="lagree home hub">
                        <img src="img/lagreeHome.svg" alt="" class="img-fluid">
                    </a>
                </div> -->
            </div>
        </div>
    </div>
    <div class="popup p-0 cookie-popup">
        <span class="close_page cookie-popup-close close py-3" onclick="createCookie('<?php echo isset($settings['cookie_name']) ? $settings['cookie_name'] : ''; ?>', 'opened', 1);">×</span>
        <div class="popup-body p-0">
            <?php if(isset($settings['popup_image']) AND $settings['popup_image'] != ''){ ?>
                <div class="cookie-popup-image"><img src="<?php echo $settings['popup_image']; ?>" alt="" class="img-fluid" /></div>
            <?php } ?>
            <div class="popup-body-cookie">
                <h3 class="f-18 bold line-height-normal mb-2 text-uppercase"><?php echo (isset($settings['popup_title']) AND $settings['popup_title'] != '') ? $settings['popup_title'] : ''; ?></h3>
                <p class="f-14 line-height-normal mb-0"><?php echo (isset($settings['popup_description']) AND $settings['popup_description'] != '') ? $settings['popup_description'] : ''; ?></p>
                <?php echo ""; ?>
                <?php if(!isset($logged_user)){ ?>
                    <?php if(isset($settings['popup_link']) AND $settings['popup_link'] != ''){ ?>
                        <a href="<?php echo $settings['popup_link']; ?>" class="btn black-bg white mt-4"><?php echo (isset($settings['popup_button_label']) AND $settings['popup_button_label'] != '') ? $settings['popup_button_label'] : 'More Info'; ?></a>
                    <?php } ?>
                <?php }else{ ?>
                    <?php if(isset($settings['popup_link_logged']) AND $settings['popup_link_logged'] != ''){ ?>
                        <a href="<?php echo $settings['popup_link_logged']; ?>" class="btn black-bg white mt-4"><?php echo (isset($settings['popup_button_label_logged']) AND $settings['popup_button_label_logged'] != '') ? $settings['popup_button_label_logged'] : 'More Info'; ?></a>
                    <?php } ?>
                <?php } ?>
            </div>
        </div>
    </div>
    <div class="popup p-0 survey-popup" style="max-width: 550px;">
        <span class="close_page close py-3 cancel_survey" onclick="cancel_survey(<?php echo isset($logged_user) ? session('user') : 0; ?>, <?php echo session('active_survey'); ?>)">×</span>
        <div class="popup-body p-0 px-5 text-center" style="max-height: 80vh;overflow-y: auto;">

        </div>
    </div>
    <div class="popup p-0 survey-thank-you" style="max-width: 550px;">
        <span class="close_page close py-3 cancel_survey" onclick="cancel_survey(<?php echo isset($logged_user) ? session('user') : 0; ?>, <?php echo session('active_survey'); ?>)">×</span>
        <div class="popup-body p-5 text-center" style="max-height: 80vh;overflow-y: auto;">
            <p class="f-16 mb-3">Thank you. We appreciate your feedback! ❤️</p>
            <a href="javascript:;" class="btn black-bg mb-mob-0 py-0 white close" title="CLOSE">CLOSE</a>
        </div>
    </div>
</div>

<div class="msg-popup">
    <span class="close_popup"><img src="images/msg-close.svg" alt="" class="img-fluid" /></span>
    <p class="m-0">Your email has been sent</p>
</div>