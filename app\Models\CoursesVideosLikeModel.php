<?php namespace App\Models;

use CodeIgniter\Model;

class CoursesVideosLikeModel extends Model
{
    protected $table = 'courses_videos_likes';
	protected $allowedFields = ['video_id', 'date', 'rate', 'user_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}