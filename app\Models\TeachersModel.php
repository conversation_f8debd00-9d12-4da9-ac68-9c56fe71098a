<?php namespace App\Models;

use CodeIgniter\Model;

class TeachersModel extends Model
{
    protected $table = 'teachers';
	protected $allowedFields = ['parent_id', 'firstname', 'lastname', 'slug', 'image', 'cover_image', 'mob_cover_image', 'webp_image', 'webp_cover_image', 'webp_mob_cover_image','certified', 'location', 'year_certified', 'facebook', 'instagram', 'super_admin', 'content', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'country', 'email', 'password', 'stripe_account', 'stripe_account_card_added'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'firstname'     => 'required|min_length[2]',
        'lastname'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[teachers.slug,id,{id}]',
        'email'     => 'required',
        'password'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	public function login_user(array $data){
		$response['success'] = FALSE;
		$users = $this->where(['email' => $data['email'], 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))))])->findAll();
		if (count($users) == 1){
			$response['user_id'] = $users[0]['id'];
			$response['super_admin'] = $users[0]['super_admin'];
			$response['user_info'] = $users[0];
			$response['success'] = TRUE;
		}else{
			$response['error'] = 'Bad username or password.';
		}
		return $response;
	}

    public function all_teachers($start = 0, $limit = 0, $search_term = NULL, $order = "certified desc, firstname ASC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (teachers.firstname LIKE '%$search_term%' OR teachers.lastname LIKE '%$search_term%')" : "";
        $data = $this->query("SELECT teachers.*, COALESCE(x.cnt,0) AS classesCount, COALESCE(y.ce,0) AS unpaidClasses
                            FROM teachers
                            LEFT OUTER JOIN (SELECT teacher, count(*) as cnt FROM classes WHERE classes.deleted_at IS NULL AND classes.status = 0 GROUP BY teacher) x ON x.teacher = teachers.id
                            LEFT OUTER JOIN (SELECT teacher_id, count(*) as ce FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.paid = 1) y ON y.teacher_id = teachers.id
                            WHERE teachers.deleted_at IS NULL
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_certified_teachers($start = 0, $limit = 0, $search_term = NULL, $order = "certified desc, hasHistory DESC, firstname ASC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (teachers.firstname LIKE '%$search_term%' OR teachers.lastname LIKE '%$search_term%')" : "";
        $data = $this->query("SELECT teachers.*, 
                            COALESCE(x.cnt,0) AS classesCount, 
                            (SELECT count(*) as ce FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.paid IN (1,2,3) AND calendar_events.teacher_id = teachers.id AND calendar_events.date <= NOW()) AS unpaidClasses, 
                            (SELECT count(*) FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.teacher_id = teachers.id AND calendar_events.date <= NOW()) AS hasHistory
                            FROM teachers
                            LEFT OUTER JOIN (SELECT teacher, count(*) as cnt FROM classes WHERE classes.deleted_at IS NULL AND classes.status = 0 GROUP BY teacher) x ON x.teacher = teachers.id
                            WHERE teachers.deleted_at IS NULL
                            AND teachers.certified = 1
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function teachers_history($id = 0, $start = 0, $limit = 100000, $search_term = NULL, $order = "sort_status DESC, date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search_term = strtolower($search_term);
        $search =  ($search_term != NULL) ? "HAVING (calendar_events.date LIKE '%$search_term%' OR calendar_events.date LIKE '%$search_term%' OR LOWER(teacher) LIKE '%$search_term%' OR LOWER(model) LIKE '%$search_term%')" : "";

        // ASC = paid
        // DESC = NOT paid

        if(strpos($order, 'ASC') !== FALSE){
            $sort_status = "IF(calendar_events.paid IN (0,4), 0, IF(calendar_events.paid = 5, 2, 1)) as sort_status, ";
        }else{
            $sort_status = "IF(calendar_events.paid IN (0,4), 1, IF(calendar_events.paid = 5, 0, 2)) as sort_status, ";
        };

        // $data = "SELECT calendar_events.id, $sort_status calendar_events.paid, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.class_id, calendar_events.model_id, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher, teachers.image as teacher_image, CONCAT(models.firstname, ' ', models.lastname) as model, IF(calendar_events.date <= NOW(), 'Recorded', 'Upcoming') as video_status
        //                             FROM calendar_events 
        //                             LEFT OUTER JOIN teachers ON teachers.id = calendar_events.teacher_id
        //                             LEFT OUTER JOIN models ON models.id = calendar_events.model_id
        //                             WHERE calendar_events.teacher_id = " . $id . "
        //                             AND calendar_events.deleted_at IS NULL
        //                             AND calendar_events.date <= NOW()
        //                             " . $search . "
        //                             ORDER BY " . $order . "
        //                             " . $limit_size . "";
        // echo '<pre>';
        // print_r($data);
        // die();
        
        $data = $this->query("SELECT calendar_events.id, $sort_status calendar_events.paid, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.class_id, calendar_events.model_id, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher, teachers.image as teacher_image, CONCAT(models.firstname, ' ', models.lastname) as model, IF(calendar_events.date <= NOW(), 'Recorded', 'Upcoming') as video_status
                                    FROM calendar_events 
                                    LEFT OUTER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                    LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                    WHERE calendar_events.teacher_id = " . $id . "
                                    AND calendar_events.deleted_at IS NULL
                                    AND calendar_events.date <= NOW()
                                    " . $search . "
                                    ORDER BY " . $order . "
                                    " . $limit_size . "
                                ")->getResultArray();
        // echo '<pre>';
        // print_r($data);
        // die();
        
        return $data;
    }

    public function teachers_history_count(){
        return NULL;
    }

    public function all_noncertified_teachers($start = 0, $limit = 0, $search_term = NULL, $order = "certified desc, firstname ASC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (teachers.firstname LIKE '%$search_term%' OR teachers.lastname LIKE '%$search_term%')" : "";
        $data = $this->query("SELECT teachers.*, COALESCE(x.cnt,0) AS classesCount, COALESCE(y.ce,0) AS unpaidClasses
                            FROM teachers
                            LEFT OUTER JOIN (SELECT teacher, count(*) as cnt FROM classes WHERE classes.deleted_at IS NULL AND classes.status = 0 GROUP BY teacher) x ON x.teacher = teachers.id
                            LEFT OUTER JOIN (SELECT teacher_id, count(*) as ce FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.paid = 1) y ON y.teacher_id = teachers.id
                            WHERE teachers.deleted_at IS NULL
                            AND (certified = 0 OR certified IS NULL)
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT teachers.*, COALESCE(x.cnt,0) AS classesCount, COALESCE(y.ce,0) AS unpaidClasses
                                        FROM teachers
                                        LEFT OUTER JOIN (SELECT teacher, count(*) as cnt FROM classes WHERE classes.deleted_at IS NULL AND classes.status = 0 GROUP BY teacher) x ON x.teacher = teachers.id
                                        LEFT OUTER JOIN (SELECT teacher_id, count(*) as ce FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.paid = 1) y ON y.teacher_id = teachers.id
                                        WHERE teachers.deleted_at IS NULL
                                        AND teachers.slug = '" . $slug . "'
                                    ")->getRowArray();
        return $data;
    }

    public function teacher_classes($id = 0){
        $classes_model = model('ClassesModel');

        $data = $classes_model->query("SELECT classes.*, difficulty.title as diff, teachers.slug as teach_slug,
                                        COALESCE(x.cnt,0) AS countView,
                                        COALESCE(y.rate,0) AS classRate,
                                        CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach,
                                        video_state.video_time as video_state,
                                        GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                        IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                                        IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                                        IF(classes.id IN (
                                            SELECT * FROM (
                                                    SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                            ) as subquery
                                        ), 1, 0) as purchased,
                                        IF(classes.id IN (
                                                SELECT * FROM (
                                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                                ) as classes_rented
                                        ), 1, 0) as rented,
                                        IF(classes.id IN (
                                                SELECT * FROM (
                                                        SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                ) as subquery2
                                        ), 1, 0) as watched
                                        FROM classes
                                        LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                        LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                        LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                        LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                        LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                        LEFT JOIN teachers ON (teachers.id = classes.teacher AND teachers.status = 0)
                                        LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                                        LEFT JOIN subscribers_classes ON (subscribers_classes .class_id = classes.id)
                                        LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                                        WHERE classes.deleted_at IS NULL
                                        AND classes.status = 0
                                        AND classes.teacher = " . $id . "
                                        GROUP BY classes.id
                                        ORDER BY classes.updated_at desc
                                    ")->getResultArray();
        return $data;
    }

    public function teacher_classes_ids($id = 0){
        $classes_model = model('ClassesModel');

        $list = [];
        if($id != 0){
            $data = $classes_model->query("SELECT classes.id
                                        FROM classes
                                        WHERE classes.deleted_at IS NULL
                                        AND classes.status = 0
                                        AND classes.teacher = " . $id . "
                                        ORDER BY classes.updated_at desc
                                    ")->getResultArray();

            $ids = [];
            foreach($data as $class_id){
                $ids[] = $class_id['id'];
            }

            $list = $ids;
        }

        return $list;
    }

    public function getUsers($id = false){
		if ($id === false){
			return $this->findAll();
		}
		return $this->where(['id' => $id])->first();
	}

	protected function prepare_data(array $data){
		if (isset($data['data']['password']) AND $data['data']['password'] <> ''){
			$data['data']['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['data']['password']))));
		}else{
			unset($data['data']['password']);
		}
		return $data;
	}
}