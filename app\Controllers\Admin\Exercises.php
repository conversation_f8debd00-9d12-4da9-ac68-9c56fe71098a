<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Exercises extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ExercisesModel');
	}

    public function index()
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['machines'] = exercises_existing_machines();
        $data['languages'] = exercises_existing_languages();
        $data['difficulty'] = exercises_existing_difficulty();
        $data['duration_less10'] = exercises_existing_duration_less10();
        $data['duration_less25'] = exercises_existing_duration_less25();
        $data['duration_more25'] = exercises_existing_duration_more25();
        $data['body_parts'] = exercises_existing_body_parts();
        $data['all_teachers'] = exercises_existing_all_teachers();

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('exercises', $search_term);
            if($search_term == ""){
                $this->set_search('exercises', "0");
                return redirect()->to('/admin/exercises');
            }
        }else if(session('exercises_search') !== "" AND session('exercises_search') !== "0"){
            $search_term = session('exercises_search');
        }else{
            $search_term = "0";
        };

        if(!empty($post)){
            // $this->set_filter($post);
            $session->set('exercises_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('exercises_filter');
            // $session->set('classes_filter', '');
        }

        $data['page'] = 1;
        $page = 1;
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('exercises_sort'));

        $data['all_exercises'] = $this->model->filter_exercises_admin(($page * session('exercises_per_page')) - session('exercises_per_page'), session('exercises_per_page'), $search_term, session('exercises_sort'), session('exercises_filter'));
        $count = $this->model->filter_exercises_admin_count(0, 10000, $search_term, session('exercises_sort'), session('exercises_filter'));
        $data['exercises_count'] = count($count);

        echo view('admin/exercises/filter_view', $data);
    }
    public function finder()
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_micro_exercises'] = $this->model->all_micro_exercises(0, 20, 0, 'exercises.title ASC');
        $data['all_mini_exercises'] = $this->model->all_mini_exercises(0, 20, 0, 'exercises.title ASC');
        $data['all_mega_exercises'] = $this->model->all_mega_exercises(0, 20, 0, 'exercises.title ASC');

        $data['count_all_micro_exercises'] = $this->model->all_micro_exercises();
        $data['count_all_mini_exercises'] = $this->model->all_mini_exercises();
        $data['count_all_mega_exercises'] = $this->model->all_mega_exercises();

		echo view('admin/exercises/finder_view', $data);
    }
    public function clear_filter()
    {
        $session = \Config\Services::session();
        $session->set('exercises_filter', '');
        return redirect()->to('/admin/exercises');
    }
    public function pending($page = 1)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = exercises_existing_machines();
        $data['languages'] = exercises_existing_languages();
        $data['difficulty'] = exercises_existing_difficulty();
        $data['duration_less10'] = exercises_existing_duration_less10();
        $data['duration_less25'] = exercises_existing_duration_less25();
        $data['duration_more25'] = exercises_existing_duration_more25();
        $data['body_parts'] = exercises_existing_body_parts();
        $data['all_teachers'] = exercises_existing_all_teachers();

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('exercises', $search_term);
            if($search_term == ""){
                $this->set_search('exercises', "0");
                return redirect()->to('/admin/exercises');
            }
        }else if(session('exercises_search') !== "" AND session('exercises_search') !== "0"){
            $search_term = session('exercises_search');
        }else{
            $search_term = "0";
        };
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('exercises_sort'));
        $data['all_exercises'] = $this->model->all_pending(($page * session('exercises_per_page')) - session('exercises_per_page'), session('exercises_per_page'));
        $data['exercises_count'] = count($this->model->all_exercises(0, 0, $search_term, session('exercises_sort'), "0,1"));
        $data['page'] = $page;

        echo view('admin/exercises/pending_view', $data);
    }
    public function page($page = 1)
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['machines'] = exercises_existing_machines();
        $data['languages'] = exercises_existing_languages();
        $data['difficulty'] = exercises_existing_difficulty();
        $data['duration_less10'] = exercises_existing_duration_less10();
        $data['duration_less25'] = exercises_existing_duration_less25();
        $data['duration_more25'] = exercises_existing_duration_more25();
        $data['body_parts'] = exercises_existing_body_parts();
        $data['all_teachers'] = exercises_existing_all_teachers();

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('exercises', $search_term);
            if($search_term == ""){
                $this->set_search('exercises', "0");
                return redirect()->to('/admin/exercises/' . $page);
            }
        }else if(session('exercises_search') !== "" AND session('exercises_search') !== "0"){
            $search_term = session('exercises_search');
        }else{
            $search_term = "0";
        };

        if(!empty($post)){
            // $this->set_filter($post);
            $session->set('exercises_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('exercises_filter');
            // $session->set('classes_filter', '');
        }

        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('exercises_sort'));
        $data['all_exercises'] = $this->model->filter_exercises_admin(($page * session('exercises_per_page')) - session('exercises_per_page'), session('exercises_per_page'), $search_term, session('exercises_sort'), session('exercises_filter'));
        $data['exercises_count'] = count($this->model->filter_exercises_admin(0, 10000, $search_term, session('exercises_sort'), session('exercises_filter')));
        $data['page'] = $page;

        echo view('admin/exercises/filter_view', $data);
    }
    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');

        $finder_search_term = session('finder_search_term');
        $finder_machine = session('finder_machine');

        $data['finder_search_term'] = $finder_search_term;
        $data['finder_machine'] = $finder_machine;

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1 OR session('teacher') == NULL){
        //     return redirect()->to('admin/classes');
        // }

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
        $data['exercise_type'] = $db->query('SELECT * FROM exercise_type  ORDER BY sort asc')->getResultArray();
		$data['range_of_motion'] = $db->query('SELECT * FROM range_of_motion  ORDER BY sort asc')->getResultArray();
		$data['direction'] = $db->query('SELECT * FROM direction  ORDER BY sort asc')->getResultArray();
		$data['tension'] = $db->query('SELECT * FROM tension  ORDER BY sort asc')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['body_position'] = $db->query('SELECT * FROM body_position  ORDER BY sort asc')->getResultArray();
		$data['terminology'] = $db->query('SELECT * FROM terminology ORDER BY sort asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY sort asc, title asc')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
		$data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();
		// -------------------------------------------------------------
        $current_tension = $db->query("SELECT * FROM exercises_tension WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_tension)){
            foreach($current_tension as $k => $single){
                $data['current_tension'][] = $single['exercise_tension'];
            }
        }else{
            $data['current_tension'] = [];
        }
        $current_exercise_type = $db->query("SELECT * FROM exercises_exercise_type WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_exercise_type)){
            foreach($current_exercise_type as $k => $single){
                $data['current_exercise_type'][] = $single['exercise_exercise_type'];
            }
        }else{
            $data['current_exercise_type'] = [];
        }
        $current_direction = $db->query("SELECT * FROM exercises_direction WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_direction)){
            foreach($current_direction as $k => $single){
                $data['current_direction'][] = $single['exercise_direction'];
            }
        }else{
            $data['current_direction'] = [];
        }
        $current_range_of_motion = $db->query("SELECT * FROM exercises_range_of_motion WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_range_of_motion)){
            foreach($current_range_of_motion as $k => $single){
                $data['current_range_of_motion'][] = $single['exercise_range_of_motion'];
            }
        }else{
            $data['current_range_of_motion'] = [];
        }

		// -------------------------------------------------------------
        $current_machines = $db->query("SELECT * FROM exercises_machine WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['exercise_machine'] ;
            }
        }else{
            $data['current_machines'] = [];
        }
		$current_body_parts = $db->query("SELECT * FROM exercises_body_parts WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['exercise_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = [];
        }
		$current_body_position = $db->query("SELECT * FROM exercises_body_position WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_position)){
            foreach($current_body_position as $k => $single){
                $data['current_body_position'][] = $single['exercise_body_position'] ;
            }
        }else{
            $data['current_body_position'] = [];
        }
        // echo '<pre>';
        // print_r($data['current_body_position']);
        // die();

		$current_terminology = $db->query("SELECT * FROM exercises_terminology WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_terminology)){
            foreach($current_terminology as $k => $single){
                $data['current_terminology'][] = $single['exercise_terminology'] ;
            }
        }else{
            $data['current_terminology'] = [];
        }
		$current_accessories = $db->query("SELECT * FROM exercises_accessories WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['exercise_accessories'] ;
            }
        }else{
            $data['current_accessories'] = [];
        }
		$current_tensions = $db->query("SELECT * FROM exercises_tensions WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_tensions)){
            foreach($current_tensions as $k => $single){
                $data['current_tensions'][] = $single['exercise_tensions'];
            }
        }else{
            $data['current_tensions'] = [];
        }
		$current_springs = $db->query("SELECT * FROM exercises_springs WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['exercise_springs'] ;
            }
        }else{
            $data['current_springs'] = [];
        }
        $current_tempo = $db->query("SELECT * FROM exercises_tempo WHERE exercise_id = " . $edit_id)->getResultArray();
        if(!empty($current_tempo)){
            foreach($current_tempo as $k => $single){
                $data['current_tempo'][] = $single['exercise_tempo'] ;
            }
        }else{
            $data['current_tempo'] = array();
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();

        if(($finder_search_term != NULL OR $finder_machine != NULL) AND session('came_from') != NULL AND session('came_from') == 'finder'){
            // echo '<p class="text-center">SOURCE: FINDER</p>';
            if($finder_search_term != "0" AND $finder_search_term != "" AND $finder_search_term != NULL){
                $words = explode(" ", $finder_search_term);
                $string = array_map(function($word){
                    return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
                }, $words);
    
                $search = 'AND (' . implode(" AND ",$string) . ')';
                // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
            }else{
                $search = "";
            };
            
            if($finder_machine != "0" AND $finder_machine != "" AND $finder_machine != NULL){
                $machine = "AND exercises_machine.exercise_machine = $finder_machine";
            }else{
                $machine = "";
            };
            
            $session_sort = (session('exercises_sort') != NULL AND session('exercises_sort') != 'countView desc' AND session('exercises_sort') != 'exerciseRate desc') ? session('exercises_sort') : 'exercises.title DESC';

            if($edit_id > 0){
                $tmp = $this->model->query("SELECT exercises.id 
                                            FROM exercises 
                                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                            WHERE exercises.deleted_at IS NULL 
                                            AND hidden_in_finder = 0
                                            $search 
                                            $machine 
                                            ORDER BY exercises.title ASC
                                        ")->getResultArray();

                if(count($tmp) > 1){                   
                    $total = count($tmp);
                    $index_list = array_column($tmp, 'id');
                    
                    $index_id = array_search($edit_id, $index_list);
                    if($index_id !== FALSE){
                        if($index_id < $total - 1){
                            $data['next'] = $this->model->where(['id' => $index_list[$index_id + 1]])->first();
                        }else{
                            $data['next'] = $this->model->where(['id' => $index_list[0]])->first();
                        }

                        if($index_id > 0){
                            $data['prev'] = $this->model->where(['id' => $index_list[$index_id - 1]])->first();
                        }else{
                            $data['prev'] = $this->model->where(['id' => $index_list[$total - 1]])->first();
                        }
                    }
                }
            }
        }else{
            // echo '<p class="text-center">SOURCE: MAIN</p>';
            if(session('exercises_search') != NULL AND session('exercises_search') != 0){
                $search_term = session('exercises_search');
                $words = explode(" ", $search_term);
                $string = array_map(function($word){
                    return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
                }, $words);
    
                $search = 'AND (' . implode(" AND ",$string) . ')';
            }else{
                $search = "";
            };
            $session_sort = (session('exercises_sort') != NULL AND session('exercises_sort') != 'countView desc' AND session('exercises_sort') != 'exerciseRate desc') ? session('exercises_sort') : 'exercises.title DESC';
            if($edit_id > 0){
                $tmp = $this->model->query("SELECT id FROM exercises WHERE deleted_at IS NULL $search ORDER BY $session_sort")->getResultArray();
                // $tmp_sql = "SELECT id FROM exercises WHERE deleted_at IS NULL $search ORDER BY $session_sort";
                // echo '<pre>';
                // print_r($tmp_sql);
                // die();

                if(count($tmp) > 1){
                    $total = count($tmp);
                    $index_list = array_column($tmp, 'id');
                    $index_id = array_search($edit_id, $index_list);
                    if($index_id !== FALSE){
                        if($index_id < $total - 1){
                            $data['next'] = $this->model->where(['id' => $index_list[$index_id + 1]])->first();
                        }else{
                            $data['next'] = $this->model->where(['id' => $index_list[0]])->first();
                        }

                        if($index_id > 0){
                            $data['prev'] = $this->model->where(['id' => $index_list[$index_id - 1]])->first();
                        }else{
                            $data['prev'] = $this->model->where(['id' => $index_list[$total - 1]])->first();
                        }
                    }
                }
            }        
        }

		return view('admin/exercises/edit_view', $data);
    }
    public function edit_bulk()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;
        $data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // echo '<pre>';
        // print_r(explode(',', ($data['ids'])));
        // echo '</pre>';
        // die();
        $teachers_model = model('TeachersModel');

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
        $data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
        $data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
        $data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
        $data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
        $data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();

        $ids = explode(',', ($data['ids']));
        foreach($ids as $key => $id){
            $data['current_exercises'][] = $this->model->where(['id' => $id])->first();
            $current_machines = $db->query("SELECT * FROM exercises_machine WHERE exercise_id = " . $id)->getResultArray();
            if(!empty($current_machines)){
                foreach($current_machines as $k => $single){
                    $data['current_machines'][$id][] = $single['exercise_machine'] ;
                }
            }else{
                $data['current_machines'][$id] = [];
            }
            $current_body_parts = $db->query("SELECT * FROM exercises_body_parts WHERE exercise_id = " . $id)->getResultArray();
            if(!empty($current_body_parts)){
                foreach($current_body_parts as $k => $single){
                    $data['current_body_parts'][$id][] = $single['exercise_body_parts'] ;
                }
            }else{
                $data['current_body_parts'][$id] = [];
            }
            $current_accessories = $db->query("SELECT * FROM exercises_accessories WHERE exercise_id = " . $id)->getResultArray();
            if(!empty($current_accessories)){
                foreach($current_accessories as $k => $single){
                    $data['current_accessories'][$id][] = $single['exercise_accessories'] ;
                }
            }else{
                $data['current_accessories'][$id] = [];
            }
            $current_tensions = $db->query("SELECT * FROM classes_tensions WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_tensions)){
                foreach($current_tensions as $k => $single){
                    $data['current_tensions'][$id][] = $single['class_tensions'] ;
                }
            }else{
                $data['current_tensions'][$id] = [];
            }
            $current_springs = $db->query("SELECT * FROM exercises_springs WHERE exercise_id = " . $id)->getResultArray();
            if(!empty($current_springs)){
                foreach($current_springs as $k => $single){
                    $data['current_springs'][$id][] = $single['exercise_springs'] ;
                }
            }else{
                $data['current_springs'][$id] = [];
            }
            $current_tempo = $db->query("SELECT * FROM exercises_tempo WHERE exercise_id = " . $id)->getResultArray();
            if(!empty($current_tempo)){
                foreach($current_tempo as $k => $single){
                    $data['current_tempo'][$id][] = $single['exercise_tempo'] ;
                }
            }else{
                $data['current_tempo'][$id] = array();
            }
        }

		// echo '<pre>';
		// print_r($data);
		// die();

		return view('admin/exercises/multi_edit_view', $data);
    }
    public function bulk_edit()
    {
        $exercises_model = model('ExercisesModel');

        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;
        $data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['exercises_sort'] = $this->exercises_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
		$data['machines'] = exercises_existing_machines();
		$data['body_parts'] = exercises_existing_body_parts();
        $data['all_exercises'] = $exercises_model->load_more_exercises(0, 100, '');
        $data['all_exercises_count'] = $exercises_model->load_more_exercises_count(0, 0, '');

		return view('admin/exercises/bulk_edit_view', $data);
    }
    public function multi()
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
        $data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		return view('admin/exercises/multi_view', $data);
    }
    public function clear_search()
    {
        $this->set_search('exercises', "0");
		return redirect('admin/exercises');
    }
    public function save_batch()
    {
		$data = $this->request->getPost();
        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('exercises');
                foreach($single_field as $k => $v){
                    $fields[$k][$key] = $v;
                }
            }
        }
        if (count($fields) > 0){
            $builder->insertBatch($fields);
        }
		return redirect('admin/exercises');
    }
    public function reject_exercise()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $ExercisesModel = model('ExercisesModel');
		$request = service('request');
        $data = $request->getPost();

        $save_exercise = array('id' => $data['exercise_id'], 'status' => 3, 'reason' => $data['reason']);
        $response['success'] = $ExercisesModel->save($save_exercise);

        $teacher = $TeachersModel->where(["id" => $data['teacher_id']])->first();

        if(!empty($teacher)){
            $subject = 'Your LOD video is rejected';
            $data_template = [
                'exercise_title' => $data['exercise_title'],
                'exercise_date' => $data['exercise_date'],
                'description' => $data['reason'],
            ];
            $template = 'front/email_templates/rejected-video';
            $to = $teacher['email'];
            $response['reason_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
        }

		return $this->respond($response);
    }
    public function hide_exercise($id = 0)
    {
        $response['success'] = FALSE;

        if($id > 0){
            $hide_exercise = array('id' => $id, 'hidden_in_finder' => 1);
            $response['success'] = $this->model->save($hide_exercise);
        }

		return $this->respond($response);
    }
    public function unhide_exercise($id = 0)
    {
        $response['success'] = FALSE;

        if($id > 0){
            $hide_exercise = array('id' => $id, 'hidden_in_finder' => 0);
            $response['success'] = $this->model->save($hide_exercise);
        }

		return $this->respond($response);
    }
    public function clear_current_exercises()
    {
        $ClassesExercisesModel = model('ClassesExercisesModel');
		$request = service('request');
        $data = $request->getPost();

        if(isset($data['class_id']) AND $data['class_id'] > 0){
            $all_exercises = $ClassesExercisesModel->where('class_id', $data['class_id'])->findAll();
            foreach($all_exercises as $single){
                $response['success'][] = $ClassesExercisesModel->delete($single['id']);
            }
        }

		return $this->respond($response);
    }
    public function save()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
		$validation =  \Config\Services::validation();
		$data = $this->request->getPost();
		$validation->reset();
        if($data['status'] == 1){
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[exercises.slug,id,{id}]',
            ];
        }else{
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[exercises.slug,id,{id}]',
                'video'         => 'required',
                'machine'       => 'required',
                // 'language'      => 'required',
                'difficulty'    => 'required',
                // 'terminology'   => 'required',
                // 'body_position' => 'required',
            ];
        }
        $response['rules'] = $rules;
        $validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
                $file->move(ROOTPATH . 'public/uploads/exercises', $name);
                $data['image'] = 'uploads/exercises/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            if(isset($data['image_removed']) AND $data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['validation'] = $validation->getErrors();
			if($this->model->save($data)){
                $response['success'] = TRUE;
            }else{
                $response['success'] = $this->model->errors();
            }
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            !isset($data['tensions']) ? $data['tensions'] = [] : '';
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['tempo']) ? $data['tempo'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';
            !isset($data['tensions']) ? $data['tension'] = [] : '';
            !isset($data['range_of_motion']) ? $data['range_of_motion'] = [] : '';
            !isset($data['direction']) ? $data['direction'] = [] : '';
            !isset($data['exercise_type']) ? $data['exercise_type'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('exercises_' . $key);
                    $builder->delete(['exercise_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'exercise_id' => $response['inserted_id'],
                            'exercise_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $teacher = $TeachersModel->where(["id" => $data['teacher']])->first();
                if($data['prev_status'] == 2){
                    $subject = 'Your LOD video is approved';
                    $data_template = [
                        'exercise_title' => $data['title'],
                        // 'exercise_date' => $data['created_at']
                    ];
                    $template = 'front/email_templates/approved-video';
                    $to = $teacher['email'];
                    $response['teacher_approved_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function find_micro()
    {
        $post = $this->request->getPost();
        if($post['search_term'] != ''){
            $result['result'] = $this->model->all_micro_exercises_finder(0, 0, $post['search_term'], 'exercises.title ASC');

            if(count($result['result']) > 0){
                $response['html'] = view('admin/exercises/search_items', $result);
            }else{
                $response['html'] = '<div class="search-item py-25 bottom-border lh-small"><span class="f-12 darkRed">No exercises found.</span>';
            }
        }else{
            $result['result'] = $this->model->all_micro_exercises_finder(0, 0, 0, 'exercises.title ASC');
            $response['html'] = view('admin/exercises/search_items', $result);
        }

        return $this->respond($response);
    }
    public function find_mini()
    {
        $post = $this->request->getPost();
        if($post['search_term'] != ''){
            $result['result'] = $this->model->all_mini_exercises_finder(0, 0, $post['search_term'], 'exercises.title ASC');

            if(count($result['result']) > 0){
                $response['html'] = view('admin/exercises/search_items', $result);
            }else{
                $response['html'] = '<div class="search-item py-25 bottom-border lh-small"><span class="f-12 darkRed">No exercises found.</span>';
            }
        }else{
            $result['result'] = $this->model->all_mini_exercises_finder(0, 0, 0, 'exercises.title ASC');
            $response['html'] = view('admin/exercises/search_items', $result);
        }

        return $this->respond($response);
    }
    public function find_mega()
    {
        $post = $this->request->getPost();
        if($post['search_term'] != ''){
            $result['result'] = $this->model->all_mega_exercises_finder(0, 0, $post['search_term'], 'exercises.title ASC');

            if(count($result['result']) > 0){
                $response['html'] = view('admin/exercises/search_items', $result);
            }else{
                $response['html'] = '<div class="search-item py-25 bottom-border lh-small"><span class="f-12 darkRed">No exercises found.</span>';
            }
        }else{
            $result['result'] = $this->model->all_mega_exercises_finder(0, 0, 0, 'exercises.title ASC');
            $response['html'] = view('admin/exercises/search_items', $result);
        }

        return $this->respond($response);
    }
    public function load_more_bulk_exercises()
    {
        $exercises_model = model('ExercisesModel');
        $data_ajax = $this->request->getPost();

        $data['all_exercises'] = $exercises_model->load_more_bulk_exercises($data_ajax['start'], 100, $data_ajax['filter']);
        $response['success'] = FALSE;

        if ($data['all_exercises'] != NULL) {
            $response['html'] = view('admin/exercises/ajax_load_exercises_view', $data);
            if ($response['html'] != "") {
                $response['count'] = count($data['all_exercises']);
                $response['success'] = TRUE;
                $response['no_more'] = FALSE;
            }else{
                $response['no_more'] = TRUE;
            }
        }else{
            $response['no_more'] = TRUE;
            $response['html'] = '';
            $response['count'] = 0;
        }

        return $this->respond($response);
    }
    public function bulk_update()
    {
        $exercises_model = model('ExercisesModel');
        $data_ajax = $this->request->getPost();
        if($data_ajax['exercises'] != '' AND $data_ajax['items'] != ''){
            $exercises = json_decode($data_ajax['exercises'], TRUE);
            $items = json_decode($data_ajax['items'], TRUE);
            $type = $data_ajax['type'];
            $type = $type == 'languages' ? 'language' : $type;
            $type = $type == 'machines' ? 'machine' : $type;

            $db = \Config\Database::connect();
            $table = 'exercises_' . $type;
            $builder = $db->table($table);
            if(is_array($exercises) AND count($exercises) > 0){
                foreach($exercises as $e){
                    if(is_array($items) AND count($items) > 0){
                        if ($db->tableExists($table)) {                            
                            $builder->delete(['exercise_id' => $e]);
                            $fields = array();
                            foreach($items as $i){
                                $fields[] = array(
                                    'exercise_id' => $e,
                                    'exercise_' . $type => $i,
                                    'date' => date('Y-m-d'),
                                );
                            }
                            if (count($fields) > 0){
                                $response['saved_array_' . $type] = $builder->insertBatch($fields);
                            }
                        }else{
                            $response['save_' . $type] = $exercises_model->save(['id' => $e, $type => $items[0]]);
                            $response['errors_' . $type] = $exercises_model->errors();
                        }
                    }else{
                        if ($db->tableExists($table)) {
                            $builder->delete(['exercise_id' => $e]);
                        }
                    }
                }
            }else{
                $response['error'] = 'No exercises selected';
            }
        }else{
            $response['error'] = 'No data to work with';
        }

        return $this->respond($response);
    }
    public function exercise_info($id = 0)
    {
        $exercises_model = model('ExercisesModel');

        $response['success'] = FALSE;
        if($id > 0){
            $data = $exercises_model->single($id);            
            if ($data != NULL) {
                // echo '<pre>';
                // print_r($data);
                // die();
                
                $response['html'] = view('admin/exercises/ajax_popup_bulk_exercises_view', $data);
                if ($response['html'] != "") {
                    $response['success'] = TRUE;
                }
            }
        }

        return $this->respond($response);
    }
    public function exercise_info_teacher_preview($id = 0)
    {
        $exercises_model = model('ExercisesModel');

        $response['html'] = '';
        $response['success'] = FALSE;
        if($id > 0){
            $data = $exercises_model->single($id);            
            if ($data != NULL) {
                $response['html'] = view('admin/exercises/ajax_popup_teacher_exercises_preview', $data);
                $response['title'] = $data['title'];
                if ($response['html'] != "") {
                    $response['success'] = TRUE;
                }
            }
        }

        return $this->respond($response);
    }
    public function checkboxes_load()
    {
        $exercises_model = model('ExercisesModel');
        $post = $this->request->getPost();

        $response['success'] = FALSE;
        $sort = '';
        if($post['type'] == 'machines' OR $post['type'] == 'accessories' OR $post['type'] == 'springs' OR $post['type'] == 'tempo'){
            $sort .= ' ORDER BY sort ASC';
        }
        $data['items'] = $exercises_model->query("SELECT * FROM " . $post['type'] . $sort)->getResultArray();
        
        if(isset($data['items']) AND is_array($data['items']) AND count($data['items']) > 0){
            $response['html'] = view('admin/exercises/ajax_popup_checkboxes_view', $data);
            if ($response['html'] != "") {
                $response['success'] = TRUE;
            }
        }

        return $this->respond($response);
    }
    public function save_referer()
    {
        $exercises_model = model('ExercisesModel');
        $data = $this->request->getPost();
        session()->set('came_from', 'finder');

        $response['success'] = FALSE;
        if(isset($data['finder_search_term']) AND $data['finder_search_term'] != ''){
            $response['success'] = TRUE;
            $search_term = $data['finder_search_term'];
            session()->set('finder_search_term', $search_term);
        }
        
        if(isset($data['finder_machine']) AND $data['finder_machine'] != ''){
            $response['success'] = TRUE;
            $machine = $data['finder_machine'];
            session()->set('finder_machine', $machine);
        }

        return $this->respond($response);
    }
    public function save_referer_main()
    {
        session()->set('came_from', 'main');
        $response['success'] = TRUE;
        return $this->respond($response);
    }
    public function discard_order()
    {
        session()->remove('finder_search_term');
        session()->remove('finder_machine');
        $response['success'] = TRUE;

        return $this->respond($response);
    }
    // public function save_new_attributes(){
    //     $db = \Config\Database::connect();

    //     // $change_all = ['tension'];
    //     $change_all = ['tension', 'direction', 'exercise_type', 'range_of_motion'];

    //     foreach($change_all as $single){
    //         $get_all[$single] = $this->model->query("SELECT * FROM exercises WHERE $single != ''")->getResultArray();

    //         foreach($get_all[$single] as $key => $single_row){ // i.e. $single_field = tension
    //             if(is_array($single_row)){
    //                 $db      = \Config\Database::connect();
    //                 $builder = $db->table('exercises_' . $single);
    //                 $fields = array(
    //                     'exercise_id' => $single_row['id'],
    //                     'exercise_' . $single => $single_row[$single],
    //                     'date' => date('Y-m-d'),
    //                 );
    //                 $builder->insert($fields);
    //             }
    //         }        
    //     }
    // }
}