<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.search-item {
    position: relative;
}
.search-item:hover .title-area span {
    opacity: 1;
}
.search-item .title-area span {
	opacity: 0;
	transition: all 0.15s ease-in-out 0s;
	position: absolute;
	top: 43px;
}
body .link {
    user-select: auto !important;
}
body.show_matching .hidden_in_finder {
    display: flex !important;
}
body.show_matching .hidden_in_finder .unhide_exercise {
    display: flex !important;
}
.search-item.textGreen a {
    color: #52C15A !important;
}
.search-item.orange a {
    color: #E8AF44 !important;
}
body .search-item.hidden_in_finder .hide_exercise {
    display: none !important;
}
body .search-item.hidden_in_finder .unhide_exercise {
    display: flex !important;
}
body .search-item:not(.hidden_in_finder) .hide_exercise {
    display: flex !important;
}
.hidden_in_finder {
    display: none !important;
}
body.show_matching .hidden_in_finder .hide_exercise {
    display: none !important;
}
body.show_matching .search-item:not(.textGreen):not(.orange) {
    display: none !important;
}
body .search-item:not(.hidden_in_finder) .unhide_exercise {
    display: none !important;
}
body .search-item:not(.textGreen):not(.orange) .hide_exercise,
body .search-item:not(.textGreen):not(.orange) .unhide_exercise{
    display: none !important;
}
body.show_all_exercises .search-results .search-item .unhide_exercise {
	display: flex !important;
}
body.show_all_exercises .search-results .search-item {
	display: flex !important;
}
.btn.midGray.gray-bg {
    min-height: 20px;
	font-weight: 500 !important;
}
.btn.midGray.gray-bg:hover {
    color: #fff !important;
    cursor: pointer;
}
.btn.btn-border {
    min-height: 20px;
	border-radius: 50px;
	border: 1px solid #F0F0F0;
	font-weight: 500 !important;
    cursor: pointer;
}
.reset_search {
	position: absolute;
	top: 2px;
	right: 40px;
	z-index: 111;
	height: 38px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 20px;
	cursor: pointer;
	font-size: 20px;
    display: none;
}
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 20px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}
body .btn--loading-small-black {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
    width: 25px;
    height: 25px;
}
body .btn--loading-small-black::after {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -5px;
    margin-top: -6px;
    border-radius: 50%;
    border: 2px solid #000;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
.search-button.btn--loading-small-black img {
	display: none;
}
.orange.textGreen a {
	color: #52C15A !important;
}
/* .ajax-class.exercises-class:first-child .video_preview_tooltip,
.ajax-class.exercises-class:nth-child(2) .video_preview_tooltip {
    bottom: auto;
    top: 98%;
} */
.search-item:hover .video_preview_tooltip {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;    
}
.video_preview_tooltip {
    transition: all 0.1s ease-in-out 0s;
	position: absolute;
	width: 200px;
	left: 6px;
    bottom: 98%;
	z-index: 99;
	background: #fff;
	box-shadow: 0 0 6px 0 rgba(0,0,0,0.05);
	height: calc(200px * 0.5625);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}
.video_preview_tooltip video {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover;
	position: absolute;
    z-index: 1;
	top: 0;
	left: 0;
}
.search-item .title_to_rename {
	width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
}
.f-12.flex.flex-column.title-area {
	overflow: hidden;
	width: 100%;
}

@media(max-width: 767px){
.col-12, .col-10, .col-6, .col-4, .col-3, .col-2-5 {
	flex: 0 0 100%;
	max-width: 100%;
}
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container mb-150">
            <div class="row">
                <div class="col-12">
                    <div class="flex bottom-border page-title">
                        <h1 class="h3 lh-small">EXERCISE FINDER</h1>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="flex aic jcsb py-3 bottom-border">
                        <div class="flex aic jcl">
                            <div class="checkbox mb-0 mr-2" id="show_matching_container">
                                <input type="checkbox" class="" id="show_matching" value="1">
                                <label for="show_matching" class="f-12" onclick="$('body').toggleClass('show_matching')">Show matching exercises</label>
                            </div>
                            <div class="checkbox mb-0" id="show_all_exercises_container">
                                <input type="checkbox" class="" id="show_all_exercises" value="1">
                                <label for="show_all_exercises" class="f-12" onclick="$('body').toggleClass('show_all_exercises')">Show all exercises</label>
                            </div>
                        </div>
                        <form class="ajax-search-classes search-form show" id="find_all_form" style="width: 300px;padding: 0;">
                            <input type="text" class="seach-input search-wide search_exercises_all" placeholder="Search all">
                            <button type="submit" class="search-button" style="right: 2px;border-radius: 0 !important;height: 38px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" style="width: 14px;height: 15px;"  /></button>
                            <span class="reset_search reset_main_search">×</span>
                        </form>
                    </div>
                </div>
            </div>
            <div class="row big-gap">
                <div class="col-4" data-type="1">
                    <h3 class="f-14 py-5 lh-small bottom-border">MICRO</h3>
                    <div class="py-3 bottom-border">
                        <div class="search-container">
                            <div class="ajax-search-classes search-form show ml-0 px-0">
                                <form action="admin/exercises/find_micro" id="find_micro_form">
                                    <input type="text" class="seach-input search-wide search_exercises_micro" placeholder="Search">
                                    <button type="submit" class="search-button" style="right: 2px;border-radius: 0 !important;height: 38px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" style="width: 14px;height: 15px;" /></button>
                                    <span class="reset_search reset_single_search">×</span>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="search-results find_micro_results" id="find_micro_results">
                        <div class="search-item-count midGray f-12 py-25 bottom-border flex aic jcsb">
                            <?php echo count($count_all_micro_exercises); ?> exercises
                            <!-- <a href="javascript:;" class="link link-black black f-10 text-underline" onclick="$('#find_micro_form').submit()">Show All</a> -->
                        </div>
                    </div>
                </div>
                <div class="col-4" data-type="3">
                    <h3 class="f-14 py-5 lh-small bottom-border">MINI</h3>
                    <div class="py-3 bottom-border">
                        <div class="search-container">
                            <div class="ajax-search-classes search-form show ml-0 px-0">
                                <form action="admin/exercises/find_mini" id="find_mini_form">
                                    <input type="text" class="seach-input search-wide search_exercises_mini" placeholder="Search">
                                    <button type="submit" class="search-button" style="right: 2px;border-radius: 0 !important;height: 38px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" style="width: 14px;height: 15px;" /></button>
                                    <span class="reset_search reset_single_search">×</span>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="search-results find_mini_results" id="find_mini_results">
                        <div class="search-item-count midGray f-12 py-25 bottom-border flex aic jcsb">
                            <?php echo count($count_all_mini_exercises); ?> exercises
                            <!-- <a href="javascript:;" class="link link-black black f-10 text-underline" onclick="$('#find_mini_form').submit()">Show All</a> -->
                        </div>
                    </div>
                </div>
                <div class="col-4" data-type="2">
                    <h3 class="f-14 py-5 lh-small bottom-border">MEGA</h3>
                    <div class="py-3 bottom-border">
                        <div class="search-container">
                            <div class="ajax-search-classes search-form show ml-0 px-0">
                                <form action="admin/exercises/find_mega" id="find_mega_form">
                                    <input type="text" class="seach-input search-wide search_exercises_mega" placeholder="Search">
                                    <button type="submit" class="search-button" style="right: 2px;border-radius: 0 !important;height: 38px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" style="width: 14px;height: 15px;" /></button>
                                    <span class="reset_search reset_single_search">×</span>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="search-results find_mega_results" id="find_mega_results">
                        <div class="search-item-count midGray f-12 py-25 bottom-border flex aic jcsb">
                            <?php echo count($count_all_mega_exercises); ?> exercises
                            <!-- <a href="javascript:;" class="link link-black black f-10 text-underline" onclick="$('#find_mega_form').submit()">Show All</a> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div id="overlay" class="overlay">
    <div class="popup p-0 rename-popup show" style="max-width: 400px;">
        <span class="close_page close"><img src="images/close-grey-icon.svg"></span>
        <div class="popup-header">
            <p class="f-18 pl-3 lh-small">RENAME EXERCISE</p>
        </div>
        <div class="popup-body p-0">
            <form action="admin/exercises/ajax_rename" method="POST" id="rename-form" class="">                
                <div id="title_container" style="position: relative;" class="input-container p-3 pb-0 mb-0">
                    <label class="f-12 semibold">Current name:</label>
                    <p class="exercise_old_title f-12 mb-2">TITLE</p>
                    <input type="text" name="title" id="exercise_title" placeholder="New name" value="" class="line-input f-12">
                </div>
                <div class="text-center p-3 top-border">
                    <input type="hidden" name="id" id="rename_id">
                    <input type="hidden" name="table" id="rename_table" value="Exercises">
                    <button type="submit" class="btn red-bg white mr-4 w100" title="SAVE">SAVE</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="msg-popup">
    <span class="close_popup" style="display: none;"><img src="images/msg-close.svg" alt="" class="img-fluid" /></span>
    <p class="m-0">Message</p>
</div>
<?php // echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
$('#rename-form').on('submit', function(e){
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var id = form.find('#rename_id').val();
    var table = form.find('#rename_table').val();
    var title = form.find('#exercise_title').val();
    var elem = $('.search-item[data-id="' + id + '"]');
    var btn = form.find('button');
    btn.addClass('btn--loading');
    
    console.log(id);
    console.log(table);
    console.log(title);
    $.ajax({
        type: 'POST',
        url: url,
        data: {
            id,
            table,
            title
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                elem.addClass('updated');
                elem.find('.title_to_rename').text(title);
                if(typeof(compare_exercises) === "function"){
                    compare_exercises();                    
                };
                close_all();
            }
            setTimeout(function(){
                btn.removeClass('btn--loading');
            }, 100);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
            setTimeout(function(){
                btn.removeClass('btn--loading');
            }, 100);
        }
    });
});
$('.search_exercises_all').on('keyup', function(){
    $('.search_exercises_micro').val($(this).val());
    $('.search_exercises_mini').val($(this).val());
    $('.search_exercises_mega').val($(this).val());
});
$('.seach-input').on('keyup', function(){
    $(this).val().length > 0 ? $(this).next().next().css({display: 'flex'}) : '';
});
$('.reset_main_search').on('click', function(){
    var form = $(this).closest('form');

    form.find('input').val('');
    $(this).css({display: 'none'});
    $('.search_exercises_micro').val('');
    $('.search_exercises_mini').val('');
    $('.search_exercises_mega').val('');
    form.submit();
});
$('.reset_single_search').on('click', function(){
    var form = $(this).closest('form');

    form.find('input').val('');
    $(this).css({display: 'none'});
    form.submit();
});
function hide_exercise(xx){
    var parent = xx.closest('.search-item');
    var id = parent.data('id');

    $.ajax({
        type: 'POST',
        url: 'admin/exercises/hide_exercise/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                parent.addClass('hidden_in_finder');
                xx.removeClass('btn-border black hide_exercise').addClass('midGray gray-bg unhide_exercise').text('UNHIDE').attr('onclick', 'unhide_exercise($(this))');
                compare_exercises();
            }   
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
};
function unhide_exercise(xx){
    var parent = xx.closest('.search-item');
    var id = parent.data('id');

    $.ajax({
        type: 'POST',
        url: 'admin/exercises/unhide_exercise/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                parent.removeClass('hidden_in_finder');
                xx.removeClass('midGray gray-bg unhide_exercise').addClass('btn-border black hide_exercise').text('HIDE').attr('onclick', 'hide_exercise($(this))');
                compare_exercises();
            }   
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
};

$('#find_micro_form').on('submit', function(e){
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var term = form.find('input').val();
    var btn = form.find('button');
    btn.addClass('btn--loading-small-black');
    // console.log(term.toLowerCase());
    
    $.ajax({
        type: 'POST',
        url: url,
        data: {
            search_term: term
        },
        dataType: 'json',
        success: function (data) {
            // console.log(data);
            // console.log('Success');
            $('.find_micro_results').html(data.html);                
            compare_exercises();
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        }
    });
});
$('#find_mini_form').on('submit', function(e){
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var term = form.find('input').val();
    var btn = form.find('button');
    btn.addClass('btn--loading-small-black');
    // console.log(term);
    
    $.ajax({
        type: 'POST',
        url: url,
        data: {
            search_term: term
        },
        dataType: 'json',
        success: function (data) {
            // console.log(data);
            // console.log('Success');
            $('.find_mini_results').html(data.html);                
            compare_exercises();
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        }
    });
});
$('#find_mega_form').on('submit', function(e){
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var term = form.find('input').val();
    var btn = form.find('button');
    btn.addClass('btn--loading-small-black');
    // console.log(term);
    
    $.ajax({
        type: 'POST',
        url: url,
        data: {
            search_term: term
        },
        dataType: 'json',
        success: function (data) {
            // console.log(data);
            // console.log('Success');
            $('.find_mega_results').html(data.html);        
            compare_exercises();        
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
            setTimeout(function(){
                btn.removeClass('btn--loading-small-black');
            }, 100);
        }
    });
});
$('#find_all_form').on('submit', function(e){
    e.preventDefault();
    $('#find_micro_form').submit();
    $('#find_mini_form').submit();
    $('#find_mega_form').submit();
});
// Get all columns by their IDs
// var column1;
// var column2;
// var column3;

// Create a function to categorize and add classes to matching items
function categorizeItems() {

    console.log('Categorizing items...');
    const column1 = document.getElementById('find_micro_results');
    const column2 = document.getElementById('find_mini_results');
    const column3 = document.getElementById('find_mega_results');

    // Get all items from each column
    const items1 = column1.children;
    const items2 = column2.children;
    const items3 = column3.children;

    // Create a map to keep track of item counts across columns
    const itemCounts = {};

    // Helper function to process items in a column
    function processItems(items, column) {
        for (let item of items) {
            const link = item.querySelector('a'); // Find the <a> child element
            if (link) {
                const title = link.textContent.trim().toLowerCase(); // Get title from <a> and convert to lowercase
                if (!itemCounts[title]) {
                    itemCounts[title] = { count: 0, columns: new Set() };
                }
                itemCounts[title].count += 1;
                itemCounts[title].columns.add(column);
            }
        }
    }

    // Process all items in each column
    processItems(items1, 'column1');
    processItems(items2, 'column2');
    processItems(items3, 'column3');

    // Apply appropriate classes based on counts and columns
    function applyClasses(items) {
        for (let item of items) {
            const link = item.querySelector('a'); // Find the <a> child element
            if (link) {
                const title = link.textContent.trim().toLowerCase(); // Get title from <a> and convert to lowercase
                const itemData = itemCounts[title];

                if (itemData.columns.size === 2) {
                    item.classList.add('orange');
                } else if (itemData.columns.size === 3) {
                    item.classList.add('textGreen');
                }
            }
        }
    }

    // Apply classes to items in each column
    applyClasses(items1);
    applyClasses(items2);
    applyClasses(items3);
}


function compare_exercises(){
    $('.search-item').removeClass('textGreen orange');
    var micro_total = $('.find_micro_results .search-item').length;
    var mini_total  = $('.find_mini_results .search-item').length;
    var mega_total  = $('.find_mega_results .search-item').length;
    var micro_hidden = $('.find_micro_results .search-item.hidden_in_finder').length;
    var mini_hidden  = $('.find_mini_results .search-item.hidden_in_finder').length;
    var mega_hidden  = $('.find_mega_results .search-item.hidden_in_finder').length;

    $('.find_micro_results').find('.search-item-count').text(micro_total + ' exercises' + ' (' + micro_hidden + ' hidden)');
    $('.find_mini_results').find('.search-item-count').text(mini_total + ' exercises' + ' (' + mini_hidden + ' hidden)');
    $('.find_mega_results').find('.search-item-count').text(mega_total + ' exercises' + ' (' + mega_hidden + ' hidden)');
    
    categorizeItems();
}
function save_referer(xx){
    var url = xx.attr('href');
    var finder_search_term = xx.closest('.col-4').find('form .seach-input').val();
    var finder_machine = xx.closest('.col-4').data('type');

    console.log("finder_search_term: ", finder_search_term);
    console.log("finder_machine: ", finder_machine);

    $.ajax({
        type: 'POST',
        url: 'admin/exercises/save_referer',
        data: {
            finder_machine,
            finder_search_term
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                console.log('filter saved in session');
            }
            window.location.href = url;
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}

$(document).ready(function(){
    $('#find_all_form').submit();
});
</script>
</body>
</html>