<?php namespace App\Models;

use CodeIgniter\Model;

class ModelsModel extends Model
{
    protected $table = 'models';
	protected $allowedFields = ['parent_id', 'firstname', 'lastname', 'slug', 'image', 'cover_image', 'mob_cover_image', 'webp_image', 'webp_cover_image', 'webp_mob_cover_image','certified', 'location', 'year_certified', 'facebook', 'instagram', 'super_admin', 'content', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'country', 'email', 'phone'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'firstname'     => 'required|min_length[2]',
        'lastname'     => 'required|min_length[2]',
        'email'     => 'required',
        'phone'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_models($start = 0, $limit = 0, $search_term = NULL, $order = "certified desc, firstname ASC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (models.firstname LIKE '%$search_term%' OR models.lastname LIKE '%$search_term%')" : "";
        $data = $this->query("SELECT models.*, 
                            (SELECT count(*) as ce FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.paid IN (1,2,4) AND calendar_events.model_id = models.id AND calendar_events.date <= NOW()) AS unpaidClasses, 
                            (SELECT count(*) FROM calendar_events WHERE calendar_events.deleted_at IS NULL AND calendar_events.model_id = models.id AND calendar_events.date <= NOW()) AS hasHistory
                            FROM models
                            WHERE models.deleted_at IS NULL
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }


    public function current($slug = ''){
        $data = $this->query("SELECT models.*
                                        FROM models
                                        WHERE models.deleted_at IS NULL
                                        AND models.slug = '" . $slug . "'
                                    ")->getRowArray();
        return $data;
    }

    // public function teacher_classes($id = 0){
    //     $classes_model = model('ClassesModel');

    //     $data = $classes_model->query("SELECT classes.*, difficulty.title as diff, models.slug as teach_slug,
    //                                     COALESCE(x.cnt,0) AS countView,
    //                                     COALESCE(y.rate,0) AS classRate,
    //                                     CONCAT(models.firstname, ' ', models.lastname)  AS teach,
    //                                     video_state.video_time as video_state,
    //                                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
    //                                     IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
    //                                     IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
    //                                     IF(classes.id IN (
    //                                         SELECT * FROM (
    //                                                 SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
    //                                         ) as subquery
    //                                     ), 1, 0) as purchased,
    //                                     IF(classes.id IN (
    //                                             SELECT * FROM (
    //                                                     SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
    //                                             ) as classes_rented
    //                                     ), 1, 0) as rented,
    //                                     IF(classes.id IN (
    //                                             SELECT * FROM (
    //                                                     SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
    //                                             ) as subquery2
    //                                     ), 1, 0) as watched
    //                                     FROM classes
    //                                     LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
    //                                     LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
    //                                     LEFT JOIN difficulty ON difficulty.id = classes.difficulty
    //                                     LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
    //                                     LEFT JOIN machines ON machines.id = classes_machine.class_machine
    //                                     LEFT JOIN models ON (models.id = classes.teacher AND models.status = 0)
    //                                     LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
    //                                     LEFT JOIN subscribers_classes ON (subscribers_classes .class_id = classes.id)
    //                                     LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
    //                                     WHERE classes.deleted_at IS NULL
    //                                     AND classes.status = 0
    //                                     AND classes.teacher = " . $id . "
    //                                     GROUP BY classes.id
    //                                     ORDER BY classes.updated_at desc
    //                                 ")->getResultArray();
    //     return $data;
    // }

    public function models_history($id = 0, $start = 0, $limit = 100000, $search_term = NULL, $order = "sort_status DESC, date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "HAVING (calendar_events.date LIKE '%$search_term%' OR calendar_events.date LIKE '%$search_term%' OR model LIKE '%$search_term%' OR model LIKE '%$search_term%')" : "";

        if(strpos($order, 'ASC') !== FALSE){
            $sort_status = "IF(calendar_events.paid IN (0,3), 0, IF(calendar_events.paid = 5, 2, 1)) as sort_status, ";
        }else{
            $sort_status = "IF(calendar_events.paid IN (0,3), 1, IF(calendar_events.paid = 5, 0, 2)) as sort_status, ";
        };

        $data = $this->query("SELECT calendar_events.id, calendar_events.paid, $sort_status calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.class_id, calendar_events.model_id, CONCAT(models.firstname, ' ', models.lastname) as model, models.image as model_image, CONCAT(models.firstname, ' ', models.lastname) as model, IF(calendar_events.date <= NOW(), 'Recorded', 'Upcoming') as video_status
                                    FROM calendar_events 
                                    LEFT OUTER  JOIN teachers ON teachers.id = calendar_events.teacher_id
                                    INNER JOIN models ON models.id = calendar_events.model_id
                                    WHERE calendar_events.model_id = " . $id . "
                                    AND calendar_events.deleted_at IS NULL                                    
                                    AND calendar_events.date <= NOW()
                                    " . $search . "
                                    ORDER BY " . $order . "
                                    " . $limit_size . "
                        ")->getResultArray();

        return $data;
    }

	protected function prepare_data(array $data)
	{
		if (isset($data['data']['password']) AND $data['data']['password'] <> ''){
			$data['data']['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['data']['password']))));
		}else{
			unset($data['data']['password']);
		}
		return $data;
	}

}