<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">accessories</h1>
                <a href="admin/accessories/edit" class="btn black-bg white ml-auto" title="New accessories">New accessory</a>
                <a href="javascript:;" onclick="sync_woo_products()" class="btn black-bg white ml-2" title="Sync accessories">Sync accessories</a>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $accessoriess_count == 1 ? $accessoriess_count . ' Accessory' : $accessoriess_count . ' Accessories'; ?></h5>
                <div class="dropdown d-inline-block ml-auto">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/accessories/sort_by/accessories.title/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/accessories/sort_by/accessories.title/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>

                <div class="search-container">
                    <form action="xxxxxxxxxxxx" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
                <hr class="mt-2 mb-2">


                <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="accessories" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12 mb-2">
                    <div class="table rows-with-borders sortable-accessories">
<?php
foreach($all_accessoriess as $single){
?>
                        <div class="table-row" data-rowid="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                                    <?php if(isset($single['image']) AND $single['image'] != ''){ ?>
                                        <img src="<?php echo $single['image']; ?>" alt="" class="img-fluid" style="width: 160px;height: 90px;object-fit: cover;" />
                                    <?php } ?>
                                </a>
                                <div class="flex flex-column">
                                    <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?> <?php echo (isset($single['lastname']) AND $single['lastname'] != '') ? $single['lastname'] : ''; ?></a>
                                    <span class="midGray f-1">
                                        <?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] . ' classes' : ''; ?>
                                    </span>
                                    <div class="row-actions f-1">
                                        <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                    </div>
                                    <?php echo (isset($single['upcoming']) AND $single['upcoming'] != '') ? $single['upcoming'] : ''; ?>
                                </div>
                                <div class="flex aic jcr ml-auto">
                                    <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="ml-2 handle"></span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>

<?php if(count($draft_accessoriess) > 0){ ?>
            <h4>Draft</h4>
            <hr class="my-4">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders noimg-table">
<?php
foreach($draft_accessoriess as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="mr-3">
                                    <?php if(isset($single['cover_image']) AND $single['cover_image'] != ''){ ?>
                                        <img src="<?php echo $single['cover_image']; ?>" alt="" class="img-fluid" style="width: 160px;height: 90px;object-fit: cover;" />
                                    <?php } ?>
                                </a>
                                <div class="flex flex-column">
                                    <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title f-16 medium mb-05"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?> <?php echo (isset($single['lastname']) AND $single['lastname'] != '') ? $single['lastname'] : ''; ?></a>
                                    <span class="midGray f-1">
                                        <?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] . ' classes' : ''; ?>
                                    </span>
                                    <div class="row-actions f-1">
                                        <a href="admin/accessories/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="accessories">Delete</a>-->
                                    </div>
                                    <?php echo (isset($single['upcoming']) AND $single['upcoming'] != '') ? $single['upcoming'] : ''; ?>
                                </div>
                                <div class="flex aic jcr ml-auto">
                                    <span class="f-1">Added: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>

            </div>
<?php
}
?>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($accessoriess_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_accessoriess); ?><span class="midGray mx-1">of <?php echo $accessoriess_count; ?></span>
                    <a href="admin/accessories/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/accessories/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_accessoriess) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_accessoriess)) == $accessoriess_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
if($('.sortable-accessories').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".table").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var pom = {
                    id: section_id,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/accessories/sort_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}

</script>

</body>
</html>