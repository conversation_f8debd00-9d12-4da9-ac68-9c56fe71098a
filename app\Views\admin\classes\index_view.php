<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-3 pt-85 border-bottom">
            <div class="flex aic jcsb minH45">
                <h1 class="h3 mb-05">Classes</h1>
                <div class="ml-auto mb-05">
                    <a href="admin/classes/edit" class="btn black-bg white" title="Upload">Upload</a>
                    <!--<a href="admin/classes/multi" class="btn btn-border white-bg black ml-2" title="Bulk">Bulk</a>-->
                </div>
            </div>
            <hr class="mt-80 mb-3">
            <div class="flex aic jcsb">
                <?php
                    $active_machines = isset($filter['machine'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['machine'][0]))) : [];
                    $active_body_parts = isset($filter['body_parts'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['body_parts'][0]))) : [];
                    $active_teacher = isset($filter['teacher'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['teacher'][0]))) : [];
                    $active_difficulty = isset($filter['difficulty'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['difficulty'][0]))) : [];
                    $active_duration = isset($filter['duration'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['duration'][0]))) : [];
                ?>
                <?php if(!isset($filter)){ ?>
                    <a href="javascript:;" class="flex aic jcc f-12" onclick="check_filter_seleboxes()" data-popup="classes-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /> Apply filters</a>
                <?php }else{ ?>
                    <div class="flex aic jcl">
                        <a href="javascript:;" class="flex aic jcc f-12" onclick="check_filter_seleboxes()" data-popup="classes-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /></a>
                        <span class="f-14 mr-05">Active filters: </span>
                        <?php if($active_machines[0] != ""){ ?><span class="f-14">Machine</span><?php } ?>
                        <?php if($active_duration[0] != ""){ ?><span class="f-14">, Duration</span><?php } ?>
                        <?php if($active_difficulty[0] != ""){ ?><span class="f-14">, Difficulty</span><?php } ?>
                        <?php if($active_body_parts[0] != ""){ ?><span class="f-14">, Body Parts</span><?php } ?>
                        <?php if($active_teacher[0] != ""){ ?><span class="f-14">, Teacher</span><?php } ?>
                        <a href="admin/classes" class="link link-midGray midGray clear_filter f-12 ml-2">x Clear</a>
                    </div>
                <?php } ?>
                <div class="flex aic jcsb">
                    <div class="dropdown d-inline-block">
                        <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" data-val="classes.created_at" data-by="desc" class="set_sort_by link midGray" title="">Date Added</a></li>
                            <li><a href="javascript:;" data-val="classes.title" data-by="asc" class="set_sort_by link midGray" title="">Ascending</a></li>
                            <li><a href="javascript:;" data-val="classes.title" data-by="desc" class="set_sort_by link midGray" title="">Descending</a></li>
                            <li><a href="javascript:;" data-val="countView" data-by="desc" class="set_sort_by link midGray" title="">Popularity</a></li>
                            <li><a href="javascript:;" data-val="classRate" data-by="desc" class="set_sort_by link midGray" title="">Best Rated</a></li>
                        </ul>
                    </div>
                    <div class="search-container">
                        <form action="admin/classes" method="GET" class="search-form <?php echo (isset($search_term) AND $search_term != '0') ? 'show' : ''; ?>">
                            <input type="text" name="search_term" class="seach-input" value="<?php echo (isset($search_term) AND $search_term != "0") ? $search_term : ''; ?>">
                            <?php if(isset($search_term) AND $search_term != "0"){ ?>
                            <a href="admin/classes/clear_search" class="clear_search" style="font-size: 18px;right: 40px;">×</a>
                            <?php } ?>
                            <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        </form>
                    </div>
                </div>
            </div>
            <hr class="mt-3 mb-25">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple normalRed" data-table="classes" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <!--<form class="edit_checked" style="display: none" method="post" action="admin/classes/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-12 link flex aic edit_bulk midGray" style="background: #fff !important;">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>-->
                </div>
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $classes_count == 1 ? $classes_count . ' Class' : $classes_count . ' Classes'; ?></h5>
            </div>
            <hr class="mt-25 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders">
<?php
foreach($all_classes as $single){
?>
                        <!-- for teacher accout (adding routine to class) - if teacher is not super admin and status is published (0) - preview only -->
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" style="<?php echo ($single['status'] == 0 AND $logged_user['super_admin'] != 1) ? 'opacity: 0.5;pointer-events: none' : '' ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : 'images/img-placeholder.jpg'); ?>" alt="" class="img-fluid aaaa" style="max-width: 210px;max-height: 120px;height: 120px;width: 210px;object-fit: cover;" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium mb-05 flex aic">
                                        <?php if($single['type'] == 0){ ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 30 30" class="mr-1">
                                            <g id="Group_10334" data-name="Group 10334" transform="translate(-100 -1359)">
                                                <rect id="Rectangle_1481" data-name="Rectangle 1481" width="30" height="30" rx="15" transform="translate(100 1359)"/>
                                                <path id="Path_4894" data-name="Path 4894" d="M4.92-1.416c-1.4,0-2.412-1.08-2.412-2.832v-.1c0-1.728.9-2.82,2.388-2.82s2.388,1.14,2.388,2.808v.1C7.284-2.532,6.372-1.416,4.92-1.416ZM4.884.132A4.217,4.217,0,0,0,9.312-4.284v-.1A4.171,4.171,0,0,0,4.9-8.7,4.242,4.242,0,0,0,.48-4.332v.1A4.179,4.179,0,0,0,4.884.132ZM12.66-1.512V-7.068h.768C15.24-7.068,16-6.12,16-4.356v.1c0,1.776-.816,2.748-2.544,2.748ZM10.716,0h2.8c3.012,0,4.512-1.716,4.512-4.284v-.1c0-2.568-1.488-4.2-4.5-4.2H10.716Z" transform="translate(106 1378)" fill="#fff"/>
                                            </g>
                                        </svg>
                                        <?php }else{ ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 30 30" class="mr-1">
                                            <g id="buy-class-icon" transform="translate(-100 -1359)">
                                                <g id="Rectangle_1482" data-name="Rectangle 1482" transform="translate(100 1359)" fill="none" stroke="#000" stroke-width="1">
                                                <rect width="30" height="30" rx="15" stroke="none"/>
                                                <rect x="0.5" y="0.5" width="29" height="29" rx="14.5" fill="none"/>
                                                </g>
                                                <text id="_" data-name="$" transform="translate(111 1378)" font-size="12" font-family="Graphik-Semibold, Graphik" font-weight="600"><tspan x="0" y="0">$</tspan></text>
                                            </g>
                                        </svg>
                                        <?php } ?>
                                        <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                    </a>
                                    <span class="midGray mb-05 f-1 normal">
                                        <?php echo $single['all_class_machines'] ?>,
                                        <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? duration_standard2($single['duration']) : 0; ?>,
                                        <span class="blockmob"></span>Difficulty: <?php echo (isset($single['diff']) AND $single['diff'] != '') ? $single['diff'] : ''; ?>,
                                        <span class="blockmob"></span>by: <a href="admin/teachers/edit/<?php echo (isset($single['teach_id']) AND $single['teach_id'] != '') ? $single['teach_id'] : ''; ?>" class="link link-black black"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : ''; ?></a>
                                    </span>
                                    <span class="midGray f-1 normal">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/y', strtotime($single['created_at'])) : ''; ?></span>
                                    <span class="midGray f-1 normal">ID: <?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?></span>
                                    <div class="row-actions f-1 red normal">
                                        <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="classes">Delete</a>-->
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-1 normal line-height-20">
                                    <?php if($single['status'] == 1){ ?>
                                        <span class="btn btn-xs status-orange f-14 btnadmin">Draft</span>
                                    <?php }else{ ?>
                                        <?php if($single['type'] == 1){ ?>
                                            <span class="most-title">Earnings: $<?php echo $single['classEarned']; ?></span>
                                        <?php }else{ ?>
                                            <span class="most-title nrviews"><?php echo (isset($single['countView']) AND $single['countView'] != '') ? $single['countView'] : ''; ?> views</span>
                                            <span class="midGray f-12"><?php echo (isset($single['classRate']) AND $single['classRate'] != '') ? number_format($single['classRate'], 1) : ''; ?> <i class="icon-star" style="position: relative;top: 0px;"></i></span>
                                        <?php } ?>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('classes_per_page')) - session('classes_per_page')) + ($classes_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('classes_per_page')) - session('classes_per_page')) + count($all_classes); ?><span class="midGray mx-1">of <?php echo $classes_count; ?></span>

                    <a href="admin/classes/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>

                    <a href="admin/classes/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page + 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_classes) < session('classes_per_page')) OR (((($page * session('classes_per_page')) - session('classes_per_page')) + count($all_classes)) == $classes_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('classes_per_page'); ?> <i class="arrow-down ml-1"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('classes_per_page') == "") ? 25 : session('classes_per_page'); ?>;
var sort_by = '<?php echo (session('classes_sort') == "") ? 'classes.created_at/desc' : session('classes_sort'); ?>';
var search = '<?php echo (session('classes_search') == "") ? '' : session('classes_search'); ?>';
var order = '<?php echo (session('classes_sort') == "") ? 'classes.created_at/desc' : session('classes_sort'); ?>';
</script>
</body>
</html>