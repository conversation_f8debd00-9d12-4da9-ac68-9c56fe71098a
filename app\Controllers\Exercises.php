<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Exercises extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ExercisesModel');
    }

    public function index()
    {
        $collections_model = model('CollectionsModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $teachers_model = model('TeachersModel');
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT exercise_machine, count(*) AS cnt FROM exercises_machine GROUP BY exercise_machine) x ON x.exercise_machine = machines.id
                                            HAVING countMachine > 0
                                      ')->getResultArray();
		$data['all_accessories'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countAccessories
                                            FROM accessories
                                            LEFT OUTER JOIN (SELECT class_accessories, count(*) AS cnt FROM classes_accessories GROUP BY class_accessories) x ON x.class_accessories = accessories.id
                                            HAVING countAccessories > 0
                                    ')->getResultArray();

        $data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countExercises > 0
                                        ')->getResultArray();
		$data['languages'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                            FROM languages
                                            LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                            HAVING countExercises > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT exercise_body_parts, count(*) AS cnt FROM exercises_body_parts GROUP BY exercise_body_parts) x ON x.exercise_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status = 0
                                            HAVING countExercises > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();

        // $data['all_exercises'] = $this->model->all_exercises_front(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Exercises | Lagree On Demand';
		$data['current']['seo_description'] = "Watch Mini, Micro and Mega exercises On Demand, experience the benefits of your practice at home or on the go, on any device.";
		echo view('front/exercises/index_view', $data);
    }

    public function slug($slug = '')
    {
        if($slug == 'micro' OR $slug == 'mini' OR $slug == 'minipro' OR $slug == 'mega' OR $slug == 'megapro' OR $slug == 'evo' OR $slug == 'evo2' OR $slug == 'proformer' OR $slug == 'abwheel' OR $slug == 'doormount' OR $slug == 'sliders'){
            $collections_model = model('CollectionsModel');
            $data['logged_user'] = $this->user;
            $data['settings'] = $this->settings;
            $data['nums'] = create_session_nums();
    
            $teachers_model = model('TeachersModel');
            $db = \Config\Database::connect();
            $machines = [
                'micro' => 1,
                'mini' => 3,
                'minipro' => 4,
                'mega' => 2,
                'megapro' => 13,
                'evo' => 10,
                'evo2' => 11,
                'proformer' => 6,
                'abwheel' => 5,
                'doormount' => 8,
                'sliders' => 9
            ];
            $machines_title = [
                'micro' => 'Micro Exercises',
                'mini' => 'Mini Exercises',
                'minipro' => 'Mini Pro Exercises',
                'mega' => 'Mega Exercises',
                'megapro' => 'Mega Pro Exercises',
                'evo' => 'Evo Exercises',
                'evo2' => 'Evo 2 Exercises',
                'proformer' => 'Proformer Exercises',
                'abwheel' => 'AB Wheel Exercises',
                'doormount' => 'Door Mount Exercises',
                'sliders' => 'Sliders Exercises'
            ];
            $data['current_machine'] = $machines[$slug];
            $data['current_title'] = $machines_title[$slug];

            $data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                                FROM machines
                                                LEFT OUTER JOIN (SELECT exercise_machine, count(*) AS cnt FROM exercises_machine GROUP BY exercise_machine) x ON x.exercise_machine = machines.id
                                                HAVING countMachine > 0
                                          ')->getResultArray();
            $data['all_accessories'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countAccessories
                                                FROM accessories
                                                LEFT OUTER JOIN (SELECT class_accessories, count(*) AS cnt FROM classes_accessories GROUP BY class_accessories) x ON x.class_accessories = accessories.id
                                                HAVING countAccessories > 0
                                        ')->getResultArray();
    
            $data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                                FROM difficulty
                                                LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                                HAVING countExercises > 0
                                            ')->getResultArray();
            $data['languages'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                                FROM languages
                                                LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                                HAVING countExercises > 0
                                            ')->getResultArray();
            $data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
            $data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
            $data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();
    
            $data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                                FROM body_parts
                                                LEFT OUTER JOIN (SELECT exercise_body_parts, count(*) AS cnt FROM exercises_body_parts GROUP BY exercise_body_parts) x ON x.exercise_body_parts = body_parts.id
                                                HAVING countBodyParts > 0
                                            ')->getResultArray();
            $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                                FROM teachers
                                                LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                                WHERE status = 0
                                                HAVING countExercises > 0
                                                ORDER BY firstname ASC
                                              ')->getResultArray();
    
            // $data['all_exercises'] = $this->model->all_exercises_front(0, 9);
    
            $data['current']['image'] = base_url() . 'images/classes1.jpg';
            $data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Exercises | Lagree On Demand';
            $data['current']['seo_description'] = "Watch Mini, Micro and Mega exercises On Demand, experience the benefits of your practice at home or on the go, on any device.";

            return view('front/exercises/index_view', $data);    
        }else{
            helper('text');
            $exercises_views_model = model('ExercisesViewModel');
            $ExercisesRate_model = model('ExercisesRateModel');
            $SubscribersFavs_model = model('SubscribersExerciseFavsModel');
            $SubscribersExercises = model('SubscribersExercises');
            $shopify_model = model('ShopifyModel');
            $comments_model = model('CommentsModel');
            $data['logged_user'] = $this->user;
            $data['settings'] = $this->settings;
            $data['current'] = $this->model->current($slug);
            $data['current']['video_state'] = $this->model->query("SELECT * FROM video_state WHERE video_id = '" . $data['current']['slug'] . "' AND user_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND video_type = 'exercises'")->getRowArray();

            $data['prev_next'] = $this->model->prev_next($data['current']['id']);
            $data['nums'] = create_session_nums();

            $data['comments'] = $comments_model->get_comments($data['current']['id'], 'exercises');
            $data['count_comments'] = 0;

            if(!empty($data['comments']) AND count($data['comments']) > 0){
                $data['count_comments'] = count($data['comments']);
                foreach($data['comments'] as $key => $single){
                    $data['comments'][$key]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'exercises'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                    if(!empty($data['comments'][$key]['reply']) AND count($data['comments'][$key]['reply']) > 0){
                        $data['comments'][$key]['count_replys'] = count($data['comments'][$key]['reply']);
                        $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply']);

                        foreach($data['comments'][$key]['reply'] as $key2 => $single2){
                            $data['comments'][$key]['reply'][$key2]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single2['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'exercises'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                            if(!empty($data['comments'][$key]['reply'][$key2]['reply']) AND count($data['comments'][$key]['reply'][$key2]['reply']) > 0){
                                $data['comments'][$key]['count_replys'] = $data['comments'][$key]['count_replys'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                                $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                            }
                        }
                    }
                }
            }

            // $data['id'] = $this->get_id_from_slug('exercises', $slug);
            $save_pageview = array('exercise_id' => $data['current']['id'], 'date' => date('Y-m-d'));
            $saved = $exercises_views_model->save($save_pageview);

            // $data['in_favs'] = $SubscribersFavs_model->where(['exercise_id' => $data['current']['id'], 'subscriber_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0)])->find();
            // $data['bought'] = $SubscribersExercises->where(['exercise_id' => $data['current']['id'], 'subscriber_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0), 'purchase_type' => NULL])->find();
            // $data['rented'] = $SubscribersExercises->query("SELECT *, DATE_ADD(MAX(date), INTERVAL 1 DAY) as expiry_rent_date FROM subscribers_exercises  WHERE exercise_id = " . $data['current']['id'] . " AND subscriber_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND purchase_type = 'rent' AND DATE_ADD(date, INTERVAL 1 DAY) > CURDATE()")->getResultArray();
            $data['own'] = $this->model->where(['id' => $data['current']['id'],'teacher' => (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0)])->find();
            $data['similar_exercises'] = $this->model->similar_exercises(0, 3, NULL, 'created_at DESC', $data['current']['id']);

            $data['rated'] = $ExercisesRate_model->where(['exercise_id' => $data['current']['id'], 'user_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0)])->find();

            if($data['current']['all_exercise_machines_shopify'] != NULL){
                $shopify_machines = explode(',', $data['current']['all_exercise_machines_shopify']);
                $data['shopify_machines_titles'] = explode(',', $data['current']['all_exercise_machines']);
                foreach($shopify_machines as $product){
                    if($product != ''){
                        $data['shopify_machines'][] = $shopify_model->single_product($product);
                    }
                }
            }

            if($data['current']['all_exercise_accessories_shopify'] != NULL){
                $shopify_accessories = explode(',', $data['current']['all_exercise_accessories_shopify']);
                $data['shopify_accessories_titles'] = explode(',', $data['current']['all_exercise_accessories']);
                foreach($shopify_accessories as $key => $product){
                    if($product != ''){
                        $data['shopify_accessories'][] = $shopify_model->single_product($product);
                    }
                }
            }
            // if(!isset($data['current']['content']) OR $data['current']['content'] == ''){
            //     $data['current']['content'] = 'We have made the core exercises for your machine(s), follow these instructions and enjoy exercising on your own!';
            // }
            /*
            echo '<pre>';
            var_dump($data);
            die();
            */
            if($data['current']['id'] == NULL){
                return redirect()->to('/');
            }
            return view('front/exercises/single_view', $data);
        }
    }
    public function filter()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'exercises.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Exercises | Lagree On Demand';
		$data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Exercises | Lagree On Demand';
		$data['current']['seo_keywords'] = 'Lagree On Demand Exercises';

        $data['all_exercises'] = $this->model->filter_exercises($filter_data);

		$response['view'] = view('front/exercises/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_exercises']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersExerciseFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('exercise_id' => $data['exercise'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));
        $remove_favs = array('exercise_id' => $data['exercise'], 'subscriber_id' => $data['user']);

        $response['favs'] = $SubscribersFavs_model->where(["exercise_id" => $data['exercise'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
        }

		return $this->respond($response);
    }
    public function mark_as_watched()
    {
        $subscribersWatched_model = model('SubscribersExerciseWatchedModel');
		$request = service('request');
        $data = $request->getPost();
        $save_watched = array('exercise_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $subscribersWatched_model->where(["exercise_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $subscribersWatched_model->save($save_watched);
        }

		return $this->respond($response);
    }
    public function get_exercise_video_preview()
    {
        $subscribersWatched_model = model('SubscribersExerciseWatchedModel');
		$request = service('request');
        $data = $request->getPost();
        $save_watched = array('exercise_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $subscribersWatched_model->where(["exercise_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $subscribersWatched_model->save($save_watched);
        }

		return $this->respond($response);
    }
    public function mark_as_viewed()
    {
        $ExercisesViewModel = model('ExercisesViewModel');
		$request = service('request');
        $data = $request->getPost();
        $save_viewed = array('exercise_id' => $data['class'], 'user_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['success'] = $ExercisesViewModel->save($save_viewed);

		return $this->respond($response);
    }
    public function watch_later()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $data = $this->request->getPost();

        $save_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user'], 
            'type' => $data['type'],
            'date' => date('Y-m-d')
        ];
        $remove_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user']
        ];

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
        }

		return $this->respond($response);
    }

    public function rate_exercise()
    {
        $ExercisesRate_model = model('ExercisesRateModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('exercise_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['favs'] = $ExercisesRate_model->where(["exercise_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $ExercisesRate_model->save($save_rate);
            $response['liked'] = TRUE;
            $response['success'] = TRUE;
        }else{
            $response['status'] = $ExercisesRate_model->delete($response['favs']['id']);
            $response['success'] = TRUE;
            $response['liked'] = FALSE;
        }

		return $this->respond($response);
    }
    public function save()
    {
		$validation =  \Config\Services::validation();

        $rules = [
            'title'         => 'required|min_length[2]',
            'slug'          => 'required|alpha_dash|is_unique[exercises.slug,id,{id}]',
            'video'         => 'required',
            'machine'       => 'required',
            'difficulty'    => 'required',
            'teacher'       => 'required',
        ];
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/exercises', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/exercises/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/exercises/' . $name, 98);
				$data['image'] = 'uploads/exercises/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('exercises_' . $key);
                    $builder->delete(['exercise_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'exercise_id' => $response['inserted_id'],
                            'exercise_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function sess()
    {
        $seller = $this->teacher_info(21);

        // session()->remove('per_page');
        echo '<pre>';
        var_dump($seller);
        echo round((4.99 * 0.7), 2) * 100;
        echo '</pre>';
    }
}