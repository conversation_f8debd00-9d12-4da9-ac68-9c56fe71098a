<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.set_sort_byy.selected {
	color: #000 !important;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Models</h1>
                <a href="admin/models/edit" class="btn black-bg white ml-auto" title="New Teacher">ADD NEW</a>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $models_count == 1 ? $models_count . ' Model' : $models_count . ' Models'; ?></h5>
                <div class="dropdown d-inline-block ml-auto">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/models/sort_by/models.created_at/desc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Date Joined' ? 'selected' : ''; ?>" title="">Date Added</a></li>
                        <li><a href="admin/models/sort_by/models.firstname/asc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Ascending' ? 'selected' : ''; ?>" title="">Ascending</a></li>
                        <li><a href="admin/models/sort_by/models.firstname/desc" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 'Descending' ? 'selected' : ''; ?>" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/models/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" placeholder="Search Models" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="models" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>

                <div class="col-name mr-2">
                  <p class="f-12 medium midGray modelsstatus">STATUS</p>
                  <p class="f-12 medium midGray modelsrole ml-1 px-05">ROLE</p>
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders">
<?php
foreach($all_models as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic w100">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <div class="flex flex-column light">
                                    <a href="admin/models/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] : ''; ?> <?php echo (isset($single['lastname']) AND $single['lastname'] != '') ? $single['lastname'] : ''; ?></a>
                                    <span class="midGray f-12 normal">
                                        <?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] . ' classes' : ''; ?>
                                    </span>
                                    <div class="row-actions f-1">
                                        <a href="admin/models/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1 normal">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record normal" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="models">Delete</a>-->
                                    </div>
                                    <?php echo (isset($single['upcoming']) AND $single['upcoming'] != '') ? $single['upcoming'] : ''; ?>
                                </div>
                                <div class="flex aic jcr ml-auto">
                                    <!-- <span class="f-12 normal teacherdate"><?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/y', strtotime($single['created_at'])) : ''; ?></span> -->
                                    <?php if(isset($single['hasHistory']) AND $single['hasHistory'] > 0){ ?>
                                    <span class="f-12 normal teacherdate flex flex-column aic jcc">
                                        <span class="flex-inline unpaidbtn aic jcc <?php echo (isset($single['unpaidClasses']) AND $single['unpaidClasses'] > 0) ? 'lightRed-bg normalRed' : 'lightGreen-bg textGreen'; ?> px-15 py-1 rounded medium mb-1">
                                            <?php echo (isset($single['unpaidClasses']) AND $single['unpaidClasses'] > 0) ? 'Unpaid' : 'Paid'; ?>
                                        </span>
                                        <a href="admin/models/history/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-midGray f-10 midGray text-underline">history</a>
                                    </span>
                                    <?php } ?>
                                    <span class="btn btn-xs white f-12 externaluser normaladmin">Model</span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($models_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_models); ?><span class="midGray mx-1">of <?php echo $models_count; ?></span>
                    <a href="admin/models/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/models/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_models) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_models)) == $models_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-1"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
</script>
</body>
</html>