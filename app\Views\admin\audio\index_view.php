<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.small-audio-icon {
	width: 120px;
	height: 120px;
	margin-right: 20px;
	border: 1px solid #f0f0f0;
	padding: 15px;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Audio Tracks</h1>
                <div class="ml-auto">
                    <a href="admin/audio/edit" class="btn black-bg white" title="Upload Class">New track</a>
                </div>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $audio_count == 1 ? $audio_count . ' Code' : $audio_count . ' audio'; ?></h5>
                <div class="flex aic jcsb">
                <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/audio/sort_by/audio.created_at/desc" class="link midGray" title="">Date Uploaded</a></li>
                        <li><a href="admin/audio/sort_by/audio.title/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/audio/sort_by/audio.title/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/audio/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
              </div>
            </div>
            <hr class="mt-2 mb-2">
            <!-- <pre>
                <?php // print_r($all_audio); ?>
            </pre> -->
            <div class="flex aic jcsb">
                <div class="flex aic ml-2">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple normalRed" data-table="audio" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders">
<?php
foreach($all_audio as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/audio/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="avatar black-bg white medium mr-2 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="39.5" height="60" viewBox="0 0 39.5 60" style="width: 14px;fill: #fff;margin: auto;height: 100%;">
                                        <path id="music_note_FILL0_wght400_GRAD0_opsz48" d="M24.65,66a12.379,12.379,0,0,1-12.5-12.5A12.379,12.379,0,0,1,24.65,41a12.5,12.5,0,0,1,4.208.667A10.835,10.835,0,0,1,32.15,43.5V6h19.5V17.25H37.15V53.5A12.379,12.379,0,0,1,24.65,66Z" transform="translate(-12.15 -6)"/>
                                    </svg>
                                </a>
                                <div class="flex flex-column">
                                    <a href="admin/audio/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title  medium mb-05">
                                        <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                    </a>
                                    <span class="midGray f-1 normal">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    <div class="row-actions f-1 red">
                                        <a href="admin/audio/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="audio">Delete</a>-->
                                    </div>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($audio_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_audio); ?><span class="midGray mx-1">of <?php echo $audio_count; ?></span>
                    <a href="admin/audio/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/audio/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_audio) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_audio)) == $audio_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
</script>
</body>
</html>