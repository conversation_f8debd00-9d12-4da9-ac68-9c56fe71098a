<?php namespace App\Models;

use CodeIgniter\Model;

class HowtoModel extends Model
{
    protected $table = 'howto';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'video', 'video_thumb', 'video_preview', 'video_encrypted_path', 'duration', 'content', 'teacher', 'difficulty', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'notification_sent'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[howto.slug,id,{id}]',
        // 'content'     => 'required',
        // 'video'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        // 'body_parts'     => 'required',
        // 'accessories'     => 'required',
        // 'springs'     => 'required',
        // 'teacher'     => 'required',
    ];
    protected $validationMessages = [
        'slug' => [
            'required'  => 'The Page URL field is required.',
            'is_unique' => 'The Page URL field must be unique!'
        ]
    ];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_howto($start = 0, $limit = 10, $search_term = "0", $order = "howto.created_at DESC", $status = "0,1"){
        $status_sql = ($status != NULL) ? " AND howto.status IN (" . $status . ")" : " AND howto.status = 0";
        $admin = session('admin') ? '' : ' AND howto.status = 0 ';
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        // if($search_term != "0"){
        //     $words = explode(" ", $search_term);
        //     $string = array_map(function(&$word){
        //         return "+" . $word . "*";
        //     }, $words);

        //     $term = implode(" ",$string);
        //     $search =  "AND (MATCH(howto.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        // }else{
        //     $search = "";
        // }

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(howto.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND howto.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT howto.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            difficulty.title as diff,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_classes,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                            WHERE howto.deleted_at IS NULL
                            " . $status_sql . "
                            " . $admin . "
                            " . $search . "
                            GROUP BY howto.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_howto_search($start = 0, $limit = 10, $search_term = "0"){
        $admin = session('admin') ? '' : ' AND howto.status = 0 ';
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        // if($search_term != "0"){
        //     $words = explode(" ", $search_term);
        //     $string = array_map(function(&$word){
        //         return "+" . $word . "*";
        //     }, $words);

        //     $term = implode(" ",$string);
        //     $search =  "AND (MATCH(howto.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        // }else{
        //     $search = "";
        // }

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(howto.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND howto.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT howto.*,
                            COALESCE(y.rate,0) AS likeCount,
                            difficulty.title as diff,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS machines,
                            '' AS tempo
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(rate > 3) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            WHERE howto.deleted_at IS NULL
                            AND howto.status = 0
                            " . $admin . "
                            " . $search . "
                            GROUP BY howto.id
                            ORDER BY howto.created_at DESC
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_micro_howto($start = 0, $limit = 0, $search_term = NULL, $order = "howto.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND howto.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, teachers.id  as teach_id, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, video_state.video_time as video_state,
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                            WHERE howto.deleted_at IS NULL
                            AND howto.status = 0
                            AND howto_machine.class_machine = 1
                            " . $search . "
                            GROUP BY howto.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_mega_howto($start = 0, $limit = 0, $search_term = NULL, $order = "howto.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND howto.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, teachers.id  as teach_id, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, video_state.video_time as video_state,
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                            WHERE howto.deleted_at IS NULL
                            AND howto.status = 0
                            AND howto_machine.class_machine = 2
                            " . $search . "
                            GROUP BY howto.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function filter_howto($data){

        $limit_size =  ($data['limit'] != 0) ? " LIMIT " . $data['start'] . ", " . $data['limit'] : "";
		$sql_add = '';

		//WHERE

        if( (isset($data['machine']) AND $data['machine'] != '') OR
            (isset($data['body_parts']) AND $data['body_parts'] != '') OR
            (isset($data['teacher']) AND $data['teacher'] != '') OR
            (isset($data['difficulty']) AND $data['difficulty'] != '') OR
            (isset($data['duration']) AND $data['duration'] != '')
        ){
            $sql_add .=  "WHERE howto.deleted_at IS NULL AND howto.status = 0 ";
            if(isset($data['machine']) AND $data['machine'] != ''){
                $sql_add .= " AND howto_machine.class_machine IN (" . ((isset($data['machine']) AND is_array($data['machine'])) ? implode(',', $data['machine']) : 0) . ") ";
            }

            if(isset($data['body_parts']) AND $data['body_parts'] != ''){
                $sql_add .= " AND howto_body_parts.class_body_parts IN (" . ((isset($data['body_parts']) AND !empty($data['body_parts']) AND is_array($data['body_parts'])) ? implode(',', $data['body_parts']) : 0) . ") ";
            }
            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $sql_add .= " AND teachers.id IN (" . ((isset($data['teacher']) AND !empty($data['teacher']) AND is_array($data['teacher'])) ? implode(',', $data['teacher']) : 0) . ") ";
            }

            if(isset($data['difficulty']) AND $data['difficulty'] != ''){
                $sql_add .=  " AND howto.difficulty IN (" . ((isset($data['difficulty']) AND !empty($data['difficulty']) AND is_array($data['difficulty'])) ? implode(',', $data['difficulty']) : 0) . ")";
            }
            if(isset($data['duration']) AND $data['duration'] != ''){
                $sql_add .=  " AND (howto.duration ";
                $sql_add .=  (isset($data['duration']) AND !empty($data['duration']) AND is_array($data['duration'])) ? implode(' OR howto.duration ', $data['duration']) . ")" : "< 0)";
            }
        }else{
            $sql_add .=  "WHERE howto.deleted_at IS NULL AND howto.status = 0 ";
        }

        $result = $this->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, video_state.video_time as video_state,
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN howto_body_parts ON howto_body_parts.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                            " . $sql_add . "
                            GROUP BY howto.id
                            ORDER BY " . $data['order'] . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $result;
    }

    public function similar_howto($start = 0, $limit = 0, $search_term = NULL, $order = "howto.updated_at desc", $id = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND howto.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines, video_state.video_time as video_state,
                            FROM howto
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = howto.difficulty
                            LEFT JOIN teachers on teachers.id = howto.teacher
                            LEFT JOIN video_state on (video_state.video_id = howto.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                            WHERE howto.deleted_at IS NULL
                            AND howto.status = 0
                            AND howto.id != " . $id . "
                            " . $search . "
                            GROUP BY howto.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    function prev_next($id = 0)
	{
		$result = array('prev' => NULL, 'next' => NULL,);

		if($id != 0){
            $tmp = $this->query("SELECT * FROM howto WHERE status = 0 AND deleted_at IS NULL ORDER BY created_at desc")->getResultArray();

			if(count($tmp) > 1)
			{
				$total = count($tmp);
				$index_list = array_column($tmp, 'id');
				$index_id = array_search($id, array_column($tmp, 'id'));
				if($index_id !== FALSE){
					if($index_id < $total - 1){
						$result['next'] = $this->where(['id' => $index_list[$index_id + 1]])->first();
					}else{
						$result['next'] = $this->where(['id' => $index_list[0]])->first();
					}

					if($index_id > 0){
						$result['prev'] = $this->where(['id' => $index_list[$index_id - 1]])->first();
					}else{
						$result['prev'] = $this->where(['id' => $index_list[$total - 1]])->first();
					}
				}
			}
		}

        // echo "<pre>";
        // var_dump($result);
        // die();

		return $result;
	}

    public function exercises_for_videos($class_id = 0){
        $exercises_model = model('ExercisesModel');

        if($class_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT class_selected_exercises AS id FROM howto_selected_exercises WHERE class_id = " . $class_id . "");
            $res = $query->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }
            $data = $exercises_model->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, howto_selected_exercises.id as csc_id, 'exercises_class' AS class, video_state.video_time as video_state
                                            FROM exercises
                                            LEFT OUTER JOIN (SELECT exercise_id, count(*) AS cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) AS rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                                            LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'videos')
                                            LEFT JOIN howto_selected_exercises ON howto_selected_exercises.class_selected_exercises = exercises.id
                                            WHERE exercises.deleted_at IS NULL
                                            AND exercises.status = 0
                                            AND exercises.id IN (" . $ids . ")
                                            GROUP BY exercises.id
                                            ORDER BY howto_selected_exercises.sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT howto.*, difficulty.title as diff, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_class_accessories_shopify,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT tensions.title ORDER BY tensions.sort ASC SEPARATOR ', ') AS all_howto_tensions,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_class_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM howto
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x ON x.class_id = howto.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y ON y.class_id = howto.id
                                LEFT JOIN howto_accessories ON  howto_accessories.class_id = howto.id
                                LEFT JOIN accessories ON accessories.id = howto_accessories.class_accessories
                                LEFT JOIN howto_tensions ON  howto_tensions.class_id = howto.id
                                LEFT JOIN tensions ON tensions.id = howto_tensions.class_tensions
                                LEFT JOIN howto_springs ON  howto_springs.class_id = howto.id
                                LEFT JOIN springs ON springs.id = howto_springs.class_springs
                                LEFT JOIN howto_body_parts ON howto_body_parts.class_id = howto.id
                                LEFT JOIN body_parts ON body_parts.id = howto_body_parts.class_body_parts
                                LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                LEFT JOIN teachers ON teachers.id = howto.teacher
                                WHERE howto.deleted_at IS NULL
                                AND howto.status = 0
                                AND howto.slug = '" . $slug . "'
                            ")->getRowArray();
        return $data;
    }
    public function cron(){
        $data = $this->query("SELECT howto.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teacher_name
                                FROM howto
                                LEFT JOIN teachers ON teachers.id = howto.teacher
                                WHERE howto.deleted_at IS NULL
                                AND howto.status = 0
                                AND howto.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

	// public function add_gallery($page_id = 0, $gallery = array())
        // {
        // 	$response['success'] = FALSE;
        // 	$db      = \Config\Database::connect();
        // 	$builder = $db->table('howto_galleries');
        // 	$builder->delete(['page_id' => $page_id]);
        // 	if (count($gallery) > 0)
        // 	{
        // 		$builder->insertBatch($gallery);
        // 	}

        // 	/*
        // 	if (count($users) == 1)
        // 	{
        // 		$response['user_id'] = $users[0]['id'];
        // 		$response['success'] = TRUE;
        // 	}
        // 	else
        // 	{
        // 		$response['error'] = 'Bad username or password.';
        // 	}*/
        // 	return $response;
	// }


	protected function prepare_data(array $data)
	{
		return $data;
	}

}