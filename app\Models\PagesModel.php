<?php namespace App\Models;

use CodeIgniter\Model;

class PagesModel extends Model
{
    protected $table = 'pages';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'webp_image', 'content', 'template', 'seo_title', 'seo_keywords', 'seo_description', 'sort', 'status'];
	protected $returnType     = 'array';

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[pages.slug,id,{id}]',
        'content'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	public function add_gallery($page_id = 0, $gallery = array())
	{
		$response['success'] = FALSE;
		$db      = \Config\Database::connect();
		$builder = $db->table('pages_galleries');
		$builder->delete(['page_id' => $page_id]);
		if (count($gallery) > 0)
		{
			$builder->insertBatch($gallery);
		}

		/*
		if (count($users) == 1)
		{
			$response['user_id'] = $users[0]['id'];
			$response['success'] = TRUE;
		}
		else
		{
			$response['error'] = 'Bad username or password.';
		}*/
		return $response;
	}
	public function get_gallery($page_id = 0)
	{
		$response['success'] = FALSE;
		$db      = \Config\Database::connect();
		$builder = $db->table('pages_galleries');
		$response =  $builder->getWhere(['page_id' => $page_id])->getResultArray();
		return $response;
	}
    public function all_pages($start = 0, $limit = 0, $search_term = NULL, $order = "sort, pages.updated_at desc"){
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search = ($search_term != NULL) ? "AND pages.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT pages.*
                            -- COALESCE(x.cnt,0) AS classesCount,
                            -- COALESCE(y.cnty,0) AS howtoCount
                            FROM pages
                            -- LEFT OUTER JOIN (SELECT pages_id, pages_selected_classes, count(*) as cnt FROM pages_selected_classes WHERE pages_selected_classes != 0 GROUP BY pages_id) x ON x.pages_id = pages.id
                            -- LEFT OUTER JOIN (SELECT pages_id, pages_selected_howto, count(*) as cnty FROM pages_selected_howto WHERE pages_selected_howto != 0 GROUP BY pages_id) y ON y.pages_id = pages.id
                            -- LEFT JOIN teachers ON teachers.id = pages.teacher
                            -- LEFT JOIN subscribers ON subscribers.id = pages.user_id
                            WHERE pages.deleted_at IS NULL
                            -- AND pages.author = 1
                            " . $search . "
                            GROUP BY pages.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();

        return $data;
    }

    public function current($slug = '')
    {
        $data = $this->query("SELECT pages.*, difficulty.title as diff,
                                        FROM pages
                                        WHERE pages.deleted_at IS NULL
                                        AND pages.slug = '" . $slug . "'
                                    ")->getRowArray();
        return $data;
    }

    public function cron(){
        $data = $this->query("SELECT pages.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teach_name
                                FROM pages
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE pages.deleted_at IS NULL
                                AND pages.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}