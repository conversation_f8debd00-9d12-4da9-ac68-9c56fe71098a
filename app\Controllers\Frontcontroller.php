<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Frontcontroller extends Controller
{
	public $main_menu;
	public function __construct() {
		$model = model('PagesModel');
		$this->email_model = model('EmailModel');

		$settingsModel = model('SettingsModel');
		$this->settings = $settingsModel->where(['id' => 1])->first();

		$subscribersModel = model('SubscribersModel');
		$this->user = $subscribersModel->where(['id' => session("user")])->first();

        helper("front");
        helper("text");

		/* echo '<pre>';
        print_r(session("user"));
		print_r($this->user);
		die(); */
	}

    public function sendMail() {

        $email = new \Config\Email();
        $request = service('request');
        $data = $request->getPost();

        $to = $data['mailTo'];
        $subject = $data['subject'];
        $message = $data['message'];

        $email = \Config\Services::email();

        $email->setTo($email->SMTPUser);
        $email->setFrom('<EMAIL>', 'Confirm Registration');
        // $email->setBCC('<EMAIL>');

        $email->setSubject($subject);
        $email->setMessage($message);

        if ($email->send()){
            echo 'Email successfully sent';
        }else{
            $data = $email->printDebugger(['headers']);
            print_r($data);
        }
    }

    public function my_favs($user_id = 0){
        $classes_model = model('ClassesModel');

        if($collection_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT class_id AS id FROM subscribers_favs WHERE class_id = " . $user_id . "");
            $res = $query->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }
            $data = $classes_model->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach, teachers.slug as teach_slug, GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines
                                            FROM classes
                                            LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                            LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                            LEFT JOIN teachers ON teachers.id = classes.teacher
                                            WHERE classes.deleted_at IS NULL
                                            AND classes.status = 0
                                            AND classes.id IN (" . $ids . ")
                                            GROUP BY classes.id
                                            ORDER BY classes.updated_at desc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }

    public function get_id_from_slug($table, $slug = 0)
	{
        $currentModel = model($table . 'Model');
		$response = $currentModel->where(['slug' => $slug])->first();

        return $response['id'];
	}

    public function class_info($class_id)
    {
        $classModel = model('ClassesModel');
        $response['success'] = TRUE;
        // $response['class'] = $classModel->where(['id' => $class_id])->first();
        $response = $classModel->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image, 'classes' AS type,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x ON x.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y ON y.class_id = classes.id
                                LEFT JOIN classes_accessories ON  classes_accessories.class_id = classes.id
                                LEFT JOIN accessories ON accessories.id = classes_accessories.class_accessories
                                LEFT JOIN classes_springs ON  classes_springs.class_id = classes.id
                                LEFT JOIN springs ON springs.id = classes_springs.class_springs
                                LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                                LEFT JOIN body_parts ON body_parts.id = classes_body_parts.class_body_parts
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 0
                                AND classes.id = '" . $class_id . "'
                            ")->getFirstRow('array');

        return $response;
    }

    public function video_info($class_id)
    {
        $howtoModel = model('HowtoModel');
        $response['success'] = TRUE;
        // $response['class'] = $howto->where(['id' => $class_id])->first();
        $response = $howtoModel->query("SELECT howto.*, difficulty.title as diff, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image, 'videos' AS type,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM howto
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x ON x.class_id = howto.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y ON y.class_id = howto.id
                                LEFT JOIN howto_accessories ON  howto_accessories.class_id = howto.id
                                LEFT JOIN accessories ON accessories.id = howto_accessories.class_accessories
                                LEFT JOIN howto_springs ON  howto_springs.class_id = howto.id
                                LEFT JOIN springs ON springs.id = howto_springs.class_springs
                                LEFT JOIN howto_body_parts ON howto_body_parts.class_id = howto.id
                                LEFT JOIN body_parts ON body_parts.id = howto_body_parts.class_body_parts
                                LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                LEFT JOIN teachers ON teachers.id = howto.teacher
                                WHERE howto.deleted_at IS NULL
                                AND howto.status = 0
                                AND howto.id = '" . $class_id . "'
                            ")->getFirstRow('array');

        return $response;
    }

        public function teacher_info($id = 0)
    {
        $TeachersModel = model('TeachersModel');
        if($id != 0){
            $response = $TeachersModel->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND id = " . $id . "")->getFirstRow('array');
        }else{
            $response = NULL;
        }

        return $response;
    }

    public function subscriber_from_teacher_info($teacher_email = "")
    {
        $TeachersModel = model('TeachersModel');
        if($teacher_email != ""){
            $response = $TeachersModel->query("SELECT * FROM subscribers WHERE deleted_at IS NULL AND email = '" . $teacher_email . "'")->getFirstRow('array');
            // $response = $TeachersModel->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND id = 21")->getFirstRow('array');
            // $response = $this->teacher_info(87);

        }else{
            $response = NULL;
        }

        return $response;
    }

    public function playlist_info($id = 0)
    {
        $PlaylistsModel = model('PlaylistsModel');
        if($id != 0){
            $response = $PlaylistsModel->query("SELECT * FROM playlists WHERE deleted_at IS NULL AND id = " . $id . "")->getFirstRow('array');
        }else{
            $response = NULL;
        }

        return $response;
    }

    public function upload()
	{
        $files = $this->request->getFiles();
        $file = $files['video'];

		if(!empty($file)){
        	$file_name   		=   $file->getName();
            $file_extension     =   $file->guessExtension();
            $allowed_extension  =   array('mp4', 'avi');

            if(in_array($file_extension,$allowed_extension)){
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/videos', $name);
				$data['uploaded_video'] = 'uploads/videos/' . $name;
                echo $data['uploaded_video'];

            }else{
            	echo 'Please upload valid file';
            }
        }
	}

    public function ajax_delete($table = 'classes', $record_id = 0)
    {
		$currentModel = model($table . 'Model');
		if ($record_id > 0){
			$response['success'] = $currentModel->delete($record_id);
		}
        return $this->respond($response);
    }
    public function device_rename()
    {
		$currentModel = model('DevicesModel');
        $request = service('request');
        $data = $request->getPost();

        $save_data = ['id' => (int)$data['id'], 'title' => trim($data['title'])];
        $response['save_data'] = $save_data;

		if ($data['title'] != '' AND (int)$data['id'] > 0){
			$response['success'] = $currentModel->save($save_data);
		}
        return $this->respond($response);
    }

	public function video_thumbnail()
	{
        $data = $this->request->getPost();
        $video_path = $data['video'];

        $video['video_path'] = $video_path;

        $tmp1 = $_SERVER['DOCUMENT_ROOT'] . '/uploads/videos/test-thumbnail1.jpg';
        if($_ENV['CI_ENVIRONMENT'] == 'production'){
            $video['exec'] = exec("ffmpeg -ss 00:00:20 -i " . str_replace("\\","/" , $video_path ) . " -an -r 1 -vframes 1 -y -vsync vfr -filter:v scale=1280:720,crop=1280:720 " . str_replace("\\","/" , $tmp1));
        }else{
            $video['exec'] = exec("ffmpeg -i '" . str_replace("\\","/" , $video_path ) . "' -ss 00:00:10 -vframes 1 -y -vf 'scale=1280:720:force_original_aspect_ratio=decrease' -f image2 '$tmp1'");
            // echo '<pre>';
            // print_r("ffmpeg -i '" . str_replace("\\","/" , $video_path ) . "' -ss 00:00:10 -vframes 1 -y -vf 'scale=1280:720:force_original_aspect_ratio=decrease' -f image2 '$tmp1'");
            // die();
            
        }

        $file1 = new \CodeIgniter\Files\File($tmp1);
        $name1 = $file1->getRandomName();
        $file1->move(ROOTPATH . 'public/uploads/videos/thumbnails', $name1);

        $video['success'] = TRUE;
        $video['video'][1] = '/uploads/videos/thumbnails/' . $name1;

        //////////////////////////VIDEO PREVIEW///////////////////////////////
        $dur = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 '$video_path'");
        $seconds = round($dur);

        $path_clip = './uploads/videos/video_preview/';
        if($_ENV['CI_ENVIRONMENT'] == 'production'){
            shell_exec("ffmpeg -i '$video_path' -an -ss 00:00:20 -t 10 -vf 'scale=640:360' -y '$path_clip'/preview_new.mp4");
        }else{          
            shell_exec("ffmpeg -i '$video_path' -ss 00:00:10 -t 10 -vf 'scale=640:360' -c:v libx264 -c:a aac -strict experimental '$path_clip'/preview_new.mp4");  
        }

        

        $file_preview = "$path_clip/preview_new.mp4";
        $file_preview1 = new \CodeIgniter\Files\File($file_preview);
        $file_preview_name1 = $file_preview1->getRandomName();
        $file_preview1->move(ROOTPATH . 'public/uploads/videos/video_preview', $file_preview_name1);
        $video['video_preview'] = '/uploads/videos/video_preview/' . $file_preview_name1;
        $video['duration'] = $seconds;
        /////////////////////////////////////////////////////////////////////

        return $this->respond($video);
	}


    public function index()
    {
		echo '<pre>';
		print_r($this->main_menu);
    }
}