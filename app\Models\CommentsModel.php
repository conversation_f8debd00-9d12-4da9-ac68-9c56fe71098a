<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class CommentsModel extends Model
{
    protected $table = 'comments';
	protected $allowedFields = ['class_id', 'parent', 'date', 'message', 'user_id', 'status', 'notify_replay', 'notified', 'type', 'teacher_replied'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'class_id'    => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_comments($start = 0, $limit = 25, $search_term = NULL, $order = "comments.date DESC"){
        $TeachersModel = model('TeachersModel');

        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND comments.message LIKE '%$search_term%'" : "";

        if(session('super_admin') != 1){
            $ids = $TeachersModel->teacher_classes_ids(session('admin'));
            
            $sql = "SELECT comments.*,
                                IF(comments.user_id = comments.teacher_replied, 
                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                ) as user_name,
                                (SELECT COUNT(*) FROM comments c WHERE c.parent = comments.id AND (c.teacher_replied = '" . (session('admin') != NULL ? session('admin') : '100000') . "' OR c.teacher_replied != '-1')) as replied_child_comments
                                FROM comments
                                LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                WHERE comments.parent = 0
                                AND comments.class_id IN (" . (count($ids) > 0 ? implode(',', $ids) : '100000000') . ")
                                " . $search . "
                                GROUP BY comments.id
                                ORDER BY date DESC
                        ";
                        // echo '<pre>';
                        // print_r($sql);
                        // die();
                        
            $data = $TeachersModel->query("SELECT comments.*,
                                IF(comments.user_id = comments.teacher_replied, 
                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                ) as user_name,
                                (SELECT COUNT(*) FROM comments c WHERE c.parent = comments.id AND (c.teacher_replied = '" . (session('admin') != NULL ? session('admin') : '100000') . "' OR c.teacher_replied != '-1')) as replied_child_comments
                                FROM comments
                                LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                WHERE comments.parent = 0
                                AND comments.class_id IN (" . (count($ids) > 0 ? implode(',', $ids) : '100000000') . ")
                                " . $search . "
                                GROUP BY comments.id
                                ORDER BY date DESC
                        ")->getResultArray();

            foreach($data as $key => $single_comment) {
                $data[$key]['my_replies'] = $TeachersModel->query("SELECT *,
                                                                            IF(comments.user_id = comments.teacher_replied, 
                                                                                CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                            ) as user_name
                                                                            FROM comments
                                                                            LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                                                            LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                            WHERE comments.teacher_replied = '" . (session('admin') != NULL ? session('admin') : '100000') . "'
                                                                            AND comments.parent = " . $single_comment['id'] . "
                                                                ")->getResultArray();
            }
        }else{
            $data = $this->query("SELECT comments.*,
                                    IF(comments.type = 'courses_videos', (SELECT slug FROM courses WHERE id = comments.class_id), '') as course_slug,
                                    IF(comments.user_id = comments.teacher_replied, 
                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                    ) as user_name
                                    FROM comments
                                    LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                    WHERE comments.date IS NOT NULL
                                    AND comments.parent = 0
                                    AND comments.class_id != 0
                                    " . $search . "
                                    ORDER BY " . $order . "
                                    " . $limit_size . "
                                    ")->getResultArray();
            foreach($data as $key => $single_comment) {
                $data[$key]['my_replies'] = $TeachersModel->query("SELECT comments.*,
                                                                            IF(comments.user_id = comments.teacher_replied, 
                                                                                CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                            ) as user_name
                                                                            FROM comments
                                                                            LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                                                            LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                            WHERE comments.parent = " . $single_comment['id'] . "
                                                                ")->getResultArray();
                if(isset($data[$key]['my_replies']) AND count($data[$key]['my_replies']) > 0){
                    foreach($data[$key]['my_replies'] as $k => $single_comment_child) {
                        $data[$key]['my_replies'][$k]['my_replies_child'] = $TeachersModel->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single_comment_child['id'] . "
                                                                        ")->getResultArray();
                    }
                }
            }
        }
        return $data;
    }

    public function get_comments($class_id = 0, $type = 'classes'){
        if($class_id != 0){
            $data = $this->query("SELECT comments.*,
                                                CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name,
                                                CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1)) as user_initials,
                                                subscribers.image as user_image
                                                FROM comments
                                                LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                WHERE comments.class_id = " . $class_id . "
                                                AND comments.status = 0
                                                AND comments.parent = 0
                                                AND comments.type = '" . $type . "'
                                                ORDER BY comments.date desc
                                            ")->getResultArray();
        }else{
            $data = [];
        }
        return $data;
    }

    public function nonapproved_comments_admin(){
        $TeachersModel = model('TeachersModel');
        if(session('admin') AND session('super_admin') != 1){
            $ids = $TeachersModel->teacher_classes_ids(session('admin'));
            
            $sql = "SELECT comments.*,
                                IF(comments.user_id = comments.teacher_replied, 
                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                ) as user_name,
                                (SELECT COUNT(*) FROM comments c WHERE c.parent = comments.id AND (c.teacher_replied = " . (session('admin') != NULL ? session('admin') : 100000) . " OR c.teacher_replied != '-1')) as replied_child_comments
                                FROM comments
                                LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                WHERE comments.status = 0
                                AND comments.class_id IN (" . (count($ids) > 0 ? implode(',', $ids) : '100000000') . ")
                                AND comments.teacher_replied != '-1'
                                HAVING replied_child_comments = 0
                                ORDER BY date DESC
                        ";
                // echo '<pre>';
                // print_r($sql);
                // die();
                
            $data = $TeachersModel->query("SELECT comments.*,
                                IF(comments.user_id = comments.teacher_replied, 
                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                ) as user_name,
                                (SELECT COUNT(*) FROM comments c WHERE c.parent = comments.id AND (c.teacher_replied = " . (session('admin') != NULL ? session('admin') : 100000) . " OR c.teacher_replied != '-1')) as replied_child_comments
                                FROM comments
                                LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                WHERE comments.status = 0
                                AND comments.class_id IN (" . (count($ids) > 0 ? implode(',', $ids) : '100000000') . ")
                                AND comments.teacher_replied != '-1'
                                HAVING replied_child_comments = 0
                                ORDER BY date DESC
                        ")->getResultArray();
        }else{
            $data = $this->query("SELECT comments.*,
                                    IF(comments.user_id = comments.teacher_replied, 
                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                    ) as user_name
                                    FROM comments
                                    LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                    WHERE comments.status != 0
                                    AND comments.parent = 0
                                    ORDER BY date DESC
                                    ")->getResultArray();
            foreach($data as $key => $single_comment) {
                $data[$key]['my_replies'] = $TeachersModel->query("SELECT *,
                                                                            IF(comments.user_id = comments.teacher_replied, 
                                                                                CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                            ) as user_name
                                                                            FROM comments
                                                                            LEFT JOIN subscribers ON subscribers.id = comments.user_id
                                                                            LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                            -- WHERE comments.teacher_replied = " . session('admin') . "
                                                                            WHERE comments.parent = " . $single_comment['id'] . "
                                                                ")->getResultArray();
            }
        }
        return $data;
    }

    // public function count_comments_admin(){
    //     $messages = $this->query("SELECT comments.*,
    //                             CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name,
    //                             COALESCE(x.cnt,0) AS countMessages
    //                             FROM comments
    //                             LEFT OUTER JOIN (SELECT conversation_id, count(*) as cnt FROM subscribers_comments WHERE admin_seen = 0 AND sender_id != 0 GROUP BY conversation_id) x on x.conversation_id = comments.id
    //                             LEFT JOIN subscribers ON subscribers.id = comments.subscriber_id
    //                             WHERE comments.hide = 0
    //                             ORDER BY comments.date DESC
    //                     ")->getResultArray();
    //     $c = 0;
    //     foreach($messages as $single)                        {
    //         if($single['countMessages'] > 0){
    //             $c++;
    //         }
    //     }

    //     return $c;
    // }

    public function current($id){
        $data = $this->query("SELECT comments.*
                            FROM comments
                            WHERE comments.id = " . $id .  "
                        ")->getRowArray();
        return $data;
    }


	protected function prepare_data(array $data)
	{
		return $data;
	}
}