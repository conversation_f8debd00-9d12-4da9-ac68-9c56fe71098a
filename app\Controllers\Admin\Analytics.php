<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;

class Analytics extends Admincontroller
{
	public function __construct() {
		parent::__construct();
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $classes_views_model = model('ClassesViewModel');
        $exercises_views_model = model('ExercisesViewModel');
        $videos_views_model = model('HowtoViewModel');
        $courses_views_model = model('CoursesViewModel');
        $classes_model = model('ClassesModel');
        $exercises_model = model('ExercisesModel');
        $howto_model = model('HowtoModel');

        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_classes'] = $classes_model->countAllResults();
        $data['all_exercises'] = $exercises_model->countAllResults();
        $data['all_videos'] = $howto_model->countAllResults();
        $data['all_video_files'] = $data['all_classes'] + $data['all_exercises'] + $data['all_videos'];

        $data['lifetime_views_classes'] = $classes_views_model->countAll();
        $data['lifetime_views_exercises'] = $exercises_views_model->countAll();
        $data['lifetime_views_videos'] = $videos_views_model->countAll();
        $data['lifetime_views_courses'] = $courses_views_model->countAll();
        $data['all_lifetime_views'] = $data['lifetime_views_classes'] + $data['lifetime_views_exercises'] + $data['lifetime_views_videos'] + $data['lifetime_views_courses'];

        $data['lifetime_classes_views'] = $classes_views_model->countAll();
        $data['lifetime_exercises_views'] = $exercises_views_model->countAll();
        $data['lifetime_videos_views'] = $videos_views_model->countAll();
        $data['lifetime_courses_views'] = $courses_views_model->countAll();
        $data['all_lifetime_views'] = $data['lifetime_views_classes'] + $data['lifetime_views_exercises'] + $data['lifetime_views_videos'] + $data['lifetime_views_courses'];

        $data['this_week_classes_views'] = $classes_views_model->where("date BETWEEN (NOW() - INTERVAL 7 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
        $data['this_week_exercises_views'] = $exercises_views_model->where("date BETWEEN (NOW() - INTERVAL 7 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
        $data['this_week_videos_views'] = $videos_views_model->where("date BETWEEN (NOW() - INTERVAL 7 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
        $data['this_week_courses_views'] = $courses_views_model->where("date BETWEEN (NOW() - INTERVAL 7 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
        $data['all_this_week_views'] = $data['this_week_classes_views'] + $data['this_week_exercises_views'] + $data['this_week_videos_views'] + $data['this_week_courses_views'];

        $data['last_week_classes_views'] = $classes_views_model->where("date BETWEEN (NOW() - INTERVAL 15 DAY) AND (NOW() - INTERVAL 8 DAY)")->countAllResults();
        $data['last_week_exercises_views'] = $exercises_views_model->where("date BETWEEN (NOW() - INTERVAL 15 DAY) AND (NOW() - INTERVAL 8 DAY)")->countAllResults();
        $data['last_week_videos_views'] = $videos_views_model->where("date BETWEEN (NOW() - INTERVAL 15 DAY) AND (NOW() - INTERVAL 8 DAY)")->countAllResults();
        $data['last_week_courses_views'] = $courses_views_model->where("date BETWEEN (NOW() - INTERVAL 15 DAY) AND (NOW() - INTERVAL 8 DAY)")->countAllResults();
        $data['all_last_week_views'] = $data['last_week_classes_views'] + $data['last_week_exercises_views'] + $data['last_week_videos_views'] + $data['last_week_courses_views'];

        $data['this_month_classes_views'] = $classes_views_model->where("date BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()")->countAllResults();
        $data['this_month_exercises_views'] = $exercises_views_model->where("date BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()")->countAllResults();
        $data['this_month_videos_views'] = $videos_views_model->where("date BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()")->countAllResults();
        $data['this_month_courses_views'] = $courses_views_model->where("date BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()")->countAllResults();
        $data['all_this_month_views'] = $data['this_month_classes_views'] + $data['this_month_exercises_views'] + $data['this_month_videos_views'] + $data['this_month_courses_views'];
        
        $data['last_month_classes_views'] = $classes_views_model->where("date BETWEEN LAST_DAY(CURDATE() - INTERVAL 2 month) + INTERVAL 1 day AND LAST_DAY(CURDATE() - INTERVAL 1 month)")->countAllResults();
        $data['last_month_exercises_views'] = $exercises_views_model->where("date BETWEEN LAST_DAY(CURDATE() - INTERVAL 2 month) + INTERVAL 1 day AND LAST_DAY(CURDATE() - INTERVAL 1 month)")->countAllResults();
        $data['last_month_videos_views'] = $videos_views_model->where("date BETWEEN LAST_DAY(CURDATE() - INTERVAL 2 month) + INTERVAL 1 day AND LAST_DAY(CURDATE() - INTERVAL 1 month)")->countAllResults();
        $data['last_month_courses_views'] = $courses_views_model->where("date BETWEEN LAST_DAY(CURDATE() - INTERVAL 2 month) + INTERVAL 1 day AND LAST_DAY(CURDATE() - INTERVAL 1 month)")->countAllResults();
        $data['all_last_month_views'] = $data['last_month_classes_views'] + $data['last_month_exercises_views'] + $data['last_month_videos_views'] + $data['last_month_courses_views'];

        $data['this_year_classes_views'] = $classes_views_model->where("YEAR(date) = YEAR(NOW())")->countAllResults();
        $data['this_year_exercises_views'] = $exercises_views_model->where("YEAR(date) = YEAR(NOW())")->countAllResults();
        $data['this_year_videos_views'] = $videos_views_model->where("YEAR(date) = YEAR(NOW())")->countAllResults();
        $data['this_year_courses_views'] = $courses_views_model->where("YEAR(date) = YEAR(NOW())")->countAllResults();
        $data['all_this_year_views'] = $data['this_year_classes_views'] + $data['this_year_exercises_views'] + $data['this_year_videos_views'] + $data['this_year_courses_views'];
        
        $data['last_year_classes_views'] = $classes_views_model->where("YEAR(date) = YEAR(NOW()) - 1")->countAllResults();
        $data['last_year_exercises_views'] = $exercises_views_model->where("YEAR(date) = YEAR(NOW()) - 1")->countAllResults();
        $data['last_year_videos_views'] = $videos_views_model->where("YEAR(date) = YEAR(NOW()) - 1")->countAllResults();
        $data['last_year_courses_views'] = $courses_views_model->where("YEAR(date) = YEAR(NOW()) - 1")->countAllResults();
        $data['all_last_year_views'] = $data['last_year_classes_views'] + $data['last_year_exercises_views'] + $data['last_year_videos_views'] + $data['last_year_courses_views'];

        echo view('admin/analytics/index_view', $data);
    }
    public function hall_of_fame()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $classes_views_model = model('ClassesViewModel');
        $classes_model = model('ClassesModel');
        $collections_model = model('CollectionsModel');
        $liveevents_model = model('LiveEventsModel');
        $teachers_model = model('TeachersModel');
        $subscribers_model = model('SubscribersModel');
        // $stripe_model = model('StripeModel');
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['most_viewed_classes'] = $classes_model->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView
                                        FROM classes
                                        LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                        WHERE classes.deleted_at IS NULL
                                        ORDER BY countView desc
                                        LIMIT 0, 10
                                    ")->getResultArray();
        $data['most_viewed_exercises'] = $classes_model->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView
                                        FROM exercises
                                        LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                        WHERE exercises.deleted_at IS NULL
                                        ORDER BY countView desc
                                        LIMIT 0, 10
                                    ")->getResultArray();
        $data['most_viewed_videos'] = $classes_model->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView
                                        FROM howto
                                        LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = howto.id
                                        WHERE howto.deleted_at IS NULL
                                        ORDER BY countView desc
                                        LIMIT 0, 10
                                    ")->getResultArray();

        echo view('admin/analytics/hall-of-fame_view', $data);
    }
}