<?php namespace App\Models;

use CodeIgniter\Model;

class SurveysFinishedModel extends Model
{
    protected $table = 'surveys_finished';
	protected $allowedFields = ["user_id", "survey_id"];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        // 'title'     => 'required|min_length[2]'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];


	protected function prepare_data(array $data)
	{
		return $data;
	}

}