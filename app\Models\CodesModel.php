<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class CodesModel extends Model
{
    protected $table = 'discount_codes';
	protected $allowedFields = ['code', 'discount', 'products', 'redemption'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    // protected $useSoftDeletes = true;
    // protected $useTimestamps = true;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'code'     => 'required',
        'discount'        => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_codes($start = 0, $limit = 10, $search_term = NULL, $order = "discount_codes.created_at DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND discount_codes.code LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT discount_codes.*, subscribers.firstname, subscribers.lastname
                            FROM discount_codes
                            LEFT JOIN subscribers ON subscribers.id = discount_codes.user_id
                            WHERE discount_codes.deleted_at IS NULL
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($id){
        $data = $this->query("SELECT discount_codes.*, subscribers.firstname, subscribers.lastname
                            FROM discount_codes
                            LEFT JOIN subscribers ON subscribers.id = discount_codes.user_id
                            WHERE discount_codes.deleted_at IS NULL
                            AND discount_codes.id = " . $id .  "
                        ")->getRowArray();
        return $data;
    }

    public function check_code($code){
        $data = $this->query("SELECT *
                            FROM discount_codes
                            WHERE deleted_at IS NULL
                            AND code = '" . $code .  "'
                            AND (CURDATE() BETWEEN date_from AND date_to)
                        ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}
}