<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.ck.ck-editor__main > .ck-editor__editable {
	border-color: var(--ck-color-base-border);
	min-height: 200px;
}
#main_form h4,
#main_form h3.h4 {
	font-size: 18px !important;
}

</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content mb-100 pb-5">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Notification <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                <a href="admin/notifications" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/notifications/save" method="post" class="default_submit container border-bottom" id="main_form">
            <div class="row mb-2">
                <div class="col-8">
                    <h5 class="mb-4 f-14 semibold">NOTIFICATION TEXT</h5>
                    <h5 class="mb-1 f-11">DESCRIPTION *</h5>
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input" value="<?php echo isset($current['content']) ? $current['content'] : '' ?>" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="200" style="min-height: 275px" id="content" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">200</span></p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                <h5 class="mb-45 f-14 semibold top-border pt-45">NOTIFICATION DETAILS</h5>
                <h5 class="mb-1 f-11">NOTIFICATION LINK</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="link" class="line-input black" placeholder="Enter" value="<?php echo isset($current['link']) ? $current['link'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-25">
                <div class="col-12">
                <h5 class="mb-1 f-11">DATE PUBLISHED</h5>
                    <div class="row">
                        <div class="col-12">
                            <div class="input-container" id="date_container" data-date="<?php echo isset($current['date']) ? strtotime($current['date']) : ''; ?>">
                                <input type="text" class="line-input datetimepicker" placeholder="From"  name="date" value="<?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y H:i:s', strtotime($current['date'])) : date('m/d/Y H:i:s'); ?>" />
                                <span class="calendar-icon"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-45">
            <div class="row mb-25">
                <div class="col-12">
                <h5 class="mb-1 f-14 semibold">SUBSCRIBER</h5>
                <p class="midGray mb-4 f-12">(if notification is addressed to a subscriber)</p>
                <h5 class="mb-1 f-11">SUBSCRIBER</h5>
                    <div class="input-container">
                        <select class="line-input" name="subscriber_id">
                            <option value="">Select</option>
<?php
foreach($users as $single){
?>
                            <option value="<?php echo $single['id']; ?>" <?php echo (isset($current['subscriber_id']) AND $current['subscriber_id'] == $single['id']) ? 'SELECTED' : ''; ?>><?php echo $single['firstname']; ?> <?php echo $single['lastname']; ?></option>
<?php
}
?>
                        </select>
                    </div>
                </div>
            </div>
            <hr class="mt-25 mb-5">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="author" id="author" value="admin">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                            <a href="/admin/notifications" class="cancel-link ml-2" title="Cancel">Cancel</a>
                            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="notifications" data-popup="delete-popup" title="Cancel">DELETE NOTIFICATION</a>
                        </div>
                    <?php }else{ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white mr-2" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                            <!-- <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button> -->
                            <a href="/admin/notifications" class="cancel-link" title="Cancel">Cancel</a>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/ckeditor.js"></script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/app.js?v=54"></script>
<script>
let editor;
ClassicEditor
        .create(document.querySelector('#content'), { toolbar: { items: [ 'bold', 'italic', 'underline', '|' ] } })
        .then( newEditor => { editor = newEditor; })
        .catch(error => { console.error(error); });

$('html, body').on('click', function(){
    const editorData = editor.getData();
    $('#content').val(editorData);
    console.log(editorData);
});
</script>
</body>
</html>