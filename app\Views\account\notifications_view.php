<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white semibold text-uppercase pb-1 mb-05 mb-mob-0">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
    <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0 bottom-border">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                        NOTIFICATIONS
                        </h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">SYSTEM (NOTIFY ME WHEN…)</h3>
                <div class="col-12 notify-chekcbox border radius-10 radius-mob-6">
                    <!-- <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_class_notif" class="" id="new_class_notif" data-user-id="<?php // echo $logged_user['id']; ?>" <?php // echo $logged_user['new_class_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_class_notif">New class is added</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_od_class_notif" class="" id="new_od_class_notif" data-user-id="<?php // echo $logged_user['id']; ?>" <?php // echo $logged_user['new_od_class_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_od_class_notif">New on demand class is added</label>
                            </div>
                        </div>
                    </div> -->
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_collection_notif" class="" id="new_collection_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['new_collection_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_collection_notif">New collection is added</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_liveevents_notif" class="" id="new_liveevents_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['new_liveevents_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_liveevents_notif">New live event is scheduled</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_teacher_notif" class="" id="new_teacher_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['new_teacher_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_teacher_notif">New teacher joins</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-mob-0">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="new_staff_playlist_notif" class="" id="new_staff_playlist_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['new_staff_playlist_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="new_staff_playlist_notif">New playlist by staff is added</label>
                            </div>
                        </div>
                    </div>
                    <!--<hr class="my-6">
                    <h3 class="mb-3 f-14 bold text-uppercase line-height-small">EARNING PLATFORM (NOTIFY ME WHEN…)</h3>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="class_approved_notif" class="" id="class_approved_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['class_approved_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="class_approved_notif">My class is approved</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="class_rejected_notif" class="" id="class_rejected_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['class_rejected_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="class_rejected_notif">My class is rejected</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="class_bought_notif" class="" id="class_bought_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['class_bought_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="class_bought_notif">Someone bought my class</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-2">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="class_rated_notif" class="" id="class_rated_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['class_rated_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="class_rated_notif">Someone rates my class</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-25 mb-mob-0">
                        <div class="col-12">
                            <div class="checkbox notifications-checkbox">
                                <input type="checkbox" name="payout_request_notif" class="" id="payout_request_notif" data-user-id="<?php echo $logged_user['id']; ?>" <?php echo $logged_user['payout_request_notif'] == 1 ? 'checked' : ''; ?>>
                                <label for="payout_request_notif">My payout request is received</label>
                            </div>
                        </div>
                    </div>-->
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>