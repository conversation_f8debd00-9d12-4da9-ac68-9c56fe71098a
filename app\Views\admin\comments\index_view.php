<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 20px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}
.comments-form textarea {
	line-height: 1.8;
	padding: 25px 25px 5px;
	border: 1px solid #f0f0f0 !important;
	width: 100%;
}
.comments-list .checkbox.contact-forms label {
	position: absolute;
	top: 41px;
}
.comments-list .checkbox.contact-forms {
	width: 40px;
    min-width: 40px;
}
.ls-50 {
    letter-spacing: 0.05em;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb page-title">
                <h1 class="h3">Comments <?php echo session('super_admin') == 1 ? 'for approval' : ''; ?></h1>
                <?php if(session('super_admin') == 1){ ?>
                <a href="/admin/comments/all" class="btn black-bg white">All Comments</a>
                <?php } ?>
            </div>
            <hr class="mt-0 mb-25">
            <div class="flex aic jcsb">
            <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $comment_count == 1 ? $comment_count . ' Comment' : $comment_count . ' Comments'; ?></h5>
            <div class="flex aic jcsb">
            <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/xxxxxxxx/sort_by/xxxxxxxx.created_at/desc" class="link midGray" title="">Date Uploaded</a></li>
                        <li><a href="admin/xxxxxxxx/sort_by/xxxxxxxx.title/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/xxxxxxxx/sort_by/xxxxxxxx.title/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/xxxxxxxx/search" method="POST" class="search-form">
                        <input type="text" name="search_term" class="seach-input" value="">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
                </div>
            </div>
            <hr class="mt-3 mb-25">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple normalRed" data-table="comments" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>
            </div>
            <hr class="mt-25 mb-0">
            <div class="container px-0">
                <div class="col-12 px-0">
                    <div class="table comments-list">
<?php
foreach($all_comments as $single){
?>
                        <div class="table-row single-comment" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="<?php echo (isset($single['type']) AND $single['type'] != '') ? $single['type'] : 'classes'; ?>">
                            <div class="class-item flex aic <?php echo count($single['my_replies']) > 0 ? '' : 'bottom-border'; ?>">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <div class="single-comment-action">
                                    <p class="f-12 pr-5 mr-5 medium" style="margin-top: -5px"><?php echo $single['message']; ?></p>
                                    <div class="flex">
                                        <span class="midGray f-1">Commented on: <?php $class = class_info($single['class_id']); ?><a href="/classes/<?php echo $class['slug']; ?>" class="normal" target="_blank"><?php echo $class['title']; ?></a></span>
                                        <span class="midGray f-1"><?php echo (isset($single['user_name']) AND $single['user_name'] != '') ? ', by: ' . $single['user_name'] : 'NO NAME'; ?></span>
                                        <span class="midGray f-1 "><?php echo (isset($single['date']) AND $single['date'] != '') ? ', ' . date('m/d/Y H:i:s', strtotime($single['date'])) : ''; ?></span>
                                    </div>
                                    <a href="javascript:;" class="link link-black black f-12 text-underline" data-class_id="<?php echo (isset($single['class_id']) AND $single['class_id'] != '') ? $single['class_id'] : ''; ?>" onclick="insert_form($(this))">Reply</a>
                                </div>
                                <div class="flex ml-auto text-right f-1 pr-2">                                    
                                    <?php if(session('super_admin') == 1){ ?>
                                    <a href="javascript:;" class="btn btn-xs red-bg white f-10 ml-1" onclick="approve(<?php echo $single['id']; ?>, <?php echo $single['parent']; ?>)">APPROVE</a>
                                    <?php } ?>
                                </div>
                            </div>
                        <?php
                        if(count($single['my_replies']) > 0){
                            foreach($single['my_replies'] as $reply) {
                        ?>
                            <div class="class-item flex aic bottom-border">
                                <!-- <div class="checkbox contact-forms">

                                </div> -->
                                <div class="single-comment-action">
                                    <p class="f-12 pr-5 mr-5 medium" style="margin-top: -5px"><?php echo $reply['teacher_replied'] == session('admin') ? 'My reply: ' : '' ?><?php echo $reply['message']; ?></p>
                                    <span class="midGray mr-1 f-1">
                                        by: <?php echo (isset($single['user_name']) AND $single['user_name'] != '') ? $single['user_name'] : 'NO NAME'; ?>
                                    </span>
                                    <span class="midGray mr-1 f-1 "><?php echo (isset($single['date']) AND $single['date'] != '') ? date('m/d/Y H:i:s', strtotime($single['date'])) : ''; ?></span>
                                </div>
                            </div>

                        <?php
                            }
                        }
                        ?>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <!-- <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php //echo (($page * session('comment_per_page')) - session('comment_per_page')) + ($comment_count > 0 ? 1 : 0); ?>-<?php //echo (($page * session('comment_per_page')) - session('comment_per_page')) + count($all_comment); ?><span class="midGray mx-1">of <?php // echo $comment_count; ?></span>

                    <a href="admin/comment/<?php // echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php // echo $page > 1 ? $page - 1 : 1; ?><?php // echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 pl-1 pr-05 <?php // echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>

                    <a href="admin/comment/<?php // echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php // echo $page + 1; ?><?php // echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 px-1 <?php // echo ((count($all_comment) < session('comment_per_page')) OR (((($page * session('comment_per_page')) - session('comment_per_page')) + count($all_comment)) == $comment_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php // echo session('comment_per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div> -->
        </div>
    </div>
</main>

<template id="reply-form-template">
    <form action="admin/comments/save" method="POST" class="comments-form mt-2" style="max-width: 600px;width: 100%;">
        <div class="textarea">
            <textarea name="comment" id="comment" class="comments-field f-12" placeholder="Your reply..."></textarea>
        </div>
        <div class="form-options">
            <div class="flex aic jcr">
                <div class="buttons mt-3 flex">
                    <input type="hidden" name="type" value="classes">
                    <!-- <input type="hidden" name="class_title" value="Full Body #5 by Sebastien">
                    <input type="hidden" name="teacher_name" value="Carinaa Nesto">
                    <input type="hidden" name="teacher_email" value="<EMAIL>"> -->
                    <button type="button" class="link link-midGray midGray f-12 f-10-mob semibold no-underline ls-50" style="background: #fff !important" onclick="$(this).closest('form').remove()">Cancel</button>
                    <button type="button" onclick="submit_reply($(this).closest('form'))" class="link link-black black f-12 f-10-mob semibold ml-2 no-underline ls-50" style="background: #fff !important">Send</button>
                </div>
            </div>
        </div>
    </form>
</template>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/textarea.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('comment_per_page') == "") ? 25 : session('comment_per_page'); ?>;
var sort_by = '<?php echo (session('comment_sort') == "") ? 'comment.created_at/desc' : session('comment_sort'); ?>';
var search = '<?php echo (session('comment_search') == "") ? '' : session('comment_search'); ?>';
var order = '<?php echo (session('comment_sort') == "") ? 'comment.created_at/desc' : session('comment_sort'); ?>';

function approve(id, parent){
    $.ajax({
        type: "POST",
        url: 'admin/comments/approve',
        data: {
            id,
            parent,
            status: 0
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Comment approved', 'success');
                setTimeout(function(){
                    window.location.reload();
                }, 1000);
            }else{
                console.log('NO SUCCESS');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
        }
    });
};

/* COMMENTS FORM */
var class_id = 0;
var user_id = <?php echo session('admin'); ?>;
var teacher_replied = <?php echo session('admin'); ?>;
const date = "2024-01-26";

var form_template = $('#reply-form-template').html();
function insert_form(xx){
    class_id = xx.data('class_id');
    console.log(xx.closest('.single-comment-action').find('form').length);
    if(xx.closest('.single-comment-action').find('form').length == 0){
        $('.comments-form').remove();
        xx.parent().append(form_template);
    }
    autosize(document.querySelectorAll('textarea'));
    $('textarea').on('keyup', function(){
        if($(this).height() > 40){
            $(this).css({"padding-bottom":"25px"});
        }else{
            $(this).css({"padding-bottom":"8px"});
        }
    });
}
function submit_reply(form){
	console.log('comments-form-ajax submit');
	// e.preventDefault();
	// var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    var parent = form.closest('.single-comment').data('id');
    var message = form.find('.comments-field').val();
    var status = 0;
    // var class_title = form.find('[name="class_title"]').val();
    // var teacher_name = form.find('[name="teacher_name"]').val();
    // var teacher_email = form.find('[name="teacher_email"]').val();
    var type = form.closest('.single-comment').data('type');
    var comment_id = parent;
    
    button.addClass('btn--loading');

    console.log("class_id: " + class_id);
    console.log("parent: " + parent);
    console.log("message: " + message);
    console.log("user_id: " + user_id);

    $.ajax({
        type: "POST",
        url: url,
        data: {
            comment_id,
            class_id,
            parent,
            // class_title,
            // teacher_name,
            // teacher_email,
            message,
            status,
            type,
            teacher_replied,
            user_id
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Your comment is submited', 'success', 2500);
                button.removeClass('btn--loading');
                // form.closest('.single-comment').fadeOut(200);
                form.remove();
                setTimeout(function(){
                    window.location.reload();
                }, 150);
            }else{
                console.log('NO SUCCESS');
                app_msg("Something went wrong", 'danger', 2500);
                // $.each(data.json, function(key, val){
                //     $('.contact-form [name=' + key + "]").addClass('error');
                // });
                button.removeClass('btn--loading');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger', 2500, 1);
            button.removeClass('btn--loading');
        }
    });
};
/* COMMENTS FORM */
</script>

</body>
</html>