<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Howto extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('HowtoModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['howto_sort'] = $this->howto_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('howto', $search_term);
            if($search_term == ""){
                $this->set_search('howto', "0");
                return redirect()->to('/admin/howto');
            }
        }else if(session('howto_search') !== "" AND session('howto_search') !== "0"){
            $search_term = session('howto_search');
        }else{
            $search_term = "0";
        };
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('howto_sort'));
        $data['all_howto'] = $this->model->all_howto(0, session('howto_per_page'), $search_term, session('howto_sort'));
        $data['howto_count'] = count($this->model->all_howto(0, 0, $search_term, session('howto_sort'), "0,1"));
        $data['page'] = 1;

        echo view('admin/howto/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['howto_sort'] = $this->howto_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

       if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('howto', $search_term);
            if($search_term == ""){
                $this->set_search('howto', "0");
                return redirect()->to('/admin/howto/page/' . $page);
            }
        }else if(session('howto_search') !== "" AND session('howto_search') !== "0"){
            $search_term = session('howto_search');
        }else{
            $search_term = "0";
        };
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('howto_sort'));
        $data['all_howto'] = $this->model->all_howto(($page * session('howto_per_page')) - session('howto_per_page'), session('howto_per_page'), $search_term, session('howto_sort'));
        $data['howto_count'] = count($this->model->all_howto(0, 0, $search_term, session('howto_sort'), "0,1"));
        $data['page'] = $page;

        echo view('admin/howto/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $exercises_model = model('ExercisesModel');
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
		$data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();
		$current_machines = $db->query("SELECT * FROM howto_machine WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['class_machine'] ;
            }
        }else{
            $data['current_machines'] = array();
        }
		$current_body_parts = $db->query("SELECT * FROM howto_body_parts WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['class_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = array();
        }
		$current_accessories = $db->query("SELECT * FROM howto_accessories WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['class_accessories'] ;
            }
        }else{
            $data['current_accessories'] = array();
        }
		$current_tensions = $db->query("SELECT * FROM howto_tensions WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_tensions)){
            foreach($current_tensions as $k => $single){
                $data['current_tensions'][] = $single['class_tensions'];
            }
        }else{
            $data['current_tensions'] = array();
        }
		$current_springs = $db->query("SELECT * FROM howto_springs WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['class_springs'] ;
            }
        }else{
            $data['current_springs'] = array();
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/howto');
        };

        $data['all_howto'] = $this->model->all_howto(0, 0, session('howto_search'), session('howto_sort'));
        $data['all_exercises'] = $exercises_model->all_exercises(0,0);
        $data['selected_exercises_for_selection'] = $this->model->exercises_for_videos($edit_id);

        $total = count($data['all_howto']);
        $index_list = array_column($data['all_howto'], 'id');
        $index_id = array_search($edit_id, array_column($data['all_howto'], 'id'));
        if($index_id !== FALSE)
        {
            if($index_id < $total - 1){
                $data['next'] = $this->model->where('id', $index_list[$index_id + 1])->first();
            }else{
                $data['next'] = $this->model->where('id', $index_list[0])->first();
            }
            if($index_id > 0){
                $data['prev'] = $this->model->where('id', $index_list[$index_id - 1])->first();
            }else{
                $data['prev'] = $this->model->where('id', $index_list[$total - 1])->first();
            }
        }

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/howto/edit_view', $data);
    }

    public function edit_bulk()
    {
        $data = $this->request->getPost();

        // echo '<pre>';
        // print_r(explode(',', ($data['ids'])));
        // echo '</pre>';
        // die();
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
        $data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
        $data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
        $data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
        $data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();

        $ids = explode(',', ($data['ids']));
        foreach($ids as $key => $id){
            $data['current_howto'][] = $this->model->where(['id' => $id])->first();
            $current_machines = $db->query("SELECT * FROM howto_machine WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_machines)){
                foreach($current_machines as $k => $single){
                    $data['current_machines'][$id][] = $single['class_machine'] ;
                }
            }else{
                $data['current_machines'][$id] = 0;
            }
            $current_body_parts = $db->query("SELECT * FROM howto_body_parts WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_body_parts)){
                foreach($current_body_parts as $k => $single){
                    $data['current_body_parts'][$id][] = $single['class_body_parts'] ;
                }
            }else{
                $data['current_body_parts'][$id] = 0;
            }
            $current_accessories = $db->query("SELECT * FROM howto_accessories WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_accessories)){
                foreach($current_accessories as $k => $single){
                    $data['current_accessories'][$id][] = $single['class_accessories'] ;
                }
            }else{
                $data['current_accessories'][$id] = 0;
            }
            $current_tensions = $db->query("SELECT * FROM howto_tensions WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_tensions)){
                foreach($current_tensions as $k => $single){
                    $data['current_tensions'][$id][] = $single['class_tensions'] ;
                }
            }else{
                $data['current_tensions'][$id] = 0;
            }
            $current_springs = $db->query("SELECT * FROM howto_springs WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_springs)){
                foreach($current_springs as $k => $single){
                    $data['current_springs'][$id][] = $single['class_springs'] ;
                }
            }else{
                $data['current_springs'][$id] = 0;
            }
        }

		// echo '<pre>';
		// print_r($data);
		// die();

		return view('admin/howto/multi_edit_view', $data);
    }
    public function clear_search()
    {
        $this->set_search('howto', "0");
		return redirect('admin/howto');
    }

    public function multi()
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

		return view('admin/howto/multi_view', $data);
    }

    public function save_batch()
    {
		$data = $this->request->getPost();
        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('howto');
                foreach($single_field as $k => $v){
                    $fields[$k][$key] = $v;
                }
            }
        }
        if (count($fields) > 0){
            $builder->insertBatch($fields);
        }
		return redirect('admin/howto');
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/howto', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/howto/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/howto/' . $name, 98);
				$data['image'] = 'uploads/howto/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);
			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            !isset($data['tensions']) ? $data['tensions'] = [] : '';
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('howto_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function save_exercises_in_class()
    {
        $HowtoExercisesModel = model('HowtoExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        // echo '<pre>';
		// var_dump($data['class_selected_exercises']);
		// die();

        $result['success'] = $HowtoExercisesModel->save($data);
        $result['csc_id'] = $HowtoExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['class_selected_exercises']);
        }

		return $this->respond($result);
    }

    public function sort_classes_table()
    {
        $HowtoExercisesModel = model('HowtoExercisesModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                // if($single['type'] == 'classes'){
				//     $PlaylistClassesModel->save($single);
                // }
                // if($single['type'] == 'videos'){
				//     $PlaylistHowtoModel->save($single);
                // }
                // if($single['type'] == 'exercises'){
				    $HowtoExercisesModel->save($single);
                // }
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

}