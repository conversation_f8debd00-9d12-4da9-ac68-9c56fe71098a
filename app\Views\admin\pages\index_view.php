<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 pt-85 border-bottom">
            <div class="flex aic jcsb minH45">
                <h1 class="h3 mb-05">Pages</h1>
                <a href="admin/pages/edit" class="btn black-bg white mb-05" title="Upload Class">New Page</a>
            </div>
            <hr class="mt-80 mb-4">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $pages_count == 1 ? $pages_count . ' Page' : $pages_count . ' Pages'; ?></h5>
                <div class="search-container">
                    <form action="admin/pages/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            <hr class="my-4">
            <div class="flex aic jcsb">
                <div class="flex aic">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-14 link link-red red red flex aic delete_multiple" data-table="pages" data-popup="delete-popup"><img src="admin_assets_new/images/trash.svg" alt="" class="img-fluid mr-05" />Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <form class="edit_checked" style="display: none" method="post" action="admin/classes/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-14 link link-red red flex aic white-bg edit_bulk">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/pages/sort_by/pages.created_at/desc" class="link link-darkGray darkGray" title="">Date Added</a></li>
                        <li><a href="admin/pages/sort_by/pages.title/asc" class="link link-darkGray darkGray" title="">Ascending</a></li>
                        <li><a href="admin/pages/sort_by/pages.title/desc" class="link link-darkGray darkGray" title="">Descending</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row big-gap">
                <div class="col-12 mb-2">
                    <div class="table sortable-pages">
<?php
$c=0;
foreach($all_pages as $single){
$c++;
$classes = (isset($single['selected_classes']) AND $single['selected_classes'] != '') ? json_decode($single['selected_classes']) : FALSE;
?>
                        <div class="table-row" data-rowid="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic mb-3">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/pages/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : '/images/by-lod.svg'; ?>" alt="" class="img-fluid" style="max-width: 120px;max-height: 120px;width: 120px;object-fit: cover;height: 120px;" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/pages/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title bold mb-05"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-1"><?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] + ((isset($single['howtoCount']) AND $single['howtoCount'] != '') ? $single['howtoCount'] : 0) : 0; ?> classes</span>
                                    <span class="midGray mb-1 f-1">by: <?php echo (isset($single['users_name']) AND $single['users_name'] != '') ? $single['users_name'] : 'LagreeOD staff'; ?></span>
                                    <div class="row-actions f-1">
                                        <a href="admin/pages/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="pages">Delete</a>-->
                                    </div>
                                </div>
                                <div class="flex aic jcr ml-auto pr-2 table-options">
                                    <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle"></span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($pages_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_pages); ?><span class="midGray mx-1">of <?php echo $pages_count; ?></span>
                    <a href="admin/pages/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/pages/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_pages) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_pages)) == $pages_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
if($('.sortable-pages').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".table").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var pom = {
                    id: section_id,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/pages/sort_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}

</script>
</body>
</html>