<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Collections extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CollectionsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_collections'] = $this->model->all_collections(0, session('per_page'));
        $data['collections_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/collections/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['all_collections'] = $this->model->all_collections(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['collections_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/collections/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_collections'] = $this->model->all_collections(0, 9, $data['search_term']);
        $data['collections_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/collections/index_view', $data);
    }

    public function sort_by($type = 'collections.title', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $teachers_model = model('TeachersModel');
		// $data = $this->request->getPost();
        $data['all_collections'] = $this->model->query("SELECT collections.*
                                        FROM collections
                                        WHERE collections.deleted_at IS NULL
                                        ORDER BY " . $type. " " . $direction . "")->getResultArray();
        $data['collections_count'] = count($data['all_collections']);
        $types = array(
            "collections.created_atdesc" => "Date Added",
            "collections.titleasc" => "Ascending",
            "collections.titledesc" => "Descending"
        );
        $data['all_teachers'] = $teachers_model->findAll();
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/collections/index_view', $data);
    }


    public function datatable()
    {
		$post = $this->request->getPost();
		$draw = $post['draw'];
		$row = $post['start'];

		$rowperpage = $post['length']; // Rows display per page
		$columnIndex = $post['order'][0]['column']; // Column index
		$columnName = $post['columns'][$columnIndex]['data']; // Column name
		$columnSortOrder = $post['order'][0]['dir']; // asc or desc
		$searchValue = $post['search']['value']; // Search value


		$db      = \Config\Database::connect();
		$sql = "SELECT
			collections.*,
			IF(collections.deleted_at IS NULL, 0, 1) as deleted
			FROM collections
			WHERE 1 ";
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecords = count($data);

		if (session()->has('filter_table'))
		{
			$filter = session()->filter_table;
			if(isset($filter['vreme']) AND $filter['vreme'] <> ''){
				$sql .= " AND users.created_at > '" . $filter['vreme'] . "'";
			}
			if(isset($filter['vrsta_potvrde']) AND $filter['vrsta_potvrde'] <> ''){
				$sql .= " AND users.vrsta_potvrde = '" . $filter['vrsta_potvrde'] . "'";
			}
		}
		if($searchValue != ''){
			$sql .= " AND (
				collections.id LIKE '%" . $searchValue . "%'
				OR collections.title LIKE '%" . $searchValue . "%'
				) ";
		}
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecordwithFilter = count($data);
		$sql .= " ORDER BY " . $columnName . " " . $columnSortOrder;
		$sql .= " LIMIT " . $row . "," . $rowperpage;
		$query = $db->query($sql);
		$data_final = $query->getResult();
		$response = array(
		  "mrnj" => $sql,
		  "draw" => intval($draw),
		  "iTotalRecords" => $totalRecords,
		  "iTotalDisplayRecords" => $totalRecordwithFilter,
		  "aaData" => $data_final
		);
		echo json_encode($response, JSON_PRETTY_PRINT);
    }

    public function edit($edit_id = 0)
    {
        $classes_model = model('ClassesModel');
        $howto_model = model('HowtoModel');
        $teachers_model = model('TeachersModel');
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/collections');
        }

        $data['teachers'] = $teachers_model->findAll();
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();

        $current_machines = $db->query("SELECT * FROM collections_machine WHERE collections_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['collection_machine'] ;
            }
        }else{
            $data['current_machines'] = 0;
        }

        $data['all_classes'] = $classes_model->all_classes(0,0);
        $data['all_howto'] = $howto_model->all_howto(0,0);
        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/collections');
        };

        $data['selected_classes_for_selection'] = $this->model->classes_for_collection($edit_id);
        $data['selected_howto_for_selection'] = $this->model->howto_for_collection($edit_id);

        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('admin/collections/edit_view', $data);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Data successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/collections', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/collections/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/collections/' . $name, 98);
				$data['image'] = 'uploads/collections/' . $name;
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/collections', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/collections/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/collections/' . $name, 98);
				$data['cover_image'] = 'uploads/collections/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/collections', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/collections/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/collections/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/collections/' . $name;
			}
            if(isset($data['image_removed']) AND $data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if(isset($data['cover_image_removed']) AND $data['cover_image_removed'] == 1){
                $data['cover_image'] = "";
            }
            unset($data['cover_image_removed']);

            if(isset($data['mob_cover_image']) AND $data['mob_cover_image'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('collections_' . $key);
                    $builder->delete(['collections_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'collections_id' => $response['inserted_id'],
                            'collection_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function sort_classes_table()
    {
        $CollectionClassesModel = model('CollectionClassesModel');
        $CollectionHowtoModel = model('CollectionHowtoModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                if($single['type'] == 'classes'){
				    $CollectionClassesModel->save($single);
                }
                if($single['type'] == 'videos'){
				    $CollectionHowtoModel->save($single);
                }
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function save_videos_in_collection()
    {
        $NotificationsModel = model('NotificationsModel');
        $CollectionHowtoModel = model('CollectionHowtoModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['classes_count_before_add'] = count($this->model->classes_for_collection($data['collections_id']));

        $result['success'] = $CollectionHowtoModel->save($data);
        $result['csc_id'] = $CollectionHowtoModel->getInsertID();
        $result['type'] = 'videos';

        if($result['success']){
            $result['class'] = $this->get_howto_info($data['collection_selected_howto']);
            if($result['classes_count_before_add'] == 0){
                $collection = $this->model->where(['id' => $data['collections_id']])->first();
                $notification_data = [
                    'content'   => '<span class="text-underline">' . $collection['title'] . '</span> collection is added.',
                    'link'      => base_url() . '/collections/' . $collection['slug'],
                    'author'    => 'system',
                    'type' => 'new_collection_notif',
                    'date'    => date('Y-m-d H:i:s')
                ];
                $result['notification_saved'] = $NotificationsModel->save($notification_data);
            }
        }

		return $this->respond($result);
    }

    public function save_class_in_collection()
    {
        $NotificationsModel = model('NotificationsModel');
        $CollectionClassesModel = model('CollectionClassesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['classes_count_before_add'] = count($this->model->classes_for_collection($data['collections_id']));

        $result['success'] = $CollectionClassesModel->save($data);
        $result['csc_id'] = $CollectionClassesModel->getInsertID();
        $result['type'] = 'classes';

        if($result['success']){
            $result['class'] = $this->get_class_info($data['collection_selected_classes']);
            if($result['classes_count_before_add'] == 0){
                $collection = $this->model->where(['id' => $data['collections_id']])->first();
                $notification_data = [
                    'content'   => '<span class="text-underline">' . $collection['title'] . '</span> collection is added.',
                    'link'      => base_url() . '/collections/' . $collection['slug'],
                    'author'    => 'system',
                    'type'      => 'new_collection_notif',
                    'date'      => date('Y-m-d H:i:s')
                ];
                $result['notification_saved'] = $NotificationsModel->save($notification_data);
            }
        }

		return $this->respond($result);
    }

    public function delete_record($record_id = 0, $ajax = FALSE)
    {
		if ($record_id > 0)
		{
			$response['success'] = $this->model->delete($record_id);
		}
		return redirect()->to(site_url('admin/collections'));
    }
}