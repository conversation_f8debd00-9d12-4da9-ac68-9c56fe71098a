<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="subscribe-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="py-0 mbsec px-2">
        <div class="container1080 mb-05">
            <div class="row">
                <div class="col-12 subscribe-title">
                    <h1 class="semibold m-0 f-24 line-height-small">SUBSCRIBE</h1>
                </div>
            </div>
            <form id="register_subscribe" action="register/validate_subscribe_logged" class="row nowrap flex" autocomplete="off">
                <input type="hidden" name="stripe_customer" value="<?php echo (isset($logged_user)) ? $logged_user['stripe_customer'] : '' ?>">
                <div class="col pr-4 pr-mob-1 max500 order-mob-2">
                    <div class="subscribe-image-field">
                        <img src="images/subscribe-top-image.jpg" alt="" class="img-fluid" />
                        <div class="lightGray-bg subscribe-whats-box">
                            <h2 class="f-14 semibold text-uppercase line-height-normal mb-3">WHAT'S INSIDE:</h2>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Hundreds of classes right at your fingertips</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Stream from any device, anywhere, anytime</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Live events & Q&A sessions</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Powerful Collections</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Custom playlists & playlists created by staff</p>
                            <p class="f-12 flex aic line-height-normal mb-2"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Opportunity to Ask Sebastien a question</p>
                            <p><a href="/what-is-lod" class="link link-black black f-12 line-height-small text-underline" title="See the full list of features">See the full list of features</a></p>
                        </div>
                    </div>
                </div>
                <div class="col pl-4 pr-0 w100 px-mob-1 mb-mob-3 order-mob-1">
                    <div class="panel big-padding mb-05 border for--loading">
                        <div class="w100">
                            <h4 class="line-height-small f-14 mb-5">CHOOSE YOUR PLAN</h4>
                            <div class="panel mb-2 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'selected' : ''; ?>">
                                <div class="radio-button f-14 rtl w100">
                                    <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe3" data-plan="Weekly" data-price="3.99" data-unit="" value="Weekly Subscription">
                                    <label for="subscribe3" class="flex aic f-12 py-2 px-3 px-mob-2 mr-3 mr-mob-1">
                                        <span class="flex flex-column line-height-small">
                                        <p class="medium line-height-small" style="margin-bottom:7px;">WEEKLY</p>
                                            $3.99/week
                                        </span>
                                    </label>
                                </div>
                            </div>
                            <div class="panel mb-2 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'selected' : ''; ?>">
                                <div class="radio-button f-14 rtl w100">
                                    <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe1" data-plan="Monthly" data-price="9.99" data-unit="/mo" value="Monthly Subscription">
                                    <label for="subscribe1" class="flex aic f-12 py-2 px-3 px-mob-2 mr-3 mr-mob-1">
                                        <span class="flex flex-column line-height-small">
                                        <p class="medium line-height-small" style="margin-bottom:7px;">MONTHLY</p>
                                            $9.99/month
                                        </span>
                                        <!--<span class="f-10 bold ml-auto mr-2 mr-mob-3">MOST POPULAR</span>-->
                                    </label>
                                </div>
                            </div>
                            <div class="panel mb-0 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'selected' : ''; ?>">
                                <div class="radio-button f-14 rtl w100">
                                    <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe2" data-plan="Annual" data-price="99.99" data-unit="" value="Annual Subscription">
                                    <label for="subscribe2" class="flex aic f-12 py-2 px-3 px-mob-2 mr-3 mr-mob-1">
                                        <span class="flex flex-column line-height-small mr-1">
                                            <p class="medium line-height-small" style="margin-bottom:7px;">ANNUALLY</p>
                                            <span class="line-height-small annual-price" style="white-space: nowrap">$99.99/year</span>
                                        </span>
                                        <span class="f-10 line-height-small ml-auto mr-2 mr-mob-3 medium">
                                            <span class="best-value">Save 20%</span>
                                            <!-- <span class="desktop-inline best-value">-</span>  -->
                                            <!--<span class="line-height-small annual-price-per-month">$8.33/mo</span>-->
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="id" id="user_id" value="<?php echo $logged_user['id']; ?>" />
                        <input type="hidden" name="firstname" id="subscribe_firstname" value="<?php echo $logged_user['firstname']; ?>" />
                        <input type="hidden" name="lastname" id="subscribe_lastname" value="<?php echo $logged_user['lastname']; ?>" />
                        <?php if($logged_user['email'] == ''){ ?>
                        <div class="col-12 px-0">
                            <div class="input-container">
                                <input type="text" name="email" class="line-input" id="subscribe_email" placeholder="Email">
                                <span class="input-label">Email</span>
                                <span id="Email_error" class="input-error"></span>
                            </div>
                        </div>
                        <?php }else{ ?>
                        <input type="hidden" name="email" id="subscribe_email" value="<?php echo $logged_user['email']; ?>" />
                        <?php } ?>
                        <div class="row w100 my-5">
                            <div class="col-12 px-0">
                                <h4 class="line-height-small f-14">Payment Details</h4>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 flex">
                                <div class="input-container">
                                    <input type="text" name="card[name]" class="line-input" id="name" placeholder="Name on card">
                                    <span class="input-label">Name on card</span>
                                    <span id="name_error" class="input-error"></span>
                                </div>
                            </div>
                            <div class="col-12 flex">
                                <div class="input-container mb-0">
                                    <input type="text" name="card[number]" class="line-input" id="card-number" maxlength="19" placeholder="Card number" onkeypress="return onlyNumberKey(event)">
                                    <span class="input-label">Card number</span>
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_month]" class="line-input card_month" id="card-month-year" placeholder="MM" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <span class="input-label">MM</span>
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 50px;" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[exp_year]" class="line-input card_year" id="card-month-year" placeholder="YY" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                    <span class="input-label">YY</span>
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                                <div style="width: 60px" class="input-container ml-auto mb-0">
                                    <input type="text" name="card[cvc]" class="line-input text-right" id="card-cvc" placeholder="CVC" maxlength="3" onkeypress="return onlyNumberKey(event)">
                                    <span class="input-label">CVC</span>
                                    <span id="card-number_error" class="input-error"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row w100 mt-5 mb-2 mt-mob-0 coupon-form ">
                            <div class="col-12 px-0">
                                <h4 class="f-14 flex aic jcsb line-height-small mb-6 mt-mob-5 mb-mob-5">
                                    <span class="mr-05 line-height-small">Have a coupon?</span>
                                </h4>
                                <div class="code-container">
                                    <div class="f-18 flex aic jcsb">
                                        <input type="text" name="coupon" class="line-input subscribe-code" placeholder="Enter code">
                                        <a href="javascript:;" class="check-code check-code-buttom">Apply</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-left w100 f-12" style="margin-top: -20px;">
                            <span style="display: none;" class="valid_coupon">Applied Coupon: <span class="coupon_message semibold"></span> <a href="javascript:;" class="link link-black black text-underline remove_coupon" title="Remove">Remove</a></span>
                            <span style="display: none;" class="not_valid_coupon">Coupon is invalid!</span>
                        </div>
                        <div class="row w100 mt-4 mb-4 mt-mob-5 mb-mob-5">
                            <div class="col-12 px-0">
                                <h4 class="f-14 flex aic jcsb line-height-small">
                                    <span class="line-height-small">
                                        <span class="subscription-type mr-05 line-height-small">Please select</span>
                                        Subscription
                                    </span>
                                    <span class="ml-auto line-height-small">
                                        <b class="line-height-small">
                                            $<span class="subscription-price line-height-small">0</span><sup class="subscription-unit">/mo</sup>
                                        </b>
                                    </span>
                                </h4>
                            </div>
                        </div>
                        <hr class="mt-0 mb-4">
                        <div class="row">
                            <div class="col-12 midGray">
                            <p class="lh-20 f-10">By clicking below, you agree to our <a class="midGray lh-20" href="/privacy-policy" target="_blank"><u class="lh-20">Privacy Policy</u></a>, and automatic renewal. You authorize this site, appearing as OTT* MAXIMUM FITNESS, to hold and charge your card automatically based on your subscription plan. Cancel any time in your account settings.</p>
                            </div>
                        </div>
                        <hr class="mt-4 mb-5">
                        <div class="row w100">
                            <div class="col-12 flex aic jcc px-0">
                                <button type="submit" class="btn f-14 black-bg white w100"><?php echo (isset($logged_user['stripe_subscription']) AND $logged_user['stripe_subscription'] != NULL) ? 'Change Your Subscription' : 'Start Your Subscription'; ?></button>
                            </div>
                        </div>
                        <!-- <hr class="my-5">
                        <div class="row w100">
                            <div class="col-12 flex aic jcc">
                                <a href="/earning-platform" class="btn black-bg white" title="START SELLING YOUR CLASSES">START SELLING YOUR CLASSES</a>
                            </div>
                        </div> -->
                    </div>
                </div>
            </form>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/subscribe.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script> -->
<!-- <script src="js/google_login.js"></script> -->
<script>
console.log('LOGGED USER SUBSCRIBE TEMPLATE');
$('.check-code').on('click', function(){
    var code = $('.subscribe-code').val();
    var button = $(this);
    button.addClass('btn--loading');
    if(code == ''){
        app_msg('Please enter coupon code!');
        setTimeout(function(){
            button.removeClass('btn--loading');
        },1200);
    }else{
        $.ajax({
            type: 'POST',
            url: 'register/check_coupon',
            data: {
                code: code
            },
            success: function (data) {
                console.log(data);
                if(data.success){
                    if(data.valid.valid){
                        $('.coupon-form').addClass('disabled');
                        $('.valid_coupon').show().find('.coupon_message').text(data.valid.name);
                        $('.not_valid_coupon').hide();
                        button.removeClass('btn--loading');
                    }else{
                        setTimeout(function(){
                            $('.subscribe-code').val('');
                            button.removeClass('btn--loading');
                        },2000);
                        $('.coupon-form').removeClass('disabled');
                        $('.valid_coupon').hide();
                        $('.not_valid_coupon').show();
                    }
                    button.removeClass('btn--loading');
                }else{
                    console.log(data.message);
                    $('.not_valid_coupon').show();
                    $('.valid_coupon').hide();
                    setTimeout(function(){
                        $('.subscribe-code').val('');
                        button.removeClass('btn--loading');
                    },2000);
                }
            },
            error: function (request, status, error) {
                alert('Error');
                button.removeClass('btn--loading');
            }
        });
    }
});
$('.remove_coupon').on('click', function(){
    $('.subscribe-code').val('');
    $('.coupon-form').removeClass('disabled');
    $('.valid_coupon').hide();
    $('.not_valid_coupon').hide();
});
$('.subscription-option').on('click', function(){
    $('.subscription-option').removeClass('selected');
    $(this).addClass('selected');
});
$(document).ready(function(){
    $('#subscribe3').trigger('click').change();
});
</script>
</body>
</html>