<?php
foreach($all_exercises as $single){
    if($single['status'] == 0){
?>
                            <div class="ajax-class exercises-class routineadded" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                                <span class="video_preview_tooltip">
                                    <video loop muted playsinline data-src="<?php echo $single['video_preview']; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                                </span>
                                <span class="pr-2 flex ail jcc flex-column text-part">
                                    <span class="f-12" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500;margin-bottom: 1px;"><span class="title_to_rename"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span><?php echo (isset($single['aka']) AND $single['aka'] != '') ? " (" . $single['aka'] . ")" : ''; ?></span>
                                    <!--<span style="color: #969696;font-size: 12px;display: inline-block;line-height: 20px;font-weight: 400;">
                                        <?php // echo (isset($single['all_exercise_machines']) AND $single['all_exercise_machines'] != '') ? $single['all_exercise_machines'] : ''; ?><?php // echo (isset($single['diff']) AND $single['diff'] != '') ? ', ' . $single['diff'] : ''; ?>
                                        <?php // echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? ', ' . duration_standard($single['custom_duration']) : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''); ?>                                        
                                    </span>-->
                                    <?php if(session('super_admin') == 1){ ?>
                                    <span class="link link-midGray midGray normal f-12 mr-2" onclick="$('.exercise_old_title').text($(this).closest('.ajax-class').find('.title_to_rename').text());$('#rename_id').val('<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>');$('#exercise_title').val('')" data-popup="rename-popup">Rename</span>
                                    <?php } ?>
                                </span>
                                <div class="flex aic jcsb ml-auto pr-2 add-part" style="max-width: 120px">
                                    
                                    <span class="btn btn-xs red-bg white f-1 ml-auto" data-popup="add-routine-exercise-popup" onclick="add_new_duration_popup($(this), <?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" style="<?php if(session('super_admin') == 1){ ?>margin-left: 0 !important;<?php } ?>right: auto !important;position: relative !important;">+</span>
                                </div>
                            </div>
<?php
    }
}
?>
