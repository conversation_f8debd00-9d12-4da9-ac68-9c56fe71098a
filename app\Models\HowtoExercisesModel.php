<?php namespace App\Models;

use CodeIgniter\Model;

class HowtoExercisesModel extends Model
{
    protected $table = 'howto_selected_exercises';
	protected $allowedFields = ['class_id', 'class_selected_exercises', 'date', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}