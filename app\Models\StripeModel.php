<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class StripeModel extends Model
{
    function __construct()
    {
        parent::__construct();
		$this->stripe_config = array(
			'api_key' => $_ENV['api_key'], // ApiKey
			'price_class' => $_ENV['price_class'] // ApiKey
		);
    }

    function earnings()
	{
		$balance = $this->all_balances();
		$earnings = [
						'total' => 0,
						'month' => 0,
						'week' => 0,
						'today' => 0,
					];
		foreach ($balance as $value) {
			$earnings['total'] += $value['amount'];
			if($value['created'] > strtotime("-1 month") AND $value['amount'] == '999' AND $value['status'] == 'available')
			{
				$earnings['month'] += $value['amount'];
			}
			if($value['created'] > strtotime("-1 week"))
			{
				$earnings['week'] += $value['amount'];
			}
			if($value['created'] > strtotime("-1 day"))
			{
				$earnings['today'] += $value['amount'];
			}
		}
		return $earnings;
	}

    function all_balances()
	{
		$tmp = $this->balance_transactions();
		$balances = $tmp['balance']['data'];
		while (isset($tmp['balance']['has_more']) && $tmp['balance']['has_more']) {
			$tmp = $this->balance_transactions(end($tmp['balance']['data'])['id']);
			$balances = array_merge($balances, $tmp['balance']['data']);
		}
		return $balances;
	}

    function balance_transactions($starting_after = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$query_array = [
						'limit' => 100,
					];
			isset($starting_after) ? $query_array['starting_after'] = $starting_after : '';
			$balance = $stripe->balanceTransactions->all($query_array);
			$tmp = json_decode(json_encode($balance), TRUE);
			$result['success'] = TRUE;
			$result['balance'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function subscribers()
	{
		$subscriptions = $this->all_subscriptions();
		$subscribers = [
						'total' => count($subscriptions),
						'month' => 0,
						'year' => 0,
						'last_month' => 0,
					];
		foreach ($subscriptions as $value) {
			if($value['plan']['interval'] == 'month')
			{
				$subscribers['month']++;
			}
			if($value['plan']['interval'] == 'year')
			{
				$subscribers['year']++;
			}
			if($value['start_date'] > strtotime("-1 month"))
			{
				$subscribers['last_month']++;
			}
		}
		return $subscribers;
	}

    function all_subscriptions()
	{
		$tmp = $this->list_subscriptions();
		$subscriptions = $tmp['subscriptions']['data'];
		while (isset($tmp['subscriptions']['has_more']) && $tmp['subscriptions']['has_more']) {
			$tmp = $this->list_subscriptions(end($tmp['subscriptions']['data'])['id']);
			$subscriptions = array_merge($subscriptions, $tmp['subscriptions']['data']);
		}
		return $subscriptions;
	}

    function list_subscriptions($starting_after = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$query_array = [
						'limit' => 100,
						'price' => 'price_1KFFPGL6EaNAw2awJpDD8rTi',
					];
			isset($starting_after) ? $query_array['starting_after'] = $starting_after : '';
			$subscriptions = $stripe->subscriptions->all($query_array);
			$tmp = json_decode(json_encode($subscriptions), TRUE);
			$result['success'] = TRUE;
			$result['subscriptions'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_subscription($customer_id = NULL, $price_id = NULL, $coupon = "", $source = "no source")
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			// Check for existing active subscriptions first
			$existing_subscriptions = $stripe->subscriptions->all([
				'customer' => $customer_id,
				'status' => 'active',
				'limit' => 10
			]);

			if (!empty($existing_subscriptions->data)) {
				$result['success'] = FALSE;
				$result['message'] = 'Customer already has an active subscription';
				$result['existing_subscription'] = $existing_subscriptions->data[0]->id;
				log_message('warning', 'Attempted to create duplicate subscription for customer: ' . $customer_id . ', existing subscription: ' . $existing_subscriptions->data[0]->id);
				return $result;
			}

			// Generate idempotency key to prevent duplicate subscriptions
			$idempotency_key = 'sub_' . $customer_id . '_' . $price_id . '_' . time();

			$subscription = $stripe->subscriptions->create([
                'customer' => $customer_id,
                'coupon' => $coupon,
                'items' => [
				    [
                        'price' => $price_id,
                        'quantity' => 1
                    ],
			    ],
                'metadata' => [
                    'source' => $source,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ], [
				'idempotency_key' => $idempotency_key
			]);
			$tmp = json_decode(json_encode($subscription), TRUE);
			$result['success'] = TRUE;
			$result['subscription'] = $subscription->id;
			$result['subscription_info'] = $subscription;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function all_coupons()
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$coupon_list = $stripe->coupons->all(['limit' => 30]);
			$result['success'] = TRUE;
			$result['coupons_list'] = $coupon_list['data'];
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_subscription_safe($customer_id = NULL, $price_id = NULL, $coupon = "", $source = "no source", $cancel_existing = false)
	{
		$result['success'] = FALSE;
		$result['message'] = '';

		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			// Check for existing active subscriptions
			$existing_check = $this->get_customer_active_subscriptions($customer_id);

			if ($existing_check['success'] && $existing_check['count'] > 0) {
				if ($cancel_existing) {
					// Cancel existing subscriptions first
					$cancel_result = $this->cancel_all_customer_subscriptions($customer_id, "replacing_with_new_subscription");
					if (!$cancel_result['success']) {
						$result['message'] = 'Failed to cancel existing subscriptions: ' . $cancel_result['message'];
						return $result;
					}
					$result['cancelled_existing'] = $cancel_result['cancelled_subscriptions'];
					log_message('info', 'Cancelled existing subscriptions for customer ' . $customer_id . ' before creating new one');
				} else {
					// Return error if existing subscription found and not cancelling
					$result['success'] = FALSE;
					$result['message'] = 'Customer already has an active subscription';
					$result['existing_subscription'] = $existing_check['subscriptions'][0]->id;
					log_message('warning', 'Attempted to create duplicate subscription for customer: ' . $customer_id . ', existing subscription: ' . $existing_check['subscriptions'][0]->id);
					return $result;
				}
			}

			// Generate idempotency key to prevent duplicate subscriptions
			$idempotency_key = 'sub_' . $customer_id . '_' . $price_id . '_' . time() . '_' . uniqid();

			$subscription = $stripe->subscriptions->create([
                'customer' => $customer_id,
                'coupon' => $coupon,
                'items' => [
				    [
                        'price' => $price_id,
                        'quantity' => 1
                    ],
			    ],
                'metadata' => [
                    'source' => $source,
                    'created_at' => date('Y-m-d H:i:s'),
                    'idempotency_key' => $idempotency_key
                ]
            ], [
				'idempotency_key' => $idempotency_key
			]);

			$result['success'] = TRUE;
			$result['subscription'] = $subscription->id;
			$result['subscription_info'] = $subscription;
			$result['message'] = 'Subscription created successfully';
			log_message('info', 'Created subscription ' . $subscription->id . ' for customer ' . $customer_id);

		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getMessage();
		}
		return $result;
	}

    function get_customer_active_subscriptions($customer_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		$result['subscriptions'] = [];
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$subscriptions = $stripe->subscriptions->all([
				'customer' => $customer_id,
				'status' => 'active',
				'limit' => 100
			]);

			$result['success'] = TRUE;
			$result['subscriptions'] = $subscriptions->data;
			$result['count'] = count($subscriptions->data);

		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function cancel_all_customer_subscriptions($customer_id = NULL, $reason = "duplicate_prevention")
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		$result['cancelled_subscriptions'] = [];

		try {
			$active_subs = $this->get_customer_active_subscriptions($customer_id);

			if ($active_subs['success'] && $active_subs['count'] > 0) {
				$stripe = new \Stripe\StripeClient(
				  $this->stripe_config['api_key']
				);

				foreach ($active_subs['subscriptions'] as $subscription) {
					$cancelled = $stripe->subscriptions->cancel($subscription->id, [
						'metadata' => [
							'cancelled_reason' => $reason,
							'cancelled_at' => date('Y-m-d H:i:s')
						]
					]);
					$result['cancelled_subscriptions'][] = $cancelled->id;
					log_message('info', 'Cancelled subscription ' . $cancelled->id . ' for customer ' . $customer_id . ' due to: ' . $reason);
				}

				$result['success'] = TRUE;
				$result['message'] = 'Cancelled ' . count($result['cancelled_subscriptions']) . ' existing subscriptions';
			} else {
				$result['success'] = TRUE;
				$result['message'] = 'No active subscriptions found to cancel';
			}

		} catch(\Stripe\Exception\CardException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			$result['message'] = $e->getMessage();
		}
		return $result;
	}

    function create_coupon($coupon = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            $coupon_create = $stripe->coupons->create([
                'id' => $coupon['code'],
                'name' => $coupon['code'],
                'percent_off' => $coupon['discount'],
                'duration' => 'once',
                'max_redemptions' => $coupon['redemption'],
                'applies_to' => [
                    'products' => $coupon['products']
                ],
                'metadata' => [
                    'products' => json_encode($coupon['products'])
                ]
            ]);
			$result['success'] = TRUE;
			$result['valid'] = $coupon_create;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function retrieve_coupon($coupon = "")
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$coupon_valid = $stripe->coupons->retrieve($coupon, []);
			$result['success'] = TRUE;
			$result['valid'] = $coupon_valid;
            if(isset($result['valid']['metadata']['products'])){
                $result['valid']['metadata'] = coupon_products(json_decode($coupon_valid['metadata']['products'], TRUE));
            }
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function delete_coupon($coupon = "")
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$coupon_deleted = $stripe->coupons->delete($coupon, []);
			$result['success'] = TRUE;
			$result['coupon_deleted'] = $coupon_deleted;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function cancel_subscription($stripe_subscription = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$subscription = $stripe->subscriptions->update(
			  $stripe_subscription,
			  [
                'cancel_at_period_end' => true,
              ]
			);
			$tmp = json_decode(json_encode($subscription), TRUE);
			$result['success'] = TRUE;
			$result['subscription'] = $subscription;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function add_card_to_customer($customer_id = NULL, $tok = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$customer = $stripe->customers->update(
			  $customer_id,
			  ['source' => $tok]
			);
			$tmp = json_decode(json_encode($customer), TRUE);
			$result['success'] = TRUE;
			$result['customer'] = $customer->id;
			$result['default_source'] = $customer->default_source;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function delete_card($customer_id = NULL, $card_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$card_delete = $stripe->customers->deleteSource(
			  $customer_id,
              $card_id,
			  []
			);
			$tmp = json_decode(json_encode($card_delete), TRUE);
			$result['success'] = TRUE;
			$result['response'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function new_card_to_customer($customer_id = NULL, $tok = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$customer = $stripe->customers->createSource(
			  $customer_id,
			  ['source' => $tok]
			);
			$tmp = json_decode(json_encode($customer), TRUE);
			$result['success'] = TRUE;
			$result['customer'] = $customer->id;
			$result['tmp'] = $tmp;
			$result['default_source'] = $customer->default_source;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function get_customer($customer_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$customer = $stripe->customers->retrieve(
			  $customer_id,
			  []
			);
			$tmp = json_decode(json_encode($customer), TRUE);
			$result['success'] = TRUE;
			$result['customer'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_customer($customer = array())
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            \Stripe\Stripe::setMaxNetworkRetries(2);
			$for_save = [
				'description' => $customer['firstname'] . ' ' . $customer['lastname'],
				'email' => $customer['email'],
				'name' => $customer['firstname'] . ' ' . $customer['lastname'],
			];
			isset($customer['source']) ? $for_save['source'] = $customer['source'] : '';
			$customer = $stripe->customers->create($for_save);
			$tmp = json_decode(json_encode($customer), TRUE);
			$result['success'] = TRUE;
			$result['customer'] = $customer->id;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function update_customer($customer_id = NULL, $data = array())
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$new_data = [];
			isset($data['default_source']) ? $new_data['default_source'] = $data['default_source'] : '';
			$customer = $stripe->customers->update(
			  $customer_id,
			  $data
			);
			$tmp = json_decode(json_encode($customer), TRUE);
			$result['success'] = TRUE;
			$result['customer'] = $customer->id;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function customer_invoices($customer_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$invoices = $stripe->invoices->all(
				[
					'customer' => $customer_id,
					'limit' => 100
				]
			);
			$tmp = json_decode(json_encode($invoices), TRUE);
			$result['success'] = TRUE;
			$result['invoices'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function customer_single_invoice($invoice_id = '')
	{
        // echo $invoice_id;
        // die();
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

            $single_invoice = $stripe->invoices->retrieve($invoice_id, []);

			$tmp = json_decode(json_encode($single_invoice), TRUE);
			$result['success'] = TRUE;
			$result['invoice'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function charge_retrieve($charge_id = '')
	{
        // echo $invoice_id;
        // die();
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

            $single_charge = $stripe->charges->retrieve($charge_id, []);

			$tmp = json_decode(json_encode($single_charge), TRUE);
			$result['success'] = TRUE;
			$result['charge'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_card_token($card = array())
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            \Stripe\Stripe::setMaxNetworkRetries(2);
			$card_token = $stripe->tokens->create([
			  'card' => $card,
			]);
			$tmp = json_decode(json_encode($card_token), TRUE);
			$result['success'] = TRUE;
			$result['token'] = $card_token->id;
			$result['last4'] = $card_token->card->last4;
			$result['brand'] = $card_token->card->brand;
			//$result['card'] = $card_token;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function retrieve_subscription($subscription_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$subscription = $stripe->subscriptions->retrieve(
			  $subscription_id,
			  []
			);
			$tmp = json_decode(json_encode($subscription), TRUE);
			$result['success'] = TRUE;
			$result['subscription'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function change_subscription($subscription_id = NULL, $subscription_item_id = NULL, $new_price = NULL, $coupon = '')
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            \Stripe\Stripe::setMaxNetworkRetries(2);
			$update = $stripe->subscriptions->update(
			  $subscription_id,
			  [
				  'metadata' => ['changed_on' => date('Y-m-d H:i:s')],
				  'cancel_at_period_end' => false,
				  'proration_behavior' => 'create_prorations',
				  //'proration_behavior' => 'none',
                  'coupon' => $coupon,
				  'items' => [
					[
					  'id' => $subscription_item_id,
					  'price' => $new_price,
					],
				  ]
			  ]
			);
			$tmp = json_decode(json_encode($update), TRUE);
			$result['success'] = TRUE;
			$result['tmp'] = $tmp;
			//$result['brand'] = $card_token->card->brand;
			//$result['card'] = $card_token;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function list_sources($customer_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$sources = $stripe->customers->allSources(
			$customer_id,
				[
					'object' => 'card',
					'limit' => 100
				]
			);
			$tmp = json_decode(json_encode($sources), TRUE);
			$result['success'] = TRUE;
			$result['sources'] = $tmp;
			//$result['brand'] = $card_token->card->brand;
			//$result['card'] = $card_token;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function list_payment_methods($customer_id = NULL)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$paymentmethods = $stripe->paymentMethods->all([
			  'customer' => $customer_id,
			  'type' => 'card',
			]);
			$tmp = json_decode(json_encode($paymentmethods), TRUE);
			$result['success'] = TRUE;
			$result['paymentmethods'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function list_accounts()
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$list_accounts = $stripe->accounts->all(['limit' => 3]);
			$tmp = json_decode(json_encode($list_accounts), TRUE);
			$result['success'] = TRUE;
			$result['list_accounts'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_account($data = array())
	{
        // print_r($data);
        // die();
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            if($data['address']['country'] == 'US'){
                $account = $stripe->accounts->create([
                    'type' => 'custom',
                    'country' => $data['address']['country'],
                    'email' => $data['email'], // DATA
                    'business_profile' => [
                        //'mcc' => 7829,
                        'url' => $data['teacher_url'],  // DATA
                    ],
                    'business_type' => 'individual',
                    // 'external_account' => 'tok_1KR1oeIF8V9V36v1sVS3Z2ro',
                    'capabilities' => [
                        // 'card_payments' => ['requested' => true],
                        'transfers' => ['requested' => true],
                    ],
                    'individual' => [
                        'first_name' => $data['firstname'],
                        'last_name' => $data['lastname'],
                        'ssn_last_4' => $data['ssn_last_4'],
                        'dob' => [
                            'day' => $data['day'],
                            'month' => $data['month'],
                            'year' => $data['year'],
                        ],
                    ],
                    'tos_acceptance' => [
                        'date' => $data['date'],
                        'ip' => $data['ip'],
                    ],
                ]);
            }else{
                // if((isset($data['routing_number']) AND $data['routing_number'] != '')){
                    $account = $stripe->accounts->create([
                        'type' => 'custom',
                        'country' => $data['address']['country'],
                        'email' => $data['email'], // DATA
                        'business_profile' => [
                            //'mcc' => 7829,
                            'url' => $data['teacher_url'],  // DATA
                        ],
                        'business_type' => 'individual',
                        'external_account' => [
                            'object' => 'bank_account',
                            'country' => $data['address']['country'],
                            'currency' => $data['address']['country'] == 'CA' ? 'cad' : 'eur',
                            'account_number' => $data['account_number'],
                            'account_holder_name' => $data['account_holder_name'],
                            'account_holder_type' => 'individual',
                            'routing_number' => $data['routing_number']
                        ],
                        'capabilities' => [
                            // 'card_payments' => ['requested' => true],
                            'transfers' => ['requested' => true],
                        ],
                        'individual' => [
                            'first_name' => $data['firstname'],
                            'last_name' => $data['lastname'],
                            'id_number' => $data['id_number'],
                            "email" => $data['email'],
                            "address" => $data['address'],
                            "phone" => $data['phone'],
                            'dob' => [
                                'day' => $data['day'],
                                'month' => $data['month'],
                                'year' => $data['year'],
                            ],
                            // 'verification' => [
                            //     'additional_document' => [
                            //         "back" => $data['additional_document_back'],
                            //         "front" => $data['additional_document_front']
                            //     ],
                            //     'document' => [
                            //         "back" => $data['document_back'],
                            //         "front" => $data['document_front']
                            //     ],
                            // ],
                        ],
                        'tos_acceptance' => [
                            'service_agreement' => 'recipient',
                            'date' => $data['date'],
                            'ip' => $data['ip'],
                        ],
                    ]);
                // }else{
                //     $account = $stripe->accounts->create([
                //         'type' => 'custom',
                //         'country' => $data['address']['country'],
                //         'email' => $data['email'], // DATA
                //         'business_profile' => [
                //             //'mcc' => 7829,
                //             'url' => $data['teacher_url'],  // DATA
                //         ],
                //         'business_type' => 'individual',
                //         'external_account' => [
                //             'object' => 'bank_account',
                //             'country' => $data['address']['country'],
                //             'currency' => 'eur',
                //             'account_number' => $data['account_number'],
                //             'account_holder_name' => $data['account_holder_name'],
                //             'account_holder_type' => 'individual',
                //         ],
                //         'capabilities' => [
                //             // 'card_payments' => ['requested' => true],
                //             'transfers' => ['requested' => true],
                //         ],
                //         'individual' => [
                //             'first_name' => $data['firstname'],
                //             'last_name' => $data['lastname'],
                //             'id_number' => $data['id_number'],
                //             "email" => $data['email'],
                //             "address" => $data['address'],
                //             "phone" => $data['phone'],
                //             'dob' => [
                //                 'day' => $data['day'],
                //                 'month' => $data['month'],
                //                 'year' => $data['year'],
                //             ],
                //             // 'verification' => [
                //             //     'additional_document' => $data['additional_doc'],
                //             //     'document' => $data['doc'],
                //             // ],
                //         ],
                //         'tos_acceptance' => [
                //             'service_agreement' => 'recipient',
                //             'date' => $data['date'],
                //             'ip' => $data['ip'],
                //         ],
                //     ]);
                // }
            }
			$tmp = json_decode(json_encode($account), TRUE);
			$result['success'] = TRUE;
			$result['account'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function test_create_account($data = array())
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$account = $stripe->accounts->create([
                'type' => 'custom',
                'country' => 'US',
                'email' => $data['email'], // DATA
                'business_profile' => [
                    //'mcc' => 7829,
                    'url' => $data['teacher_url'],  // DATA
                ],
                'business_type' => 'individual',
                // 'external_account' => 'tok_1KR1oeIF8V9V36v1sVS3Z2ro',
                'capabilities' => [
                    //'card_payments' => ['requested' => true],
                    'transfers' => ['requested' => true],
                ],
                'individual' => [
                    'first_name' => $data['firstname'],
                    'last_name' => $data['lastname'],
                    'ssn_last_4' => $data['ssn_last_4'],
                    'dob' => [
                        'day' => $data['day'],
                        'month' => $data['month'],
                        'year' => $data['year'],
                    ],
                ],
                'tos_acceptance' => [
                    'date' => $data['date'],
                    'ip' => $data['ip'],
                ],
			]);
			$tmp = json_decode(json_encode($account), TRUE);
			$result['success'] = TRUE;
			$result['account'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function update_account($teacher_account, $token)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$account = $stripe->accounts->update(
                $teacher_account,
                ['external_account' => $token]
			);
			$tmp = json_decode(json_encode($account), TRUE);
			$result['success'] = TRUE;
			$result['account'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function delete_account($account_id = '')
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);

			$account = $stripe->accounts->delete(
			  $account_id,
			  []
			);
			$tmp = json_decode(json_encode($account), TRUE);
			$result['success'] = TRUE;
			$result['account'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_transfer($data)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$transfer = $stripe->transfers->create([
			  'amount' => $data['amount'],
			  'currency' => 'usd',
			  'destination' => $data['destination'],
			  'transfer_group' => $data['transfer_group'],
			  'description' => $data['description'],
			]);
			$tmp = json_decode(json_encode($transfer), TRUE);
			$result['success'] = TRUE;
			$result['transfer'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_payout($teacher, $amount, $currency = 'usd')
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$payout = $stripe->payouts->create(
                [
                    'amount' => $amount,
                    'currency' => $currency,
                    'metadata' => [
                        'Teacher ID' => $teacher['id']
                    ]
                ],
                [
                    'stripe_account' => $teacher['stripe_account'] // acct_12a431s4d124asasgtr
                ]
            );
			$tmp = json_decode(json_encode($payout), TRUE);
			$result['success'] = TRUE;
			$result['payout'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function retrieve_account_balance($teacher)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$balance = $stripe->balance->retrieve([],
                ['stripe_account' => $teacher['stripe_account']]
            );
			$tmp = json_decode(json_encode($balance), TRUE);
			$result['success'] = TRUE;
			$result['balance'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function create_charge($buyer, $seller, $class, $stripe_customer, $price, $token)
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$charge = $stripe->charges->create([
                'amount' => $price * 100,
                'currency' => 'usd',
                'source' => $token,
                'customer' => $stripe_customer,
                'description' => 'Single class: ' . $class['title'],
                "metadata" => [
                    "Buyer ID" => $buyer,
                    "Seller" => $seller['firstname'] . ' ' . $seller['lastname'],
                    "Seller email" => $seller['email'],
                    "Seller LOD ID" => $seller['id'],
                    "Class" => $class['title'],
                    "Class ID" => $class['id'],
                ]
            ]);
			$tmp = json_decode(json_encode($charge), TRUE);
			$result['success'] = TRUE;
			$result['charge'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function list_transfers($account_id = '')
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
			$transfer = $stripe->transfers->all(
				[
					'destination' => $account_id,
					'limit' => 100
				]
			);
			$tmp = json_decode(json_encode($transfer), TRUE);
			$result['success'] = TRUE;
			$result['transfer'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}

    function upload_document($file = '', $purpose = 'identity_document')
	{
		$result['success'] = FALSE;
		$result['message'] = '';
		try {
			$stripe = new \Stripe\StripeClient(
			  $this->stripe_config['api_key']
			);
            $fp = fopen($file, 'r');
            $file_upload = $stripe->files->create(
                [
                    'purpose' => $purpose,
                    'file' => $fp
                ]
            );
			$tmp = json_decode(json_encode($file_upload), TRUE);
			$result['success'] = TRUE;
			$result['file_upload'] = $tmp;
		} catch(\Stripe\Exception\CardException $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\RateLimitException $e) {
			// Too many requests made to the API too quickly
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\InvalidRequestException $e) {
			// Invalid parameters were supplied to Stripe's API
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\AuthenticationException $e) {
			// Authentication with Stripe's API failed
			// (maybe you changed API keys recently)
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiConnectionException $e) {
			// Network communication with Stripe failed
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (\Stripe\Exception\ApiErrorException $e) {
			// Display a very generic error to the user, and maybe send
			// yourself an email
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		} catch (Exception $e) {
			// Something else happened, completely unrelated to Stripe
			$result['message'] = $e->getError()->message;
			$result['status'] = $e->getHttpStatus();
			$result['type'] = $e->getError()->type;
			$result['code'] = $e->getError()->code;
			$result['param'] = $e->getError()->param;
		}
		return $result;
	}
}