<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="css/mvp.css" />
<link rel="stylesheet" type="text/css" href="css/flat.css?3" />
<link href="css/videojs.custom.css" rel="stylesheet">

</head>
<body class="">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root" class="mob-text-center">
    <section class="py-100">
        <div class="container">
            <div class="row">
                <div class="col-8 offset-2 text-center">
                    <h1 class="h2 mb-3 line-height-small">LAGREE WHENEVER, WHEREVER.!</h1>
                    <p class="light f-16">Access Micro, Mini and Mega classes on your schedule.</p>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container1030">
            <div class="row">
                <div class="col-12">
                    <div class="main-text">
                        <div id="lod-video">
                            <div class="mvp-player-wrap">
                                <div class="mvp-player-holder">
                                    <div class="mvp-media-holder"></div>
                                    <div class="mvp-unmute-toggle">Enable volume</div>
                                    <div class="mvp-player-loader"></div>
                                    <div class="mvp-live-note">
                                        <div class="mvp-live-note-inner">
                                            <div class="mvp-live-note-icon"></div>
                                            <div class="mvp-live-note-title">LIVE</div>
                                        </div>
                                    </div>
                                    <div class="mvp-big-play">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#fff"/>
                                        </svg>
                                    </div>
                                    <div class="mvp-player-controls">
                                        <div class="mvp-player-controls-bottom">
                                        <div class="mvp-player-controls-bottom-left">
                                                <div class="mvp-skip-backward-toggle mvp-contr-btn" data-tooltip="Skip backward">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="rewind" d="M14.115,19.886a7.411,7.411,0,1,0-5.671-13.1l2.92,2.518-8.02,1.947.381-8.464L6.433,5.095A9.894,9.894,0,0,1,11.745,2.64,10,10,0,1,1,4.994,18.4l2.053-1.587A7.363,7.363,0,0,0,14.115,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-playback-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-play">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
                                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#fff"/>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-pause">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20">
                                                            <g id="pause" transform="translate(-240.764 -1653)">
                                                                <path id="Path_1312" data-name="Path 1312" d="M0,0H6V20H0Z" transform="translate(240.764 1653)" fill="#fff"/>
                                                                <path id="Path_1313" data-name="Path 1313" d="M0,0H6V20H0Z" transform="translate(249.764 1653)" fill="#fff"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="mvp-skip-forward-toggle mvp-contr-btn" data-tooltip="Skip forward">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="Path_1030" data-name="Path 1030" d="M12.349,19.886a7.411,7.411,0,1,1,5.671-13.1L15.1,9.306l8.02,1.947-.381-8.464L20.03,5.095A9.894,9.894,0,0,0,14.719,2.64,10,10,0,1,0,21.469,18.4l-2.053-1.587A7.363,7.363,0,0,1,12.349,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-volume-wrapper mvp-contr-btn">
                                                    <div class="mvp-volume-toggle mvp-contr-btn" data-tooltip="Volume">
                                                        <div class="mvp-btn mvp-btn-volume-up">
                                                            <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-down">
                                                            <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#fff"/>
                                                                    </g>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-off">
                                                            <svg aria-hidden="true" focusable="false" role="img" viewBox="0 0 640 512"><path d="M633.82 458.1l-69-53.33C592.42 360.8 608 309.68 608 256c0-95.33-47.73-183.58-127.65-236.03-11.17-7.33-26.18-4.24-33.51 6.95-7.34 11.17-4.22 26.18 6.95 33.51 66.27 43.49 105.82 116.6 105.82 195.58 0 42.78-11.96 83.59-33.22 119.06l-38.12-29.46C503.49 318.68 512 288.06 512 256c0-63.09-32.06-122.09-85.77-156.16-11.19-7.09-26.03-3.8-33.12 7.41-7.09 11.2-3.78 26.03 7.41 33.13C440.27 165.59 464 209.44 464 256c0 21.21-5.03 41.57-14.2 59.88l-39.56-30.58c3.38-9.35 5.76-19.07 5.76-29.3 0-31.88-17.53-61.33-45.77-76.88-11.58-6.33-26.19-2.16-32.61 9.45-6.39 11.61-2.16 26.2 9.45 32.61 11.76 6.46 19.12 18.18 20.4 31.06L288 190.82V88.02c0-21.46-25.96-31.98-40.97-16.97l-49.71 49.7L45.47 3.37C38.49-2.05 28.43-.8 23.01 6.18L3.37 31.45C-2.05 38.42-.8 48.47 6.18 53.9l588.36 454.73c6.98 5.43 17.03 4.17 22.46-2.81l19.64-25.27c5.41-6.97 4.16-17.02-2.82-22.45zM32 184v144c0 13.25 10.74 24 24 24h102.06l88.97 88.95c15.03 15.03 40.97 4.47 40.97-16.97V352.6L43.76 163.84C36.86 168.05 32 175.32 32 184z"></path></svg>
                                                        </div>
                                                    </div>
                                                    <div class="mvp-volume-seekbar">
                                                        <div class="mvp-volume-bg">
                                                            <div class="mvp-volume-level"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- <div class="mvp-media-time-current"></div> -->
                                            </div>
                                            <div class="mvp-seekbar">
                                                <div class="mvp-seekbar-wrap">
                                                    <div class="mvp-progress-bg">
                                                        <div class="mvp-load-level"></div>
                                                        <div class="mvp-progress-level"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mvp-player-controls-bottom-right">
                                                <!-- <div class="mvp-media-time-total"></div> -->
                                                <div class="mvp-cast-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-cast-off" data-tooltip="Play on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-cast-on" data-tooltip="Stop playing on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>

                                                <div class="mvp-airplay-toggle mvp-contr-btn" data-tooltip="AirPlay">
                                                    <svg id="cast" xmlns="http://www.w3.org/2000/svg" width="27.5" height="20" viewBox="0 0 27.5 20">
                                                        <path id="Path_932" data-name="Path 932" d="M6,18V34.25h7.348L14.6,33H7.25V19.25h25V33H24.9l1.25,1.25H33.5V18Z" transform="translate(-6 -18)" fill="#fff"/>
                                                        <g id="Group_4129" data-name="Group 4129" transform="translate(6.25 12.5)">
                                                            <path id="Path_933" data-name="Path 933" d="M26,65.5H41L33.5,58Z" transform="translate(-26 -58)" fill="#fff"/>
                                                        </g>
                                                    </svg>
                                                </div>

                                                <div class="mvp-fullscreen-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-fullscreen" data-tooltip="Fullscreen">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-normal" data-tooltip="Exit Fullscreen">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- end mvp-player-holder -->
                            </div><!-- end mvp-player-wrap -->
                            <div id="mvp-playlist-list">
                                <div class="playlist-video">
                                    <div class="mvp-playlist-item"
                                        data-type="video"
                                        data-path='[{"quality": "HD", "mp4": "/uploads/videos/what-is-LagreeOD.mp4"}]'
                                        data-poster="/images/lobby-bg.jpg"
                                        data-share="<?php echo current_url(); ?>"
                                        data-title="WELCOME TO LAGREE ON DEMAND!">
                                    </div>
                                </div>
                            </div>
                            <!-- base_url() . '/video.php?name=' . $name -->
                        </div>
                        <h3 class="line-height-small f-14 bold mt-6 text-center">SEBASTIEN LAGREE, FOUNDER OF LAGREE ON DEMAND WANTS TO SAY HELLO TO YOU!</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 px-0 py-mob-75">
        <div class="px-150">
            <hr class="mt-0">
        </div>
        <div class="container-fluid">
            <div class="row aic">
                <div class="col-6 order-mob-2 pl-150">
                    <div class="pr-120 px-mob-0">
                        <p class="f-14 medium text-uppercase mb-4 line-height-small red small-subtitle">PLATFORM CREATED BY LAGREE</p>
                        <h2 class="mb-4 line-height-small">LAGREE AT GLANCE</h2>
                        <img src="images/glance-img.jpg" alt="" class="img-fluid d-inline-block mb-mob-3 mobile mob-middle-img" />
                        <h3 class="f-18 light mb-6">With over 100 classes on demand, you can experience the benefits of your practice at home or on the go, on any device.</h3>
                        <a href="/classes" class="btn btn-tall btn-wide btn-border btn-black-border white-bg" title="Classes">Classes</a>
                    </div>
                </div>
                <div class="col-6 order-mob-1 pr-0 text-right overflow">
                    <img src="images/glance-img.jpg" alt="" class="img-fluid d-inline-block mb-3 img-overflow-right" />
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container-fluid">
            <hr class="mt-0">
            <div class="row aic">
                <div class="col-6 order-mob-1 text-right px-0 desktop">
                    <img src="images/access-anywhere-homepage.jpg" alt="" class="img-fluid d-inline-block" />
                </div>
                <div class="col-6 order-mob-2 pl-150">
                    <div class="pr-120 px-mob-0">
                        <p class="f-14 medium text-uppercase mb-4 line-height-small red small-subtitle">STREAM ON ALL DEVICES</p>
                        <h2 class="mb-4 line-height-small">ACCESS ANYWHERE</h2>
                        <img src="images/access-anywhere-homepage.jpg" alt="" class="img-fluid d-inline-block mb-3 mobile mob-middle-img" />
                        <h3 class="f-18 light mb-6">Whether you’re at home, the gym, your hotel, or small apartment, you can get Lagree On Demand on any device.</h3>
                        <p class="f-16 medium mb-3">Lagree Player<sup>TM</sup> Supports:</p>
                        <img src="images/devices.svg" alt="" class="img-fluid mob-middle-img" />
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 desktop">
        <div class="container-fluid">
            <hr class="mt-0">
            <div class="row">
                <div class="col-12 mb-100">
                    <h2 class="m-0 line-height-small">Key Features</h2>
                </div>
            </div>
            <div class="row big-gap">
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">The Lagree Method<sup>TM</sup></h3>
                    <p class="f-16 light">Lagree is a high-intensity low impact core, muscular strength; and muscular endurance workout.</p>
                </div>
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">Live Events</h3>
                    <p class="f-16 light">Our Micro events are a great opportunity to get familiar with our method and equipment. Plus you get to workout to live music.</p>
                </div>
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">Powerful Filtering</h3>
                    <p class="f-16 light">Search workout by duration, intensity, teachers, body parts, etc…</p>
                </div>
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">Certified Teachers</h3>
                    <p class="f-16 light">All of our teachers are certified by Lagree Fitness.</p>
                </div>
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">Cancel Any-Time</h3>
                    <p class="f-16 light">You can cancel your subscription at any time.</p>
                </div>
                <div class="col-4 mb-80">
                    <h3 class="mb-5 with-line f-18 bold text-uppercase">Teacher Earnings</h3>
                    <p class="f-16 light">Lagree certified teachers are welcome to upload their own workouts and get paid for them!</p>
                </div>
            </div>
            <div class="row big-gap">
                <div class="col-12">
                    <a href="/platform-upgrades" class="link link-black black f-16 semibold text-underline">VIEW UPCOMING FEATURES</a>
                </div>
            </div>
        </div>
    </section>
    <section class="mobile pt-0">
        <div class="container-fluid">
            <hr class="mt-0">
            <div class="row">
                <div class="col-12 mb-mob-5">
                    <h2 class="m-0">Key Features</h2>
                </div>
            </div>
            <div class="row aic">
                <div class="col-12">
                    <div class="swiper homepage-collection">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">The Lagree Method<sup>TM</sup></h3>
                                <p class="f-18 light">Lagree is a high-intensity low impact core, muscular strength; and muscular endurance workout.</p>
                            </div>
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">Live Events</h3>
                                <p class="f-18 light">Our Micro events are a great opportunity to get familiar with our method and equipment. Plus you get to workout to live music.</p>
                            </div>
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">Powerful Filtering</h3>
                                <p class="f-18 light">Search workout by duration, intensity, teachers, body parts, etc…</p>
                            </div>
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">Certified Teachers</h3>
                                <p class="f-18 light">All of our teachers are certified by Lagree Fitness.</p>
                            </div>
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">Cancel Any-Time</h3>
                                <p class="f-18 light">You can cancel your subscription at any time.</p>
                            </div>
                            <div class="swiper-slide">
                                <h3 class="mb-5 with-line f-18 bold text-uppercase">Teacher Earnings <sup class="red">FEB 2022</sup></h3>
                                <p class="f-18 light">Lagree certified teachers are welcome to upload their own workouts and get paid for them!</p>
                            </div>
                        </div>
                        <div class="swiper-pagination homepage-sponsors-pagination"></div>
                    </div>
                </div>
            </div>
            <div class="row big-gap">
                <div class="col-12 mb-100">
                    <a href="/platform-upgrades" class="link link-black black f-16 semibold text-underline">VIEW UPCOMING FEATURES</a>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 px-0">
        <div class="px-150">
            <hr class="mt-0">
        </div>
        <div class="container-fluid">
            <div class="row aic">
                <div class="col-6 order-mob-2 pl-150">
                    <div class="pr-120 px-mob-0 mb-mob-3">
                        <p class="f-14 medium text-uppercase mb-4 line-height-small red small-subtitle">REGULAR CLASS UPDATES</p>
                        <h2 class="mb-4">NEW CLASSES EVERY WEEK</h2>
                        <img src="images/recently-added.jpg" alt="" class="img-fluid d-inline-block mb-mob-3 mobile mob-middle-img" />
                        <h3 class="f-18 light mb-6">New classes uploaded by certified Lagree teachers will be added every week, searchable by duration, body part, accessories, machine, etc…</h3>
                        <a href="/classes" class="btn btn-tall btn-wide btn-border btn-black-border white-bg" title="Classes">Classes</a>
                    </div>
                </div>
                <div class="col-6 order-mob-1 pr-0 text-right overflow">
                    <img src="images/recently-added.jpg" alt="" class="img-fluid d-inline-block mb-3 img-overflow-right" />
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container-fluid">
            <hr class="mt-0">
            <div class="row aic">
                <div class="col-6 order-mob-1 text-right px-0 desktop">
                    <img src="images/class-collections.jpg" alt="" class="img-fluid d-inline-block mb-3" />
                </div>
                <div class="col-6 order-mob-2 pl-150">
                    <div class="pr-120 px-mob-0 mb-mob-3">
                        <p class="f-14 medium text-uppercase mb-4 line-height-small red small-subtitle">CAREFULLY TAILORED CLASSES</p>
                        <h2 class="mb-4">COLLECTIONS</h2>
                        <img src="images/class-collections.jpg" alt="" class="img-fluid d-inline-block mb-mob-3 mobile mob-middle-img" />
                        <h3 class="f-18 light mb-6">We’ve created collections of classes so you can choose the workouts or lessons that fit your goals most—anytime, anywhere!</h3>
                        <a href="/collections/lagree-101" class="btn btn-tall btn-wide btn-border btn-black-border white-bg" title="WATCH LAGREE 101">WATCH LAGREE 101</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php if(NULL === session('subscription') AND session('subscription') != 'active'){ ?>
    <section class="py-0 mb-0">
        <div class="container-fluid">
            <hr class="mt-0">
            <div class="row aic">
                <div class="col-12 text-center mb-150 mb-mob-5">
                    <h2 class="mb-mob-2 mb-3 line-height-normal">SUBSCRIBE. WATCH. <br class="mobile">CANCEL ANY-TIME.</h2>
                    <h3 class="f-18 light mb-6">Start Lagree On Demand today with no commitment.</h3>
                    <a href="/subscribe" class="btn red-bg btn-wide white" title="Subscribe Now, Cancel Any-Time">Subscribe Now, <span class="text-underline ml-05">Cancel Any-Time</span></a>
                </div>
            </div>
        </div>
    </section>
    <?php } ?>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/swiper-bundle.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script> -->
<!-- <script src="js/fb_login.js"></script> -->
<!-- <script src="js/google_login.js"></script> -->
<script type="text/javascript" src="js/new.js"></script>
<script>
jQuery(document).ready(function($) {
    var player = $("#lod-video").mvp({
        sourcePath: "",
        instanceName: "player1",
        activePlaylist: ".playlist-video",
        activeItem: 0,
        volume: 1,
        usePlayer: false,
        autoPlay: false,
        autoPlayAfterFirst: true,
        randomPlay: false,
        loopingOn: true,
        mediaEndAction: 'poster',
        useMobileNativePlayer: false,
        showPosterOnPause: false,
        hideQualityMenuOnSingleQuality: true,
        aspectRatio: 1, // 1
        facebookAppId: "",
        playlistOpened: false,
        useKeyboardNavigationForPlayback: true,
        truncatePlaylistDescription: true,
        rightClickContextMenu: "custom",
        playlistItemContent: "thumb,title, description,duration",
        elementsVisibilityArr: [
            {
                width: 500, elements: [ "play", "next", "seekbar", "fullscreen", "volume"]
            }
        ],
        skin: "flat-light",
        playlistPosition: "no-playlist",
        playerType: "normal",
        playlistScrollType: "perfect-scrollbar",
        useSearchBar: false
    });
    player.on("mediaEnd", function(e, data){
        $('.mvp-poster-holder').removeClass('hidden_poster');
    });
    var video = document.getElementById("lod-video");
    video.addEventListener('ended', () => {
        console.log('video ENDED');
        video.pause();
        video.currentTime = 0;
        // $('.mvp-poster-holder').css({"opacity":"1"});
    });
});
</script>
</body>
</html>
