<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class NotificationsModel extends Model
{
    protected $table = 'notifications';
	protected $allowedFields = ['content', 'date', 'author', 'link', 'subscriber_id', 'hide', 'type'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'content'    => 'required',
        'author'     => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_notifications($start = 0, $limit = 25, $search_term = NULL, $order = "notifications.date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND notifications.content LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT *
                                FROM notifications
                                -- LEFT JOIN subscribers ON subscribers.id = notifications.user_id
                                WHERE date IS NOT NULL
                                " . $search . "
                                ORDER BY " . $order . "
                                " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_my_notifications($start = 0, $limit = 12, $order = "notifications.date DESC"){
        $user = (NULL !== session('user') AND session('user') != '') ? $this->query("SELECT * FROM subscribers WHERE id = " . session('user') . "")->getResultArray() : NULL;

        $sql_add = " AND (notifications.type = 'welcome_od'";
        $sql_add .= " OR notifications.type = 'new_conversation_notif'";
        $sql_add .= " OR notifications.type = 'from_admin'";
        if($user != NULL AND count($user) > 0){
            $sql_add .= $user[0]['new_class_notif'] == 1 ? " OR notifications.type = 'new_class_notif'" : '';
            $sql_add .= $user[0]['new_od_class_notif'] == 1 ? " OR notifications.type = 'new_od_class_notif'" : '';
            $sql_add .= $user[0]['new_liveevents_notif'] == 1 ? " OR notifications.type = 'new_liveevents_notif'" : '';
            $sql_add .= $user[0]['new_teacher_notif'] == 1 ? " OR notifications.type = 'new_teacher_notif'" : '';
            $sql_add .= $user[0]['new_staff_playlist_notif'] == 1 ? " OR notifications.type = 'new_staff_playlist_notif'" : '';
            $sql_add .= $user[0]['class_approved_notif'] == 1 ? " OR notifications.type = 'class_approved_notif'" : '';
            $sql_add .= $user[0]['class_rejected_notif'] == 1 ? " OR notifications.type = 'class_rejected_notif'" : '';
            $sql_add .= $user[0]['class_bought_notif'] == 1 ? " OR notifications.type = 'class_bought_notif'" : '';
            $sql_add .= $user[0]['class_rated_notif'] == 1 ? " OR notifications.type = 'class_rated_notif'" : '';
            $sql_add .= $user[0]['payout_request_notif'] == 1 ? " OR notifications.type = 'payout_request_notif'" : '';
        }
        $sql_add .= ")";

        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $data = $this->query("SELECT notifications.*, subscribers_notifications.hide AS hidden,
                                IF(notifications.id IN (
                                    SELECT * FROM (
                                        SELECT subscribers_notifications.notification_id FROM subscribers_notifications WHERE subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . "
                                    ) as nesto
                                ), 1, 0) as seen
                                FROM notifications
                                LEFT JOIN subscribers_notifications ON (subscribers_notifications.notification_id = notifications.id AND subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                WHERE (notifications.subscriber_id IS NULL OR notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                AND notifications.date < NOW()
                                " . $sql_add . "
                                HAVING (hidden = 0 OR hidden IS NULL)
                                ORDER BY notifications.date desc
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_notifications_admin($start = 0, $limit = 25, $search_term = NULL, $order = "notifications.date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND notifications.content LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT *
                            FROM notifications
                            -- LEFT JOIN subscribers ON subscribers.id = notifications.user_id
                            WHERE date IS NOT NULL
                            " . $search . "
                            AND author = 'admin'
                            AND hide = 0
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($id){
        $data = $this->query("SELECT notifications.*
                            FROM notifications
                            WHERE notifications.id = " . $id .  "
                        ")->getRowArray();
        return $data;
    }


	protected function prepare_data(array $data)
	{
		return $data;
	}
}