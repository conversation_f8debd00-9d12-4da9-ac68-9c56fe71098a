<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\Admincontroller;
use App\Models\StripeModel;
use App\Models\SubscribersModel;

class DuplicateSubscriptionCleanup extends Admincontroller
{
    protected $stripe_model;
    protected $subscribers_model;

    public function __construct()
    {
        parent::__construct();
        $this->stripe_model = new StripeModel();
        $this->subscribers_model = new SubscribersModel();
    }

    public function index()
    {
        $data = $this->data;
        $data['current']['title'] = "Duplicate Subscription Cleanup | Admin";
        
        return view('admin/duplicate_subscription_cleanup_view', $data);
    }

    /**
     * Find all customers with duplicate subscriptions
     */
    public function find_duplicates()
    {
        $duplicates = [];
        $total_checked = 0;
        $total_duplicates = 0;

        try {
            // Get all subscribers with stripe_customer IDs
            $subscribers = $this->subscribers_model->where('stripe_customer IS NOT NULL')->findAll();
            
            foreach ($subscribers as $subscriber) {
                $total_checked++;
                
                // Get active subscriptions for this customer
                $active_subs = $this->stripe_model->get_customer_active_subscriptions($subscriber['stripe_customer']);
                
                if ($active_subs['success'] && $active_subs['count'] > 1) {
                    $total_duplicates++;
                    
                    $duplicates[] = [
                        'subscriber_id' => $subscriber['id'],
                        'email' => $subscriber['email'],
                        'stripe_customer' => $subscriber['stripe_customer'],
                        'subscription_count' => $active_subs['count'],
                        'subscriptions' => array_map(function($sub) {
                            return [
                                'id' => $sub->id,
                                'status' => $sub->status,
                                'created' => date('Y-m-d H:i:s', $sub->created),
                                'current_period_start' => date('Y-m-d H:i:s', $sub->current_period_start),
                                'current_period_end' => date('Y-m-d H:i:s', $sub->current_period_end),
                                'plan_interval' => $sub->items->data[0]->price->recurring->interval ?? 'unknown'
                            ];
                        }, $active_subs['subscriptions'])
                    ];
                }
            }

            $response = [
                'success' => true,
                'total_checked' => $total_checked,
                'total_duplicates' => $total_duplicates,
                'duplicates' => $duplicates
            ];

        } catch (Exception $e) {
            $response = [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }

        return $this->respond($response);
    }

    /**
     * Clean up duplicates by keeping the most recent subscription
     */
    public function cleanup_duplicates()
    {
        $data = $this->request->getPost();
        $customer_id = $data['customer_id'] ?? null;
        $keep_subscription = $data['keep_subscription'] ?? null;
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            // Get all active subscriptions for the customer
            $active_subs = $this->stripe_model->get_customer_active_subscriptions($customer_id);
            
            if (!$active_subs['success'] || $active_subs['count'] <= 1) {
                return $this->respond([
                    'success' => false,
                    'message' => 'No duplicate subscriptions found for this customer'
                ]);
            }

            $cancelled_subscriptions = [];
            
            foreach ($active_subs['subscriptions'] as $subscription) {
                // Skip the subscription we want to keep
                if ($keep_subscription && $subscription->id === $keep_subscription) {
                    continue;
                }
                
                // Cancel this subscription
                $cancel_result = $this->stripe_model->cancel_subscription($subscription->id);
                if ($cancel_result['success']) {
                    $cancelled_subscriptions[] = $subscription->id;
                }
            }

            // Update the subscriber's stripe_subscription field to the kept subscription
            if ($keep_subscription) {
                $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();
                if ($subscriber) {
                    $this->subscribers_model->save([
                        'id' => $subscriber['id'],
                        'stripe_subscription' => $keep_subscription
                    ]);
                }
            }

            return $this->respond([
                'success' => true,
                'message' => 'Cleaned up ' . count($cancelled_subscriptions) . ' duplicate subscriptions',
                'cancelled_subscriptions' => $cancelled_subscriptions,
                'kept_subscription' => $keep_subscription
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get detailed information about a specific customer's subscriptions
     */
    public function get_customer_subscriptions()
    {
        $customer_id = $this->request->getGet('customer_id');
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            $active_subs = $this->stripe_model->get_customer_active_subscriptions($customer_id);
            
            if ($active_subs['success']) {
                $subscriptions = array_map(function($sub) {
                    return [
                        'id' => $sub->id,
                        'status' => $sub->status,
                        'created' => date('Y-m-d H:i:s', $sub->created),
                        'current_period_start' => date('Y-m-d H:i:s', $sub->current_period_start),
                        'current_period_end' => date('Y-m-d H:i:s', $sub->current_period_end),
                        'plan_interval' => $sub->items->data[0]->price->recurring->interval ?? 'unknown',
                        'plan_amount' => ($sub->items->data[0]->price->unit_amount ?? 0) / 100,
                        'metadata' => $sub->metadata->toArray()
                    ];
                }, $active_subs['subscriptions']);

                return $this->respond([
                    'success' => true,
                    'subscriptions' => $subscriptions
                ]);
            } else {
                return $this->respond($active_subs);
            }

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate a report of all duplicate subscriptions
     */
    public function generate_report()
    {
        $duplicates_result = $this->find_duplicates();
        
        if (!$duplicates_result['success']) {
            return $this->respond($duplicates_result);
        }

        $report = [
            'generated_at' => date('Y-m-d H:i:s'),
            'summary' => [
                'total_customers_checked' => $duplicates_result['total_checked'],
                'customers_with_duplicates' => $duplicates_result['total_duplicates'],
                'total_duplicate_subscriptions' => 0
            ],
            'details' => []
        ];

        foreach ($duplicates_result['duplicates'] as $duplicate) {
            $report['summary']['total_duplicate_subscriptions'] += ($duplicate['subscription_count'] - 1);
            $report['details'][] = $duplicate;
        }

        return $this->respond([
            'success' => true,
            'report' => $report
        ]);
    }
}
