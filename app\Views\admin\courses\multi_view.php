<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone::before {
    content: "";
    position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your videos file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
    z-index: 111;
    background: #f8f8f8;
}
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.single_video_upload:last-of-type .class-item{
	margin-bottom: 0 !important;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Bulk Class Upload</h1>
                <!-- <a href="admin/classes" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a> -->
            </div>
            <hr class="mt-0 mb-5">
            <div class="col-12 count_uploading_classes" style="display: none">
                <div class="flex aic jcsb pb-5 bottom-border mb-6">
                    <h4>Uploading <span class="count_uploading">9</span> classes</h4>
                    <p class="midGray">You can upload up to 10 classes</p>
                </div>
            </div>
            <form action="admin/classes/upload" method="post" enctype="multipart/form-data" class="mb-2 upload-zone" id="video_container">
                <input type="file" multiple name="video[]" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()">
                <div class="before_upload">
                    <span class="h3 mb-1">Drag and drop video files to upload or <span class="link link-red red text-underline video_choose">select a file.</span></span>
                    <span class="h3 midGray">You can upload up to 10 classes</span>
                </div>
                <span id="video-is-uploading"></span>
                <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                <span id="progress-bar-status-show"></span>
                <span id="toshow" style="display: none;"></span>
            </form>
            <hr class="mt-6 mb-2">
        </div>
        <form action="admin/classes/save_batch" method="post" enctype="multipart/form-data" class="container pb-100">
            <div class="row big-gap">
                <div class="col-12 mb-2">
                    <div class="table">

                    </div>
                </div>
            </div>
            <hr class="my-3 multi-hr" style="display: none;">
            <div class="row">
                <div class="col-12 flex aic">
                    <button type="submit" class="btn btn-wide btn-tall red-bg white submit_batch" style="display: none;">Save Changes</button>
                    <h4 class="red f-16 ml-2 enter_titles" style="display: none;">Please enter title for all uploaded videos</h4>
                </div>
            </div>
        </form>
    </div>
</main>

<template id="one-video-template">
    <div class="table-row single_video_upload" data-video_path="uploads/videos" data-index="{id}">
        <input type="hidden" name="video[]" class="video_path">
        <input type="hidden" name="status[]" value="1">
        <input type="hidden" name="video_preview[]" value="0" class="video_preview">
        <input type="hidden" name="video_thumb[]" value="0" class="video_thumb">
        <input type="hidden" name="duration[]" value="0" class="video_duration">
        <input type="hidden" name="created_at[]" value="<?php echo date("Y-m-d H:i:s"); ?>">
        <div class="class-item flex aic mb-6">
            <div class="img--loading mr-3">
                <img src="" alt="" class="img-fluid class_image" style="max-width: 210px;max-height: 120px;width: 210px;object-fit: cover;display: none;" />
            </div>
            <div class="flex flex-column col-6 multi-desc">
                <input type="text" name="title[]" required class="line-input no-border medium h4 line-height-small class-title" placeholder="Enter Class Name…" style="height: 40px;" />
                <input type="hidden" name="slug[]" class="line-input no-border light line-height-small slug{id}" placeholder="Page URL" style="height: 40px;" />
                <div class="progress-percentage semibold red">0%</div>
            </div>
            <div class="flex flex-column ml-auto text-right f-14 pr-2">
                <a href="javascript:;" class="midGray f-14" onclick="$(this).closest('.single_video_upload').remove();videosToUpload = videosToUpload - 1">Delete</a>
            </div>
        </div>
    </div>
</template>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes_multi2.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>