<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Pages extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('PagesModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_pages'] = $this->model->all_pages(0, session('per_page'));
        $data['all_pages'] = $this->model->all_pages(0, session('per_page'), NULL, "sort, pages.updated_at desc");
        $data['pages_count'] = count($this->model->all_pages());
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/pages/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['all_pages'] = $this->model->all_pages(($page * session('per_page')) - session('per_page'), session('per_page'), NULL, "sort, pages.updated_at desc", 1);
        $data['pages_count'] = count($this->model->all_pages());
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/pages/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_pages'] = $this->model->all_pages(0, 9, $data['search_term'], "sort, pages.updated_at desc", 1);
        $data['pages_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/pages/index_view', $data);
    }

    public function sort_by($type = 'pages.title', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $teachers_model = model('TeachersModel');
		// $data = $this->request->getPost();
        $data['all_pages'] = $this->model->all_pages(0, session('per_page'), NULL, $type. " " . $direction, 1);
        // $data['all_pages'] = $this->model->query("SELECT pages.*
        //                                 FROM pages
        //                                 WHERE pages.deleted_at IS NULL
        //                                 ORDER BY " . $type. " " . $direction . "")->getResultArray();
        $data['pages_count'] = count($this->model->all_pages());
        $types = array(
            "pages.created_atdesc" => "Date Added",
            "pages.titleasc" => "Ascending",
            "pages.titledesc" => "Descending"
        );
        $data['all_teachers'] = $teachers_model->findAll();
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/pages/index_view', $data);
    }


    public function datatable()
    {
		$post = $this->request->getPost();
		$draw = $post['draw'];
		$row = $post['start'];

		$rowperpage = $post['length']; // Rows display per page
		$columnIndex = $post['order'][0]['column']; // Column index
		$columnName = $post['columns'][$columnIndex]['data']; // Column name
		$columnSortOrder = $post['order'][0]['dir']; // asc or desc
		$searchValue = $post['search']['value']; // Search value


		$db      = \Config\Database::connect();
		$sql = "SELECT
			pages.*,
			IF(pages.deleted_at IS NULL, 0, 1) as deleted
			FROM pages
			WHERE 1 ";
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecords = count($data);

		if (session()->has('filter_table'))
		{
			$filter = session()->filter_table;
			if(isset($filter['vreme']) AND $filter['vreme'] <> ''){
				$sql .= " AND users.created_at > '" . $filter['vreme'] . "'";
			}
			if(isset($filter['vrsta_potvrde']) AND $filter['vrsta_potvrde'] <> ''){
				$sql .= " AND users.vrsta_potvrde = '" . $filter['vrsta_potvrde'] . "'";
			}
		}
		if($searchValue != ''){
			$sql .= " AND (
				pages.id LIKE '%" . $searchValue . "%'
				OR pages.title LIKE '%" . $searchValue . "%'
				) ";
		}
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecordwithFilter = count($data);
		$sql .= " ORDER BY " . $columnName . " " . $columnSortOrder;
		$sql .= " LIMIT " . $row . "," . $rowperpage;
		$query = $db->query($sql);
		$data_final = $query->getResult();
		$response = array(
		  "mrnj" => $sql,
		  "draw" => intval($draw),
		  "iTotalRecords" => $totalRecords,
		  "iTotalDisplayRecords" => $totalRecordwithFilter,
		  "aaData" => $data_final
		);
		echo json_encode($response, JSON_PRETTY_PRINT);
    }

    public function edit($edit_id = 0)
    {
        $classes_model = model('ClassesModel');
        $howto_model = model('HowtoModel');
        $teachers_model = model('TeachersModel');
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$data['machines'] = $this->model->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
        $data['current'] = $this->model->where(['id' => $edit_id])->first();

        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('admin/pages/edit_view', $data);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Data successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/pages', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/pages/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/pages/' . $name, 98);
				$data['image'] = 'uploads/pages/' . $name;
                $webp = webpImage($data['image'], $_ENV['webP_quality']);
                $data['webp_image'] = str_replace('\\', '/', $webp);
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/pages', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/pages/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/pages/' . $name, 98);
				$data['cover_image'] = 'uploads/pages/' . $name;
                $webp = webpImage($data['cover_image'], $_ENV['webP_quality']);
                $data['webp_cover_image'] = str_replace('\\', '/', $webp);
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/pages', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/pages/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/pages/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/pages/' . $name;
                $webp = webpImage($data['mob_cover_image'], $_ENV['webP_quality']);
                $data['webp_mob_cover_image'] = str_replace('\\', '/', $webp);
			}
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            // if($data['cover_image_removed'] == 1){
            //     $data['cover_image'] = "";
            // }
            // unset($data['cover_image_removed']);

            // if($data['mob_cover_image_removed'] == 1){
            //     $data['mob_cover_image'] = "";
            // }
            // unset($data['mob_cover_image_removed']);

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('pages_' . $key);
                    $builder->delete(['pages_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'pages_id' => $response['inserted_id'],
                            'pages_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }


    public function delete_record($record_id = 0, $ajax = FALSE)
    {
		if ($record_id > 0)
		{
			$response['success'] = $this->model->delete($record_id);
		}
		return redirect()->to(site_url('admin/pages'));
    }
}