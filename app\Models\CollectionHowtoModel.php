<?php namespace App\Models;

use CodeIgniter\Model;

class CollectionHowtoModel extends Model
{
    protected $table = 'collections_selected_howto';
	protected $allowedFields = ['collections_id', 'collection_selected_howto', 'date', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}