<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Courses</h1>
                <div class="ml-auto">
                    <a href="admin/courses/edit" class="btn black-bg white" title="Upload Course">Add Course</a>
                </div>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $courses_count == 1 ? $courses_count . ' Course' : $courses_count . ' Courses'; ?></h5>

                <div class="flex aic jcsb">
                    <div class="dropdown d-inline-block">
                        <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" data-val="courses.created_at" data-by="desc" class="set_sort_by link midGray <?php echo ($courses_sort[0] == 'courses.created_at' AND $courses_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Date Added</a></li>
                            <li><a href="javascript:;" data-val="courses.title" data-by="asc" class="set_sort_by link midGray <?php echo ($courses_sort[0] == 'courses.title' AND $courses_sort[1] == 'asc') ? 'selected' : ''; ?>" title="">Ascending</a></li>
                            <li><a href="javascript:;" data-val="courses.title" data-by="desc" class="set_sort_by link midGray <?php echo ($courses_sort[0] == 'courses.title' AND $courses_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Descending</a></li>
                        </ul>
                    </div>
                    <div class="search-container">
                        <form action="admin/courses" method="GET" class="search-form <?php echo (isset($search_term) AND $search_term != '0') ? 'show' : ''; ?>">
                            <input type="text" name="search_term" class="seach-input" value="<?php echo (isset($search_term) AND $search_term != "0") ? $search_term : ''; ?>">
                            <?php if(isset($search_term) AND $search_term != "0"){ ?>
                            <a href="admin/courses/clear_search" class="clear_search" style="font-size: 18px;right: 40px;">×</a>
                            <?php } ?>
                            <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        </form>
                    </div>
                </div>
            </div>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="courses" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <!--<form class="edit_checked" style="display: none" method="post" action="admin/courses/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-12 link flex aic edit_bulk midGray" style="background: #fff !important;">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>-->
                </div>
            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders">
<?php
foreach($all_courses as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/courses/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" style="max-width: 210px;max-height: 120px;height: 120px;width: 210px;object-fit: cover;<?php echo($single['status'] == 1) ? 'opacity: 0.3 !important' : ''; ?>" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/courses/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium mb-05 flex aic">
                                        <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                    </a>
                                    <span class="midGray mb-05 f-1 normal">
                                        <?php echo $single['all_course_machines'] ?>,
                                        <span class="blockmob"></span>by: <a href="admin/teachers/edit/<?php echo (isset($single['teach_id']) AND $single['teach_id'] != '') ? $single['teach_id'] : ''; ?>" class="link link-black black"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : ''; ?></a>
                                    </span>
                                    <span class="midGray f-1 normal">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    <div class="row-actions f-1 red normal">
                                        <a href="admin/courses/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="courses">Delete</a>-->
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-1 normal">
                                    <span class="most-title nrviews"><?php echo (isset($single['countVideos']) AND $single['countVideos'] != '') ? $single['countVideos'] : ''; ?> videos</span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('courses_per_page')) - session('courses_per_page')) + ($courses_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('courses_per_page')) - session('courses_per_page')) + count($all_courses); ?><span class="midGray mx-1">of <?php echo $courses_count; ?></span>

                    <a href="admin/courses/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>

                    <a href="admin/courses/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page + 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_courses) < session('courses_per_page')) OR (((($page * session('courses_per_page')) - session('courses_per_page')) + count($all_courses)) == $courses_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('courses_per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('courses_per_page') == "") ? 10 : session('courses_per_page'); ?>;
var sort_by = '<?php echo (session('courses_sort') == "") ? 'courses.created_at/desc' : session('courses_sort'); ?>';
var search = '<?php echo (session('courses_search') == "") ? '' : session('courses_search'); ?>';
var order = '<?php echo (session('courses_sort') == "") ? 'courses.created_at/desc' : session('courses_sort'); ?>';
</script>
</body>
</html>