<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Accessories extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('AccessoriesModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_accessoriess'] = $this->model->query("SELECT * FROM accessories WHERE deleted_at IS NULL AND status = 0 ORDER BY sort LIMIT 0, " . session('per_page'))->getResultArray();
        $data['draft_accessoriess'] = $this->model->query("SELECT * FROM accessories WHERE deleted_at IS NULL AND status = 1")->getResultArray();

        $data['accessoriess_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Joined";
        $data['page'] = 1;

        echo view('admin/accessories/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        $data['all_accessoriess'] = $this->model->query("SELECT * FROM accessories WHERE deleted_at IS NULL AND status = 0 ORDER BY sort LIMIT " . (($page * session('per_page')) - session('per_page')) . ", " . session('per_page'))->getResultArray();
        $data['draft_accessoriess'] = $this->model->query("SELECT * FROM accessories WHERE deleted_at IS NULL AND status = 1")->getResultArray();

        $data['accessoriess_count'] = $this->model->countAllResults();
        $data['page'] = $page;

        echo view('admin/accessories/index_view', $data);
    }

    public function deleted($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $current = $this->model->query("SELECT * FROM accessories WHERE deleted_at IS NULL AND status = 1 AND id = " . $edit_id . " LIMIT 0, " . session('per_page'))   ->getResultArray();
		$data['current'] = $current[0];

        echo view('admin/accessories/deleted_view', $data);
    }
    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function edit($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		// $data['machines'] = $this->model->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['machines'] = $this->model->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['current'] = $this->model->where(['id' => $edit_id])->first();

        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/accessories');
        }

		return view('admin/accessories/edit_view', $data);
    }
    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Slide successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/accessories', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/accessories/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/accessories/' . $name, 98);
				$data['image'] = 'uploads/accessories/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/accessories', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/accessories/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/accessories/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/accessories/' . $name;
			}

            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if($data['mob_cover_image_removed'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);
            // machines
            if(isset($data['machines'])){
                $data['machine'] = implode(',', $data['machines']);
            }
            unset($data['machines']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}