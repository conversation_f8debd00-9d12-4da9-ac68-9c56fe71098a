<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Surveys extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SurveysModel');
	}

    public function index()
    {
		$this->all();
    }

    public function onboarding($page = 1)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $total_responses = $this->model->query("SELECT DISTINCT user_id FROM surveys_results WHERE deleted_at IS NULL")->getResultArray();
        $data['total'] = count($total_responses);
        // QUESTION 1
        $data['question_1'] = $this->model->query("SELECT q.*
                                            FROM (
                                                SELECT count(*) as count, 'Tone the body' as answer FROM surveys_results WHERE question_id = 1 AND survey_id = 1 AND answer = 'Tone the body'
                                                UNION
                                                SELECT count(*) as count, 'Lose weight' as answer FROM surveys_results WHERE question_id = 1 AND survey_id = 1 AND answer = 'Lose weight'
                                                UNION
                                                SELECT count(*) as count, 'Improve health' as answer FROM surveys_results WHERE question_id = 1 AND survey_id = 1 AND answer = 'Improve health'
                                                UNION
                                                SELECT count(*) as count, 'Get strong' as answer FROM surveys_results WHERE question_id = 1 AND survey_id = 1 AND answer = 'Get strong'
                                                UNION
                                                SELECT count(*) as count, 'TOTAL' as answer FROM surveys_results WHERE question_id = 1 AND survey_id = 1
                                            ) as q
                                            ORDER BY q.count DESC
                                        ")->getResultArray();
        $first1 = reset($data['question_1']);
        $data['question_1_total'] = $first1['count'];
        unset($data['question_1'][0]);

        // QUESTION 2
        $data['question_2'] = $this->model->query("SELECT q.*
                                            FROM (
                                                SELECT count(*) as count, 'Arms' as answer FROM surveys_results WHERE question_id = 2 AND survey_id = 1 AND answer = 'Arms'
                                                UNION
                                                SELECT count(*) as count, 'Abs' as answer FROM surveys_results WHERE question_id = 2 AND survey_id = 1 AND answer = 'Abs'
                                                UNION
                                                SELECT count(*) as count, 'Glute' as answer FROM surveys_results WHERE question_id = 2 AND survey_id = 1 AND answer = 'Glute'
                                                UNION
                                                SELECT count(*) as count, 'Overall body' as answer FROM surveys_results WHERE question_id = 2 AND survey_id = 1 AND answer = 'Overall body'
                                                UNION
                                                SELECT count(*) as count, 'TOTAL' as answer FROM surveys_results WHERE question_id = 2 AND survey_id = 1
                                            ) as q
                                            ORDER BY q.count DESC
                                        ")->getResultArray();
        $first2 = reset($data['question_2']);
        $data['question_2_total'] = $first2['count'];
        unset($data['question_2'][0]);

        // QUESTION 3
        $data['question_3'] = $this->model->query("SELECT q.*
                                        FROM (
                                            SELECT count(*) as count, 'Lower back' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Lower back'
                                            UNION
                                            SELECT count(*) as count, 'Neck' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Neck'
                                            UNION
                                            SELECT count(*) as count, 'Elbows' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Elbows'
                                            UNION
                                            SELECT count(*) as count, 'Wrists' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Wrists'
                                            UNION
                                            SELECT count(*) as count, 'Shoulders' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Shoulders'
                                            UNION
                                            SELECT count(*) as count, 'Hips' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Hips'
                                            UNION
                                            SELECT count(*) as count, 'Knees' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND answer = 'Knees'
                                            UNION
                                            SELECT count(*) as count, 'No' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1 AND (answer = 'No' OR answer_id = 10)
                                            UNION
                                            SELECT count(*) as count, 'TOTAL' as answer FROM surveys_results WHERE question_id = 3 AND survey_id = 1  AND answer != 'Yes'
                                        ) as q
                                        ORDER BY q.count DESC
                                        ")->getResultArray();
        $first3 = reset($data['question_3']);
        $data['question_3_total'] = $first3['count'];
        unset($data['question_3'][0]);

        // QUESTION 4
        $data['question_4'] = $this->model->query("SELECT q.*
                                        FROM (
                                            SELECT count(*) as count, 'The Micro' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'The Micro'
                                            UNION
                                            SELECT count(*) as count, 'The Mini' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'The Mini'
                                            UNION
                                            SELECT count(*) as count, 'The Mega' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'The Mega'
                                            UNION
                                            SELECT count(*) as count, 'The EVO' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'The EVO'
                                            UNION
                                            SELECT count(*) as count, 'Reformer' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'Reformer'
                                            UNION
                                            SELECT count(*) as count, 'Other' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1 AND answer = 'Other'
                                            UNION
                                            SELECT count(*) as count, 'TOTAL' as answer FROM surveys_results WHERE question_id = 4 AND survey_id = 1
                                        ) as q
                                        ORDER BY q.count DESC
                                        ")->getResultArray();
        $first4 = reset($data['question_4']);
        $data['question_4_total'] = $first4['count'];
        unset($data['question_4'][0]);

        $data['avg_age'] = $this->model->query("SELECT AVG(answer) as age FROM surveys_results WHERE question_id = 5 AND survey_id = 1 AND answer_id = 25")->getRowArray();
        $data['age'] = round((float)$data['avg_age']['age']);

        $data['avg_height'] = $this->model->query("SELECT AVG(answer) as height FROM surveys_results WHERE question_id = 5 AND survey_id = 1 AND answer_id = 23")->getRowArray();
        $data['height'] = round((float)$data['avg_height']['height']);

        $data['avg_weight'] = $this->model->query("SELECT AVG(answer) as weight FROM surveys_results WHERE question_id = 5 AND survey_id = 1 AND answer_id = 24")->getRowArray();
        $data['weight'] = round((float)$data['avg_weight']['weight']);

        $data['all_onboarding'] = $this->model->all_onboarding(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['onboarding_count'] = $this->model->all_onboarding_count();
        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/surveys/onboarding_view', $data);
    }

    public function results($id = 0)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['survey'] = $this->model->query("SELECT * FROM surveys WHERE id = " . $id . " AND deleted_at IS NULL")->getRowArray();
        $data['survey_questions'] = $this->model->query("SELECT * FROM surveys_questions WHERE survey_id = " . $id . " AND deleted_at IS NULL ORDER BY id asc")->getResultArray();
        $total_responses = $this->model->query("SELECT DISTINCT user_id FROM surveys_results WHERE survey_id = " . $id . " AND deleted_at IS NULL")->getResultArray();
        $data['total'] = count($total_responses);

        // QUESTIONS
        if(count($data['survey_questions']) > 0){
            foreach($data['survey_questions'] as $key => $question){
                if($question['type'] == 'check' OR $question['type'] == 'radio'){
                    $answers = $this->model->query("SELECT * FROM surveys_answers WHERE survey_id = " . $id . " AND question_id = " . $question['id'] . " AND deleted_at IS NULL")->getResultArray();
                    if(count($answers) > 0){
                        $q = [];
                        foreach($answers as $answer){
                            $q[] = "SELECT count(*) as count, \"{$answer['title']}\" as answer
                                    FROM surveys_results                                    
                                    WHERE question_id = \"{$question['id']}\" 
                                    AND survey_id = $id 
                                    AND answer = \"{$answer['title']}\"
                            ";
                        }
                        $qq = implode(" UNION ", $q);
                        $data['survey_questions'][$key]['result'] = $this->model->query("SELECT q.*
                                                                            FROM (" . $qq . ") as q
                                                                            ORDER BY q.count DESC
                                                                        ")->getResultArray();
                        if(is_array($data['survey_questions'][$key]['result']) AND count($data['survey_questions'][$key]['result']) > 0){
                            $first1 = reset($data['survey_questions'][$key]['result']);
                            $countt = $this->model->query("SELECT count(*) as count
                                                                FROM surveys_results                                    
                                                                WHERE question_id = " . $question['id'] . " 
                                                                AND survey_id = " . $id . "
                            ")->getRowArray();
                            $data['survey_questions'][$key]['result_total'] = $countt['count'];
                            // unset($data['survey_questions'][$key]['result'][0]);
                        }
                    }
                }else{
                    $data['survey_questions'][$key]['result'] = $this->model->query("SELECT surveys_results.id, surveys_results.answer, CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name FROM surveys_results LEFT JOIN subscribers ON subscribers.id = surveys_results.user_id WHERE surveys_results.survey_id = " . $id . " AND surveys_results.question_id = " . $question['id'] . " AND surveys_results.deleted_at IS NULL")->getResultArray();
                }
            }
        }

        // echo '<pre>';
        // print_r($data);
        // die();
        
        echo view('admin/surveys/results_view', $data);
    }

    public function all($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_surveys'] = $this->model->all_surveys(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['surveys_count'] = $this->model->where('id != 1')->countAllResults();
        // $data['surveys_count'] = $this->model->where(['id >' => 1])->countAllResults();

        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/surveys/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_surveys'] = $this->model->all_surveys(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['surveys_count'] = $this->model->where('id != 1')->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/surveys/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_surveys'] = $this->model->like('title', $data['search_term'])->findAll();
        $data['all_surveys'] = $this->model->query("SELECT  surveys.*, COALESCE(x.cnt,0) AS classes_count
                                        FROM surveys
                                        LEFT JOIN (SELECT teacher, count(*) as cnt FROM classes GROUP BY teacher) x on x.teacher = surveys.id
                                        WHERE surveys.deleted_at IS NULL
                                        AND id != 1
                                        AND surveys.title LIKE '%" . $data['search_term'] . "%'
                                        ORDER BY surveys.created_at desc")->getResultArray();
        $data['surveys_count'] = $this->model->like('title', $data['search_term'])->where('id != 1')->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/surveys/index_view', $data);
    }

    public function sort_by($type = 'surveys.firstname', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_surveys'] = $this->model->all_surveys(0, session('per_page'), '', $type . " " . $direction);
        $data['surveys_count'] = $this->model->where('id != 1')->countAllResults();
        $types = array(
            "surveys.created_atdesc" => "Date Joined",
            "surveys.firstnameasc" => "Ascending",
            "surveys.firstnamedesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/surveys/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $SurveysQuestionsModel = model('SurveysQuestionsModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['current'] = $SurveysQuestionsModel->single_survey($edit_id);

        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/surveys');
        };

		return view('admin/surveys/edit_view', $data);
    }
    public function save()
    {
        $SurveysQuestionsModel = model('SurveysQuestionsModel');
        $SurveysAnswersModel = model('SurveysAnswersModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        // echo '<pre>';
        // var_dump($data);
        // die();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Survey successfully saved';

            $survey_data = [
                'id' => $data['id'],
                'status' => (isset($data['id']) AND $data['id'] != '') ? $data['status'] : 1,
                'title' => $data['title']
            ];
			$response['success'] = $this->model->save($survey_data);
			$response['survey_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
			$response['inserted_id'] = $response['survey_id'];

            if(isset($data['questions']) AND is_array($data['questions']) AND count($data['questions']) > 0){
                foreach ($data['questions'] as $key => $question) {
                    $question_data = [
                        'id' => (isset($question['id']) AND $question['id'] != '') ? $question['id'] : '',
                        'survey_id' => $response['survey_id'],
                        'title' => $question['title'],
                        'mandatory' => $question['mandatory'],
                        'type' => $question['type'],
                    ];
                    if(isset($question['id']) AND !is_numeric($question['id'])){
                        unset($question_data['id']);
                    };
                    $response['result' . '_success'] = $SurveysQuestionsModel->save($question_data);
                    $response['question_id'] = (isset($question['id']) AND $question['id'] > 0) ? $question['id'] : $SurveysQuestionsModel->getInsertID();

                    if(isset($question['answers']) AND is_array($question['answers']) AND count($question['answers']) > 0){
                        foreach ($question['answers'] as $key2 => $answer) {
                            if(isset($answer['title']) AND $answer['title'] != ''){
                                $answer_data = [
                                    'id' => (isset($answer['id']) AND $answer['id'] != '') ? $answer['id'] : '',
                                    'question_id' => $response['question_id'],
                                    'survey_id' => $response['survey_id'],
                                    'title' => $answer['title'],
                                    'type' => $question['type'],
                                ];
                                if(isset($answer['id']) AND !is_numeric($answer['id'])){
                                    unset($answer_data['id']);
                                };
                                $response['answer_' . $key2 . '_success'] = $SurveysAnswersModel->save($answer_data);
                                $response['answer_id'] = (isset($answer['id']) AND $answer['id'] > 0) ? $answer['id'] : $SurveysAnswersModel->getInsertID();
                            }
                        }
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function save_status()
    {
        $data = $this->request->getPost();

        // $this->model->where('status', 0)->update(['status' => 1]);
        $this->model->set(['status' => 1])->where('status', 0)->update();

        $save_data = [
            'id' => $data['id'],
            'status' => $data['status'] == 1 ? 0 : 1
        ];
        $response['success'] = $this->model->save($save_data);

		return $this->respond($response);
    }
    public function remove_answer($id = 0)
    {
        $SurveysAnswersModel = model('SurveysAnswersModel');
        $data = $this->request->getPost();

        if($id != 0){
            $response['success'] = $SurveysAnswersModel->delete($id, TRUE);
        }else{
            $response['msg'] = 'NO ID';
            $response['success'] = FALSE;
        }

		return $this->respond($response);
    }
}
