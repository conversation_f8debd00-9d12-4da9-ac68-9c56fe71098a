<?php namespace App\Controllers;

require_once 'dompdf/autoload.inc.php';
use Dompdf\Dompdf;
use Dompdf\Options;

use CodeIgniter\Controller;
use App\Models\StripeModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Account extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
		$this->prices = array(
			'Monthly Subscription' => $_ENV['price_month'],
			'Annual Subscription' => $_ENV['price_year'],
			'Weekly Subscription' => $_ENV['price_week'],
		);
	}
    public function index()
    {
        $data['logged_user'] = $this->user;
		$teacherModel = model('TeachersModel');
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();
        $data['current']['teacher_profile'] = $this->teacher_info(session('teacher'));

		$data['current']['seo_title'] = 'Classes | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Classes';
		$data['current']['seo_keywords'] = 'Lagree On Demand Classes';

		echo view('account/index_view', $data);
    }
    public function dashboard()
    {
		$stripe_model = new StripeModel();
        $classesModel = model('ClassesModel');
        $collections_model = model('CollectionsModel');
        $liveevents_model = model('LiveEventsModel');
        $teachers_model = model('TeachersModel');
        $exercises_model = model('ExercisesModel');
        $SubscribersClasses = model('SubscribersClasses');
		$FeaturedVideosModel = model('FeaturedVideosModel');

        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
        $data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $featured_videos = $FeaturedVideosModel->findAll();
        foreach($featured_videos as $single){
            if($single['type'] == "classes"){
                $data['featured_videos'][] = $this->class_info($single['class_id']);
            }else{
                $data['featured_videos'][] = $this->video_info($single['class_id']);
            }
        }

        $data['all_classes'] = $classesModel->countAllResults();
        $data['all_collections'] = $collections_model->countAllResults();
        $data['all_liveevents'] = $liveevents_model->countAllResults();
        $data['all_teachers'] = $teachers_model->countAllResults();
        $data['all_exercises'] = $exercises_model->countAllResults();

        $my_views = $SubscribersClasses->query("SELECT
                                                            (
                                                                SELECT COUNT(id) as total
                                                                    FROM classes_views
                                                                    WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                            ) + (
                                                                SELECT COUNT(id) as total
                                                                    FROM exercises_views
                                                                    WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                            ) as total")->getRowArray();

        $data['my_views'] = $my_views['total'];
        // ------------------------------------
        $my_views_last_month = $SubscribersClasses->query("SELECT
                                                                    (
                                                                        SELECT COUNT(id) as total
                                                                            FROM classes_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                                                                    ) + (
                                                                        SELECT COUNT(id) as total
                                                                            FROM exercises_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                                                                    ) as total")->getRowArray();
        $data['my_views_last_month'] = $my_views_last_month['total'];
        // ------------------------------------
        $my_views_this_week = $SubscribersClasses->query("SELECT
                                                                    (
                                                                        SELECT COUNT(id) as total
                                                                            FROM classes_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
                                                                    ) + (
                                                                        SELECT COUNT(id) as total
                                                                            FROM exercises_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
                                                                    ) as total")->getRowArray();
        $data['my_views_this_week'] = $my_views_this_week['total'];
        // ------------------------------------
        $my_views_today = $SubscribersClasses->query("SELECT
                                                                    (
                                                                        SELECT COUNT(id) as total
                                                                            FROM classes_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 0 DAY)
                                                                    ) + (
                                                                        SELECT COUNT(id) as total
                                                                            FROM exercises_views
                                                                            WHERE user_id = " . ((NULL === session('user') OR session('user') == 0) ? 0 : session('user')) . "
                                                                            AND date >= DATE_SUB(CURDATE(), INTERVAL 0 DAY)
                                                                    ) as total")->getRowArray();

        $data['my_views_today'] = $my_views_today['total'];
        // ------------------------------------
		$data['subscription'] = !empty($data['logged_user']['stripe_subscription']) ? $stripe_model->retrieve_subscription($data['logged_user']['stripe_subscription']) : NULL;

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Dashboard | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Dashboard';
		$data['current']['seo_keywords'] = 'Lagree On Demand Dashboard';

		return view('account/dashboard-new_view', $data);
    }
    public function payments()
    {
		$stripe_model = new StripeModel();
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

        if(session('teacher') != NULL){
            return redirect()->to('/account/dashboard');
        }

        $data['nums'] = create_session_nums();
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();
		$data['subscription'] = !empty($data['logged_user']['stripe_subscription']) ? $stripe_model->retrieve_subscription($data['logged_user']['stripe_subscription']) : NULL;
        if($data['subscription'] != NULL AND $data['subscription']['success']){
            $plans = [
                'week' => 'Weekly Plan',
                'month' => 'Monthly Plan',
                'year' => 'Annual Plan'
            ];
            $plans_sub = [
                'week' => 'Weekly Subscription',
                'month' => 'Monthly Subscription',
                'year' => 'Annual Subscription'
            ];
            $data['subscription']['plan_name'] = $plans[$data["subscription"]["subscription"]['plan']['interval']];
            $data['subscription']['plan_sub'] = $plans_sub[$data["subscription"]["subscription"]['plan']['interval']];
            $data['plan'] = $data["subscription"]["subscription"]['plan']['interval'];
        }else{
            $data['subscription']['plan_name'] = 'none';
            $data['subscription']['plan_sub'] = 'none';
            $data['plan'] = 'none';
        }

		$data['sources'] = $stripe_model->list_sources($this->user['stripe_customer']);
		$data['cards'] = $stripe_model->list_payment_methods($this->user['stripe_customer']);
		$data['customer'] = $stripe_model->get_customer($this->user['stripe_customer']);
		$data['invoices'] = $stripe_model->customer_invoices($this->user['stripe_customer']);

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Payments | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Payments';
		$data['current']['seo_keywords'] = 'Lagree On Demand Payments';

        // plans: week, month, year
        // status: 
        //      incomplete  - if the initial payment attempt fails
        //      incomplete_expired - If the first invoice is not paid within 23 hours, the subscription transitions to incomplete_expired
        //      trialing    - A subscription that is currently in a trial period is trialing and moves to active when the trial period is over
        //      active      - ACTIVE, all good (but can have cancel_at_period_end set to true and cancel_at is not null)
        //      past_due    - If subscription collection_method=charge_automatically, it becomes past_due when payment is required but cannot be paid (due to failed payment or waiting additional user actions). Once Stripe has exhausted all payment retry attempts, the subscription will become canceled or unpaid
        //      canceled    - by user or by Stripe
        //      unpaid      - 
        //      paused      - A subscription can only enter a paused status when a trial ends without a payment method

        $status = NULL;
        $data['status'] = NULL;
        if(isset($data['subscription']) AND isset($data['subscription']['subscription'])){
            if(isset($data['subscription']['subscription']['status'])){
                $status = $data['subscription']['subscription']['status'];
                $data['status'] = $data['subscription']['subscription']['status'];
                session()->set('subscription', $status);
            }
            if($status == 'active' AND $data['subscription']['subscription']['cancel_at_period_end'] == FALSE){
                return view('account/payments-active_view', $data);
            }else if($status == 'active' AND $data['subscription']['subscription']['cancel_at_period_end'] == TRUE){
                return view('account/payments-canceled-active_view', $data);
            }else if($status == 'canceled'){
                return view('account/payments-canceled_view', $data);
            // }else if($status == 'incomplete'){
            //     return view('account/payments-incomplete-active_view', $data);
            // }else if($status == 'incomplete_expired'){
            //     return view('account/payments-none_view', $data);
            // }else if($status == 'past_due'){
            //     return view('account/payments-past_due-active_view', $data);
            }else{
                return view('account/payments-none_view', $data);
            }
        }else{
            return view('account/payments-none_view', $data);
        }
    }
    public function create_subscribe()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$validation =  \Config\Services::validation();
		$rules['subscription_type']    = [
				'label'  => 'Subscription type',
				'rules'  => 'required',
			];
		$rules['card.name']    = [
				'label'  => 'Name on card',
				'rules'  => 'required',
			];
		$rules['card.number']    = [
				'label'  => 'Card number',
				'rules'  => 'required|numeric',
			];
		$rules['card.exp_month']    = [
				'label'  => 'Month',
				'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
			];
		$rules['card.exp_year']    = [
				'label'  => 'Year',
				'rules'  => 'required|numeric|exact_length[2]',
			];
		$rules['card.cvc']    = [
				'label'  => 'CVC',
				'rules'  => 'required|numeric',
			];
		$messages = $this->model->validationMessages;
		$data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
			$token = $stripe_model->create_card_token($data['card']);
			if (!$token['success'])
			{
				return $this->respond($token);
			}
			$customer = $stripe_model->add_card_to_customer($this->user['stripe_customer'], $token['token']);
			if (!$customer['success'])
			{
				return $this->respond($customer);
			}
            $coupon = '';
            // $coupon = $data['coupon'];

            // Use safe subscription creation to prevent duplicates
			$subscription = $stripe_model->create_subscription_safe($this->user['stripe_customer'], $this->prices[$data['subscription_type']], $coupon, 'account/create_subscribe - create subscription from account page after simple registration (doesn\'t exist any more)', false);
			if (!$subscription['success'])
			{
				return $this->respond($subscription);
			}
            session()->set('subscription', $subscription['subscription_info']['status']);
            session()->set('subscription_plan', $subscription["subscription_info"]['plan']['interval']);
            if($subscription['subscription_info']['status'] == 'active'){
                //email user
                $to = $this->user['email'];
                $subject = 'Subscription Confirmed';
                $template_data = [
                    'classes_link' => base_url() . '/classes/',
                    'contact_link' => base_url() . '/contact-us/'
                ];
                $template = 'front/email_templates/subscribtion-confirmation';
                $response['email_to_user'] = $this->email_model->send_template($to, FALSE, $subject, $template_data, $template);
            }
			$data['id'] = $this->user['id'];
			$data['stripe_subscription'] = $subscription['subscription'];
			$response['stripe_subscription_api'] = $subscription;
			$response['message'] = 'Congratulations! <br>You are now subscribed to Lagree On Demand!';
			$response['success'] = $this->model->save($data);
			$response['return_url'] = site_url('account/payments');
		}
		return $this->respond($response);
    }
    public function delete_card()
    {
		$stripe_model = new StripeModel();
        $request = service('request');
		$data = $this->request->getPost();

        $response['success'] = FALSE;
        if($data['card_id'] != ''){
            $response = $stripe_model->delete_card($this->user['stripe_customer'], $data['card_id']);
            if($response['success']){
                $response['message'] = "Card deleted successfully";
            }
        }

        return $this->respond($response);
    }
    public function resubscribe()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$validation =  \Config\Services::validation();
		$rules['subscription_type']    = [
            'label'  => 'Subscription type',
            'rules'  => 'required',
        ];
		$messages = $this->model->validationMessages;
		$data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
			// $token = $stripe_model->create_card_token($data['card']);
			// if (!$token['success'])
			// {
			// 	return $this->respond($token);
			// }
			// $customer = $stripe_model->add_card_to_customer($this->user['stripe_customer'], $token['token']);
			// if (!$customer['success'])
			// {
			// 	return $this->respond($customer);
			// }
            $coupon = '';
            // $coupon = $data['coupon'];

            // First check if customer already has active subscriptions
            $existing_subs = $stripe_model->get_customer_active_subscriptions($this->user['stripe_customer']);

            if ($existing_subs['success'] && $existing_subs['count'] > 0) {
                // Customer already has active subscription(s), use the most recent one
                $active_subscription = $existing_subs['subscriptions'][0];

                $subscription = [
                    'success' => TRUE,
                    'subscription' => $active_subscription->id,
                    'subscription_info' => $active_subscription,
                    'message' => 'Using existing active subscription'
                ];

                log_message('info', 'Resubscribe: Found existing active subscription ' . $active_subscription->id . ' for customer ' . $this->user['stripe_customer']);
            } else {
                // No active subscriptions, safe to create new one
                $subscription = $stripe_model->create_subscription_safe($this->user['stripe_customer'], $this->prices[$data['subscription_type']], $coupon, 'account/resubscribe - after subscription expired, create new one', false);
            }
			if (!$subscription['success'])
			{
				return $this->respond($subscription);
			}
            session()->set('subscription', $subscription['subscription_info']['status']);
            session()->set('subscription_plan', $subscription["subscription_info"]['plan']['interval']);
            if($subscription['subscription_info']['status'] == 'active'){
                //email user
                $to = $this->user['email'];
                $subject = 'Subscription Confirmed';
                $template_data = [
                    'classes_link' => base_url() . '/classes/',
                    'contact_link' => base_url() . '/contact-us/'
                ];
                $template = 'front/email_templates/subscribtion-confirmation';
                $response['email_to_user'] = $this->email_model->send_template($to, FALSE, $subject, $template_data, $template);
            }
            $plans = [
                'week' => 'Weekly Plan',
                'month' => 'Monthly Plan',
                'year' => 'Annual Plan'
            ];
            $new_plan = $plans[$subscription['subscription_info']['plan']['interval']];
			$save_data = [
                'id' => $this->user['id'],
                'stripe_subscription' => $subscription['subscription'],
                'subscription_type' => $new_plan
            ];
			$response['stripe_subscription_api'] = $subscription;
			$response['message'] = 'Congratulations! <br>You are now subscribed to Lagree On Demand!';
			$response['success'] = $this->model->save($save_data);
			$response['return_url'] = site_url('account/payments');
		}
		return $this->respond($response);
    }
    public function add_card()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$validation =  \Config\Services::validation();
		$rules['card.name']    = [
				'label'  => 'Name on card',
				'rules'  => 'required',
			];
		$rules['card.number']    = [
				'label'  => 'Card number',
				'rules'  => 'required|numeric',
			];
		$rules['card.exp_month']    = [
				'label'  => 'Month',
				'rules'  => 'required|numeric|exact_length[2]|greater_than_equal_to[1]|less_than_equal_to[12]',
			];
		$rules['card.exp_year']    = [
				'label'  => 'Year',
				'rules'  => 'required|numeric|exact_length[2]',
			];
		$rules['card.cvc']    = [
				'label'  => 'CVC',
				'rules'  => 'required|numeric',
			];
		$messages = $this->model->validationMessages;
		$data = $this->request->getPost();
		$response['data'] = $data;
		$response['rules'] = $rules;

		$validation->reset();
		$validation->setRules($rules);

		if (!$validation->run($data))
		{
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
			return $this->respond($response);
		}
		else
		{
			$token = $stripe_model->create_card_token($data['card']);
			if (!$token['success'])
			{
				return $this->respond($token);
			}
			$response['token'] = $token;
			$customer = $stripe_model->new_card_to_customer($this->user['stripe_customer'], $token['token']);
			if (!$customer['success'])
			{
				return $this->respond($customer);
			}
			$response['customer'] = $customer;
			$response['message'] = 'New credit card added';
			$response['success'] = TRUE;
			$response['return_url'] = site_url('account/payments');
		}
		return $this->respond($response);
    }
    public function cancel_subscription()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$data = $this->request->getPost();
		$response['data'] = $data;
		$subscription = $stripe_model->cancel_subscription($data['stripe_subscription']);
		if (!$subscription['success'])
		{
			return $this->respond($subscription);
        }

        // echo '<pre>';
        // print_r($subscription);
        // die();
        
        $response['subscription'] = !empty($this->user['stripe_subscription']) ? $stripe_model->retrieve_subscription($this->user['stripe_subscription']) : NULL;
        if($response['subscription'] != NULL){
            session()->set('subscription', $subscription['subscription']['status']);
            $plans = [
                'week' => 'Weekly Subscription',
                'month' => 'Monthly Subscription',
                'year' => 'Annual Subscription'
            ];
            $new_plan = $plans[$subscription['subscription']['plan']['interval']];
            $save_data = [
                'id' => $this->user['id'],
                'stripe_subscription' => $subscription['subscription']['id'],
                'subscription_type' => $new_plan
            ];
            $this->model->save($save_data);
        }

		$data['id'] = $this->user['id'];
		// $data['stripe_subscription'] = NULL;
		// $data['subscription_type'] = NULL;

		$response['message'] = 'Subscription cancelled';
		$response['success'] = $subscription['success'];
		$response['return_url'] = site_url('account/payments');
		return $this->respond($response);
    }
    public function change_subscription()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$data = $this->request->getPost();
		$response['data'] = $data;
		$subscription = !empty($this->user['stripe_subscription']) ? $stripe_model->retrieve_subscription($this->user['stripe_subscription']) : NULL;
		$response['subscription'] = $subscription;
		$response['coupon'] = '';
		// $response['coupon'] = $data['coupon'];

		if (!empty($subscription) AND isset($subscription['subscription']['items']['data'][0]['id']))
		{
			$response['changed'] = $stripe_model->change_subscription($this->user['stripe_subscription'], $subscription['subscription']['items']['data'][0]['id'], $this->prices[$data['subscription_type']], $response['coupon']);
			$data['id'] = $this->user['id'];
			$response['message'] = 'Subscription plan changed';
            session()->set('subscription', 'active');
            $plans = [
                'week' => 'Weekly Plan',
                'month' => 'Monthly Plan',
                'year' => 'Annual Plan'
            ];
            $new_plan = $plans[$subscription['subscription']['plan']['interval']];
			$save_data = [
                'id' => $this->user['id'],
                'subscription_type' => $new_plan
            ];

			$response['success'] = $this->model->save($save_data);
		}
		return $this->respond($response);
    }
    public function set_new_default_method()
    {
		$stripe_model = new StripeModel();
		$request = service('request');
		$data = $this->request->getPost();
		$response['data'] = $data;
		$customer = $stripe_model->update_customer($this->user['stripe_customer'], $data);
		if (!$customer['success'])
		{
			return $this->respond($customer);
		}
		$response['success'] = TRUE;
		$response['message'] = 'New default card is set';
		$response['customer'] = $customer;
		$response['return_url'] = site_url('account/payments');
		return $this->respond($response);
    }
    public function payments_history()
    {
		$stripe_model = new StripeModel();
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(session('teacher') != NULL){
            return redirect()->to('/account/dashboard');
        }

        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();
		$data['subscription'] = !empty($data['logged_user']['stripe_subscription']) ? $stripe_model->retrieve_subscription($data['logged_user']['stripe_subscription']) : NULL;
		// $data['sources'] = $stripe_model->list_sources($this->user['stripe_customer']);
		// $data['customer'] = $stripe_model->get_customer($this->user['stripe_customer']);
		$data['invoices'] = $stripe_model->customer_invoices($this->user['stripe_customer']);
		$data['invoice'] = $stripe_model->customer_single_invoice("in_1KLCGOL6EaNAw2aw5JFlOGVV");

        $data['nums'] = create_session_nums();
        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Payments | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Payments';
		$data['current']['seo_keywords'] = 'Lagree On Demand Payments';

		return view('account/payments-history_view', $data);
    }
    public function payments_history_download($invoice_id = 0, $to_file = 0)
    {
		$stripe_model = new StripeModel();
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['invoice'] = $stripe_model->customer_single_invoice($invoice_id);
        if(isset($data['invoice']['invoice']['charge']) AND $data['invoice']['invoice']['charge'] !=''){
            $data['charge'] = $stripe_model->charge_retrieve($data['invoice']['invoice']['charge']);
        }

        // DomPDF
        // return view('front/email_templates/receipt.php', $data);
        $tmp_pdf = view('front/email_templates/receipt.php', $data);

        $options = new Options();
        $options->setIsRemoteEnabled(true);
        $dompdf = new Dompdf($options);
		$dompdf->loadHtml($tmp_pdf, 'UTF-8');
        $dompdf->setPaper('A4', 'portrait');
		// Render the HTML as PDF
		$dompdf->render();
		if($to_file == 1){
			// Output the generated PDF to Browser
			$dompdf->stream('LagreeOD receipt - ' . $data['invoice']['invoice']['number'] . '.pdf');
			// Output the generated PDF to file on server
		    $output = $dompdf->output();
			file_put_contents('./invoices/invoice_' . $data['invoice']['invoice']['number'] . '.pdf', $output);
		}else{
			// Output the generated PDF to file on server
		    $output = $dompdf->output();
			file_put_contents('./invoices/invoice_' . $data['invoice']['invoice']['number'] . '.pdf', $output);
		}
//
    }
    public function connect()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['nums'] = create_session_nums();
        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Connect | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Connect';
		$data['current']['seo_keywords'] = 'Lagree On Demand Connect';

		return view('account/connect_view', $data);
    }
    public function devices()
    {
		$DevicesModel = model('DevicesModel');
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
		$data['deleted_device'] = $this->model->query("SELECT * FROM devices WHERE deleted_at IS NOT NULL AND user_id = " . $data['logged_user']['id'] . " ORDER BY deleted_at desc LIMIT 0, 1")->getRowArray();
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();
        if(!empty($data['deleted_device']) AND count($data['deleted_device']) > 0){
            $date1 = new Time();
            $date2 = new Time($data['deleted_device']['deleted_at']);
            $difference = $date1->diff($date2);
            $data['diff'] = $difference->days;
        }else{
            $data['diff'] = "NO DELETED DEVICES";
        }

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Connect | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Connect';
		$data['current']['seo_keywords'] = 'Lagree On Demand Connect';

        $data['devices'] = $DevicesModel->user_devices($data['logged_user']['id']);

		return view('account/devices_view', $data);
    }
    public function fb_id($fb_id = NULL)
    {
		$response['fb_id'] = $fb_id;
		$response['success'] = TRUE;
		$data['id'] = $this->user['id'];
		$data['fb_id'] = $fb_id;
		$response['success'] = $this->model->save($data);
		return $this->respond($response);
    }
    public function google_id($google_id = NULL)
    {
		$response['google_id'] = $google_id;
		$response['success'] = TRUE;
		$response['id'] = $this->user['id'];
		$data['id'] = $this->user['id'];
		$data['google_id'] = $google_id;
		$response['success'] = $this->model->save($data);
		return $this->respond($response);
    }
    public function google_token()
    {
		$request = service('request');
		$data = $this->request->getPost();
		$response['data'] = $data;
		$data['id'] = $this->user['id'];
		$response['success'] = $this->model->save($data);
		return $this->respond($response);
    }
    public function save_notifications()
    {
		$request = service('request');
		$data = $this->request->getPost();
		$response['data'] = $data;
		$data['id'] = $this->user['id'];
        $data[$data['field_name']] = $data['field_val'];

		$response['success'] = $this->model->save($data);
		return $this->respond($response);
    }
    public function save_profile()
    {
        $TeachersModel = model('TeachersModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
        $data['user_data'] = $data['user'];
        if(isset($data['teacher'])){
            $data['teacher_data'] = $data['teacher'];
        }else{
            $data['teacher_data'] = NULL;
        }

        if (isset($data['user_data']['password']) AND $data['user_data']['password'] <> ''){
            $data['user_data']['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['user_data']['password']))));
		}else{
            $rules['password'] = "if_exist";
            unset($data['user_data']['password']);
		}

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data['user_data'])){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Profile successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['user']['image']) AND $files['user']['image']->isValid()){
				$file = $files['user']['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/subscribers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/subscribers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/subscribers/' . $name);
				$data['user_data']['image'] = 'uploads/subscribers/' . $name;
			}
			if (isset($files['teacher']['image']) AND $files['teacher']['image']->isValid()){
				$file = $files['teacher']['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/teachers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/teachers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/teachers/' . $name);
				$data['teacher_data']['image'] = 'uploads/teachers/' . $name;
			}
			if (isset($files['teacher']['cover_image']) AND $files['teacher']['cover_image']->isValid()){
				$file = $files['teacher']['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/teachers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/teachers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/teachers/' . $name);
				$data['teacher_data']['cover_image'] = 'uploads/teachers/' . $name;
			}
            if($data['user_data']['image_removed'] == 1){
                $data['user_data']['image'] = "";
            }
            if($data['teacher_data']['cover_image_removed'] == 1){
                $data['teacher_data']['cover_image'] = "";
            }
            if($data['teacher_data']['mob_cover_image_removed'] == 1){
                $data['teacher_data']['image'] = "";
            }
            unset($data['user_data']['image_removed']);
			$response['success'] = $this->model->save($data['user_data']);
			$response['inserted_id'] = $data['user_data']['id'] > 0 ? $data['user_data']['id'] : $this->model->getInsertID();

            if($data['teacher_data'] != NULL){
                $response['data']['teacher'] = $data['teacher_data'];
                $TeachersModel->save($data['teacher_data']);
            }
		}
		$response['data']['user'] = $data['user_data'];
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function support()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['nums'] = create_session_nums();
        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Support | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand support';
		$data['current']['seo_keywords'] = 'Lagree On Demand support';

		return view('account/support_view', $data);
    }
    public function support_form() {

        $email_model = model('EmailModel');
        $request = service('request');
        $data = $request->getPost();

        $to = '<EMAIL>';
        $reply = $data['email'];
        $subject = 'Message from ' . $data['fullname'] . ' Lagree On Demand';
        $data = [
            'subject' => $data['subject'],
            'message' => $data['message'],
            'fullname' => $data['fullname'],
            'email' => $data['email']
        ];
        $template = 'front/email_templates/support-form';
        $response = $email_model->send_template($to, $reply, $subject, $data, $template);

        return $this->respond($response);

    }
    public function notifications()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['nums'] = create_session_nums();
        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Notifications | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Notifications';
		$data['current']['seo_keywords'] = 'Lagree On Demand Notifications';

		return view('account/notifications_view', $data);
    }
    public function favs()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }else{
            return redirect()->to('/account/playlists');
        }
		$data['all_favs'] = $this->model->my_favs($data['logged_user']['id']);
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['nums'] = create_session_nums();
        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'My favs | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand My favs';
		$data['current']['seo_keywords'] = 'Lagree On Demand My favs';

		return view('account/favs_view', $data);
    }
    public function bought_classes()
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
		$data['all_bought'] = $this->model->bought_classes($data['logged_user']['id']);
		$data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'My Bought Classes | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand My Bought Classes';
		$data['current']['seo_keywords'] = 'Lagree On Demand My Bought Classes';

		return view('account/bought-classes_view', $data);
    }
    public function class_history()
    {
        $pager = service('pager');

        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
		$data['count_watched'] = $this->model->count_watched_classes($data['logged_user']['id']);

        $page    = (int) ($this->request->getGet('page') ?? 1);
        $perPage = 10;
        $total   = count($data['count_watched']);

		$data['all_watched'] = $this->model->watched_classes($data['logged_user']['id'], $page);
        // Call makeLinks() to make pagination links.
        $pager_links = $pager->makeLinks($page, $perPage, $total);

        $data['pager_links'] = $pager_links;

        $data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Watch History | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Watch History';
		$data['current']['seo_keywords'] = 'Lagree On Demand Watch History';

		return view('account/watch-history_view', $data);
    }
    public function watch_later()
    {
        $pager = service('pager');

        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $data['nums'] = create_session_nums();
        
        $page    = (int) ($this->request->getGet('page') ?? 1);
		$data['all_watch_later'] = $this->model->watch_later($data['logged_user']['id'], $page);
        $perPage = 10;
        $total   = $data['all_watch_later']['count_all'];

        // Call makeLinks() to make pagination links.
        $pager_links = $pager->makeLinks($page, $perPage, $total);

        $data['pager_links'] = $pager_links;

        $data['current'] = $this->model->where(['id' => $data['logged_user']['id']])->first();

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'Watch Later | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Watch Later';
		$data['current']['seo_keywords'] = 'Lagree On Demand Watch Later';

		return view('account/watch-later_view', $data);
    }
    public function playlists()
    {
		$playlistsModel = model('PlaylistsModel');
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

        $data['nums'] = create_session_nums();
        $data['all_playlists'] = $playlistsModel->get_all_my_playlists($data['logged_user']['id']);

        $data['current']['image'] = base_url() . '/images/classes1.jpg';
		$data['current']['seo_title'] = 'My Playlists | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand My Playlists';
		$data['current']['seo_keywords'] = 'Lagree On Demand My Playlists';

		return view('account/my_playlists_view', $data);
    }
    public function watch_later_remove()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $data = $this->request->getPost();
        
        $response['success'] = $SubscribersFavs_model->delete($data['id']);
        $response['errors'] = $SubscribersFavs_model->errors();

		return $this->respond($response);
    }
}