<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="container800">
            <div class="row">
                <div class="col-12">
                    <div class="flex aic jcl flex-column-mob">
                        <span class="avatar150 mr-4 mb-mob-3 mr-mob-0">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column aic-mob">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 mt-1 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="py-0">
        <div class="container800">
            <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
            <div class="row">
                <div class="col-12">
                    <div class="py-5 bottom-border flex aic jcsb">
                        <h2 class="f-18 flex aic jcsb mob-w100 mb-mob-0 line-height-small">
                            MY CLASSES
                        </h2>
                        <div class="dropdown flex aic">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="mb-1 f-14 semibold py-3 bottom-border flex aic jcsb">
                        <p class="f-14 text-uppercase line-height-small"><?php echo count($active_classes); ?> Active <?php echo count($active_classes) == 1 ? 'Class' : 'Classes'; ?></p>
                        <a href="account/class" class="btn black-bg white mt-mob-0">UPLOAD CLASS</a>
                    </div>
                    <div class="table my-6">
<?php
if(count($active_classes) > 0){
    foreach($active_classes as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic mb-5">
                                <a href="classes/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container light mr-3 mb-0" target="_blank">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid class-item-img" style="max-width: 160px;max-height: 90px;width: 160px;height: 90px;object-fit: cover;<?php echo($single['status'] == 1) ? 'opacity: 0.3 !important' : ''; ?>" />
                                    <span class="play-button"><span></span></span>
                                </a>
                                <div class="flex flex-column">
                                    <a href="classes/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="most-title f-14 semibold mb-1 line-height-small" target="_blank"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-12 line-height-small">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    <div class="row-actions f-1 red line-height-small">
                                        <a href="account/class/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-12 pr-2">
                                    <?php if($single['status'] == 1){ ?>
                                        <span class="btn btn-xs red-bg white f-1">Draft</span>
                                    <?php }else{ ?>
                                        <span class="most-title earn-title">Earnings: <br class="mobile">$<?php echo $single['classEarned']; ?></span>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
<?php
    }
}
?>
                    </div>
<?php
if(count($pending_classes) > 0){
?>
                    <p class="top-border text-uppercase py-4 desktop semibold">AWAITING APPROVAL/REVISION</p>
                    <div class="table my-6">
<?php
    foreach($pending_classes as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic mb-5">
                                <span class="video-container light mr-3" style="opacity: 0.3">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid class-item-img" style="max-width: 160px;max-height: 90px;width: 160px;height: 90px;object-fit: cover;<?php echo($single['status'] == 1) ? 'opacity: 0.3 !important' : ''; ?>" />
                                    <span class="play-button"><span></span></span>
                                </span>
                                <div class="flex flex-column">
                                    <p class="most-title f-14 semibold mb-05 line-height-small"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></p>
                                    <span class="midGray f-12 line-height-small">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    <div class="row-actions f-1 midGray line-height-small">
                                        <a href="account/class/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-14 pr-2">
                                    <?php if($single['status'] == 2){ ?>
                                        <span class="btn yellow-bg white f-12" style="text-transform: none !important;min-height: 22px;padding: 0 7px;font-size: 12px !important;line-height: 1.2 !important;font-weight: 600 !important;">Pending <br class="mobile">Approval</span>
                                    <?php }else if($single['status'] == 3){ ?>
                                        <div class="flex aic">
                                            <span class="btn darkRed-bg white f-12 mr-1" style="text-transform: none !important;min-height: 22px;padding: 0 7px;font-size: 12px !important;line-height: 1.2 !important;font-weight: 600 !important;">Rejected</span>
                                            <span class="btn btn-badge btn-border btn-round" style="width: 40px !important;height: 40px !important;min-height: 40px !important;cursor: pointer;" data-popup="reason-popup" data-title="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" data-reason="<?php echo (isset($single['reason']) AND $single['reason'] != '') ? $single['reason'] : ''; ?>"><img src="images/reason-icon.svg" alt="" class="img-fluid" /></span>
                                        </div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
<?php
    }
?>
                    </div>
<?php
}
?>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/payments_view.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
$('.check-code').on('click', function(){
    var code = $('.subscribe-code').val();
    var button = $(this);
    button.addClass('btn--loading');
    if(code == ''){
        app_msg('Please enter coupon code!');
    }else{
        $.ajax({
            type: 'POST',
            url: 'register/check_coupon/' + code,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if(data.success){
                    if(data.valid.valid){
                        $('.coupon-form').addClass('disabled');
                        $('.valid_coupon').show().find('.coupon_message').text(data.valid.name);
                        $('.not_valid_coupon').hide();
                        button.removeClass('btn--loading');
                    }else{
                        setTimeout(function(){
                            $('.subscribe-code').val('');
                            button.removeClass('btn--loading');
                        },2000);
                        $('.coupon-form').removeClass('disabled');
                        $('.valid_coupon').hide();
                        $('.not_valid_coupon').show();
                    }
                    button.removeClass('btn--loading');
                }else{
                    console.log(data.message);
                    $('.not_valid_coupon').show();
                    $('.valid_coupon').hide();
                    setTimeout(function(){
                        $('.subscribe-code').val('');
                        button.removeClass('btn--loading');
                    },2000);
                }
            },
            error: function (request, status, error) {
                alert('Error');
                button.removeClass('btn--loading');
            }
        });
    }
});
$('.remove_coupon').on('click', function(){
    $('.subscribe-code').val('');
    $('.coupon-form').removeClass('disabled');
    $('.valid_coupon').hide();
    $('.not_valid_coupon').hide();
});
$('.subscription-option').on('click', function(){
    $('.subscription-option').removeClass('selected');
    $(this).addClass('selected');
});
</script>
</body>
</html>