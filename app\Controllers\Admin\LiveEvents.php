<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class LiveEvents extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('LiveEventsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_liveevents'] = $this->model->all_liveevents(0, session('per_page'), NULL, 'liveevents.sort asc');
        $data['liveevents_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Stream Date";
        $data['page'] = 1;

        echo view('admin/liveevents/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_liveevents'] = $this->model->all_liveevents(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['liveevents_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/liveevents/index_view', $data);
    }

    public function search()
    {
        $teachers_model = model('TeachersModel');
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();
        // $data['all_liveevents'] = $this->model->like('title', $data['search_term'])->findAll();
        $data['all_liveevents'] = $this->model->all_liveevents(0, 0, $data['search_term']);
        $data['liveevents_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Stream Date";
        $data['page'] = 1;

        echo view('admin/liveevents/index_view', $data);
    }

    public function sort_by($type = 'liveevents.title', $direction = 'desc')
    {
        $teachers_model = model('TeachersModel');
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();

        $data['all_liveevents'] = $this->model->all_liveevents(0, 0, NULL, ($type. " " . $direction));
        $data['liveevents_count'] = count($data['all_liveevents']);
        $types = array(
            "liveevents.datedesc" => "Stream Date",
            "liveevents.titleasc" => "Ascending",
            "liveevents.titledesc" => "Descending",
            "countViewdesc" => "Popularity",
            "classRatedesc" => "Best Rated"
        );
        $data['all_teachers'] = $teachers_model->findAll();
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/liveevents/index_view', $data);
    }

    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();
		$current_machines = $db->query("SELECT * FROM liveevents_machine WHERE liveevents_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['liveevents_machine'] ;
            }
        }else{
            $data['current_machines'] = 0;
        }
		$current_body_parts = $db->query("SELECT * FROM liveevents_body_parts WHERE liveevents_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['liveevents_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = 0;
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/liveevents');
        };

		return view('admin/liveevents/edit_view', $data);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/liveevents', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/liveevents/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/liveevents/' . $name, 98);
				$data['image'] = 'uploads/liveevents/' . $name;
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/liveevents', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/liveevents/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/liveevents/' . $name, 98);
				$data['cover_image'] = 'uploads/liveevents/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/liveevents', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/liveevents/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/liveevents/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/liveevents/' . $name;
			}
            // if(isset($data['date']) AND Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles'))
            // {
            //     $tmp = Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles');
            //     $data['date'] = $tmp->toDateString('Y-m-d');
            // }

            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if($data['cover_image_removed'] == 1){
                $data['cover_image'] = "";
            }
            unset($data['cover_image_removed']);

            if($data['mob_cover_image_removed'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            if($data['id'] == 0){
                $notification_data = array(
                    'content'   => 'New live event is scheduled, <span class="text-underline">check info</span>.',
                    'link'      => base_url() . '/liveevents/' . $data['slug'],
                    'author'    => 'system',
                    'type' => 'new_liveevents_notif',
                    'date'    => date('Y-m-d H:i:s')
                );
                $response['notification_saved'] = $NotificationsModel->save($notification_data);
            }

            $response['saved_data'] = $data;

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('liveevents_' . $key);
                    $builder->delete(['liveevents_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'liveevents_id' => $response['inserted_id'],
                            'liveevents_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
        }
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}