<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
/*Dropdown checkboxes*/ 
.dropdown-container {position: relative;}
.dropdown-label {font-size:14px; color:#969696;}
.dropdown-label:after {content: ""; width:8px; height:5px; background: url(/admin_assets_new/images/triangle-down.svg) no-repeat center center/cover; position: absolute; right: 15px; top: 50%; margin-top: -3px;}
/*.dropdown-container.is-active .dropdown-label:after {content: "\25B2";}*/
.dropdown-button {cursor: pointer;  border: 1px solid #f0f0f0; background: white; display: flex; flex-flow: row wrap;}
.dropdown-quantity {flex: 1;display: flex; flex-flow: row wrap; flex-basis: 100%; }
.dropdown-list {position: absolute; overflow-y: auto; z-index: 9999999; top: 55px; width: 100%; max-height: 250px; padding: 15px 20px 0 20px; border: 1px solid #ddd !important; border-top: 0; background: white; display: none; max-width: 500px; box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);}
.dropdown-container.is-active .dropdown-list {display: block; border-radius:10px;}
.dropdown-list input[type="search"] {padding: 5px; display: block; width: 100%;}
.dropdown-list ul {padding: 0; padding-top: 10px; list-style: none;}
.dropdown-list li {padding: 0.24em 0;}
input[type="checkbox"] {margin-right: 5px;}
.dropdown-container .is-hidden { display: none; }
.hidebtn {font-size:10px; font-weight:600; border:1px solid #000; padding:7px 20px; float:right; cursor:pointer; margin-top: -5px;} 
.hidebtn:hover {color:#fff; background:#000;} 
.showdiv {display:flex !important;}
.text-part {
    padding-right: 70px !important;
    max-width: 100%;
}
.add-part {
	position: absolute;
	top: 50%;
	right: 0;
	z-index: 1;
	transform: translateY(-50%);
}
.upload-image.small-uplad-image {border-radius:8px;}
/*Disabled input if Please select machine first displayed*/
.machine_first {margin-top: 10px; color:#DB1818 !important;}
.machinefirst-box.disabled-input {display:block !important;}
.machinefirst-box.disabled-input .dropdown-quantity {display:none !important;}
.dropdown-container.machinefirst-box.disabled-input .dropdown-button.noselect {pointer-events: none; cursor: default; background:#f8f8f8;}
/*end*/
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content mb-100 pb-5">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Accessory</h1>
                <a href="admin/accessories" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/accessories/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <!-- <h3 class="mb-3">Machine cover image</h3>
            <div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image image_size">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                  <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 650px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Machine badge</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container" style="height: 205px !important">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                    <span>Max. file size is 1mb. Supported formats: PNG/JPG/SVG.<br>Desirable size: 640px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5"> -->
            <div class="row">
                <div class="col-12">
                <h5 class="mb-4 f-14 semibold">ACCESSORY INFO</h5>
                <h5 class="mb-1 f-11">ACCESSORY NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input f-14" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Short Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="short_name" class="line-input f-14" placeholder="Enter" value="<?php echo isset($current['short_name']) ? $current['short_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Long Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="long_name" class="line-input f-3 bold black red" placeholder="Enter" value="<?php echo isset($current['long_name']) ? $current['long_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Description</h3>
                    <div class="input-container" id="title_container">
                        <textarea name="description" class="line-input" placeholder="Enter description"><?php echo isset($current['description']) ? $current['description'] : '' ?></textarea>
                    </div>
                </div>
            </div> -->
            <!-- <hr class="my-5"> -->
            <?php
            if(isset($current['machine']) AND $current['machine'] != ''){
                $current_machines = explode(',', $current['machine']);
            }else{
                $current_machines = [];
            }
            ?>
            <hr class="mt-2 mb-45">
            <h5 class="mb-1 f-11">MACHINES</h5>
            <div class="row dropdown-container mx-0 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_machines) AND !empty($current_machines) AND count($current_machines) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_machines) AND !empty($current_machines) AND count($current_machines) > 0) ? 'Selected (' . count($current_machines) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    foreach($machines as $single){
                    ?>
                    <div class="checkbox mb-15" id="machine_container">
                        <input type="checkbox" class="available_machines machinecheck" name="machines[]" data-name="<?php echo $single['title']; ?>" id="machine_select<?php echo isset($single['id']) ? $single['id'] : 0; ?>" <?php echo (isset($single['id']) AND in_array($single['id'], $current_machines)) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>                        
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if(!empty($current_machines) AND count($current_machines) > 0){
                        foreach($machines as $single){
                            foreach($current_machines as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="machine_select<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                ?>                    
                </div>

            </div>
            <!-- <hr class="mt-4 mb-55">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-2 f-14 semibold">SHOPIFY ID</h5>
                    <div class="input-container">
                        <input type="text" name="shopify_id" class="line-input" placeholder="Enter ID" value="<?php echo isset($current['shopify_id']) ? $current['shopify_id'] : '' ?>" />
                    </div>
                </div>
            </div> -->
            <hr class="mt-4 mb-55">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-2 f-14 semibold">WOOCOMMERCE ID</h5>
                    <div class="input-container">
                        <input type="text" name="woo_id" class="line-input" placeholder="Enter ID" value="<?php echo isset($current['woo_id']) ? $current['woo_id'] : '' ?>" />
                        <span class="btn btn-xs black-bg white f-10 mt-2" onclick="reload_product($(this))">SYNC PRODUCT</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="flex aic mt-2 gap-2">
                        <div class="woo-image">
                            <img src="<?php echo isset($current['woo_image']) ? $current['woo_image'] : '' ?>" alt="" class="img-fluid" style="width: 120px" />
                        </div>
                        <input type="hidden" name="woo_image" id="woo_image" value="<?php echo isset($current['woo_image']) ? $current['woo_image'] : '' ?>">
                        <div class="woo">
                            <h4 class="f-14 woo-title">
                                <?php echo isset($current['woo_title']) ? $current['woo_title'] : '' ?>
                            </h4>
                            <input type="hidden" name="woo_title" id="woo_title" value="<?php echo isset($current['woo_title']) ? $current['woo_title'] : '' ?>">
                            <p class="f-12 woo-url">
                                <?php echo isset($current['woo_url']) ? $current['woo_url'] : '' ?>
                            </p>
                            <input type="hidden" name="woo_url" id="woo_url" value="<?php echo isset($current['woo_url']) ? $current['woo_url'] : '' ?>">
                        </div>
                    </div>
                </div>
            </div>
            <!-- <hr class="mt-3 mb-55">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-3 f-14 semibold">DRAFT?</h5>
                    <div class="checkbox mb-2">
                        <input type="checkbox"  id="status_check" <?php // echo $current['status'] == 1 ? 'checked' : '' ?> onchange="$(this).is(':checked') ? $('#status').val(1) : $('#status').val(0)">
                        <label for="status_check" class="f-14">Yes</label>
                        <input type="hidden" name="status" id="status" value="<?php // echo $current['status']; ?>">
                    </div>
                </div>
            </div>
            <hr class="mt-4 mb-6"> -->
            <hr class="mt-3 mb-5 mt-mob-25">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                            <a href="/admin/accessories" class="cancel-link ml-2" title="Cancel">Cancel</a>
                            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="accessories" data-popup="delete-popup" title="Cancel">DELETE ACCESSORY</a>
                        </div>
                    <?php }else{ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                            <!-- <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button> -->
                            <a href="/admin/accessories" class="cancel-link" title="Cancel">Cancel</a>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function reload_product(xx){
    var id = xx.prev().val();
    xx.addClass('btn--loading-small-white');
    $.ajax({
        type: 'POST',
        url: 'admin/products/get_woo_product/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                xx.removeClass('btn--loading-small-white');
                $('.woo-image img').attr('src', data.images[0].src);
                $('.woo-title').html(data.name);
                $('.woo-url').html(data.permalink);
                $('#woo_image').val(data.images[0].src);
                $('#woo_title').val(data.name);
                $('#woo_url').val(data.permalink);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
const $dropdown = $('.dropdown-container'); // Cache all;

function UI_dropdown() {
    const $this = $(this);
    const $btn = $('.dropdown-button', this);
    const $list = $('.dropdown-list', this);
    const $li = $('li', this);
    const $search = $('.dropdown-search', this);
    const $ckb = $(':checkbox, :radio', this);
    const $qty = $('.dropdown-quantity', this);

    $btn.on('click', function() {
        $dropdown.not($this).removeClass('is-active'); // Close other
        $this.toggleClass('is-active'); // Toggle this
    });

    $search.on('input', function() {
        const val = $(this).val().trim();
        const rgx = new RegExp(val, 'i');
        $li.each(function() {
        const name = $(this).text().trim();
        $(this).toggleClass('is-hidden', !rgx.test(name));
        });
    });

    $ckb.on('change', function(elem) {
        const names = $ckb.get().filter(el => el.checked).map(el => {
            return `<span class="dropdown-sel">${el.dataset.name.trim()}<span class="remove_tag" data-uncheck="${el.id.trim()}">×</span></span>`;
        });
        var num = $(elem.target).closest('.dropdown-container').find(':checked').length;
        setTimeout(function(){
            if(num > 0){
                $(elem.target).closest('.dropdown-container').find('.input-container').removeClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
            }else{
                $(elem.target).closest('.dropdown-container').find('.input-container').addClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').removeClass('black').text('Select');
            }
        }, 50);
        $qty.html(names.join(''));
        select_machine();
    });
}

$dropdown.each(UI_dropdown); // Apply logic to all dropdowns

// Dropdown - Close opened 
$(document).on('click', function(ev) {
    const $targ = $(ev.target).closest('.dropdown-container');
    if (!$targ.length) $dropdown.filter('.is-active').removeClass('is-active');
});

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>