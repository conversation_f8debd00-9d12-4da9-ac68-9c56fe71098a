<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\Admincontroller;
use App\Models\StripeModel;
use App\Models\SubscribersModel;

class FixSpecificDuplicates extends Admincontroller
{
    protected $stripe_model;
    protected $subscribers_model;

    public function __construct()
    {
        parent::__construct();
        $this->stripe_model = new StripeModel();
        $this->subscribers_model = new SubscribersModel();
    }

    public function index()
    {
        $data = $this->data;
        $data['current']['title'] = "Fix Specific Duplicate Subscriptions | Admin";
        
        return view('admin/fix_specific_duplicates_view', $data);
    }

    /**
     * Check the specific subscriptions mentioned in the issue
     */
    public function check_specific_subscriptions()
    {
        $subscription_ids = [
            'sub_1RX6jML6EaNAw2awQuk05qEU',
            'sub_1Ql0qRL6EaNAw2awrOE1gFNo'
        ];

        $results = [];

        try {
            $stripe = new \Stripe\StripeClient(
                $this->stripe_model->stripe_config['api_key']
            );

            foreach ($subscription_ids as $sub_id) {
                try {
                    $subscription = $stripe->subscriptions->retrieve($sub_id);
                    
                    $results[] = [
                        'subscription_id' => $sub_id,
                        'customer_id' => $subscription->customer,
                        'status' => $subscription->status,
                        'created' => date('Y-m-d H:i:s', $subscription->created),
                        'current_period_start' => date('Y-m-d H:i:s', $subscription->current_period_start),
                        'current_period_end' => date('Y-m-d H:i:s', $subscription->current_period_end),
                        'plan_interval' => $subscription->items->data[0]->price->recurring->interval ?? 'unknown',
                        'plan_amount' => ($subscription->items->data[0]->price->unit_amount ?? 0) / 100,
                        'metadata' => $subscription->metadata->toArray()
                    ];
                } catch (\Stripe\Exception\InvalidRequestException $e) {
                    $results[] = [
                        'subscription_id' => $sub_id,
                        'error' => 'Subscription not found or invalid: ' . $e->getMessage()
                    ];
                }
            }

            // Group by customer to see if they're duplicates
            $customers = [];
            foreach ($results as $result) {
                if (isset($result['customer_id'])) {
                    $customers[$result['customer_id']][] = $result;
                }
            }

            return $this->respond([
                'success' => true,
                'subscriptions' => $results,
                'customers' => $customers
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get all subscriptions for a specific customer
     */
    public function get_customer_all_subscriptions()
    {
        $customer_id = $this->request->getGet('customer_id');
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            $stripe = new \Stripe\StripeClient(
                $this->stripe_model->stripe_config['api_key']
            );

            // Get all subscriptions (active, cancelled, etc.)
            $all_subscriptions = $stripe->subscriptions->all([
                'customer' => $customer_id,
                'limit' => 100
            ]);

            $subscriptions = array_map(function($sub) {
                return [
                    'id' => $sub->id,
                    'status' => $sub->status,
                    'created' => date('Y-m-d H:i:s', $sub->created),
                    'current_period_start' => date('Y-m-d H:i:s', $sub->current_period_start),
                    'current_period_end' => date('Y-m-d H:i:s', $sub->current_period_end),
                    'plan_interval' => $sub->items->data[0]->price->recurring->interval ?? 'unknown',
                    'plan_amount' => ($sub->items->data[0]->price->unit_amount ?? 0) / 100,
                    'cancel_at_period_end' => $sub->cancel_at_period_end,
                    'cancelled_at' => $sub->cancelled_at ? date('Y-m-d H:i:s', $sub->cancelled_at) : null,
                    'metadata' => $sub->metadata->toArray()
                ];
            }, $all_subscriptions->data);

            // Get subscriber info
            $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();

            return $this->respond([
                'success' => true,
                'customer_id' => $customer_id,
                'subscriber' => $subscriber,
                'subscriptions' => $subscriptions,
                'total_subscriptions' => count($subscriptions),
                'active_subscriptions' => count(array_filter($subscriptions, function($sub) {
                    return $sub['status'] === 'active';
                }))
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Fix a specific customer's duplicate subscriptions
     */
    public function fix_customer_duplicates()
    {
        $data = $this->request->getPost();
        $customer_id = $data['customer_id'] ?? null;
        $keep_subscription = $data['keep_subscription'] ?? null;
        $action = $data['action'] ?? 'cancel_others'; // 'cancel_others' or 'cancel_all_create_new'
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            $stripe = new \Stripe\StripeClient(
                $this->stripe_model->stripe_config['api_key']
            );

            // Get all active subscriptions
            $active_subs = $this->stripe_model->get_customer_active_subscriptions($customer_id);
            
            if (!$active_subs['success']) {
                return $this->respond([
                    'success' => false,
                    'message' => 'Failed to retrieve customer subscriptions: ' . $active_subs['message']
                ]);
            }

            $cancelled_subscriptions = [];
            $kept_subscription = null;

            if ($action === 'cancel_others' && $keep_subscription) {
                // Cancel all except the one to keep
                foreach ($active_subs['subscriptions'] as $subscription) {
                    if ($subscription->id === $keep_subscription) {
                        $kept_subscription = $subscription->id;
                        continue;
                    }
                    
                    $cancelled = $stripe->subscriptions->cancel($subscription->id, [
                        'metadata' => [
                            'cancelled_reason' => 'duplicate_cleanup',
                            'cancelled_at' => date('Y-m-d H:i:s'),
                            'cancelled_by' => 'admin_fix_tool'
                        ]
                    ]);
                    $cancelled_subscriptions[] = $cancelled->id;
                }
            } elseif ($action === 'cancel_all_create_new') {
                // Cancel all existing subscriptions
                foreach ($active_subs['subscriptions'] as $subscription) {
                    $cancelled = $stripe->subscriptions->cancel($subscription->id, [
                        'metadata' => [
                            'cancelled_reason' => 'duplicate_cleanup_recreate',
                            'cancelled_at' => date('Y-m-d H:i:s'),
                            'cancelled_by' => 'admin_fix_tool'
                        ]
                    ]);
                    $cancelled_subscriptions[] = $cancelled->id;
                }

                // Create new subscription
                $price_id = $data['price_id'] ?? 'price_1KFFPGL6EaNAw2awJpDD8rTi'; // Default price
                $new_subscription = $this->stripe_model->create_subscription_safe(
                    $customer_id, 
                    $price_id, 
                    '', 
                    'admin_fix_tool - recreated after duplicate cleanup',
                    false
                );

                if ($new_subscription['success']) {
                    $kept_subscription = $new_subscription['subscription'];
                } else {
                    return $this->respond([
                        'success' => false,
                        'message' => 'Failed to create new subscription: ' . $new_subscription['message'],
                        'cancelled_subscriptions' => $cancelled_subscriptions
                    ]);
                }
            }

            // Update subscriber record
            if ($kept_subscription) {
                $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();
                if ($subscriber) {
                    $this->subscribers_model->save([
                        'id' => $subscriber['id'],
                        'stripe_subscription' => $kept_subscription,
                        'subscription_status' => 'active'
                    ]);
                }
            }

            return $this->respond([
                'success' => true,
                'message' => 'Successfully fixed duplicate subscriptions',
                'cancelled_subscriptions' => $cancelled_subscriptions,
                'kept_subscription' => $kept_subscription,
                'action_taken' => $action
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate a detailed report for the specific subscriptions
     */
    public function generate_specific_report()
    {
        $check_result = $this->check_specific_subscriptions();
        
        if (!$check_result['success']) {
            return $this->respond($check_result);
        }

        $report = [
            'generated_at' => date('Y-m-d H:i:s'),
            'specific_subscriptions' => [
                'sub_1RX6jML6EaNAw2awQuk05qEU',
                'sub_1Ql0qRL6EaNAw2awrOE1gFNo'
            ],
            'analysis' => $check_result,
            'recommendations' => []
        ];

        // Add recommendations based on findings
        foreach ($check_result['customers'] as $customer_id => $subs) {
            if (count($subs) > 1) {
                $active_subs = array_filter($subs, function($sub) {
                    return isset($sub['status']) && $sub['status'] === 'active';
                });

                if (count($active_subs) > 1) {
                    $report['recommendations'][] = [
                        'customer_id' => $customer_id,
                        'issue' => 'Multiple active subscriptions',
                        'action' => 'Cancel older subscription, keep newer one',
                        'subscriptions' => $active_subs
                    ];
                }
            }
        }

        return $this->respond([
            'success' => true,
            'report' => $report
        ]);
    }
}
