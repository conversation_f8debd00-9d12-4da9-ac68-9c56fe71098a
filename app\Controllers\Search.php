<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Search extends Frontcontroller
{
	use ResponseTrait;

    protected $model;
    protected $user;
    protected $settings;

	public function __construct() {
		parent::__construct();
		$this->model = model('TeachersModel');
	}

    public function index($page = 1)
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
		$ClassesModel = model('ClassesModel');
		$ExercisesModel = model('ExercisesModel');

        $type = $this->request->getPost('search_type');
        $data['search_type'] = $type;
        $data['search_keywords'] = $this->request->getPost('search_input');
        $data['results'] = [];
        if($type == 'classes'){
            $data['results']['classes'] = $ClassesModel->all_classes_search(($page-1)*12, 12, $data['search_keywords']);
        }
        if($type == 'exercises'){
            $data['results']['exercises'] = $ExercisesModel->all_exercises_search(($page-1)*12, 12, $data['search_keywords']);
        }

        $data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Search results';
		$data['current']['seo_description'] = 'Search results description';

		echo view('front/search/index_view', $data);
    }
}