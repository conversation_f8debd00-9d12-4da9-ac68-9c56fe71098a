<?php
foreach($all_classes as $single){
?>
                <div class="col-4 single-ajax-class">
                    <div class="single-video-item">
                        <a href="classes/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container">
                            <span class="duration"><?php echo (isset($single['duration']) AND $single['duration'] != '') ? duration_standard($single['duration']) : ''; ?></span>
                            <?php if(!empty($logged_user) AND NULL !== session('subscription') AND session('subscription') == 'active'){ ?>
                                <span class="play-button"><span></span></span>
                            <?php } ?>
                            <div class="image-overlay h100"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" /></div>
                            <?php if(empty($logged_user) OR NULL === session('subscription') OR session('subscription') != 'active'){ ?>
                                <span class="locked"></span>
                            <?php } ?>
                        </a>
                        <?php if(!empty($logged_user) AND NULL !== session('subscription') AND session('subscription') == 'active'){ ?>
                        <span class="favorite ajax-fav <?php echo $single['inFavs'] == 1 ? 'ajax-favs-active' : ''; ?>" data-class-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-user-id="<?php echo (isset($logged_user['id']) AND $logged_user['id'] != '') ? $logged_user['id'] : 0; ?>"></span>
                        <?php } ?>
                        <div class="video-text-container">
                            <a href="classes/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>">
                                <h4 class="flex jcsb medium mb-2 f-14 ail">
                                    <span class="pr-2"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                    <p class="flex aic red ml-2 normal" style="min-width: 47px;"><?php echo (isset($single['classRate']) AND $single['classRate'] != '') ? number_format($single['classRate'], 1) : ''; ?> <i class="icon-small-star ml-05"></i></p>
                                </h4>
                            </a>
                            <p class="midGray f-14 mb-3 light">
                                <span class="d-inline-block"><?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] . ', ' : ''; ?></span>
                                <span class="d-inline-block"><?php echo (isset($single['duration']) AND $single['duration'] != '') ? only_minutes($single['duration']) . ' minutes,' : ''; ?></span>
                                <span class="d-inline-block"><?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] : ''; ?></span>
                                <br>
                                <span class="d-inline-block">by: <a href="teachers/<?php echo (isset($single['teach_slug']) AND $single['teach_slug'] != '') ? $single['teach_slug'] : ''; ?>" class="link link-black black text-underline d-inline-block"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : ''; ?></a></span>
                            </p>
                        </div>
                    </div>
                </div>
<?php
}
?>
