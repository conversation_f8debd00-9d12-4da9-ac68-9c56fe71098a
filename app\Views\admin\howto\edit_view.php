<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.single-selected-exercises .handle,
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
  margin-top: 37px;
  right: 35px;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.ajax-class {
	position:relative;
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 25px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 155px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
  flex: initial;
max-width: 35px;
margin-left: auto !important;
align-self: center;
height: 35px;
color: #969696 !important;
background: #fff !important;
border: 1px solid #f0f0f0;
width: 35px;
position: absolute;
right: 5px;
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto:hover {
  color: #fff !important;
  background: #000 !important;
  border: 1px solid black !important;
}
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone::before {
    content: "";
  position: absolute;
  border: 1px solid #f0f0f0;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 10px;
  background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 18px !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
    z-index: 999999999;
}

@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
}
</style>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <?php if(isset($current['status']) AND $current['status'] == 2){ ?>
                    <h1 class="h3">Video Review</h1>
                    <span class="btn btn-xs yellow-bg white f-1 ml-2" style="min-height: 25px;">Pending</span>
                    <a href="admin/howto" class="btn btn-border white-bg black ml-auto" title="Cancel">Cancel</a>
                <?php }else{ ?>
                    <h1 class="h3 mr-05 pr-1"><?php echo isset($current['id']) ? '' : 'Upload' ?> Video <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                    <span class="btn red-bg white f-1 ml-auto" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                    <a href="admin/howto" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
                <?php } ?>
            </div>
            <form action="admin/howto/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 400px;"' : '' ?>>
                <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                    <span class="f-16 semibold mb-1">DRAG AND DROP VIDEO HERE</span><span class="f-14 midGray video_choose">or <u>select a file</u></span>
                </div>
                <div class="video_placeholder">
                    <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                </div>
                <span id="video-is-uploading"></span>
                <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                <span id="progress-bar-status-show"></span>
                <span id="toshow" style="display: none;"></span>
            </form>

            <div class="flex aic jcsb mt-1">
                <span id="remove_video" class="link link-red red text-underline f-12" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                <!-- <div class="duration-container ml-auto">
                    <div class="flex aic jcr f-14 no-wrap" hidden>
                        <p class="mr-1">Duration</p>
                        <input type="text" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                        <span class="duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                    </div>
                </div> -->
                <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
            </div>
            <hr class="mt-45 mb-4">
        </div>

        <!-- <div class="container flex-vertical aic video_thumbs" <?php // echo ((isset($current['image']) AND $current['image'] != '') OR $current['id'] == NULL) ? 'style="display: none"' : ''; ?>>
            <h3 class="mb-3">Video Thumbnail</h3>
            <div class="row">
                <div class="col-12 flex aic">
                    <div class="upload-image">
                        <img src="<?php // echo !isset($current['video_thumb']) ? 'admin_assets_new/images/upload-icon.svg' : $current['video_thumb']; ?>" alt="" class="image_preview video_thumb <?php // echo !isset($current['video_thumb']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo !isset($current['video_thumb']) ? 1 : 1; ?>">
                    </div>
                    <div class="midGray f-14">
                        <div class="flex flex-column mb-2 image_options">
                            <a href="javascript:;" class="link link-midGray midGray text-underline remove_thumb mb-2" <?php // echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? '' : 'style="display: none"'; ?>>Remove</a>
                            <a href="javascript:;" class="btn btn-sm f-14 red-bg white choose_php_thumb" onclick="get_thumbs($(this))" title="">Select thumbnail from video</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="thumbs-container" style="display: none;">
                <hr class="my-4">
                <div class="row">
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail1" id="thumbnail1" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail2" id="thumbnail2" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                    <div class="col-4">
                        <img src="" class="canvas_image thumbnail3" id="thumbnail3" style="display: none;" />
                        <input type="hidden" class="video_thumb_src">
                    </div>
                </div>
            </div>
            <hr class="my-4">
        </div> -->
        <form action="admin/howto/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom classes_form mt-3 pt-05 mb-45" id="main_form">
            <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
            <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>
            <h5 class="mb-1 f-14 semibold">CUSTOM THUMBNAIL</h5>
            <p class="midGray mb-5 f-14">Select or upload a photo that shows what's in your video. A good thumbnail stands out and draws viewers' attention.</p>
            <div class="image_container flex aic">
                <div class="upload-image small-uplad-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="mt-5 mb-55">
            <div class="row mb-2">
                <div class="col-12">
                    <h5 class="mb-5 f-14 semibold">CLASS NAME</h5>
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container" style="position: relative;">
                        <input type="text" name="title" class="line-input black" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" />
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Page URL</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" style="padding-left: 235px;">
                        <span class="base_url">www.lagreeod.com/classes/</span>
                    </div>
                </div>
            </div> -->
            <!-- <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Description</h3>
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input" placeholder="Describe Your Class"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                    </div>
                </div>
            </div> -->
            <hr class="mt-0 mb-45">
            <div class="row">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">MACHINE *</h5>
<?php
$curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
foreach($machines as $single){
?>
                    <div class="checkbox mb-15" id="machine_container">
                        <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-45 f-14 semibold">TEACHERS *</h5>
                    <div class="row w100">
<?php
$c=0;
$teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
foreach($all_teachers as $single){
$c++;
    if(isset($current['type']) AND $current['type'] == 1){
?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container" style="<?php echo ($single['id'] != $teacher) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-14"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>
    <?php
    }else{
    ?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container" style="<?php echo ($single['certified'] == 0) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-14"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>

    <?php
    }
    ?>
<?php
}
?>
                    </div>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">LANGUAGE *</h5>
<?php
$current_language = (isset($current['language']) AND $current['language'] != '') ? $current['language'] : 0;
foreach($languages as $single){
?>
                    <div class="checkbox mb-15" id="language_container">
                        <input type="radio" class="" name="language" id="language<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_language ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="language<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">DIFFICULTY *</h5>
<?php
$current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
foreach($difficulty as $single){
?>
                    <div class="checkbox mb-15" id="difficulty_container">
                        <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-45 f-14 semibold">BODY PARTS</h5>
                    <div class="mb-55 flex aic gap-3">
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" id="full_body">
                            <label for="full_body" class="f-14">Full Body</label>
                        </div>
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" id="upper_body">
                            <label for="upper_body" class="f-14">Upper Body</label>
                        </div>
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" id="lower_body">
                            <label for="lower_body" class="f-14">Lower Body</label>
                        </div>
                    </div>
                </div>
                <div class="col-12 flex">
                    <div class="mr-150">
<?php
$curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '') ? $current_body_parts : array();
?>
                        <!-- <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts16" <?php echo in_array(16, $curr_body_parts) ? 'checked' : '' ?> value="16">
                            <label for="body_parts16" class="f-14">Full body</label>
                        </div> -->

<?php
$c=0;
foreach($body_parts as $single){
    if($single['id'] != 16){
        $c++;
?>
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>" data-group="<?php echo $single['parts_group']; ?>">
                            <label for="body_parts<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
        if($c == 8){
            echo '</div><div class="mr-150">';
            $c = 0;
        }
    }
}
?>

                    </div>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">ACCESSORIES</h5>
<?php
$curr_accessories = (isset($current_accessories) AND $current_accessories != '') ? $current_accessories : array();
foreach($accessories as $single){
?>
                        <div class="checkbox mb-15" id="accessories_container" data-machine="<?php echo $single['machine']; ?>">
                            <input type="checkbox" <?php if($single['id'] == 41){ ?>onchange="$(this).is(':checked') ? $('.bungee_tension').show() : $('.bungee_tension').hide()"<?php } ?> class="" name="accessories[]" id="accessories<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="accessories<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
<?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14 newRed">Please select machine first</h5>' : ''; ?>
                </div>
                <div class="col-6 bungee_tension" style="<?php echo ($current_tensions != 0 AND in_array(41, $curr_accessories)) ? '' : 'display: none'; ?>">
                        <h3 class="mb-45 f-14 semibold">Bungee Tension <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h3>
<?php
$curr_tensions = (isset($current_tensions) AND $current_tensions != '') ? $current_tensions : array();
foreach($tensions as $single){
?>
                        <div class="checkbox mb-15" id="tensions_container" data-class-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>" data-id="<?php echo $single['id']; ?>">
                            <input type="checkbox" class="" name="tensions[]" id="tensions<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_tensions) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="tensions<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
                </div>
            </div>
            <hr class="mt-3 mb-45">
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-5 f-14 semibold flex aic jcsb">SPRING LOAD <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h5>
<?php
$curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array();
foreach($springs as $single){
?>
                        <div class="checkbox mb-15" id="springs_container" data-machine="<?php echo $single['machine']; ?>">
                            <input type="checkbox" class="" name="springs[]" id="springs<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="springs<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
<?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14 newRed">Please select machine first</h5>' : ''; ?>
                </div>
            </div>
            <hr class="mt-45 mb-0">
            <div class="row">
                <div class="col-12">
                    <div class="uploading" style="display: none;">Uploading video. Please wait...</div>
                    <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                    <input type="hidden" name="type" id="type" value="<?php echo isset($current['type']) ? $current['type'] : 0 ?>">
                    <input type="hidden" name="prev_status" id="prev_status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <!-- <input type="hidden" name="created_at" id="created_at" value="<?php // echo isset($current['created_at']) ? $current['created_at'] : 0 ?>"> -->
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="video_encrypted_path" id="video_encrypted_path" value="<?php echo isset($current['video_encrypted_path']) ? $current['video_encrypted_path'] : 0 ?>">
                    <input type="hidden" name="video_preview" id="video_preview" value="<?php echo isset($current['video_preview']) ? $current['video_preview'] : '' ?>">

                    <div class="approve-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? '' : 'style="display: none;"';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">APPROVE AND PUBLISH CLASS</button>
                        <button type="button" class="btn btn-wide btn-tall btn-border white-bg black ml-2" data-popup="reject-video" onclick="save_status(3);$('[name=teacher_id]').val($('[name=teacher]:checked').val());">REJECT</button>
                    </div>

                    <?php if(isset($prev) OR isset($next)){ ?>
                        <div class="bottom-fixed-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? 'style="display: none;"' : '';?>>
                        <?php if(isset($prev)){ ?>
                            <a href="admin/howto/edit/<?php echo $prev['id']; ?>" class="link link-black black text-underline f-14 mr-2">Previous Video</a>
                        <?php } ?>
                        <?php if(isset($next)){ ?>
                            <a href="admin/howto/edit/<?php echo $next['id']; ?>" class="link link-black black text-underline f-14">Next Video</a>
                        <?php } ?>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>

        <div class="container" style="<?php echo isset($current['id']) ? '' : 'display: none'; ?>">
            <div class="row big-gap reversecols">
                <div class="col-6">
                    <h5 class="mb-2 pb-55 f-14 ml-1 semibold flex aic jcsb bottom-border bordertopmob">CLASS EXERCISES</h5>
                    <div class="row selected_clases sortable">
                    <?php
                    foreach($selected_exercises_for_selection as $single){
                    ?>
                        <div class="col-12 pl-0 pr-2 single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : ($single['class'] == 'exercises_class' ? 'exercises' : 'howto'); ?> white-bg " data-rowid="<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="<?php echo (isset($single['type']) AND $single['type'] != '') ? $single['type'] : ''; ?>" data-sort="<?php echo (isset($single['sort']) AND $single['sort'] != '') ? $single['sort'] : ''; ?>">
                            <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
                            <div class="single-class ml-2 pl-05 aic">
                                <div class="single-class-image">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                                </div>

                                <div class="single-class-rest">
                                    <div class="single-class-title f-14 medium" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></div>
                                    <div class="single-class-desc normal">
                                      <?php echo (isset($single['all_exercise_machines']) AND $single['all_exercise_machines'] != '') ? $single['all_exercise_machines'] : ''; ?>
                                      <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''; ?>
                                      <?php echo (isset($single['diff']) AND $single['diff'] != '') ? ', ' . $single['diff'] : ''; ?>
                                      <?php echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                      </div>
                                    <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : ($single['class'] == 'exercises_class' ? 'exercises' : 'howto'); ?>').remove();remove_class_from_selected(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>, <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>, '<?php echo $single['class'] == 'class_class' ? 'Classes' : ($single['class'] == 'exercises_class' ? 'Exercises' : 'Howto'); ?>')">Remove</span>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    </div>
                </div>
                <div class="col-6">
                    <h5 class="mb-3 pb-45 f-14 semibold flex aic jcsb bottom-border">ALL EXERCISES
                        <span class="link midGray f-12 normal" onclick="remove_filters()">Clear</span>
                        <!-- <div class="flex aic jcr classes_videos_show">
                            <div class="checkbox mr-2 small-checkbox">
                                <input type="checkbox" class="classes_show" id="classes_show" checked>
                                <label for="classes_show" class="f-12">CLASSES</label>
                            </div>
                            <div class="checkbox m-0 small-checkbox">
                                <input type="checkbox" class="videos_show" id="videos_show" checked>
                                <label for="videos_show" class="f-12">VIDEOS</label>
                            </div>
                        </div> -->
                    </h5>
                    <div class="row mb-1 pb-05 pr-05 pl-05 mb-mob-0">
                        <div class="col-6 pl-1 pr-1">
                            <div class="custom-select small">
                                <div class="custom-selectbox-holder">
                                    <div class="custom-selectbox">
                                        <span class="select_val">Machines <span class="select_count"></span></span>
                                        <ul>
                                        <?php foreach($machines as $single){ ?>
                                            <li class="exercises_machines" onclick="seach_classes()" data-val="<?php echo strtolower(str_replace(" ", "_", $single['short_name'])); ?>"><?php echo $single['title']; ?></li>
                                        <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 pl-1 pr-1 mb-mob-2">
                            <div class="custom-select small">
                                <div class="custom-selectbox-holder">
                                    <div class="custom-selectbox">
                                        <span class="select_val">Body Parts <span class="select_count"></span></span>
                                        <ul>
                                        <?php foreach($body_parts as $single){ ?>
                                            <li class="exercises_body_parts" onclick="seach_classes()" data-val="<?php echo strtolower(str_replace(" ", "_", $single['title'])); ?>"><?php echo $single['title']; ?></li>
                                        <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="search-container mb-3">
                        <div class="ajax-search-classes search-form show ml-0 px-0">
                            <input type="text" class="seach-input search-wide search_exercises_filter" placeholder="Search (enter at least 2 characters)..." onkeyup="seach_classes()">
                            <button type="button" class="search-button" style="right: 7px; top: 7px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" /></button>
                        </div>
                    </div>
                    <div class="search-ajax-classes">
                        <h3 class="f-14 px-2 text-center no_result" style="display: none;">There are no exercises matching your filters. <br>Try removing some.</h3>
<?php
foreach($all_exercises as $single){
    $added = FALSE;
    if($single['status'] == 0){
        foreach($selected_exercises_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class exercises-class <?php echo $single['machines_exercises']; ?> <?php echo $single['body_parts_exercises']; ?> <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2" style="margin-top: -7px">
                                <span class="f-14 medium" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500; margin-bottom:1px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #969696;font-size: 12px;display: inline-block;line-height: 20px;font-weight: 400;">
                                    <?php echo (isset($single['all_exercise_machines']) AND $single['all_exercise_machines'] != '') ? $single['all_exercise_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?>
                                    <span class="blockmob"></span><?php echo (isset($single['diff']) AND $single['diff'] != '') ? '<br>Difficulty: ' . $single['diff'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto exercises_add">+</span>
                        </div>
<?php
    }
}
?>
                    </div>
                </div>

            </div>

        </div>


        <div class="container">
        <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
            <hr class="mt-4 mt-mob-0 mb-6">
            <div class="default-buttons flex aic">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                <a href="/admin/howto" class="cancel-link ml-2" title="Cancel">Cancel</a>
                <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="howto" data-popup="delete-popup" title="Cancel">DELETE VIDEO</a>
            </div>
        <?php }else{ ?>
            <div class="default-buttons flex aic">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                <a href="/admin/howto" class="cancel-link" title="Cancel">Cancel</a>
            </div>
        <?php } ?>
        </div>
    </div>
</main>

<!-- CLASS TEMPLATE -->
<div id="class-template" style="display: none">
    <div class="col-12 single-selected-class" data-id="0" data-rowid="0" data-type="0">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>
<!-- HOW TO CLASS TEMPLATE -->
<div id="howto-template" style="display: none">
    <div class="col-12 single-selected-howto" data-id="0" data-rowid="0" data-type="0" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>
<!-- EXERCISES CLASS TEMPLATE -->
<div id="exercises-template" style="display: none">
    <div class="col-12 single-selected-exercises" data-id="0" data-rowid="0" data-type="0" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/videos_exercises.js?v=<?php echo $_ENV['version']; ?>"></script>

<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
var statuss = 0;
<?php if(isset($current['duration']) AND ($current['duration'] == '' OR $current['duration'] == 'NaN' OR is_numeric($current['duration'])) AND isset($current['id']) AND $current['id'] > 0){ ?>
    setTimeout(function(){
        $('#duration_val').val(($('video').get(0).duration).toFixed(0));
    }, 4500);
<?php } ?>
$('#lower_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="2"]').prop('checked', true);
    }else{
        $('[data-group="2"]').prop('checked', false);
    }
});
$('#upper_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="1"]').prop('checked', true);
    }else{
        $('[data-group="1"]').prop('checked', false);
    }
});
$('#full_body').on('change', function(e){
    if($(this).is(':checked')){
        $('#lower_body').prop('checked', false);
        $('#upper_body').prop('checked', false);
        $('[data-group]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});

function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
$('#reject_class').on('submit', function (e) {
	console.log('reject_class submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
	var formData = form.serialize();
    button.addClass('btn--loading');
	$.ajax({
		type: "POST",
		url: url,
		data: formData,
		dataType: "json",
		success: function(data) {
			console.log(data);
			if (data.success) {
				console.log('SUCCESS');
                app_msg('Video rejected');
                close_all();
                <?php if(isset($pending) AND is_array($pending) AND count($pending) > 1){ ?>
                    window.location = '/admin/classes/pending';
                <?php }else{ ?>
                    window.location = '/admin/classes';
                <?php } ?>

                button.removeClass('btn--loading');
			} else {
				console.log('NO SUCCESS');
                app_msg('Something went wrong. Please try again', 'danger');
                button.removeClass('btn--loading');
			}
		},
		error: function(result) {
			console.log('ERROR WITH PHP');
			console.log(result);
            app_msg('Server problem', 'danger');
            button.removeClass('btn--loading');
		}
	});
});
if($('.selected_clases').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".selected_clases").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/classes/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}

</script>
</body>
</html>