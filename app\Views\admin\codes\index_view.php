<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">COUPON CODES</h1>
                <div class="ml-auto">
                    <a href="admin/codes/edit" class="btn black-bg white" title="Upload Class">New coupon</a>
                </div>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo count($all_coupons['coupons_list']) == 1 ? count($all_coupons['coupons_list']) . ' Code' : count($all_coupons['coupons_list']) . ' codes'; ?></h5>
                <div class="dropdown d-inline-block ml-auto">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/codes/sort_by/codes.created_at/desc" class="link midGray" title="">Date Added</a></li>
                        <li><a href="admin/codes/sort_by/codes.title/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/codes/sort_by/codes.title/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/codes/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button" disabled><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            <hr class="mt-2 mb-2">
            <!-- <pre>
                <?php // print_r($all_codes); ?>
            </pre> -->
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple normalRed" data-table="codes" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>

                <div class="col-name">
                  <p class="f-12 medium midGray mr-1 pr-05">MAX REDEMPTIONS</p>
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders noimg-table">
<?php
$stripe_products = [
    $_ENV['product_year'] => 'annual',
    $_ENV['product_month'] => 'monthly',
    $_ENV['product_week'] => 'weekly'
];
// echo '<pre>';
// print_r($all_coupons['coupons_list']);
// die();
if(count($all_coupons['coupons_list']) > 0){
    foreach($all_coupons['coupons_list'] as $single){
        if($single['valid']){
            $products = [];
            $response_products = json_decode($single['metadata']['products'], TRUE);
            if(is_array($response_products) AND count($response_products) > 0){
                foreach($response_products as $single_prod){
                    if(isset($stripe_products[$single_prod])){
                        $products[] = $stripe_products[$single_prod];
                    }
                };
            }else{
                $products = [];
            }
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <div class="flex flex-column">
                                    <span class="most-title medium mb-05"><?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?></span>
                                    <span class="midGray f-1 normal">Discount percentage: <?php echo $single['percent_off']; ?>%, applies to: <span style="text-transform: capitalize;"><?php echo count($products) > 0 ? implode(', ', $products) . ' subscriptions' : ''; ?></span></span>
                                    <div class="row-actions f-1 red">
                                        <!-- <a href="admin/codes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a> -->
                                        <!-- | -->
                                        <a href="javascript:;" class="link link-red red ml-0 data-popup="delete-coupon" onclick="$('.delete_coupon').attr  ('data-delete-id', '<?php echo $single['id']; ?>')" data-id="<?php echo $single['id']; ?>">Delete</a>
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-1">
                                    <span class="f-1 normal maxredempt"><?php echo $single['max_redemptions']; ?></span>
                                </div>
                            </div>
                        </div>
<?php
        }
    }
}
?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
$('.delete_coupon').on('click', function(){
    var coupon_id = $(this).data('delete-id');

    $.ajax({
        type: 'POST',
        url: 'admin/codes/delete/' + coupon_id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            if(data.success){
                window.location = '/admin/codes';
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});
</script>
</body>
</html>