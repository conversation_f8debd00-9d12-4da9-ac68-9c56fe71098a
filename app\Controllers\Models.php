<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Models extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ModelsModel');
        $controller = 'models';
	}

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['all_models'] = $this->model->all_certified_models();

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Models of Lagree On Demand | Online Lagree Fitness Trainers';
		$data['current']['seo_description'] = 'Meet our online Lagree models who are serving up workouts on the Micro, Mini, and Megaformer. Our Lagree On Demand models can help you meet your fitness goals!';
		echo view('front/models/index_view', $data);
    }

    public function slug($slug = '')
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['current'] = $this->model->current($slug);
		$data['current']['seo_title'] = $data['current']['firstname'] . ' ' . $data['current']['lastname'];
		$data['current']['seo_description'] = 'Lagree On Demand Models';
		$data['current']['seo_keywords'] = 'Lagree On Demand Models';
        $data['teacher_classes'] = $this->model->teacher_classes($data['current']['id']);

        // $data['total_duration'] = 0;
        // foreach($data['selected_classes_for_selection'] as $single){
        //     $data['total_duration'] = $data['total_duration'] + ((isset($single['duration']) AND $single['duration'] !='') ? $single['duration'] : 0);
        // }
        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/models/single_view', $data);
    }
}