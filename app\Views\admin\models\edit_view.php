<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.line-input[readonly] {
	background: #f8f8f8;
	pointer-events: none;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Model</h1>
                <a href="admin/models" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/models/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <div class="row mb-3">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">BASIC INFO</h5>
                    <h5 class="mb-1 f-11">FIRST NAME *</h5>
                    <div class="input-container" id="firstname_container">
                        <input type="text" name="firstname" class="line-input black make_name_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['firstname']) ? $current['firstname'] : '' ?>" <?php echo ($logged_user['super_admin'] == 1) ? '' : 'readonly' ?> />
                    </div>

                    <h5 class="mb-1 f-11">LAST NAME *</h5>
                    <div class="input-container" id="lastname_container">
                        <input type="text" name="lastname" class="line-input black make_name_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['lastname']) ? $current['lastname'] : '' ?>" <?php echo ($logged_user['super_admin'] == 1) ? '' : 'readonly' ?> />
                    </div>
                    <input type="hidden" name="slug" id="slug" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" />
                    <h5 class="mb-1 f-11">LOCATION (CITY)</h5>
                    <div class="input-container mb-0" id="location_container">
                        <input type="text" name="location" class="line-input black" placeholder="Enter" value="<?php echo isset($current['location']) ? $current['location'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="mt-5 mb-45">
            <div class="row mb-0">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">CONTACT INFO</h5>
                    <h5 class="mb-1 f-11">EMAIL *</h5>
                    <div class="input-container">
                        <input type="email" class="line-input" placeholder="Email" id="Email" name="email" value="<?php echo (isset($current['email']) AND $current['email'] != '') ? $current['email'] : ''; ?>" />
                        <!--<span class="input-label">Email</span>-->
                        <span id="email_error" class="input-error"></span>
                    </div>
                    <h5 class="mb-1 f-11">PHONE # *</h5>
                    <div class="input-container">
                        <input type="text" class="line-input" placeholder="Enter" id="phone" name="phone" value="<?php echo (isset($current['phone']) AND $current['phone'] != '') ? $current['phone'] : ''; ?>" />
                        <!--<span class="input-label">Email</span>-->
                        <span id="phone_error" class="input-error"></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                </div>
            </div>
            <hr class="mt-25 mb-5">
            <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                <div class="default-buttons flex aic">
                    <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                    <a href="/admin/models" class="cancel-link  <?php echo ($logged_user['super_admin'] == 1) ? 'ml-2' : 'ml-auto' ?>" title="Cancel">Cancel</a>
                    <?php if($logged_user['super_admin'] == 1){ ?>
                    <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="models" data-popup="delete-popup" title="Cancel">DELETE MODEL</a>
                    <?php } ?>
                </div>
            <?php }else{ ?>
                <div class="default-buttons flex aic">
                    <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                    <!-- <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button> -->
                    <a href="/admin/models" class="cancel-link" title="Cancel">Cancel</a>
                </div>
            <?php } ?>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>