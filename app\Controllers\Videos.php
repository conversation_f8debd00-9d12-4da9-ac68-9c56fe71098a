<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Videos extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('HowtoModel');
    }

    public function index()
    {
        $collections_model = model('CollectionsModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $teachers_model = model('TeachersModel');
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                            HAVING countMachine > 0
                                      ')->getResultArray();
		$data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$data['duration_less25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$data['duration_more25'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();

        $data['featured_collections'] = $collections_model->all_collections(0, 2);
        $data['all_classes'] = $this->model->all_classes(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Micro, Mega Classes on Demand';
		$data['current']['seo_description'] = "Watch Micro and Mega classes On Demand, experience the benefits of your practice at home or on the go, on any device.";
		echo view('front/classes/index_view', $data);
    }

    public function slug($slug = '')
    {
        helper('text');
        $videos_views_model = model('HowtoViewModel');
        $shopify_model = model('ShopifyModel');
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $comments_model = model('CommentsModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['current'] = $this->model->current($slug);
        $data['nums'] = create_session_nums();

        $data['current']['video_state'] = $this->model->query("SELECT * FROM video_state WHERE video_id = '" . $data['current']['slug'] . "' AND user_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND video_type = 'videos'")->getRowArray();

        $data['prev_next'] = $this->model->prev_next($data['current']['id']);
        $data['class_exercises'] = $this->model->exercises_for_videos($data['current']['id']);

        $data['comments'] = $comments_model->get_comments($data['current']['id'], 'videos');
        $data['count_comments'] = 0;

        if(!empty($data['comments']) AND count($data['comments']) > 0){
            $data['count_comments'] = count($data['comments']);
            foreach($data['comments'] as $key => $single){
                $data['comments'][$key]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                ) as user_name,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                    CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                ) as user_initials,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    teachers.image,
                                                                                    subscribers.image
                                                                                ) as user_image
                                                                                FROM comments
                                                                                LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                WHERE comments.parent = " . $single['id'] . "
                                                                                AND comments.status = 0
                                                                                AND comments.type = 'videos'
                                                                                ORDER BY comments.date desc
                                                                            ")->getResultArray();
                if(!empty($data['comments'][$key]['reply']) AND count($data['comments'][$key]['reply']) > 0){
                    $data['comments'][$key]['count_replys'] = count($data['comments'][$key]['reply']);
                    $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply']);

                    foreach($data['comments'][$key]['reply'] as $key2 => $single2){
                        $data['comments'][$key]['reply'][$key2]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                    CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                ) as user_name,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                    CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                ) as user_initials,
                                                                                IF(comments.user_id = comments.teacher_replied, 
                                                                                    teachers.image,
                                                                                    subscribers.image
                                                                                ) as user_image
                                                                                FROM comments
                                                                                LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                WHERE comments.parent = " . $single2['id'] . "
                                                                                AND comments.status = 0
                                                                                AND comments.type = 'videos'
                                                                                ORDER BY comments.date desc
                                                                            ")->getResultArray();
                        if(!empty($data['comments'][$key]['reply'][$key2]['reply']) AND count($data['comments'][$key]['reply'][$key2]['reply']) > 0){
                            $data['comments'][$key]['count_replys'] = $data['comments'][$key]['count_replys'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                            $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                        }
                    }
                }
            }
        }
        // $data['id'] = $this->get_id_from_slug('classes', $slug);
        $save_pageview = array('class_id' => $data['current']['id'], 'date' => date('Y-m-d'));
        $saved = $videos_views_model->save($save_pageview);

        $data['in_favs'] = $SubscribersFavs_model->where(['class_id' => $data['current']['id'], 'subscriber_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0)])->find();
        // $data['similar_classes'] = $this->model->similar_classes(0, 3, NULL, 'created_at DESC', $data['current']['id']);

        // $data['rated'] = $ClassesRate_model->where(['class_id' => $data['current']['id'], 'user_id' => $data['logged_user']['id']])->find();

        if($data['current']['all_class_machines_shopify'] != NULL){
            $shopify_machines = explode(',', $data['current']['all_class_machines_shopify']);
            $data['shopify_machines_titles'] = explode(',', $data['current']['all_class_machines']);
            foreach($shopify_machines as $product){
                if($product != ''){
                    $data['shopify_machines'][] = $shopify_model->single_product($product);
                }
            }
        }

        if($data['current']['all_class_accessories_shopify'] != NULL){
            $shopify_accessories = explode(',', $data['current']['all_class_accessories_shopify']);
            $data['shopify_accessories_titles'] = explode(',', $data['current']['all_class_accessories']);
            foreach($shopify_accessories as $key => $product){
                if($product != ''){
                    $data['shopify_accessories'][] = $shopify_model->single_product($product);
                }
            }
        }
        $data['current']['content'] = 'Whether you’re at home, the gym, your hotel, or small apartment, you can stream Lagree On Demand on any device.';
        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
        if($data['current']['id'] == NULL){
            return redirect()->to('/');
        }
		return view('front/videos/single_view', $data);
    }
    public function filter()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'classes.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Classes | Lagree On Demand';
		$data['current']['seo_description'] = 'Lagree On Demand Classes';
		$data['current']['seo_keywords'] = 'Lagree On Demand Classes';

        $data['all_classes'] = $this->model->filter_classes($filter_data);

		$response['view'] = view('front/classes/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_classes']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));
        $remove_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user']);

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
        }

		return $this->respond($response);
    }
    public function mark_as_watched()
    {
        $subscribersWatched_model = model('SubscribersVideosWatchedModel');
		$request = service('request');
        $data = $request->getPost();
        $save_watched = array('video_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $subscribersWatched_model->where(["video_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $subscribersWatched_model->save($save_watched);
        }

		return $this->respond($response);
    }

    public function rate_class()
    {
        $ClassesRate_model = model('ClassesRateModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('class_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['favs'] = $ClassesRate_model->where(["class_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $ClassesRate_model->save($save_rate);
            $response['success'] = TRUE;
        }

		return $this->respond($response);
    }
    public function sess()
    {
        // session()->remove('per_page');
        echo '<pre>';
        var_dump(session('per_page'));
        echo '</pre>';
    }
}