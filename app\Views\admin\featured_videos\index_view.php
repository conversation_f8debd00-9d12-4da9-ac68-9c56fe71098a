<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.reorder {
	position: absolute;
	top: 0px;
	left: 15px;
	z-index: 11;
	background: #000;
	height: 30px;
	width: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.ajax-class.added::before {
	content: "FEATURED";
	position: absolute;
	top: 50%;
	right: 15px;
	transform: translateY(-50%);
}
.ajax-class.hide_search,
.ajax-class.hide_in_popup {
	display: none !important;
}
.ajax-class {
    padding: 0px;
    cursor: pointer;
    user-select: none;
    position: relative;
}
.search-ajax-classes .ajax-class {padding:20px;}
.single-video-item  {
    width: 100% !important;
}
.single-video-item .image-overlay {
	position: relative;
	width: 100%;
	padding-top: 56.25%;
}
.add_video {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}
.add_video_text {
	font-size: 12px;
	display: block;
}
.image-overlay:not(.add-image-container)::before {
	content: "";
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: none;
	opacity: 0.8;
	z-index: 0;
	border: 1px solid #f0f0f0;
}
.image-overlay:not(.add-image-container):hover {
	box-shadow: 0 0 0 1px #000;
	border-radius: 8px;
}
.single-video-item .image-overlay img {
	-o-object-fit: cover;
	object-fit: cover;
	position: absolute;
	top: 0;
	left: 0;
}
.video-container .play-button {
	border: none;
    pointer-events: none;
}
.video-container:hover .play-button {
	background: no-repeat;
}
.video-container:hover .play-button > span {
	border-left: 20px solid #fff;
	transform: scale(1.25);
}
.popup.add-video-popup {
	max-width: 1120px;
}
.ajax-search-classes.search-form .search-button {
	height: 38px;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb page-title">
                <h1 class="h3">Featured Videos</h1>
                <!-- <a href="admin/collections/edit" class="btn black-bg white ml-auto" title="Upload Class">New Collection</a> -->
            </div>
            <hr class="mt-0 mb-55">
            <div class="row settings-wrapper" style="flex-wrap: nowrap">
                <div class="col settingsmenu">
                    <?php echo view('admin/templates/settings-menu.php'); ?>
                </div>
                <div class="col left-border sett-right featvideoscol" style="width: 100% !important; position: relative; padding-left:60px;">
                    <!-- <div class="row sortable-x"> -->

                    <h5 class="semibold f-14 text-uppercase bottom-border pb-55">FEATURED VIDEOS</h5>
                    <!-- <a style="top: -5px; position: absolute;right: 15px;" href="admin/featuredvideos/edit" class="btn black-bg white btn-xs" title="Add Video">ADD</a> -->
                    <div class="table rows-with-borders">
                        <?php if(isset($first)){ ?>
                        <div class="col-12 class-container table-row featvideorow pl-0" data-rowid="1" data-row-id="row_1">
                            <!-- <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid handle" style="filter: invert();"></span> -->
                            <div class="single-video-item mb-0 classes flex aic <?php echo $first['type']; ?>">
                                <a class="settvideoph" href="<?php echo $first['type']; ?>/<?php echo (isset($first['slug']) AND $first['slug'] != '') ? $first['slug'] : ''; ?>" target="_blank">
                                    <div class="video-container">
                                        <span class="play-button"><span></span></span>
                                        <div class="image-overlay h100 add-image-container" <?php echo ((isset($first['video_preview']) AND $first['video_preview'] != "")) ? 'style="background: url(' . ((isset($first['image']) AND $first['image'] != '') ? $first['image'] : ((isset($first['video_thumb']) AND $first['video_thumb'] != '') ? $first['video_thumb'] : '')) . ') no-repeat center center / cover"' : ''; ?> <?php echo (isset($first['watched']) AND $first['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?>>
                                            <img src="<?php echo (isset($first['image']) AND $first['image'] != '') ? $first['image'] : ((isset($first['video_thumb']) AND $first['video_thumb'] != '') ? $first['video_thumb'] : ''); ?>" alt="<?php echo (isset($first['title']) AND $first['title'] != '') ? $first['title'] : ''; ?>" class="img-fluid" />
                                        </div>
                                    </div>
                                </a>
                                <div class="flex flex-column ml-3 pr-1">
                                    <p class="f-14 medium">
                                        <span><?php echo (isset($first['title']) AND $first['title'] != '') ? $first['title'] : ''; ?></span>
                                        <span class="flex aic jcl line-height-small f-12 mt-05 midGray">
                                            <a href="javascript:;" class="link link-midGray midGray  change_featured_class mr-1 normal" title="Change" data-popup="add-video-popup" onclick="current_position = 1">Change</a>
                                        </span>
                                    </p>
                                </div>
                                <a href="javascript:;" class="f-12 link link-midGray midGray remove_featured_class ml-auto removefeatvideo" title="Remove" data-popup="remove-featured-popup" onclick="current_position = 1">Remove</a>
                            </div>
                        </div>
                        <?php }else{ ?>
                        <div class="col-12 class-container table-row pl-0">
                            <div class="single-video-item mb-0" style="max-width: 185px;">
                                <a href="javascript:;" class="" data-popup="add-video-popup" onclick="current_position = 1">
                                    <div class="video-container">
                                        <div class="image-overlay h100">
                                            <span class="add_video">
                                                <span class="f-5">+</span>
                                                <span class="add_video_text">ADD VIDEO</span>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php } ?>
                        <?php if(isset($second)){ ?>
                        <div class="col-12 class-container table-row featvideorow pl-0" data-rowid="2" data-row-id="row_2">
                            <!-- <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid handle" style="filter: invert();"></span> -->
                            <div class="single-video-item mb-0 classes flex aic <?php echo $second['type']; ?>">
                                <a class="settvideoph" href="<?php echo $second['type']; ?>/<?php echo (isset($second['slug']) AND $second['slug'] != '') ? $second['slug'] : ''; ?>" target="_blank">
                                    <div class="video-container">
                                        <span class="play-button"><span></span></span>
                                        <div class="image-overlay h100 add-image-container" <?php echo ((isset($second['video_preview']) AND $second['video_preview'] != "")) ? 'style="background: url(' . ((isset($second['image']) AND $second['image'] != '') ? $second['image'] : ((isset($second['video_thumb']) AND $second['video_thumb'] != '') ? $second['video_thumb'] : '')) . ') no-repeat center center / cover"' : ''; ?> <?php echo (isset($second['watched']) AND $second['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?>>
                                            <img src="<?php echo (isset($second['image']) AND $second['image'] != '') ? $second['image'] : ((isset($second['video_thumb']) AND $second['video_thumb'] != '') ? $second['video_thumb'] : ''); ?>" alt="<?php echo (isset($second['title']) AND $second['title'] != '') ? $second['title'] : ''; ?>" class="img-fluid" />
                                        </div>
                                    </div>
                                </a>
                                <div class="flex flex-column ml-3 pr-1">
                                    <p class="f-14 medium">
                                        <span><?php echo (isset($second['title']) AND $second['title'] != '') ? $second['title'] : ''; ?></span>
                                        <span class="flex aic jcl line-height-small f-12 mt-05 midGray">
                                            <a href="javascript:;" class="link link-midGray midGray change_featured_class mr-1 normal" title="Change" data-popup="add-video-popup" onclick="current_position = 2">Change</a>
                                        </span>
                                    </p>
                                </div>
                                <a href="javascript:;" class="f-12 link link-midGray midGray remove_featured_class ml-auto removefeatvideo" title="Remove" data-popup="remove-featured-popup" onclick="current_position = 2">Remove</a>
                            </div>
                        </div>
                        <?php }else{ ?>
                        <div class="col-12 class-container table-row pl-0">
                            <div class="single-video-item mb-0" style="max-width: 185px;">
                                <a href="javascript:;" class="" data-popup="add-video-popup" onclick="current_position = 2">
                                    <div class="video-container">
                                        <div class="image-overlay h100">
                                            <span class="add_video">
                                                <span class="f-5">+</span>
                                                <span class="add_video_text">ADD VIDEO</span>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php } ?>
                        <?php if(isset($third)){ ?>
                        <div class="col-12 class-container table-row featvideorow pl-0" data-rowid="3" data-row-id="row_3">
                            <!-- <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid handle" style="filter: invert();"></span> -->
                            <div class="single-video-item mb-0 classes flex aic <?php echo $third['type']; ?>">
                                <a class="settvideoph" href="<?php echo $third['type']; ?>/<?php echo (isset($third['slug']) AND $third['slug'] != '') ? $third['slug'] : ''; ?>" target="_blank">
                                    <div class="video-container">
                                        <span class="play-button"><span></span></span>
                                        <div class="image-overlay h100 add-image-container" <?php echo ((isset($third['video_preview']) AND $third['video_preview'] != "")) ? 'style="background: url(' . ((isset($third['image']) AND $third['image'] != '') ? $third['image'] : ((isset($third['video_thumb']) AND $third['video_thumb'] != '') ? $third['video_thumb'] : '')) . ') no-repeat center center / cover"' : ''; ?> <?php echo (isset($third['watched']) AND $third['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?>>
                                            <img src="<?php echo (isset($third['image']) AND $third['image'] != '') ? $third['image'] : ((isset($third['video_thumb']) AND $third['video_thumb'] != '') ? $third['video_thumb'] : ''); ?>" alt="<?php echo (isset($third['title']) AND $third['title'] != '') ? $third['title'] : ''; ?>" class="img-fluid" />
                                        </div>
                                    </div>
                                </a>
                                <div class="flex flex-column ml-3 pr-1">
                                    <p class="f-14 medium">
                                        <span><?php echo (isset($third['title']) AND $third['title'] != '') ? $third['title'] : ''; ?></span>
                                        <span class="flex aic jcl line-height-small f-12 mt-05 midGray">
                                            <a href="javascript:;" class="link link-midGray midGray change_featured_class mr-1 normal" title="Change" data-popup="add-video-popup" onclick="current_position = 3">Change</a>
                                        </span>
                                    </p>
                                </div>
                                <a href="javascript:;" class="f-12 link link-midGray midGray remove_featured_class ml-auto removefeatvideo" title="Remove" data-popup="remove-featured-popup" onclick="current_position = 3">Remove</a>
                            </div>
                        </div>
                        <?php }else{ ?>
                        <div class="col-12 class-container table-row pl-0">
                            <div class="single-video-item mb-0" style="max-width: 185px;">
                                <a href="javascript:;" class="" data-popup="add-video-popup" onclick="current_position = 3">
                                    <div class="video-container">
                                        <div class="image-overlay h100">
                                            <span class="add_video">
                                                <span class="f-5">+</span>
                                                <span class="add_video_text">ADD VIDEO</span>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var current_position = 0;
$('.search_classes').on('keyup', function(){
    var chars = $(this).val().length;
    var val = $(this).val().toLowerCase();

    if(chars > 1){
        $('.ajax-classes').slideDown();
        $(".ajax-class").addClass('hide_search');
        $(".ajax-class").each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(val) != -1){
                $(this).removeClass('hide_search');
            }
        });
    }else{
        $('.ajax-class').removeClass('hide_search');
    }
});
$('.popup').on('click', function(){
    $('.dropdown').removeClass('opened');
});
$(document).ready(function(){
    $.ajax({
        type: 'POST',
        url: 'admin/featuredvideos/load_classes',
        dataType: 'json',
        async: true,
        success: function (data) {
            // alert('Success');
            $('.all_classes_load').html(data.html);
        },
        error: function (request, status, error) {
            alert('Error');
        }
    });
    $.ajax({
        type: 'POST',
        url: 'admin/featuredvideos/load_videos',
        async: true,
        dataType: 'json',
        success: function (data) {
            // alert('Success');
            $('.all_videos_load').html(data.html);
        },
        error: function (request, status, error) {
            alert('Error');
        }
    });
});
function select_this_video(xx){
    var id = xx.data('id');
    var type = xx.data('type');

    console.log("id: ", id);
    console.log("type: ", type);
    console.log("position: ", current_position);

    if(id != 0 && id != '' && type != ''){
        $.ajax({
            type: 'POST',
            url: 'admin/featuredvideos/select_video',
            data: {
                class_id: id,
                type: type,
                id: current_position
            },
            dataType: 'json',
            success: function (data) {
                console.log('AJAX SUCCESS');
                if(data.success){
                    app_msg('Video added!');
                    close_all();
                    setTimeout(function(){
                        window.location.reload();
                    }, 200);
                }else{
                    app_msg('Something went wrong!!');
                }
            },
            error: function (request, status, error) {
                console.log('ERROR WITH PHP');
            }
        });
    }else{
        app_msg('Something went wrong!!');
    }
};
function remove_this_video(xx){
    if(xx == 1 || xx == 2 || xx == 3){
        $.ajax({
            type: 'POST',
            url: 'admin/featuredvideos/remove_video/' + xx,
            dataType: 'json',
            success: function (data) {
                console.log('AJAX SUCCESS');
                if(data.success){
                    app_msg('Video removed!');
                    close_all();
                    setTimeout(function(){
                        window.location.reload();
                    }, 200);
                }else{
                    app_msg('Something went wrong!!');
                }
            },
            error: function (request, status, error) {
                console.log('ERROR WITH PHP');
            }
        });
    }else{
        app_msg('Something went wrong!!');
    }
};
$('.search-form, .remove_class').on('click', function(e){
    e.stopPropagation();
});
function select_teacher(xx){
    var teach = xx.data('teacher-selected');
    console.log(teach);

    $('.dropdown').removeClass('opened');
    $('.dropdown-button').html(xx.text() + ' <i class="arrow-down ml-05"></i>');
    $('.select_this_video').addClass('hide_in_popup');
    $('.select_this_video').each(function(){
        if($(this).hasClass(teach)){
            $(this).removeClass('hide_in_popup');
        }
    });
};

</script>
</body>
</html>