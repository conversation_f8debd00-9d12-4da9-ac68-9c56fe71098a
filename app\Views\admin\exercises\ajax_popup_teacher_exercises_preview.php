<div class="video-container radius-10" style="overflow: hidden;">
    <video controls playsinline class=radius-10 w-100" id="my_video" poster="<?php echo (isset($image) AND $image != '') ? $image : ((isset($video_thumb) AND $video_thumb != '') ? $video_thumb : ''); ?>" src="<?php echo (isset($video) AND $video != '') ? $video : ''; ?>" style="width: 100%;overflow: hidden;<?php echo (isset($video) AND $video != '') ? '' : 'display: none'; ?>"></video>
</div>
<div class="border radius-10 mt-3">
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Machine</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_machines) AND $all_exercise_machines != '') ? $all_exercise_machines : '<span class="normalRed">Machine is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Accessories</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_accessories) AND $all_exercise_accessories != '') ?  $all_exercise_accessories : '<span class="normalRed">Accessories list is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Spring Load</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_springs) AND $all_exercise_springs != '') ?  $all_exercise_springs : '<span class="normalRed">Spring Load is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Tempo Count</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_tempo) AND $all_exercise_tempo != '') ?  $all_exercise_tempo : '<span class="normalRed">Tempo Count is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Difficulty</p>
        <p class="mb-0 lh-small"><?php echo (isset($diff) AND $diff != '') ? $diff : '<span class="normalRed">Difficulty is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Exercise Type</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_exercise_type) AND $all_exercise_exercise_type != '') ?  $all_exercise_exercise_type : '<span class="normalRed">Exercise Type is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Body Positions</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_body_position) AND $all_body_position != '') ? $all_body_position : '<span class="normalRed">Body Positions is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Direction</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_direction) AND $all_exercise_direction != '') ?  $all_exercise_direction : '<span class="normalRed">Direction is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Terminology</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_terminology) AND $all_terminology != '') ? $all_terminology : '<span class="normalRed">Body Parts is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Travel Distance</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_range_of_motion) AND $all_exercise_range_of_motion != '') ?  $all_exercise_range_of_motion : '<span class="normalRed">Travel Distance Count is missing</span>'; ?></p>
    </div>
    <div class="py-1 bottom-border flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Tension</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_exercise_tension) AND $all_exercise_tension != '') ?  $all_exercise_tension : '<span class="normalRed">Tension is missing</span>'; ?></p>
    </div>
    <div class="py-1 flex px-2 aic f-12">
        <p class="w-100px mb-0 lh-small">Body Parts</p>
        <p class="mb-0 lh-small"><?php echo (isset($all_body_parts) AND $all_body_parts != '') ? $all_body_parts : '<span class="normalRed">Body Parts is missing</span>'; ?></p>
    </div>
</div>
