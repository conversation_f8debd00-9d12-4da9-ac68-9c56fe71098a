<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
	top: 0;
	margin-top: 40px;
	left: -26px;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 25px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 155px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
	flex: 1;
	max-width: 30px;
	margin-left: auto !important;
	align-self: center;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container pt-100">
            <div class="flex aic jcsb">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Collection</h1>
                <a href="admin/collections" class="btn btn-border white-bg black ml-2" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-80 mb-4">
        </div>
        <form action="admin/collections/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom new_collection_upload" id="main_form">
            <h3 class="mb-3">Featured Photo</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="image_container flex aic">
                <div class="upload-image big-uplad-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 830px x 500px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Cover Photo</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image cover_image_size" id="image_container">
                    <input type="file" name="cover_image" id="cover_image">
                    <img src="<?php echo empty($current['cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Cover Photo (mobile)</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 640px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Collection Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input f-3 bold black red make_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Page URL</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" style="padding-left: 265px;">
                        <span class="base_url">www.lagreeod.com/collections/</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                   <!--<h3 class="mb-3">Description</h3>
                    <div class="input-container mb-5" id="short_desc_container">
                        <textarea type="text" name="short_desc" class="line-input short-textarea" placeholder="Short Description (max 200 characters)…"><?php echo isset($current['short_desc']) ? $current['short_desc'] : '' ?></textarea>
                    </div>-->
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input" placeholder="Describe Your Collection"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Machine</h3>
<?php
$curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
foreach($machines as $single){
?>
                    <div class="checkbox mb-2" id="machine_container">
                        <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Difficulty</h3>
<?php
$current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
foreach($difficulty as $single){
?>
                    <div class="checkbox mb-2" id="difficulty_container">
                        <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6 for_submit">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="cover_image_removed" id="cover_image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <button type="submit" class="btn btn-wide btn-tall red-bg white"><?php echo isset($current['id']) ? 'Save Changes' : 'Publish Collection' ?></button>
                </div>
            </div>
        </form>
        <hr class="my-5">
        <div class="container" style="<?php echo isset($current['id']) ? '' : 'display: none'; ?>">
            <div class="row big-gap">
                <div class="col-6">
                    <h3 class="mb-4 f-18">Select Classes</h3>
                    <div class="row selected_clases sortable">
                    <?php
                    foreach($selected_classes_for_selection as $single){
                    ?>
                        <div class="col-12 single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : 'howto'; ?> white-bg " data-rowid="<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="<?php echo (isset($single['type']) AND $single['type'] != '') ? $single['type'] : ''; ?>" data-sort="<?php echo (isset($single['sort']) AND $single['sort'] != '') ? $single['sort'] : ''; ?>">
                            <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
                            <div class="single-class">
                                <div class="single-class-image">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                                </div>
                                <span class="btn btn-sm red-bg white remove-class remove_class" onclick="$(this).closest('.single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : 'howto'; ?>').remove();remove_class_from_selected(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>, <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>, '<?php echo $single['class'] == 'class_class' ? 'Classes' : 'Howto'; ?>')">×</span>
                                <div class="single-class-rest">
                                    <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></div>
                                    <div class="single-class-desc">
                                        <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                        <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? '<br style="display:block;">Duration: ' . duration_standard($single['duration']) : ''; ?><br style="margin-bottom;">
                                        <?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] : ''; ?>, <br>
                                        <?php echo (isset($single['teach']) AND $single['teach'] != '') ? 'by: ' . $single['teach'] : ''; ?></div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    </div>
                </div>
                <div class="col-6">
                    <h3 class="mb-4 flex aic jcsb f-18">
                        All Classes
                        <div class="flex aic jcr classes_videos_show">
                            <div class="checkbox mr-2 small-checkbox">
                                <input type="checkbox" class="classes_show" id="classes_show" checked>
                                <label for="classes_show" class="f-12">CLASSES</label>
                            </div>
                            <div class="checkbox m-0 small-checkbox">
                                <input type="checkbox" class="videos_show" id="videos_show" checked>
                                <label for="videos_show" class="f-12">VIDEOS</label>
                            </div>
                        </div>
                    </h3>
                    <div class="search-container mb-2">
                        <div class="ajax-search-classes search-form show ml-0">
                            <input type="text" class="seach-input search-wide search_classes" placeholder="Search (enter at least 2 characters)...">
                            <button type="button" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        </div>
                    </div>
                    <div class="search-ajax-classes">
<?php
foreach($all_classes as $single){
    $added = FALSE;
    if($single['status'] == 0){
        foreach($selected_classes_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class classes-class <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2">
                                <span style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 600; margin-bottom: 3px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #999;font-size: 11px;display: inline-block;line-height: 1.2;font-weight: 400;">
                                    <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?>
                                    <?php echo (isset($single['diff']) AND $single['diff'] != '') ? '<br>Difficulty: ' . $single['diff'] : ''; ?>
                                    <?php echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto">+</span>
                        </div>
<?php
    }
}
?>
                        <hr class="my-2 videos-title top-border">
                        <p class="f-1 videos-title px-2 semibold pt-2">VIDEOS</p>
                        <hr class="my-2">
<?php
foreach($all_howto as $single){
    $added = FALSE;
    if($single['status'] == 0){
        foreach($selected_howto_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class videos-class <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2">
                                <span style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500;margin-bottom: 3px; font-size:14px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #999;font-size: 12px;display: inline-block;line-height: 20px;font-weight: 400;">
                                    <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?>
                                    <?php echo (isset($single['diff']) AND $single['diff'] != '') ? '<br>Difficulty: ' . $single['diff'] : ''; ?>
                                    <?php echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto howto_add">+</span>
                        </div>
<?php
    }
}
?>
                    </div>
                </div>
            </div>
    </div>
</main>

<!-- CLASS TEMPLATE -->
<div id="class-template" style="display: none">
    <div class="col-12 single-selected-class" data-id="0" data-rowid="0" data-type="0">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
                <span class="btn btn-sm red-bg white remove-class remove_class" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">×</span>
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
            </div>
        </div>
    </div>
</div>
<!-- HOW TO CLASS TEMPLATE -->
<div id="howto-template" style="display: none">
    <div class="col-12 single-selected-howto" data-id="0" data-rowid="0" data-type="0" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
                <span class="btn btn-sm red-bg white remove-class remove_class" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">×</span>
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
            </div>
        </div>
    </div>
</div>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/collections.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
const collection_id = <?php echo $current['id']; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
if($('.sortable').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".sortable").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/collections/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}
$('.classes_videos_show input').on('change', function(){
    var type = $(this).attr('class');
    console.log(type);
    if($(this).is(':checked')){
        console.log('checked');
        if(type == 'classes_show'){
            $('.classes-class').show();
        }
        if(type == 'videos_show'){
            $('.videos-class').show();
            $('.videos-title').show();
        }
    }else{
        console.log('NOT checked');
        if(type == 'classes_show'){
            $('.classes-class').hide();
        }
        if(type == 'videos_show'){
            $('.videos-class').hide();
            $('.videos-title').hide();
        }
    }
    console.log('------------');
});
</script>
</body>
</html>
