                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Name</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($title) AND $title != '') ? $title : '<span class="normalRed">Title is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Description</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($content) AND $content != '') ? $content : '<span class="normalRed">Description is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">AKA</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($aka) AND $aka != '') ? $aka : '<span class="normalRed">Aka is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Machine</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_machines) AND $all_exercise_machines != '') ? str_replace(', ', '<br>', $all_exercise_machines) : '<span class="normalRed">Machine is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Difficulty</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($diff) AND $diff != '') ? $diff : '<span class="normalRed">Difficulty is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Body Positions</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_body_position) AND $all_body_position != '') ? str_replace(', ', '<br>', $all_body_position) : '<span class="normalRed">Body Positions is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Terminology</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_terminology) AND $all_terminology != '') ? str_replace(', ', '<br>', $all_terminology) : '<span class="normalRed">Body Parts is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Body Parts</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_body_parts) AND $all_body_parts != '') ? str_replace(', ', '<br>', $all_body_parts) : '<span class="normalRed">Body Parts is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Accessories</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_accessories) AND $all_exercise_accessories != '') ?  str_replace(', ', '<br>', $all_exercise_accessories) : '<span class="normalRed">Accessories list is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Spring Load</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_springs) AND $all_exercise_springs != '') ?  str_replace(', ', '<br>', $all_exercise_springs) : '<span class="normalRed">Spring Load is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Tempo Count</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_tempo) AND $all_exercise_tempo != '') ?  $all_exercise_tempo : '<span class="normalRed">Tempo Count is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Tension</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_tension) AND $all_exercise_tension != '') ?  $all_exercise_tension : '<span class="normalRed">Tension is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Exercise Type</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_exercise_type) AND $all_exercise_exercise_type != '') ?  $all_exercise_exercise_type : '<span class="normalRed">Exercise Type is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Direction</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_direction) AND $all_exercise_direction != '') ?  $all_exercise_direction : '<span class="normalRed">Direction is missing</span>'; ?></p>
                </div>
                <div class="bulkpopup-info">
                    <p class="f-12 semibold mb-1 lh-small">Travel Distance</p>
                    <p class="f-12 mb-0 lh-small"><?php echo (isset($all_exercise_range_of_motion) AND $all_exercise_range_of_motion != '') ?  $all_exercise_range_of_motion : '<span class="normalRed">Travel Distance Count is missing</span>'; ?></p>
                </div>
