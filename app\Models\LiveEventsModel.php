<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\I18n\Time;

class LiveEventsModel extends Model
{
    protected $table = 'liveevents';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'cover_image', 'mob_cover_image', 'content', 'location', 'duration', 'teacher', 'date', 'time', 'stream_url', 'difficulty', 'seo_title', 'hero_title', 'hero_subtitle', 'guest_name', 'seo_keywords', 'seo_description', 'status', 'sort', 'notification_sent'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[liveevents.slug,id,{id}]',
        'content'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        'location'     => 'required',
        // 'date'     => 'required',
        // 'time'     => 'required',
        // 'stream_url'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	// public function add_gallery($page_id = 0, $gallery = array())
        // {
        // 	$response['success'] = FALSE;
        // 	$db      = \Config\Database::connect();
        // 	$builder = $db->table('liveevents_galleries');
        // 	$builder->delete(['page_id' => $page_id]);
        // 	if (count($gallery) > 0)
        // 	{
        // 		$builder->insertBatch($gallery);
        // 	}

        // 	/*
        // 	if (count($users) == 1)
        // 	{
        // 		$response['user_id'] = $users[0]['id'];
        // 		$response['success'] = TRUE;
        // 	}
        // 	else
        // 	{
        // 		$response['error'] = 'Bad username or password.';
        // 	}*/
        // 	return $response;
        // }

        // public function get_gallery($page_id = 0)
        // {
        // 	$response['success'] = FALSE;
        // 	$db      = \Config\Database::connect();
        // 	$builder = $db->table('liveevents_galleries');
        // 	$response =  $builder->getWhere(['page_id' => $page_id])->getResultArray();
        // 	return $response;
	// }

    public function all_liveevents($start = 0, $limit = 0, $search_term = NULL, $order = "liveevents.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND liveevents.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT liveevents.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines, IF(liveevents.date > CURDATE(), 'Upcoming', '') as upcoming
                            FROM liveevents
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM liveevents_views GROUP BY class_id) x on x.class_id = liveevents.id
                            LEFT OUTER JOIN (SELECT liveevents_id, AVG(rate) as rate FROM liveevents_rate GROUP BY liveevents_id) y on y.liveevents_id = liveevents.id
                            LEFT JOIN liveevents_machine ON liveevents_machine.liveevents_id = liveevents.id
                            LEFT JOIN machines ON machines.id = liveevents_machine.liveevents_machine
                            LEFT JOIN difficulty on difficulty.id = liveevents.difficulty
                            WHERE liveevents.deleted_at IS NULL
                            " . $search . "
                            GROUP BY liveevents.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    public function past_liveevents($start = 0, $limit = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $data = $this->query("SELECT liveevents.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines, IF(liveevents.date > CURDATE(), 'Upcoming', '') as upcoming
                            FROM liveevents
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM liveevents_views GROUP BY class_id) x on x.class_id = liveevents.id
                            LEFT OUTER JOIN (SELECT liveevents_id, AVG(rate) as rate FROM liveevents_rate GROUP BY liveevents_id) y on y.liveevents_id = liveevents.id
                            LEFT JOIN liveevents_machine ON liveevents_machine.liveevents_id = liveevents.id
                            LEFT JOIN machines ON machines.id = liveevents_machine.liveevents_machine
                            LEFT JOIN difficulty on difficulty.id = liveevents.difficulty
                            WHERE liveevents.deleted_at IS NULL
                            AND liveevents.date < CURDATE()
                            GROUP BY liveevents.id
                            ORDER BY liveevents.date DESC
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function incoming_liveevents($start = 0, $limit = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $data = $this->query("SELECT liveevents.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines, IF(liveevents.date > CURDATE(), 'Upcoming', '') as upcoming
                            FROM liveevents
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM liveevents_views GROUP BY class_id) x on x.class_id = liveevents.id
                            LEFT OUTER JOIN (SELECT liveevents_id, AVG(rate) as rate FROM liveevents_rate GROUP BY liveevents_id) y on y.liveevents_id = liveevents.id
                            LEFT JOIN liveevents_machine ON liveevents_machine.liveevents_id = liveevents.id
                            LEFT JOIN machines ON machines.id = liveevents_machine.liveevents_machine
                            LEFT JOIN difficulty on difficulty.id = liveevents.difficulty
                            WHERE liveevents.deleted_at IS NULL
                            AND liveevents.date >= CURDATE()
                            -- HAVING db_time > cur_time
                            -- GROUP BY liveevents.id
                            ORDER BY liveevents.date ASC
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT liveevents.*, difficulty.title as diff,
                                machines.long_name AS machine_long_name,
                                machines.description AS machines_desc,
                                machines.shopify_link AS machines_link,
                                machines.image AS machines_image,
                                machines.mob_cover_image AS machines_badge,
                                teachers.image AS teacher_image,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                COALESCE(z.favs,0) AS in_favs,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short,
                                GROUP_CONCAT(DISTINCT machines.long_name SEPARATOR ', ') AS all_class_machines_long
                                FROM liveevents
                                LEFT OUTER JOIN (SELECT class_id, count(*) as favs FROM subscribers_favs GROUP BY class_id) z ON z.class_id = liveevents.id
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM liveevents_views GROUP BY class_id) x on x.class_id = liveevents.id
                                LEFT OUTER JOIN (SELECT liveevents_id, AVG(rate) as rate FROM liveevents_rate GROUP BY liveevents_id) y on y.liveevents_id = liveevents.id
                                LEFT JOIN liveevents_machine ON liveevents_machine.liveevents_id = liveevents.id
                                LEFT JOIN machines ON machines.id = liveevents_machine.liveevents_machine
                                LEFT JOIN liveevents_body_parts ON liveevents_body_parts.liveevents_id = liveevents.id
                                LEFT JOIN body_parts ON body_parts.id = liveevents_body_parts.liveevents_body_parts
                                LEFT JOIN difficulty ON difficulty.id = liveevents.difficulty
                                LEFT JOIN teachers on teachers.id = liveevents.teacher
                                WHERE liveevents.deleted_at IS NULL
                                AND liveevents.slug = '" . $slug . "'
                            ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
        if(isset($data['data']['date']) AND Time::createFromFormat("m/d/Y" , $data['data']['date'], 'America/Los_Angeles'))
        {
            $tmp = Time::createFromFormat("m/d/Y" , $data['data']['date'], 'America/Los_Angeles');
            $data['data']['date'] = $tmp->toDateString('Y-m-d');
        }
        return $data;
	}

}