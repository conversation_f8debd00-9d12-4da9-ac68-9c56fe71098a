<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Collections extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CollectionsModel');
    }

    public function index()
    {
        $classes_views_model = model('ClassesViewModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['all_collections'] = $this->model->all_collections();

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Collections of On Demand Lagree Classees';
		$data['current']['seo_description'] = 'We specially curacted collections of Lagree Micro, Mini, and Mega classes so you can choose the workouts that fit your goals best, anytime, anywhere!';
		echo view('front/collections/index_view', $data);
    }

    public function slug($slug = '')
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['current'] = $this->model->current($slug);

        $data['selected_classes_for_selection'] = $this->model->classes_for_collection($data['current']['id']);
        // $data['selected_howto_for_selection'] = $this->model->howto_for_collection($data['current']['id']);

        $data['total_duration'] = 0;
        foreach($data['selected_classes_for_selection'] as $single){
            $data['total_duration'] = $data['total_duration'] + ((isset($single['duration']) AND $single['duration'] !='' AND $single['duration'] !='NaN' AND $single['status'] == 0) ? $single['duration'] : 0);
        }
        // foreach($data['selected_howto_for_selection'] as $single){
        //     $data['total_duration'] = $data['total_duration'] + ((isset($single['duration']) AND $single['duration'] !='' AND $single['duration'] !='NaN' AND $single['status'] == 0) ? $single['duration'] : 0);
        // }
        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/collections/single_view', $data);
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($save_favs)->delete();
        }

		return $this->respond($response);
    }

    public function rate_class()
    {
        $ClassesRate_model = model('ClassesRateModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('class_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['favs'] = $ClassesRate_model->where(["class_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $ClassesRate_model->save($save_rate);
            $response['success'] = TRUE;
        }

		return $this->respond($response);
    }
}