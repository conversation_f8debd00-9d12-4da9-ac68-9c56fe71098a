<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content mb-100 pb-5">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Class Accessory</h1>
                <a href="admin/custom_accessories" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/custom_accessories/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <!-- <h3 class="mb-3">Machine cover image</h3>
            <div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image image_size">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                  <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 650px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Machine badge</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container" style="height: 205px !important">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                    <span>Max. file size is 1mb. Supported formats: PNG/JPG/SVG.<br>Desirable size: 640px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5"> -->
            <div class="row">
                <div class="col-12">
                <h5 class="mb-4 f-14 semibold">ACCESSORY INFO</h5>
                <h5 class="mb-1 f-11">ACCESSORY NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input f-14" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Short Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="short_name" class="line-input f-14" placeholder="Enter" value="<?php echo isset($current['short_name']) ? $current['short_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Long Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="long_name" class="line-input f-3 bold black red" placeholder="Enter" value="<?php echo isset($current['long_name']) ? $current['long_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Description</h3>
                    <div class="input-container" id="title_container">
                        <textarea name="description" class="line-input" placeholder="Enter description"><?php echo isset($current['description']) ? $current['description'] : '' ?></textarea>
                    </div>
                </div>
            </div> -->
            <!-- <hr class="my-5"> -->
            <hr class="mt-2 mb-45">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-45 f-14 semibold">MACHINE</h5>
<?php
// $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
foreach($machines as $single){
?>
                        <div class="checkbox mb-2" id="machines_container">
                            <input type="checkbox" class="" name="machines[]" id="machines<?php echo $single['id']; ?>" <?php echo (isset($current['machine']) AND in_array($single['id'], explode(',', $current['machine']))) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="machines<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
                </div>
            </div>
            <!--<hr class="mt-4 mb-55">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-2 f-14 semibold">SHOPIFY ID</h5>
                    <div class="input-container">
                        <input type="text" name="shopify_id" class="line-input" placeholder="Enter ID" value="<?php echo isset($current['shopify_id']) ? $current['shopify_id'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="mt-3 mb-55">
            <div class="row">
                <div class="col-6">
                <h5 class="mb-3 f-14 semibold">DRAFT?</h5>
                    <div class="checkbox mb-2">
                        <input type="checkbox"  id="status_check" <?php echo (isset($current['status']) AND $current['status'] == 1) ? 'checked' : '' ?> onchange="$(this).is(':checked') ? $('#status').val(1) : $('#status').val(0)">
                        <label for="status_check" class="f-14">Yes</label>
                        <input type="hidden" name="status" id="status" value="<?php echo (isset($current['status']) AND $current['status'] == 1) ? $current['status'] : ''; ?>">
                    </div>
                </div>
            </div>
            <hr class="mt-4 mb-6">-->
            <hr class="mt-3 mb-5">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                            <a href="/admin/custom_accessories" class="cancel-link ml-2" title="Cancel">Cancel</a>
                            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="CustomAccessories" data-popup="delete-popup" title="Cancel">DELETE ACCESSORY</a>
                        </div>
                    <?php }else{ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white mr-2" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                            <!-- <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button> -->
                            <a href="/admin/custom_accessories" class="cancel-link" title="Cancel">Cancel</a>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>