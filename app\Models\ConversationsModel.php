<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class ConversationsModel extends Model
{
    protected $table = 'conversations';
	protected $allowedFields = ['subscriber_id', 'date', 'hide'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'subscriber_id'    => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_conversations($start = 0, $limit = 25, $search_term = NULL, $order = "conversations.date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND conversations.content LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT *
                                FROM conversations
                                -- LEFT JOIN subscribers ON subscribers.id = conversations.user_id
                                WHERE date IS NOT NULL
                                " . $search . "
                                ORDER BY " . $order . "
                                " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_my_conversations($start = 0, $limit = 12, $order = "conversations.date DESC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $data = $this->query("SELECT conversations.*, subscribers_conversations.hide AS hidden,
                                IF(conversations.id IN (
                                    SELECT * FROM (
                                        SELECT subscribers_conversations.conversation_id FROM subscribers_conversations WHERE subscribers_conversations.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . "
                                    ) as nesto
                                ), 1, 0) as seen
                                FROM conversations
                                LEFT JOIN subscribers_conversations ON (subscribers_conversations.conversation_id = conversations.id AND subscribers_conversations.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                WHERE (conversations.subscriber_id IS NULL OR conversations.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                HAVING (hidden = 0 OR hidden IS NULL)
                                ORDER BY conversations.date desc
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_conversations_admin(){
        $data = $this->query("SELECT conversations.*,
                                CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name,
                                COALESCE(x.cnt,0) AS countMessages,
                                (SELECT MAX(date) FROM subscribers_conversations WHERE subscribers_conversations.conversation_id = conversations.id) AS messageDate
                                FROM conversations
                                LEFT OUTER JOIN (SELECT conversation_id, MAX(date) as max_date, count(*) as cnt FROM subscribers_conversations WHERE admin_seen = 0 AND sender_id != 0 GROUP BY conversation_id) x on x.conversation_id = conversations.id
                                LEFT JOIN subscribers ON subscribers.id = conversations.subscriber_id
                                WHERE conversations.hide = 0
                                ORDER BY countMessages DESC, messageDate DESC
                        ")->getResultArray();
        return $data;
    }

    public function admin_conversations_search($search_term = '')
    {
        $s = explode(' ', $search_term);
        $ss = [];
        foreach($s as $single){
            if($single != ''){
                // $ss[] = (strlen($single) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $single)) . "*";
                $ss[] = '+' . $single . '*';
            }
        }

        $search = implode(' ', $ss);
        $data = $this->query("SELECT (MATCH(message) AGAINST('($search) (\"$search_term\")' IN BOOLEAN MODE)) as rating, subscribers_conversations.id, subscribers_conversations.conversation_id, subscribers_conversations.sender_id, subscribers_conversations.message, CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name
                                FROM subscribers_conversations
                                LEFT JOIN subscribers ON subscribers.id = subscribers_conversations.sender_id
                                LEFT JOIN conversations ON conversations.id = subscribers_conversations.conversation_id
                                WHERE subscribers_conversations.hide = 0
                                AND subscribers_conversations.sender_id != 0
                                AND conversations.hide = 0
                                GROUP BY subscribers_conversations.message
                                HAVING rating > 0
                                ORDER BY rating desc")->getResultArray();

        return $data;
    }

    public function count_conversations_admin(){
        $messages = $this->query("SELECT conversations.*,
                                CONCAT(subscribers.firstname, ' ', subscribers.lastname) AS user_name,
                                COALESCE(x.cnt,0) AS countMessages
                                FROM conversations
                                LEFT OUTER JOIN (SELECT conversation_id, count(*) as cnt FROM subscribers_conversations WHERE admin_seen = 0 AND sender_id != 0 GROUP BY conversation_id) x on x.conversation_id = conversations.id
                                LEFT JOIN subscribers ON subscribers.id = conversations.subscriber_id
                                WHERE conversations.hide = 0
                                ORDER BY conversations.date DESC
                        ")->getResultArray();
        $c = 0;
        foreach($messages as $single)                        {
            if($single['countMessages'] > 0){
                $c++;
            }
        }

        return $c;
    }

    public function current($id){
        $data = $this->query("SELECT conversations.*
                            FROM conversations
                            WHERE conversations.id = " . $id .  "
                        ")->getRowArray();
        return $data;
    }


	protected function prepare_data(array $data)
	{
		return $data;
	}
}