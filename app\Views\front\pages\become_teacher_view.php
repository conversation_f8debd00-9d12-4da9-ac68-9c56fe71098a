<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<link rel="stylesheet" type="text/css" href="css/mvp.css" />
<link rel="stylesheet" type="text/css" href="css/flat.css?3" />
<link href="css/videojs.custom.css" rel="stylesheet">
<style>
.mvp-big-play svg {
	height: 80px;
	width: 80px;
}
#become_teacher .line-input {
    padding-top: 10px !important
}
#become_teacher .line-input[type="file"] {
	padding-top: 26px !important;
	font-size: 12px;
}
#become_teacher .line-input:placeholder-shown {
    padding-top: 0 !important
}
#become_teacher .input-label {
	left: 21px;
	top: -6px;
	font-size: 12px;
}
#become_teacher .reveal_password {
	right: 20px;
}
.earning-model-box {
	margin-bottom: 25px;
}
@media(max-width: 768px){
    .mvp-big-play svg {
        height: 40px;
        width: 40px;
    }
    .col-6:first-of-type .earning-model-box {
        margin-bottom: 60px;
    }
    .plus-between::before {
        top: calc(100% - 48px);
        right: calc(50% - 20px);
    }
    .earning-model-box.start-earning-box {
        margin-bottom: 25px;
    }
    .col-4:last-of-type .earning-model-box.start-earning-box {
        margin-bottom: 0px;
    }
}
</style>
</head>
<body class="contact-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="pb-100">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-4 line-height-small">LAGREE OD EARNING PLATFORM</h1>
                    <p class="light f-16 mb-0">Lagree certified teachers are welcome to upload their own workouts and get paid for them!</p>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 pb-100">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12">
                    <img src="images/become_bg.jpg" alt="" class="img-fluid" />
                    <!-- <a href="javascript:;" data-anchor="#become" class="btn white-bg black f-2 bold abs-center" title="START SELLING YOUR CLASSES TODAY!">START SELLING YOUR CLASSES TODAY!</a> -->
                </div>
            </div>
        </div>
    </section>
    <section class="py-0">
        <hr class="mt-0 mb-100">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3 line-height-small">RECORD CLASSES FROM HOME AND EARN!</h1>
                    <p class="light f-18 mb-6">Hi Lagree Teachers, LagreeOD is giving you opportunity to teach and earn!</p>
                    <a href="javascript:;" data-anchor="#become" class="btn black-bg white" title="APPLY FOR TEACHER ACCOUNT">APPLY FOR TEACHER ACCOUNT</a>
                </div>
            </div>
        </div>
        <hr class="my-100">
    </section>
    <section class="py-0">
        <div class="container">
            <div class="row big-gap pb-80">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3">EARNING MODELS</h1>
                    <p class="light f-18 mb-6">Your uploaded classes can make a profit on a two different models:</p>
                </div>
            </div>
            <div class="row big-gap pb-100">
                <div class="col-6 text-center plus-between">
                    <div class="earning-model-box">
                        <h2 class="mt-3">RENT A CLASS</h2>
                        <p class="price-big">$0.99*</p>
                        <p class="f-14 light">Your class will be rented and available to buyer for 24h</p>
                    </div>
                </div>
                <div class="col-6 text-center">
                    <div class="earning-model-box">
                        <h2 class="mt-3">SELL A CLASS</h2>
                        <p class="price-big">$3.99*</p>
                        <p class="f-14 light">The buyer will have unlimited access to the purchased class</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="f-18 bold">* EARNING RATIO: 70% YOU - 30% LAGREE</p>
                </div>
            </div>
        </div>
        <hr class="my-100">
    </section>
    <section class="py-0">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3 line-height-small">AUTO PAYOUTS</h1>
                    <p class="light f-18 mb-100">Request payout directly from your account within a click!</p>
                    <img src="images/MacBook.jpg" alt="MacBook" class="img-fluid" />
                </div>
            </div>
        </div>
        <hr class="my-100">
    </section>
    <section class="py-0">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3 line-height-small">HOW CAN I START EARNING?</h1>
                    <p class="light f-18 mb-100">In the three simple steps below, start selling your classes.</p>
                </div>
            </div>
            <div class="row big-gap">
                <div class="col-4 text-center">
                    <div class="earning-model-box start-earning-box">
                        <h2 class="f-18">STEP 1</h2>
                        <img src="images/step1.svg" alt="" class="img-fluid my-4" />
                        <p class="f-18 light">Create a Teacher Account</p>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="earning-model-box start-earning-box">
                        <h2 class="f-18">STEP 2</h2>
                        <img src="images/step2.svg" alt="" class="img-fluid my-4" />
                        <p class="f-18 light">Record & Upload Classes</p>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="earning-model-box start-earning-box">
                        <h2 class="f-18">STEP 3</h2>
                        <img src="images/step3.svg" alt="" class="img-fluid my-4" />
                        <p class="f-18 light">Start Selling Them!</p>
                    </div>
                </div>
            </div>
        </div>
        <hr class="my-100">
    </section>
    <section class="py-0">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3 line-height-small">TEACHER PROFILE</h1>
                    <p class="light f-18 mb-100">Your profile will be featured on teachers page with link to all classes.</p>
                    <img src="images/MacBook2.jpg" alt="MacBook" class="img-fluid" />
                </div>
            </div>
        </div>
        <hr class="my-100" id="become">
    </section>
    <?php if(session('teacher') === NULL){ ?>
    <section class="pt-0">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-3 line-height-small">READY? START TODAY!</h1>
                    <div class="my-80">
                        <div class="small-box" style="border: 2px solid #000;margin: auto;">
                            STEP 1
                        </div>
                    </div>
                    <h3 class="bold">CREATE A TEACHER ACCOUNT</h3>
                </div>
            </div>
        </div>
        <div class="container640">
            <div class="row">
                <div class="col-12">
                    <div class="main-text pt-8">
                        <form id="become_teacher" action="register/<?php echo (empty($logged_user)) ? 'subscriber_to_teacher' : 'subscriber_to_teacher_logged' ?>" method="post" class="register-form" id="register-form">
                            <div class="p-0">
                                <div class="row">
                                    <?php if(empty($logged_user)){ ?>
                                    <div class="col-12">
                                        <div class="input-container">
                                            <input type="text" name="firstname" class="line-input border px-2" id="register_firstname" placeholder="First name">
                                            <span class="input-label">First name</span>
                                            <span id="Firstname_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-container">
                                            <input type="text" name="lastname" class="line-input border px-2" id="register_lastname" placeholder="Last name">
                                            <span class="input-label">Last name</span>
                                            <span id="Lastname_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <div class="col-12">
                                        <div class="input-container custom-select-container">
                                            <input type="text" readonly data-input-write="#country" class="line-input border px-2 line-custom-select" placeholder="Country" style="user-select: none">
                                            <span class="input-label">Country</span>
                                            <span id="month_error" class="input-error"></span>
                                            <input type="hidden" name="address[country]" id="country" />
                                            <ul class="line-dropdown">
                                                <li data-val="AU">Australia</li>
                                                <li data-val="AT">Austria</li>
                                                <li data-val="BE">Belgium</li>
                                                <!-- <li data-val="BR">Brazil</li> -->
                                                <li data-val="BG">Bulgaria</li>
                                                <li data-val="CA">Canada</li>
                                                <li data-val="HR">Croatia Preview</li>
                                                <li data-val="CY">Cyprus</li>
                                                <li data-val="CZ">Czech Republic</li>
                                                <li data-val="DK">Denmark</li>
                                                <li data-val="EE">Estonia</li>
                                                <li data-val="FI">Finland</li>
                                                <li data-val="FR">France</li>
                                                <li data-val="DE">Germany</li>
                                                <li data-val="GR">Greece</li>
                                                <li data-val="HK">Hong Kong</li>
                                                <li data-val="HU">Hungary</li>
                                                <li data-val="IN">India Preview</li>
                                                <li data-val="IE">Ireland</li>
                                                <li data-val="IT">Italy</li>
                                                <li data-val="JP">Japan</li>
                                                <li data-val="LV">Latvia</li>
                                                <li data-val="LT">Lithuania</li>
                                                <li data-val="LU">Luxembourg</li>
                                                <li data-val="MY">Malaysia</li>
                                                <li data-val="MT">Malta</li>
                                                <li data-val="MX">Mexico</li>
                                                <li data-val="NL">Netherlands</li>
                                                <li data-val="NZ">New Zealand</li>
                                                <li data-val="NO">Norway</li>
                                                <li data-val="PL">Poland</li>
                                                <li data-val="PT">Portugal</li>
                                                <li data-val="RO">Romania</li>
                                                <li data-val="SG">Singapore</li>
                                                <li data-val="SK">Slovakia</li>
                                                <li data-val="SI">Slovenia</li>
                                                <li data-val="ES">Spain</li>
                                                <li data-val="SE">Sweden</li>
                                                <li data-val="CH">Switzerland</li>
                                                <li data-val="AE">United Arab Emirates</li>
                                                <li data-val="UK">United Kingdom</li>
                                                <li data-val="US">United States</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-12" data-country="United States" style="display: none">
                                        <div class="input-container mb-1">
                                            <input type="text" name="ssn" maxlength="4" class="line-input border px-2" id="ssn" placeholder="SSN (Social Security Number) last 4 digits">
                                            <span class="input-label">SSN (Social Security Number) last 4 digits</span>
                                            <span id="ssn_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12 mb-2" data-country="United States" style="display: none">
                                        <p class="f-14 light text-center line-height-normal">Why SSN is required? In order to proceed with payments, we need your SSN.</p>
                                    </div>
                                    <div class="col-12" data-country="other" style="display: none">
                                        <div class="input-container">
                                            <input type="text" name="address[city]" class="line-input border px-2" id="city" placeholder="City">
                                            <span class="input-label">City</span>
                                            <span id="city_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <input type="text" name="address[line1]" class="line-input border px-2" id="line1" placeholder="Address (street, PO Box or Company name)">
                                            <span class="input-label">Address (street, PO Box or Company name)</span>
                                            <span id="line1_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <input type="text" name="address[postal_code]" class="line-input border px-2" id="postal_code" placeholder="ZIP code">
                                            <span class="input-label">ZIP code</span>
                                            <span id="postal_code_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <input type="text" name="id_number" class="line-input border px-2" id="id_number" placeholder="Your ID Number">
                                            <span class="input-label">Your ID Number</span>
                                            <span id="id_number_error" class="input-error"></span>
                                            <p class="f-14 light text-left line-height-normal mt-2">Why is this info required? In order to proceed with payments, we need a basic information about you.</p>
                                        </div>
                                        <!-- <h4 class="f-12">Identity document</h4>
                                        <div class="input-container">
                                            <span class="input-label">Front side</span>
                                            <input type="file" name="document_front" class="line-input border px-2" id="document_front" placeholder="Identity document">
                                            <span id="document_front_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <span class="input-label">Back side</span>
                                            <input type="file" name="document_back" class="line-input border px-2" id="document_back" placeholder="Identity document">
                                            <span id="document_back_error" class="input-error"></span>
                                            <p class="f-14 light text-left line-height-normal mt-2">An identifying document, either a passport or local ID card.</p>
                                        </div>
                                        <h4 class="f-12">Verify home address document</h4>
                                        <div class="input-container">
                                            <span class="input-label">Back side</span>
                                            <input type="file" name="additional_document_back" class="line-input border px-2" id="additional_document_back" placeholder="Verify home address document">
                                            <span id="additional_document_back" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <span class="input-label">Front side</span>
                                            <input type="file" name="additional_document_front" class="line-input border px-2" id="additional_document_front" placeholder="Verify home address document">
                                            <span id="additional_document_front_error" class="input-error"></span>
                                            <p class="f-14 light text-left line-height-normal mt-2">A document showing address, either a passport, local ID card, or utility bill from a well-known utility company.</p>
                                        </div> -->
                                        <div class="input-container">
                                            <input type="text" name="phone" class="line-input border px-2" id="phone" placeholder="Phone">
                                            <span class="input-label">Phone</span>
                                            <span id="phone_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <?php if(empty($logged_user)){ ?>
                                    <div class="col-12">
                                        <div class="input-container">
                                            <input type="text" name="email" class="line-input border px-2" id="register_email" placeholder="Email">
                                            <span class="input-label">Email</span>
                                            <span id="Email_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-container">
                                            <span class="reveal_password">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                                <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                            </svg>
                                            </span>
                                            <input type="password" name="password" class="line-input border px-2" id="register_password" placeholder="Password">
                                            <span class="input-label">Password</span>
                                            <span id="Password_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <?php }else{ ?>
                                        <input type="hidden" name="firstname" value="<?php echo $logged_user['firstname']; ?>">
                                        <input type="hidden" name="lastname" value="<?php echo $logged_user['lastname']; ?>">
                                        <input type="hidden" name="email" value="<?php echo $logged_user['email']; ?>">
                                        <input type="hidden" name="stripe_customer" value="<?php echo $logged_user['stripe_customer']; ?>">
                                    <?php } ?>
                                    <div class="col-12">Date of Birth</div>
                                    <div class="col-4">
                                        <div class="input-container">
                                            <input type="text" readonly name="day" maxlength="2" class="line-input border px-2 line-custom-select" id="day" placeholder="Day">
                                            <span class="input-label">Day</span>
                                            <span id="day_error" class="input-error"></span>
                                            <ul class="line-dropdown">
                                                <?php for($i = 1; $i < 32; $i++){ ?>
                                                <li><?php echo $i; ?></li>
                                                <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="input-container">
                                            <input type="text" readonly name="month" readonly class="line-input border px-2 line-custom-select" id="month" placeholder="Month">
                                            <span class="input-label">Month</span>
                                            <span id="month_error" class="input-error"></span>
                                            <ul class="line-dropdown">
                                                <li>January</li>
                                                <li>February</li>
                                                <li>March</li>
                                                <li>April</li>
                                                <li>May</li>
                                                <li>June</li>
                                                <li>July</li>
                                                <li>August</li>
                                                <li>September</li>
                                                <li>October</li>
                                                <li>November</li>
                                                <li>December</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="input-container">
                                            <input type="text" readonly name="year" maxlength="4" minlength="4" class="line-input border px-2 line-custom-select" id="year" placeholder="Year">
                                            <span class="input-label">Year</span>
                                            <span id="year_error" class="input-error"></span>
                                            <ul class="line-dropdown">
                                                <?php for($i = 1960; $i < date('Y'); $i++){ ?>
                                                <li><?php echo $i; ?></li>
                                                <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-12" data-country="other" style="display: none">
                                        <div class="input-container">
                                            <input type="text" name="account_holder_name" class="line-input border px-2" id="account_holder_name" placeholder="Account Holder Name">
                                            <span class="input-label">Account Holder Name</span>
                                            <span id="account_holder_name_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <input type="text" name="account_number" class="line-input border px-2" id="account_number" placeholder="Account Number">
                                            <span class="input-label">Account Number</span>
                                            <span id="account_number_error" class="input-error"></span>
                                        </div>
                                        <div class="input-container">
                                            <input type="text" name="routing_number" class="line-input border px-2" id="routing_number" placeholder="Routing Number (not required if IBAN provided)">
                                            <span class="input-label">Routing Number <span class="text-muted">(not required if IBAN provided)</span></span>
                                            <span id="routing_number_error" class="input-error"></span>
                                        </div>
                                    </div>
                                    <!--
                                    div class="col-12 flex">
                                        <<div class="input-container mb-0">
                                            <input type="text" name="card[number]" class="line-input border px-2" id="card-number" maxlength="19" placeholder="Card number" onkeypress="return onlyNumberKey(event)">
                                            <span class="input-label">Card number</span>
                                            <span id="card-number_error" class="input-error"></span>
                                        </div>
                                        <div style="width: 70px;" class="input-container ml-auto mb-0">
                                            <input type="text" name="card[exp_month]" class="line-input border px-1 text-center card_month" id="card-month-year" placeholder="MM" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                            <span class="input-label">MM</span>
                                            <span id="card-number_error" class="input-error"></span>
                                        </div>
                                        <div style="width: 70px;" class="input-container ml-auto mb-0">
                                            <input type="text" name="card[exp_year]" class="line-input border px-1 text-center card_year" id="card-month-year" placeholder="YY" maxlength="2" onkeypress="return onlyNumberKey(event)">
                                            <span class="input-label">YY</span>
                                            <span id="card-number_error" class="input-error"></span>
                                        </div>
                                        <div style="width: 80px" class="input-container ml-auto mb-0">
                                            <input type="text" name="card[cvc]" class="line-input border px-1 text-center" id="card-cvc" placeholder="CVC" maxlength="3" onkeypress="return onlyNumberKey(event)">
                                            <span class="input-label">CVC</span>
                                            <span id="card-number_error" class="input-error"></span>
                                        </div>
                                    </div>
                                     -->
                                </div>
                                <div class="row">
                                    <div class="col-12 mt-2 flex aic jcc">
                                        <?php
                                            if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
                                                $ip = $_SERVER['HTTP_CLIENT_IP'];
                                            } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                                                $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
                                            } else {
                                                $ip = $_SERVER['REMOTE_ADDR'];
                                            }
                                        ?>
                                        <input type="hidden" name="ip" value="<?php echo $ip; ?>">
                                        <input type="hidden" name="date" value="<?php echo time(); ?>">

                                        <button type="submit" class="btn black-bg white f-16">CREATE TEACHER ACCOUNT</button>
                                    </div>
                                    <?php if(empty($logged_user)){ ?>
                                    <hr class="mt-5 mb-3">
                                    <div class="col-12 mt-2 flex aic jcc">
                                        <a href="javascript:;" class="link link-black black light text-underline" title="Already have an account?" data-popup="login-popup">Already have an account?</a>
                                    </div>
                                    <?php } ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php }else{ ?>
    <section class="pt-0">
        <div class="container">
            <div class="row big-gap">
                <div class="col-12 text-center">
                    <h1 class="h2 mb-6 line-height-small">YOU ARE TEACHER ALREADY</h1>
                    <a href="account/classes" class="btn black-bg white" title="MY CLASSES">MY CLASSES</a>
                </div>
            </div>
        </div>
    </section>
    <?php } ?>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/credit-card-input.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
$('.line-custom-select').on('keyup', function(){
    var chars = $(this).val().length;
    var val = $(this).val().toLowerCase();
    var pp = $(this).parent();

    if(chars > 1){
        pp.find(".line-dropdown li").hide();
        pp.find(".line-dropdown li").each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(val) != -1){
                $(this).show();
            }
        });
    }else{
        pp.find(".line-dropdown li").show();
    }
});

$('html, body').on('click', function(){
    $('.line-dropdown').hide(200);
});
$('.line-custom-select').on('click focus', function(e){
    e.stopPropagation();
    var pp = $(this).parent();
    pp.find(".line-dropdown li").show();
    $('.line-dropdown').hide();
    $(this).parent().find('.line-dropdown').show();
});
$('.line-dropdown li').on('click', function(e){
    if($(this).data('val')){
        var data_val = $(this).data('val');
        console.log(data_val);
        $('#country').val(data_val);
        if(data_val == 'US'){
            $('[data-country="United States"]').show();
            $('[data-country="other"]').hide();
            $('[name="address[city]"]').val('');
            $('[name="address[postal_code]"]').val('');
            $('[name="id_number"]').val('');
        }else if(data_val != 'US'){
            $('[data-country="United States"]').hide();
            $('[data-country="other"]').show();
            $('[name="ssn"]').val('');
        }
    }
    $(this).parent().parent().find('.line-custom-select').val($(this).text());
    $(this).parent().parent().find('.line-dropdown').hide();
});
$('#card-number').on('keypress change blur', function () {
    $(this).val(function (index, value) {
        return value.replace(/[^a-z0-9]+/gi, '').replace(/(.{4})/g, '$1 ');
    });
});

$('#card-number').on('keyup', function () {
    if($(this).val().length > 18){
        $('#card-month-year').focus();
    }
});

$('.card_month').on('keyup', function () {
    if($(this).val().length > 1){
        $('.card_year').focus();
    }
});
$('.card_year').on('keyup', function () {
    if($(this).val().length > 1){
        $('#card-cvc').focus();
    }
});
$('#card-number').on('copy cut paste', function () {
    setTimeout(function () {
        $('#credit-card').trigger("change");
    });
});

</script>
</body>
</html>