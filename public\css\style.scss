@import "include-media";

// colors
$white: #fff;
$lightGrayAdmin: #fafafa;
$lightGray: #f8f8f8;
$gray: #F0F0F0;
$lightText: #bcbcbc;
$midGray: #969696;
$darkGray: #000;
$red: #000;
$darkRed: #BF2828;
$lagreeRed: #871a1d;
$yellow: #F8C158;
$green: #52C15A;
$textGreen: #52C15A;
$textRed: #DB1818;
$blue: #1264FF;
$black: #000;
$fullRedLink: #DB1818;

.g-color {color: #e34133}
.g-bg-color {background-color: #e34133 !important;}
.tw-color {color: #4EA5D9}
.fb-color {color: #4267B2}
.fb-bg-color {background-color: #4267B2}
.ln-color {color: #0077B5}
.ig-color {color: #2D333B}
.yt-color {color: $red}

.social-link {
    display: block;
}

//static variables
$header: 90px;
$gap: 20px;
$bigGap: 150px;
$fontSize: 16px;
$fieldHeight: 60px;
$border2x: 2px solid $gray;
$border: 1px solid $gray;
$borderLight: 1px solid $lightGray;
$borderRadius: 5px;

$transition1: all 0.25s ease-in-out 0s;
$transition2: all 0.4s cubic-bezier(.13,.56,.38,.89) 0s;

.transition1 { transition: $transition1};
.transition2 { transition: $transition2};
/* scrollbar */
::-webkit-scrollbar-track {
    background-color: $gray;
}
::-webkit-scrollbar {
    width: 6px;
    background-color: $black;
}
::-webkit-scrollbar-thumb {
    background-color: $black;
}
* {
    scrollbar-color: $black $gray;
    scrollbar-width: thin;
}
/* selection */
::-moz-selection {
	color: #fff;
	background: $black;
}
::selection {
	color: #fff;
	background: $black;
}
:focus {
	outline: none !important;
}
button:focus {
	outline: none !important;
}
p {
    line-height: 35px;
    word-break: break-word;
}
textarea {
    resize: vertical;
}
 
.star-container {
    min-width: 60px;
    text-align: right;
    position: relative;
    line-height: 1.1;
    top: 0;
    display: inline-block;
}
.lh-small {line-height: 1;}
.lh-20 {line-height: 20px;}
.lh-25 {line-height: 25px;}
.lh-35 {line-height: 25px;}
.lh-30 {line-height: 30px;}
.ls-50 {letter-spacing: 0.05em !important;}
.line-height-small {line-height: 1.1 !important;}
.lh-1 {line-height: 1.1 !important;}
.lh-all * {line-height: 1.1 !important;}
.line-height-normal {line-height: 1.5 !important;}
.line-height-big {line-height: 1.8 !important;}
body .btn .lettet-50 {letter-spacing: 0.05em !important;}
.hidden { display: none; }
.text-center {text-align: center !important;}
.text-left {text-align: left !important;}
.text-right {text-align: right !important;}
.text-justify {text-align: justify !important;}
.text-underline {text-decoration: underline !important;}
.text-underline.midGray {text-decoration-color: #969696 !important;}
.text-uppercase {text-transform: uppercase !important;}
.text-capitalize {text-transform: capitalize !important;}
.text-transf-none {text-transform: none !important;}


.light { font-weight: 300 !important}
.normal { font-weight: 400 !important}
.medium { font-weight: 500 !important}
.semibold { font-weight: 600 !important}
b, strong { font-weight: 600 !important}
.bold { font-weight: 600 !important}

.white { color: $white !important; }
.lightGray { color: $lightGray !important; }
.gray { color: $gray !important; }
.midGray { color: $midGray !important; }
.darkGray { color: $darkGray !important; }
.red { color: $red !important; }
.black { color: $black !important; }
.green { color: $green !important; }
.blue { color: $blue !important; }
.darkRed { color: $darkRed !important; }
.lagreeRed { color: $lagreeRed !important; }
.yellow { color: $yellow !important; }
.textGreen { color: $textGreen !important; }
.textRed { color: $textRed !important; }
.fullRedLink { color: $fullRedLink !important; }
.fullRedLink:hover, .fullRedLink.active { color: #969696 !important; }
.red-hover:hover { color: $red;}

.white-bg { background-color: $white !important; }
.lightGray-bg { background-color: $lightGray !important; }
.lightGrayAdmin-bg { background-color: $lightGrayAdmin !important; }
.gray-bg { background-color: $gray !important; }
.midGray-bg { background-color: $midGray !important; }
.darkGray-bg { background-color: $darkGray !important; }
.red-bg { background-color: $red !important; }
.black-bg { background-color: $black !important; }
.darkRed-bg { background-color: $darkRed !important; }
.blue-bg { background-color: $blue !important; }
.lagreeRed-bg { background-color: $lagreeRed !important; }
.yellow-bg { background-color: $yellow !important; }
.normalRed-bg { background-color: $fullRedLink !important; }

.radius-10 {border-radius:10px;}
.radius-20 {border-radius:20px;}
.radius-30 {border-radius:30px;}
.radius-40 {border-radius:40px;}
.radius-50 {border-radius:50px;}

.btn.white-bg { color: $darkGray; }
.btn.gray-bg { color: $darkGray; }
.btn.lightGray-bg { color: $darkGray; }
.btn.gray-bg { color: $darkGray; }
.btn.midGray-bg { color: $midGray; }
.btn.darkGray-bg { color: $white; }
.btn.darkGray-bg { background-color: #000 !important; }
.btn.red-bg { color: $white;}
.btn.greyborder {border:1px solid #ddd;}
.btn.greyborder:hover {background:#ddd;}

.btn-badge.white-bg:hover, body .btn.white-bg:hover, button.white-bg:hover { background-color: $black !important; color: $white !important; border-color: #000;}
.btn-badge.lightGray-bg:hover, .btn.lightGray-bg:hover, button.lightGray-bg:hover { background-color: $gray !important; }
.btn-badge.gray-bg:hover, .btn.gray-bg:hover, button.gray-bg:hover { background-color: $midGray !important; }
.btn-badge.midGray-bg:hover, .btn.midGray-bg:hover, button.midGray-bg:hover { background-color: $darkGray !important; }
.btn-badge.darkGray-bg:hover, .btn.darkGray-bg:hover, button.darkGray-bg:hover { background-color: #fff !important; color:#000 !important;}
.btn-badge.red-bg:hover, .btn.red-bg:hover, button.red-bg:hover {background-color: #fff !important; color: #000 !important; }
.btn-badge.black-bg:hover, .btn.black-bg:hover, button.black-bg:hover { background-color: #fff !important; color: #000 !important; border: 1px solid #000;}
.btn.black-bg.btn--loading:hover {background: #000 !important;}
.btn.btn-border.bg-white:hover { color: #fff !important;}
.btn.fb-bg-color {background-color: #4267B2 !important}
.btn.fb-bg-color:hover {background-color: #224385 !important}
.btn.hover-to-border:hover {background-color: #fff !important; border: 1px solid $gray !important; color: $black !important}
.btn.hover-to-border-black:hover {background-color: #fff !important; border: 1px solid $black !important; color: $black !important}

.btn.g-bg-color {background-color: #af2f23 !important;}
.btn.g-bg-color:hover {background-color: #af2f23 !important}

.link.link-white:hover { color: $lightGray !important; }
.link.link-lightGray:hover { color: $gray !important; }
.link.link-gray:hover { color: $midGray !important; }
.link.link-midGray:hover { color: $darkGray !important; }
.dark-form .link.link-midGray:hover { color: $white !important; }
.link.link-darkGray:hover { color: #000 !important; }
.link.link-red:hover { color: #444 !important; }
.link.link-blue:hover { color: $blue !important; }
.link.link-black:hover { color: #666 !important; }
.link.no-underline:hover { text-decoration: none !important; }
.opacityfull {opacity:1 !important;}

.change_subscription_submit.disabled {
	box-shadow: 0 0 0 1px #F0F0F0 inset;
	background: #fff !important;
	color: #000 !important;
	opacity: 1;
}
// buttons, links
.link:hover {
    text-decoration: underline;
}
.link {
    text-decoration: none;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    line-height: 1;
}
.invert {
    filter: invert(1);
}
a:hover .invert{
    filter: invert(0) !important;
}
body .radius-20 {
    border-radius: 20px !important;
}
// margin
.m-auto { margin: auto; }
.m-0 { margin: 0 !important; }
.m-1 { margin: calc($gap / 2) !important; }
.m-2 { margin: $gap !important; }
.m-3 { margin: $gap * 1.5 !important; }
.m-4 { margin: $gap * 2 !important; }
.m-5 { margin: $gap * 2.5 !important; }

// margin-top
.mt-auto { margin-top: auto !important; }
.mt-0 { margin-top: 0; }
.mt-05 { margin-top: 05px !important; }
.mt-1 { margin-top: calc($gap / 2) !important; }
.mt-15 { margin-top: 15px !important; }
.mt-2 { margin-top: $gap; }
.mt-3 { margin-top: $gap * 1.5 !important; }
.mt-4 { margin-top: $gap * 2 !important; }
.mt-5 { margin-top: $gap * 2.5 !important; }
.mt-55 {margin-top: 55px !important;}
.mt-6 { margin-top: $gap * 3 !important; }
.mt-80 { margin-top: $gap * 4 !important; }
.mt-100 { margin-top: $gap * 5 !important; }
.mt--100 { margin-top: -($gap * 5) !important; }

// margin-bottom
.mb-auto { margin-bottom: auto !important; }
.mb-0 { margin-bottom: 0 !important; }
.mb-05 { margin-bottom: 05px !important; }
.mb-1 { margin-bottom: calc($gap / 2) !important; }
.mb-15 { margin-bottom: 15px !important; }
.mb-25 { margin-bottom: 25px !important; }
.mb-2 { margin-bottom: $gap !important; }
.mb-3 { margin-bottom: $gap * 1.5 !important; }
.mb-4 { margin-bottom: $gap * 2 !important; }
.mb-5 { margin-bottom: $gap * 2.5 !important; }
.mb-55 {margin-bottom: 55px !important;}
.mb-6 { margin-bottom: $gap * 3 !important; }
.mb-75 { margin-bottom: 75px !important; }
.mb-80 { margin-bottom: $gap * 4 !important; }
.mb-100 { margin-bottom: $gap * 5 !important; }
.mb-150 { margin-bottom: 150px !important; }
.mb-200 { margin-bottom: $gap * 10 !important; }
.mb-250 { margin-bottom: $gap * 12.5 !important; }

// margin-left
.ml-auto { margin-left: auto !important; }
.ml-0 { margin-left: 0 !important; }
.ml-05 { margin-left: calc($gap / 4) !important; }
.ml-1 { margin-left: calc($gap / 2) !important; }
.ml-15 { margin-left: 15px !important; }
.ml-2 { margin-left: $gap !important; }
.ml-3 { margin-left: $gap * 1.5 !important; }
.ml-4 { margin-left: $gap * 2 !important; }
.ml-5 { margin-left: $gap * 2.5 !important; }
.ml-6 { margin-left: $gap * 3 !important; }

// margin-right
.mr-auto { margin-right: auto !important; }
.mr-0 { margin-right: 0 !important; }
.mr-05 { margin-right: calc($gap / 4) !important; }
.mr-1 { margin-right: calc($gap / 2) !important; }
.mr-15 { margin-right: 15px !important; }
.mr-2 { margin-right: $gap !important; }
.mr-3 { margin-right: $gap * 1.5 !important; }
.mr-4 { margin-right: $gap * 2 !important; }
.mr-5 { margin-right: $gap * 2.5 !important; }
.mr-6 { margin-right: $gap * 3 !important; }
.mr-150 { margin-right: $gap * 6 !important; }

// margin-x
.mx-auto { margin-right: auto !important; margin-left: auto !important; }
.mx-0  { margin-right: 0 !important;margin-left: 0 !important; }
.mx-05 { margin-right: calc($gap / 4) !important;margin-left: calc($gap / 4) !important; }
.mx-1  { margin-right: calc($gap / 2) !important;margin-left: calc($gap / 2) !important; }
.mx-2  { margin-right: calc($gap / 1) !important;margin-left: calc($gap / 1) !important; }
.mx-3  { margin-right: $gap * 1.5 !important;margin-left: $gap * 1.5 !important; }
.mx-4  { margin-right: $gap * 2 !important;margin-left: $gap * 2 !important; }
.mx-5  { margin-right: $gap * 2.5 !important;margin-left: $gap * 2.5 !important; }

// margin-y
.my-auto { margin-top: auto; margin-bottom: auto !important; }
.my-0  { margin-top: 0;margin-bottom: 0 !important; }
.my-05 { margin-top: calc($gap / 4) !important;margin-bottom: calc($gap / 4) !important; }
.my-1  { margin-top: calc($gap / 2) !important;margin-bottom: calc($gap / 2) !important; }
.my-2  { margin-top: calc($gap / 1) !important;margin-bottom: calc($gap / 1) !important; }
.my-3  { margin-top: $gap * 1.5 !important;margin-bottom: $gap * 1.5 !important; }
.my-4  { margin-top: $gap * 2 !important;margin-bottom: $gap * 2 !important; }
.my-5  { margin-top: $gap * 2.5 !important;margin-bottom: $gap * 2.5 !important; }
.my-6  { margin-top: $gap * 3 !important;margin-bottom: $gap * 3 !important; }
.my-80  { margin-top: $gap * 4 !important;margin-bottom: $gap * 4 !important; }
.my-100  { margin-top: $gap * 5 !important;margin-bottom: $gap * 5 !important; }

// padding
.p-0 { padding: 0 !important; }
.p-1 { padding: calc($gap / 2) !important; }
.p-2 { padding: $gap !important; }
.p-3 { padding: $gap * 1.5 !important; }
.p-4 { padding: $gap * 2 !important; }
.p-5 { padding: $gap * 2.5 !important; }
.p-8 { padding: $gap * 4 !important; }
.p-10 { padding: $gap * 5 !important; }

// padding-top
.pt-0 { padding-top: 0 !important; }
.pt-05 { padding-top: 5px !important; }
.pt-1 { padding-top: calc($gap / 2) !important; }
.pt-15 { padding-top: 15px !important; }
.pt-2 { padding-top: $gap !important; }
.pt-25 { padding-top: 25px !important; }
.pt-3 { padding-top: $gap * 1.5 !important; }
.pt-4 { padding-top: $gap * 2 !important; }
.pt-5 { padding-top: $gap * 2.5 !important; }
.pt-55 { padding-top: 55px !important; }
.pt-6 { padding-top: 60px !important; }
.pt-7 { padding-top: 70px !important; }
.pt-8 { padding-top: 80px !important; }
.pt-100 { padding-top: 100px !important; }
.pt-150 { padding-top: 150px !important; }
.pt-200 { padding-top: 200px !important; }

// padding-bottom
.pb-0 { padding-bottom: 0 !important; }
.pb-05 { padding-bottom: 5px !important; }
.pb-1 { padding-bottom: calc($gap / 2) !important; }
.pb-15 { padding-bottom: 15px !important; }
.pb-2 { padding-bottom: $gap !important; }
.pb-25 { padding-bottom: 25px !important; }
.pb-3 { padding-bottom: $gap * 1.5 !important; }
.pb-4 { padding-bottom: $gap * 2 !important; }
.pb-5 { padding-bottom: $gap * 2.5 !important; }
.pb-55 { padding-bottom: 55px !important; }
.pb-6 { padding-bottom: 60px !important; }
.pb-7 { padding-bottom: 70px !important; }
.pb-8 { padding-bottom: $gap * 4 !important; }
.pb-100 { padding-bottom: 100px !important; }
.pb-150 { padding-bottom: 150px !important; }
.pb-200 { padding-bottom: 200px !important; }

// padding-left
.pl-0 { padding-left: 0 !important; }
.pl-05 { padding-left: 5px !important; }
.pl-1 { padding-left: calc($gap / 2) !important; }
.pl-2 { padding-left: $gap !important; }
.pl-3 { padding-left: $gap * 1.5 !important; }
.pl-4 { padding-left: $gap * 2 !important; }
.pl-5 { padding-left: $gap * 2.5 !important; }
.pl-75 { padding-left: 75px !important; }
.pl-80 { padding-left: 80px !important; }
.pl-100 { padding-left: 100px !important; }
.pl-120 { padding-left: 120px !important; }
.pl-150 { padding-left: 150px !important; }
.pl-200 { padding-left: 200px !important; }

// padding-right
.pr-0 { padding-right: 0 !important; }
.pr-05 { padding-right: 5px !important; }
.pr-1 { padding-right: calc($gap / 2) !important; }
.pr-2 { padding-right: $gap !important; }
.pr-3 { padding-right: $gap * 1.5 !important; }
.pr-4 { padding-right: $gap * 2 !important; }
.pr-5 { padding-right: $gap * 2.5 !important; }
.pr-6 { padding-right: 60px !important; }
.pr-75 { padding-right: 75px !important; }
.pr-100 { padding-right: 100px !important; }
.pr-120 { padding-right: 120px !important; }
.pr-150 { padding-right: 150px !important; }
.pr-200 { padding-right: 200px !important; }

// padding-y
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-05 { padding-top: calc($gap / 4) !important; padding-bottom: calc($gap / 4) !important; }
.py-1 { padding-top: calc($gap / 2) !important; padding-bottom: calc($gap / 2) !important; }
.py-2 { padding-top: $gap !important; padding-bottom: $gap !important; }
.py-25 { padding-top: 25px !important; padding-bottom: 25px !important; }
.py-3 { padding-top: $gap * 1.5 !important; padding-bottom: $gap * 1.5 !important; }
.py-4 { padding-top: $gap * 2 !important; padding-bottom: $gap * 2 !important; }
.py-5 { padding-top: $gap * 2.5 !important; padding-bottom: $gap * 2.5 !important; }
.py-6 { padding-top: $gap * 3 !important; padding-bottom: $gap * 3 !important; }
.py-8 { padding-top: $gap * 4 !important; padding-bottom: $gap * 4 !important; }
.py-100 { padding-top: $gap * 5 !important; padding-bottom: $gap * 5 !important; }
.py-120 { padding-top: 120px !important; padding-bottom: 120px !important; }
.py-150 { padding-top: 150px !important; padding-bottom: 150px !important; }
.py-200 { padding-top: 200px !important; padding-bottom: 200px !important; }

// padding-x
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-05 { padding-left: calc($gap / 4) !important; padding-right: calc($gap / 4) !important; }
.px-1 { padding-left: calc($gap / 2) !important; padding-right: calc($gap / 2) !important; }
.px-2 { padding-left: $gap !important; padding-right: $gap !important; }
.px-3 { padding-left: $gap * 1.5 !important; padding-right: $gap * 1.5 !important; }
.px-4 { padding-left: $gap * 2 !important; padding-right: $gap * 2 !important; }
.px-5 { padding-left: $gap * 2.5 !important; padding-right: $gap * 2.5 !important; }
.px-6 { padding-left: $gap * 3 !important; padding-right: $gap * 3 !important; }
.px-8 { padding-left: $gap * 4 !important; padding-right: $gap * 4 !important; }
.px-100 { padding-left: $gap * 5 !important; padding-right: $gap * 5 !important; }
.px-120 { padding-left: $gap * 6 !important; padding-right: $gap * 6 !important; }
.px-135 { padding-left: 145px !important; padding-right: 145px !important; }
.px-150 { padding-left: 150px !important; padding-right: 150px !important; }
.px-200 { padding-left: 200px !important; padding-right: 200px !important; }

.h-40 {height:40px;}
.h-42 {height:42px !important;}
.h-46 {height:46px !important;}
.h-54 {height:54px !important;}

// font
.f-0 { font-size: calc($fontSize / 2) !important; }
.f-10 { font-size: 10px !important; }
.f-11 { font-size: 11px !important; }
.f-1 { font-size: 12px !important; }
.f-12 { font-size: 12px !important; }
.f-14 { font-size: 14px !important; }
.f-16 { font-size: $fontSize !important; }
.f-18 { font-size: 18px !important; }
.f-20 { font-size: 20px !important; }
.f-24 { font-size: 24px !important; }
.f-2 { font-size: $fontSize !important; }
.f-30 { font-size: 30px !important; }
.f-3 { font-size: $fontSize * 1.5 !important; }
.f-4 { font-size: $fontSize * 2 !important; }
.f-5 { font-size: $fontSize * 2.5 !important; }
.f-6 { font-size: $fontSize * 5 !important; }

hr.my-100.mb-0 {
    margin-bottom: 0 !important;
}
// border
.left-border {border-left: $border !important}
.top-border {border-top: $border !important}
.right-border {border-right: $border !important}
.bottom-border {border-bottom: $border !important}
.border {border: $border !important}
.line-input.border {border: 1px solid #ddd !important}
.no-border {border: none !important}
.no-top-border {border-top: none !important}

.help-form .input-label {
	left: 21px;
	top: -9px;
	font-size: 10px;
}
.p-align-just p {
	text-align:justify !important;
}
// HEADING
h1, .h1 { font-weight: normal; font-size: 64px !important; letter-spacing: 0.05em; line-height: 80px; }
h2, .h2-big { font-weight: normal; font-size: 48px !important; letter-spacing: 0.05em; line-height: 64px; }
h2, .h2 { font-weight: normal; font-size: 30px !important; letter-spacing: 0.05em; line-height: 50px; }
h3, .h3 { font-weight: normal; font-size: 24px !important; }
h4, .h4 { font-weight: normal; font-size: 20px !important; }
h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }

h1.big-title {
    font-size: $fontSize * 4 !important;
}
h1.secondary, .h1.secondary { color: $darkGray; }
h2.secondary, .h2.secondary { color: $darkGray; }
h3.secondary, .h3.secondary { color: $darkGray; }
h4.secondary, .h4.secondary { color: $darkGray; }
h5.secondary, .h5.secondary { color: $darkGray; }

.with-line {
    position: relative;

    &::before {
        content: "";
        width: 40px;
        height: 2px;
        background: $red;
        position: absolute;
        bottom: -24px;
        left: 0;
    }
}
a, button { transition: $transition1; }
img {
    max-width: 100%;
    display: block;
}
.d-block {
    display: block;
}
.d-inline-block {
    display: inline-block;
}
hr {
	border: none;
	height: 1px;
	width: 100%;
	background: #F0F0F0;
	margin: 150px 0;
}
.full-row {
    width: calc(100% + 30px);
}
.row{
    display:flex;
    flex-wrap:wrap;
    margin-right: -($gap * 0.75);
    margin-left:-($gap * 0.75)
}
.container {
    margin: auto;
    max-width: 1280px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container1530 {
    margin: auto;
    max-width: 1500px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}


.container1100 {
    margin: auto;
    max-width: 1115px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container1030 {
    margin: auto;
    max-width: 1030px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container1080 {
    margin: auto;
    max-width: 1100px;
    width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container700 {
    margin: auto;
    max-width: 730px;
    width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container750 {
    margin: 0 auto;
    max-width: 780px;
    width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container800 {
  margin: auto;
  max-width: 830px;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
.container870 {
    margin: auto;
    max-width: 700px;
    width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.container640 {
    margin: auto;
    max-width: 640px;
    width: 640px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.max450 {max-width:450px !important; margin-left: auto; margin-right: auto;}
.max550 {max-width:550px !important;}
.max570 {
  max-width: 570px;
}
.courses-left-wrap {
  max-width: 735px !important;
}

.max640 {
    max-width: 640px !important;
}

.max680 {
    max-width: 600px;
}
  
.max750 {
    max-width: 750px;
}
.max800 {
    max-width: 800px;
}
.container-footer {
    margin: auto;
    max-width: 1400px;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
footer .container {max-width:1200px;}

.container-fluid {
    margin: auto;
    max-width: 100%;
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
}
.row-vertical{
    display:flex;
    flex-direction: column;
    flex-wrap:wrap;
    margin-right:-($gap * 0.75);
    margin-left:-($gap * 0.75)
}
.transition1 { transition: $transition1;}
.transition2 { transition: $transition2;}
.overflow {overflow:hidden;}
.float-right {
    float: right !important;
}
.float-left {
    float: left !important;
}
.flex-column{
    flex-direction: column !important;
}
.flex-row{
    flex-direction: row !important;
}
.flex {
    display: flex !important;
}
.nowrap {
    flex-wrap: nowrap;
}
.h100 {
    height: 100%;
}
.h100vh {
    height: 100vh;
}
.h-auto {
    height: auto !important;
}
.h450 {
	height: 23.45vw;
}
.h450px {
	height: 450px;
}
.h360 {
	height: 18.75vw;
}
body .h50 {
	height: 50px !important;
}
body .h60 {
	height: 60px !important;
}
.d-block {
    display: block;
}
.d-inline {
    display: inline !important;
}
.d-inline-block {
    display: inline-block !important;
}
.w100 {
    width: 100%;
}
.w160px {
    width: 160px;
    height: 90px;
}
.w50px {
    width: 50px;
}
.aic { align-items: center !important; }
.ail { align-items: flex-start !important; }
.air { align-items: flex-end !important; }
.jcr { justify-content: flex-end !important; }
.jcl { justify-content: flex-start !important; }
.jcsb { justify-content: space-between !important; }
.jcc { justify-content: center !important;}
[class*='col-']{
    position: relative;
}
.arrow-down {
    width: 12px;
    height: 8px;
    background: url(../images/arrow-down.svg) no-repeat center center / cover;
    display: inline-block;
}
.block-btn {
	width: 100%;
}
.big-big-gap {
    margin-left: -2vw;
    margin-right: -2vw;

    [class*='col-'] {
        padding-left: 1.5vw;
        padding-right: 1.5vw;
    }
}
.big-gap {
    margin-right: -($gap * 1.25);
    margin-left: -($gap * 1.25);

    [class*='col-'] {
        padding-left: $gap * 1.25;
        padding-right: $gap * 1.25;
    }
}
.normal-gap {

    [class*='col-'] {
        padding-left: $gap * 0.75;
        padding-right: $gap * 0.75;
    }
}
.small-gap {
    margin-right: -($gap * 0.4);
    margin-left: -($gap * 0.4);

    [class*='col-'] {
        padding-left: $gap * 0.4;
        padding-right: $gap * 0.4;
    }
}
.col-12{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 100%;
    flex:0 0 100%;
    max-width: 100%;
}
.col-10{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 83.333333%;
    flex:0 0 83.333333%;
    max-width: 83.333333%;
}
.col-9{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 75%;
    flex:0 0 75%;
    max-width: 75%;
}
.col-8{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 66.6666%;
    flex:0 0 66.6666%;
    max-width: 66.6666%;
}
.col-7{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 58.333333%;
    flex:0 0 58.333333%;
    max-width: 58.333333%;
}
.col-6{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 50%;
    flex:0 0 50%;
    max-width: 50%;
}
.col-5{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 41.666667%;
    flex:0 0 41.666667%;
    max-width: 41.666667%;
}
.col-4{
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 33.3333%;
    flex:0 0 33.3333%;
    max-width: 33.3333%;
}
.col-3 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 25%;
    flex:0 0 25%;
    max-width: 25%;
}
.col-2-5 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 25%;
    flex:0 0 25%;
    max-width: 25%;
}
.col-20 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 20%;
    flex:0 0 20%;
    max-width: 20%;
}
.col-2 {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: 16.666667%;
    flex:0 0 16.666667%;
    max-width: 16.666667%;
}
.col {
    padding-left: $gap * 0.75;
    padding-right: $gap * 0.75;
    width: auto;
}

.offset-1 { margin-left: 8.333333%; }
.offset-2 { margin-left: 16.666667%; }
.offset-3 { margin-left: 25%; }
.offset-4 { margin-left: 33.333333%; }
.offset-5 { margin-left: 41.666667%; }
.offset-6 { margin-left: 50%; }

@media(max-width: 767px){
    .col, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2-5, .col-2 {
        position: relative;
        width: 100%;
        padding-right: 10px;
        padding-left: 10px;
        max-width: 100%;
        flex: 0 0 100%;
    }
    .col-6.mob-half {
        width: 50%;
        max-width: 50%;
        flex: 0 0 50%;
    }
    .row.row-mob {
        flex-direction: row !important;
    }
    .container,
    .container-footer,
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    .container640 {
        width: 100%;
        padding-left: 10px;
        padding-right: 10px;
    }
    .row-vertical{
        margin-right:-10px;
        margin-left:-10px
    }
    .single-wrapper .mvp-poster-holder, .single-wrapper .mvp-player-holder {
        border-radius: 0 !important;
    }
}
// groups
@mixin fieldLabel {
    font-size: $fontSize * 0.75;
    font-weight: 400;
    display: block;
    padding: 10px 0;
    color: $darkGray;
}
@mixin height_no_header {
    height: calc(100vh - #{$header});
}
@mixin small_header {
    min-height: 600px;
}
@mixin smaller_header {
    min-height: 300px;
}
@mixin baseFont {
    font-size: $fontSize;
    font-weight: 400;
    color: $black;
    line-height: 1.4;
}
@mixin centerPosition {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}
@mixin centerAbsPosition {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}

// main style
body {
    @include baseFont;
    &.show-popup {
        overflow: hidden;
    }

    main {
        position: relative;
        padding-top: $header;
    }
    &.homepage main,
    &.transparent main{
        padding-top: 0;
    }
    &.transparent header {
        border: none !important;
        background: rgba(255, 255, 255, 0);

        .white-logo {
            display: block;
        }
        .black-logo {
            display: none;
        }
        a{
            font-weight: 600;
            color: $white;

            &.favs {
                filter: invert(1);
            }
            &:hover{
                color: $midGray;
            }
            &.active{
                color: #969696;
            }
        }
    }
}
header .left-menu a{
    font-weight: 600;
    margin-left:17.5px; 
    margin-right: 17.5px;
    display: flex;
  align-items: center;

    &.active{
        color: #969696;
    }
    &:hover{
        color: $midGray !important;
    }
}
.left-menu.hidemenu {opacity:0;} 
section {
    padding-top: 150px;
    padding-bottom:150px;
    position: relative;
}
section:not(.classesroot), section.px-100, section.p-10 {
    padding-left: 145px !important;
    padding-right: 145px !important;
}

section.sec_100 {padding-top: 90px; padding-bottom: 75px;}

section.normal-padding {
    padding: calc($bigGap / 2);
}
.logo-small {
    display: none;
    margin: $gap*1.2 0 $gap*1.2 0;
    text-align: center;
}
.logo {
    display: block;
    text-align: center;
}
.icon-rotated {
    display: inline-block;
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    padding: 0 $gap * 2.5;
    height: $header;
    width: 100%;
    transition: all 0.25s ease-in-out 0s, border 0.25s ease-in-out 0s;
    background: rgba(255, 255, 255, 1);
    border-bottom: $borderLight;

    .white-logo {
        display: none;
    }
    .black-logo {
        display: block;
    }

    a {
        font-size: 12px;
        text-transform: uppercase;
        color: $black;
        letter-spacing: 0.1em;
        font-weight: 500;

        // &:last-of-type:not(.logo) {
        //     margin-right: 0;
        // }
        &:hover {
            color: $red;
        }
        svg {
            display: block;
            height:14px;
        }
    }
    .mainlink.subnav {
        position: relative;

    }
    .menu-button {
        display: none;

        &:hover {
            color: $gray;

            rect {
                fill: $red;
            }
        }
        &.active {
            color: $gray;

            rect {
                fill: $red;
            }
        }
    }
}
.dropdown-opacity {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	top: 0;
	left: 0;
	z-index: 50;
}
.dropdown-opacity.sub-bg {
	display: block;
}
.left-menu {
	width: auto;
    height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: auto;
    align-items: stretch;
}
header .mainlink.subnav {
	position: relative;
	height: 100%;
}
.sub-menu {
	position: absolute;
	top: 100%;
	left: -15px;
	line-height: 40px !important;
	background: #ffffff;
	z-index: 99999999;
	justify-content: center;
	display: block;
	pointer-events: none;
	visibility: hidden;
	opacity: 0;
	transition: transform .18s, opacity .18s, visibility 0s .18s;
    min-width: 230px;
    margin-top: 1px;
}
.subnav:hover .js-sub-menu {
    pointer-events: auto;
	visibility: visible;
	opacity: 1;
	transition-delay: 0s;
}
.js-arrow.navarrow.shop-nav + .desktopvanplus {
	display: none;
}
.sub-menu .subttl {
	font-size: 14px;
    font-weight: 600;
	margin-bottom: 25px;
    line-height: 1;
}
.sub-menu a {
	color: #000000;
	font-size: 12px;
    font-weight: 400;
}
header .left-menu .sub-menu li {line-height: 30px;}
header .left-menu .sub-menu a {
	color: #000000;
	font-size: 12px;
	font-weight: 400 !important;
    line-height: normal;
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0px) !important;
    text-transform: initial;
    display: inline-block;
}
header .left-menu .sub-menu .subnav-wrap li a {margin: 0; letter-spacing: 0.05em;}
.sub-menu:not(.submenu-large) {padding:20px 30px;}

.sub-menu2 a {
	text-transform: none !important;
}
.desktopvanplus {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	left: 10px;
	position: relative;
	font-size: 28px;
	z-index: 1;
    font-weight: 300;
}
.sub-menu2 {
	display: none;
	margin-top: 0px;
}
body.scrolled header {
    height: 70px;
}
.right-menu {
    display: flex;
    align-items: center;
    position: relative;

    .dropdown-menu.drop-right {
        border-radius: 0;
        border: none;
        min-width: 250px;
        padding: 12px 20px 10px;
    }
    li {
        margin: 0 !important;
    }
    .link {
        font-size: 11px !important;
        font-weight: 400;
        line-height: 13px !important;
        margin: 10px 0 !important;
    }
    hr {
        margin: 10px 0;
    }
    .avatar {
        height: 30px;
        width: 30px;
        display: block;

        .initials {
            border: 2px solid #000;
            font-size: 12px;
            background: #000;
            color: #fff;
            font-weight: 600;
        }
    }
}
.dropdown-button {
    z-index: 11;
    background: $white;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}
.dropdown {
	position: relative;

    > .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 150px;
        width: auto;
        z-index: 1112;
        background: $white;
        transition: all 0.25s ease-in-out 0s;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transform: translateY(-10px);
        padding: 20px 45px 20px 25px;
        border-radius: 0px;
        border: 1px solid #F0F0F0;
        margin-top: 10px;
        box-shadow: 0 0 50px rgba(51, 51, 51, 0.1);

        &.drop-right{
            right: 0;
            left: auto;
        }
        > li {
            padding: 0;
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            &:last-of-type {
                margin-bottom: 0px;
            }

            a {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: $black;
                line-height: 1.4;
                transition: $transition1;
                white-space: nowrap;

                img {
                    margin-right: 10px;
                    width: 25px;
                    height: 25px;
                }
                &:hover {
                    color: $red;
                }
            }
        }
    }
    &.opened > .dropdown-menu{
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        transform: translateY(0px);
        border-radius:10px;

    }
    &.opened > .dropdown-button{
        color: $darkGray;
    }
    &.opened > .dropdown-button:hover{
        color: $darkGray;
        background: $white;
    }
}
.dropdown.opened > .panel-button {
    z-index: 13;
}
.dropdown.opened:last-child > .panel-button,
.right-menu > .dropdown.opened > li:last-child .panel-button {
	border-radius: 0 10px 0px 0 !important;
}
.btn:hover .unlock-icon {
    filter: invert(1);
}
.btn {
    min-height: 40px;
    padding: 0 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: $transition1;
    // font-family: 'Arquitecta' !important;
    font-weight: 600 !important;
    letter-spacing: 0.1em !important;
    text-transform: uppercase !important;
    font-weight: 400;
    font-size: 12px !important;
    text-align: center;
    white-space: nowrap;
    user-select: none;
    border:1px solid #000;
    border-radius: 50px;

    & > span {
        // font-family: 'Arquitecta' !important;
        font-weight: 600 !important;
        // letter-spacing: 0 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.1em;
    }
    &.btn-sm {
        min-height: 42px;
        padding: 0 25px;
    }
    &.btn-xs {
        min-height: 35px;
        text-transform: uppercase;
        padding: 0 15px;
        font-size: 10px !important;

        &.no-hover {
            pointer-events: none;
        }
    }
    &.btn-wide {
        padding: 0 35px;
    }
    &.btn-tall {
        height: 45px;
    }
    &.btn-60 {
        height: 60px;
    }
    &.f-14 {
        font-size: 14px !important;
    }
    &.btn-round {
        border-radius: 50% !important;
    }
    &.btn-red {
      color: #DB1818;
      padding: 0;
    }
    &.btn-border {
        border-radius: 50px;
        border: 1px solid #dddddd;

        &.btn-black-border {
            border: 1px solid #000;
        }
        &:hover {
            border-color: #000;
            background: #000;

            img {
                filter: invert(1);
            }
        }
    }
    &.btn-badge {
        width: 56px;
        height: 56px !important;
        border-radius: 50% !important;
        padding: 0 10px;

        &.lightWhite-bg {
            background: rgba(255, 255, 255, 0.2);

            &:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        }
    }
}
.show-popup .overlay-blur {
    opacity: 1 !important;
    pointer-events: auto;
}
.overlay {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100vh;
    left: 0;
    // transition: all 0.5s ease-in-out 0s;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    overflow: auto;


    .overlay-blur {
        position: fixed;
        top: 0;
        width: 100%;
        height: 100vh;
        left: 0;
        z-index: 1;
        transition: all 0.5s ease-in-out 0s;
        background-color: rgba(0, 0, 0, 0.6);
        opacity: 0;
    }
    .popup {
        position: absolute;
        top: 50%;
        left: 50%;
        transition: $transition2;
        transform: translate(-50%,-50%) scale(1.05);
        width: 100%;
        max-width: 500px;
        z-index: 1002;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        background: $white;
        padding: 45px 50px;

        &.with_scroll {
            top: 50px !important;
            transform: translate(-50%, 0%) scale(1) !important;
            margin-bottom: 50px !important;
        }
        &.mini-popup {
            max-width: 400px;
        }
        &.mid-popup {
            max-width: 480px;
        }
        &.small-padding {
            padding: 25px 50px;

            .popup-body {
                padding: 0px 50px 25px 50px;
            }
        }
        .popup-body {
            padding: 0 ($gap * 4) ($gap * 3);
        }
        .popup-header {
            padding: 30px 0;
            border-bottom: 1px solid #F0F0F0;
            text-transform: uppercase;
        }
    }
}

.hub-badge {
    position: relative;
    top: -20px;
    width: 120px;
    height: 120px;
    background: #fff;
    border-radius: 50%;
    -webkit-box-shadow: 0 0 0 5px #fff;
    box-shadow: 0 0 0 5px #fff;
    margin-bottom: 10px;
}
.rate-class {
	text-align: center;
	margin: 50px 0;
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 50px;
	display: flex;
	align-items: center;
	justify-content: center;

    .rate-value {
        border: 1px solid #F0F0F0;
        width: 20%;
        border-right: none;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #000;

        &:hover {
            background: #F0F0F0;
            color: #000;
            cursor: pointer;
        }

        &.active {
            background: #000;
            color: #fff;
            cursor: pointer;
            box-shadow: 1px 0px 0px 0px #000;
            border: none !important;
            position: relative;
            z-index: 10;
        }

        &:last-of-type {
            border-right: 1px solid #F0F0F0;
            border-radius: 0 30px 30px 0;
        }
        &:first-of-type {
            border-radius: 30px 0 0 30px;
        }
    }
}
.share-social {
	text-align: center;
	margin-top: 30px;
    padding-bottom: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
    flex-direction: column;
    border-bottom: 1px solid #f0f0f0;


    .share-item {
        display: inline-flex;
        width: 100%;
        max-width: 220px;
        height: 45px;
        align-items: center;
        justify-content: center;
        border: 1px solid #ddd;
        border-radius: 50px;
        font-size: 10px;
        font-weight: 600;
        background:#fdfdfd;

        &:hover {
            background: #f8f8f8;
            /*box-shadow: 0 0 2px 0 rgba(0,0,0,0.4);*/

            svg path {
                fill: #fff;
            }
        }
    }
}
.share-social .share-item:not(:last-of-type) {margin-bottom: 15px;}

.show-popup .overlay {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;

    .popup.show {
        transform: translate(-50%,-50%) scale(1);
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        border-radius:10px;
    }
}
.close_page {
	position: absolute;
	top: 0;
	right: 0;
	color: $gray;
	line-height: 30px;
	font-size: 40px;
	padding: 30px;
	cursor: pointer;

    &:hover img {
        filter: invert(1);
    }
}
.close_page.close.cancel_survey {padding: 15px 15px 0 0 !important;}
.close-popup {
    cursor: pointer;

    &:hover img {
    filter: invert(1);    
    }
}
.image-overlay {
    position: relative; overflow: hidden;

    &:not(.no-overlay)::before {
        content: "";
        position: absolute;
        z-index: 2;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to top, #333, rgba(51, 51, 51, 0.3));
        opacity: 0.4;
    }
}
.big-icon {
	font-size: 60px;
}
.input-container {
	position: relative;
    width: 100%;
    margin-bottom: 30px;
}
.line-input {
	width: 100%;
	height: 40px;
    border: 1px solid #ddd;
    font-size: 14px;
    border-radius: 8px;
    background:#fdfdfd; 
    padding-left: 15px;

    &:hover {
      box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;
  }
    &.error {
        border-color: red;
    }
}

.subsc-plans .radio-button label {height:70px; padding-left: 25px !important; padding-right: 25px; margin-right: 25px;}
.subsc-plans h4 {margin-bottom: 47px;}
#register_subscribe .input-container .line-input, #register_subscribe .coupon-form .line-input {border: 1px solid #ddd; background:#fdfdfd; height: 40px; padding-left: 15px;}
#register_subscribe .input-container label, #register_subscribe .coupon-form label {text-transform: uppercase; font-size: 11px; display: block;}
#register_subscribe .cardinfo input {padding-left: 0 !important; text-align: center; width: 36px; border-left: none !important; margin-top: 35px;}
#register_subscribe input#card-number {border-right: none;}
#register_subscribe .cardinfo input#card-month-year {border-right: none;}
#register_subscribe .cardinfo input#card-cvc {width: 50px; padding-right: 20px !important;}
#register_subscribe .cardinfo .line-input:hover {box-shadow: none !important;}
#register_subscribe .reveal_password {top: 47px; right: 20px;} 
.account-page .default_submit.account-form .reveal_password {line-height:30px;}
.subscribe-form .input-label {bottom: auto; top: 1px; font-size: 12px; left: 5px;}
#create_subscribe .input-label {bottom: auto; top: -10px; font-size: 12px; left: 20px;}
.subscribe-image-field img {border-top-left-radius: 10px; border-top-right-radius: 10px;}
.subscribe-title {margin-top:95px; margin-bottom: 95px;}
.subscribe_steps_wrapper .payment-part h4 {margin-bottom: 33px;}
.payment-part .coupon-form {margin-top: 50px; padding-top: 45px; padding-bottom: 50px;}
.subscribe-txt {background-color: #f8f8f8; padding:20px 25px; border-radius: 10px; margin-bottom: 40px;}
/*.continue_button_wrap button {font-size: 14px !important;}*/
.ddyycvc input#card-number {border-top-right-radius: 0; border-bottom-right-radius: 0;}
.ddyycvc input#card-month-year {border-radius: 0;}
.ddyycvc input#card-cvc {border-top-left-radius: 0; border-bottom-left-radius: 0;}
.step-two-codefields .code-field  {border: 1px solid #ddd; background: #fdfdfd; width: 20%; text-align: center; padding-top: 4.5%; padding-bottom: 4.5%; font-size: 20px;}
.code-field {border-radius: 10px;}




/*subscribe css from inline:*/
.subscribe-createacc .w100 {border-top:1px solid #f0f0f0; padding-top: 47px; margin-top: 50px; margin-bottom:35px;}
.subscribe-haveacc {width: 250px; text-align:right;}
.subscribe-whats-box {padding: 45px 50px 25px 50px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;}
.subscribe-whats-box p {margin-bottom: 15px;}
.panel.subsc-right {overflow: hidden; width: 100%; align-items: flex-start; padding: 0 !important; border-radius:10px;}
.subscribe_step {transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;}
.subscribe_steps_wrapper {position: relative; width: 100%; padding:47px 50px 40px 50px; transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;}
.subsc-right .input-container {margin-bottom: 15px;}
.subscribe_step.step_3 {left: 66.66667%;}
.subscribe_step.step_2 {left: 33.3333%;}
.subscribe_step {position: absolute; width: 33.33333%; padding: 57px 59px 0px 59px;}
.input-error {position: absolute; font-size: 12px; text-align: right; color: red; width: 100%; line-height: 1.6;}
.subscribe_step.step_1:not(.active) {transform: translateX(-100%);}
.subscribe_step.step_1:not(.active) + .subscribe_step.step_2:not(.active) {transform: translateX(-200%);}
.subscribe_step.step_2.active {transform: translateX(-100%);}
.subscribe_step.step_3.active {transform: translateX(-200%);}
#register_subscribe_1 .input-container .line-input, #register_subscribe_1 .coupon-form .line-input {height: 40px;}
.subscription-option .best-value {color:#52C15A; background:rgba(82,193,90,0.15); padding:5px 7px; border-radius:50px;}
.subsc-right #Email_error {font-size: 12px; text-align: right; width: 100%;	color: red;}
 #register_subscribe_2 .input-container .line-input, #register_subscribe_2 .coupon-form .line-input {height: 50px; padding-left: 20px;}
.input-container.cardinfo input {width: 100% !important;}
.coupons_msg {position: absolute; top: 100%; left: 0;}
/*end subscribe css from inline:*/

.round-select {
	width: auto;
	height: 56px;
	border-radius: 30px;
    padding: 0px 45px 0px 25px;
	border: 1px solid $gray;
	font-size: 18px;
	background: $lightGray url(../images/arrow-down.svg) no-repeat right 30px center;
	appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;

    &:hover {
        border-color: $lightText;
        cursor: pointer;
    }
}

.input-container.full-field{
  max-width:570px;
}
.input-container.full-field .line-input, .code-container.full-field .line-input {
  border: 1px solid #ddd;
  padding-left: 15px;
}



.line-select {
	width: 100%;
	height: 60px;
	border: none;
	border-bottom: 1px solid #D4D8DD;
	font-size: 18px;
	background: #fff url(../images/select-arrow.png) no-repeat right 15px center;
	appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;
}
.line-select:hover {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;}
select:valid + .select-label {
	opacity: 1;
}
select:invalid { color: gray; outline: 0 !important; box-shadow: none !important}
select:valid  + .select-label{ opacity: 0.3;transform: translateY(-50%); }
select + .select-label {
    position: absolute;
    transition: all 0.25s ease-in-out 0s;
	top: 0;
	left: 0;
	-webkit-transform: translateY(-50%);
	transform: translateY(0%);
	opacity: 0;
	font-size: 14px;
}
.clear_select {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 40px;
	font-size: 30px;
	display: none;
	line-height: 1;
	height: 40px;
    cursor: pointer;
    &:hover {
        opacity: 0.5;
    }
}
textarea.line-input {
	line-height: 1.4;
    min-height: 250px;
    padding: 15px 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
}
.line-input:placeholder-shown + .input-label {
    opacity: 0;
    top: 17px;
    font-size: 18px;
}
.input-label {
    display: block;
    width: 100%;
    position: absolute;
    bottom: 18px;
    left: 1px;
    opacity: 0.4;
    top: -10px;
    font-size: 14px;
    transition: $transition2;
    pointer-events: none;
    text-align: left;
}
.reveal_password {
	width: auto;
	height: auto;
	display: block;
	color: $red;
	position: absolute;
	top: 7px;
	right: 0;
	z-index: 1;
	line-height: 40px;
	cursor: pointer;
	font-size: 14px;
}
.reveal_password.active svg path {
	fill: #000 !important;
}
sup {
	vertical-align: baseline;
	font-size: 60%;
	display: inline;
	margin-right: 3px;
	top: -0.5em;
	position: relative;
}
.big-header-image {
    @include height_no_header;
    padding: 0 !important;
    margin: 0;
    position: relative;
}
.small-header-image {
    @include small_header;
    padding: 0 !important;
    margin: 0;
    position: relative;

    &.h650 {
        height: 650px;

        .image-overlay img{
            height: 650px;
            max-height: 650px;
        }
    }
    .image-overlay img{
        @include small_header;
        object-fit: cover;
        width: 100%;
        max-height: 600px;
    }
}
.smaller-header-image {
    @include smaller_header;
    padding: 0 !important;
    margin: 0;
    position: relative;
}
.panel {
	background: #fff;
	display: flex;
	flex-direction: column;
	padding: 40px 30px;
	align-items: center;
    justify-content: flex-start;
    // height: 100%;

    &.big-padding {
        padding: 50px 60px;
    }
    &.dark-panel {
        background: $darkGray;
    }

    &.with-shadow {
        box-shadow: 20px 0px 30px rgba(212, 216, 221, 0.15);
    }
    .panel-header {
        width: 100%;
        padding: $gap*1.5;
        text-align: center;

        &.help-panel-header {
            padding: $gap*2.5 $gap*1.5 $gap*1.5;
            height: 180px;
            display: flex;
            align-items: center;
            flex-direction: column;

            p {
                font-size: $fontSize;
            }
        }
    }
    .panel-body {
        width: 100%;
        padding: 50px;
    }
}

.hero-container {
	position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    transform: translate(-50%, -50%);
    max-width: 1500px;
    width: 100%;
    text-align: center;

    h1 {
        font-size: 40px !important;
        color: #fff;
        font-weight: 500;
        margin-bottom: 0;
        letter-spacing: 0.05em;
        line-height: 50px;
    }
    h3 {
        margin-bottom: 30px;
        color: #fff;
    }
}
.hero-banner {
	position: relative;
    margin-top: $header;
    height: 76vh;
    max-height:700px;

    .image-overlay.h100vh {
        height: 100%;
    }
    img {
        height: 100%;
        width: 100%;
        object-fit: cover;
    }
}

// .hero-banner .image-overlay::before {
// 	background: rgba(0,0,0,0.4);
// }

.homepage-slider .swiper-pagination-bullet {
	width: 100px;
	height: 5px;
	display: inline-block;
	border-radius: 0;
	background: #fff;
	opacity: 0.3;
	margin: 0 5px !important;
}
.homepage-slider .swiper-pagination-bullet-active {
	opacity: 1;
}
.homepage-slider .swiper-pagination.homepage-slider-pagination {
    bottom: 28px;
}
.homepage-sponsors-pagination {
	bottom: -6px;
}
.swiper.homepage-sponsors {
	padding-bottom: 0px;
}
.homepage-collection .swiper-jquery-bullet-active {
	background: #000 !important;
}
.max400 {
    max-width: 400px;
    width: 100%;
}
.max450 {
    max-width: 450px;
    width: 100%;
}
.max500, .input-container.full-field.max500 {
    max-width: 500px;
    min-width: 500px;
    width: 100%;
}
.min250 {
	min-width: 250px;
}
.max65 {
	max-width: 65%;
}
.img-panel {
	position: relative;
}
.img-panel-content {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
    z-index: 3;
	padding: 0 5vw;
	text-align: left;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
}
.img-panel-content a.btn {border:none;}
.homepage-teachers .img-fluid,
.homepage-collection .img-fluid {
	width: 100%;
	max-height: 500px;
	height: 500px;
	object-fit: cover;
}
.homepage-collection .swiper-slide {
	height: 500px;
}
.hero-container .btn span {font-size: 16px;}

.hero-badge {
	position: absolute;
	top: 50px;
	right: 50px;
	z-index: 10;
	width: 100px;
	height: 100px;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination {
	bottom: 70px;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background: #fff !important;
}
.swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination .swiper-pagination-bullet {
	background: rgba(255, 255, 255, 0.3) !important;
    opacity: 1 !important;
}
footer {
	background: $black;

    ul {

        li {
            line-height: 30px;
            padding-bottom: 0px;
            font-size: 15px;
            color: #aaa;

            a {
                font-size: 14px;
                color: #aaa;
                line-height:30px;

                &:hover {
                    opacity: 0.7;
                    text-decoration: underline;
                }
            }
        }
        &:last-of-type {
            margin-bottom: 0 !important;
        }
    }
    .footer-menu-title {
        font-weight: 600;
        margin-bottom: 0;
        height: 35px;
        line-height: 20px;

    }
    .col-3, .col-4, .col-6, .col-8 {padding-left:0; padding-right:0;}

}
.footer-logo {
    width: 140px;
}
.footer-social {
	display: flex;
	align-items: center;
}
.footer-social a {
    display: block;
    height: auto;
    width: auto;
    margin: 0 0 0 20px;

    &:hover {
        opacity: 0.4;
    }
}
// .footer-social .fb {background-position: 0 0;}
// .footer-social .fb:hover {background-position: 0 -45px;}
// .footer-social .ig {background-position: -45px 0;}
// .footer-social .ig:hover {background-position: -45px -45px;}
// .footer-social .tw {background-position: -90px 0;}
// .footer-social .tw:hover {background-position: -90px -45px;}
// .footer-social .yt {background-position: -135px 0;}
// .footer-social .yt:hover {background-position: -135px -45px;}

.foo-nav {
  border-top: 1px solid #1e1e1e;
  padding-top: 55px;
}
.footer-top-row {
  padding-top:60px; padding-bottom:60px;
}




.foo-second {
	border-top: 1px solid #1e1e1e;
  color: #ffffff !important;
	margin-top: 110px;
	margin-bottom: 80px;
	padding-top: 80px;

	display: flex;
}
.footbtm-left {
	width: 50%;
	display: flex;
  align-items: center;
}
.footbtm-left img {
	display: inline-block;
	vertical-align: middle;
}
.foo-second .footbtm-left a {
	font-size: 14px;
	text-decoration: underline;
}
.footbtm-right {
	width: 50%;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	text-align: right;
}
.foo-second .footbtm-right p {
	font-size: 14px;
  font-weight: 600;
}
.footbtm-right a.btn, .foo-selling a.btn {
  background: #fff;
  color: #000000 !important;
  border:1px solid #fff;
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 8px 35px;
  margin-left: 30px;
  cursor: pointer;
  font-size: 12px !important;
}
.footbtm-right a.btn:hover, .foo-selling a.btn:hover {background:#000; color:#fff !important;}

.footbtm-left *, .footbtm-left a, .footbtm-right *, .footbtm-right a {color: #ffffff; font-size: 14px; font-weight: 400;}
.main-text p {
	margin-bottom: 10px;
}
.classes-page .image-overlay img {
    height: 26vw;
	width: 100%;
	object-fit: cover;
}
.classes-page .video-container .image-overlay img {
    height: 16vw;
	width: 100%;
	object-fit: cover;
}
 
.favorite {
	position: absolute;
    display: block;
	top: 18px;
	right: 18px;
	width: 45px;
	height: 45px;
	z-index: 13;
	padding: 10px;
}
.favorite:hover {
    opacity: 0.6;
    cursor: pointer;
}
.ajax-fav.favorite {
    opacity: 1 !important;
}
.ajax-fav.favorite.ajax-favs-active {
    opacity: 1 !important;
}
.ajax-fav.favorite:hover {
    opacity: 0.7 !important;
}

.locked {
    position: absolute;
    bottom: 35px;
    right: 35px;
    width: 12px;
    height: 15px;
    z-index: 14;
    background: url(../images/lock.svg) no-repeat center center;
}
.duration {
	position: absolute;
	bottom: 22px;
	left: 25px;
	z-index: 13;
	color: #fff;
	font-size: 14px;
	line-height: 1 !important;
    text-shadow: 0 0 3px #999;
}
.filters, .sortby-popup {
	display: flex;
	align-items: flex-start;
	position: fixed;
	top: 50%;
	left: 50%;
	width: 400px;
	transform: translate(-50%, -50%);
	flex-direction: column;
	background: #fff;
	box-shadow: 0 0 100px 0 rgba(0,0,0,0.1);
	z-index: 1111111;
    transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    border-radius: 10px;
}
.sortby-popup ul {
	padding: 0 30px;
	display: flex;
	flex-direction: column;
	width: 100%;
}
.sortby-popup ul li.checked {
    border: 1px solid #000;
}
.sortby-popup ul li {
    position: relative;
	border: 1px solid #ddd;
	background-color: #fff;
	border-radius: 8px;
	margin: 0 0 10px;
	padding: 0 20px !important;
	display: flex !important;
	align-items: center;
	height: 40px;
	width: 100%;
	justify-content: flex-start;
	font-size: 12px !important;
	line-height: 20px;
	cursor: pointer;
}
.sortby-popup ul li:hover {
	box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;
}
.btn.black-bg.white.sortby_list:hover span {
	color: #fff !important;
}
.filter-fields {padding:0 30px 30px; width:100%;}
.filters.show, .sortby-popup.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}
body:not(.show-popup) .sortby-popup {display:none;}
.sortby-popup {padding-bottom: 30px;}
.sortby-popup .custom-selectbox.single-select ul {display: block !important; margin:0; max-height: 100%; padding-left: 30px; padding-right: 30px; height:auto !important;}
.sortby-popup .custom-selectbox.single-select ul li {display: flex !important; border:1px solid #ddd; background-color: #fff; border-radius: 8px; margin: 0 0 10px; padding: 0 20px !important; display: flex !important; align-items: center; height:40px;}
body.show-popup .sortby-popup .custom-selectbox.single-select ul li.checked {background-color: #F8F8F8; border-color:#000;}
 .sortby-popup .custom-selectbox {position: relative; float: left; border: none !important; background:none !important;}
 .sortby-popup .custom-select, .sortby-popup .custom-selectbox-holder {width:100%; margin:0;}
 .sortby-popup .sortby-actions {padding-left: 30px; padding-right:30px; width: 100%;}
 .sortby-popup .sortby-action {display:flex !important;}
 .sortby-popup .custom-selectbox.not-empty:not(.opened) {background:none !important;}
 .sortby-popup .custom-selectbox:hover {box-shadow: none !important;}
 .sortby-popup .custom-selectbox.single-select ul li:hover {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;}

.filters .close-popup, .sortby-popup .close-popup {
	position: absolute;
	top: 18px;
	right: 20px;
	line-height: 1;
	padding: 10px;
	font-size: 34px;
	z-index: 1;
}
.filters .custom-select {
	width: 100% !important;
	margin: 0 0 7px;
}
.filters .custom-selectbox.opened {
    position: relative;
    z-index: 111111;
}
.filters .custom-selectbox-holder {
	width: 100% !important;
}
.video-container{
    position: relative;
    margin-bottom: 30px;
    display: block;
    overflow: hidden;
    border-radius: 8px;

    .play-button {
        width: 5.8vw;
        height: 5.8vw;
        border-radius: 50%;
        border: none;
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 3;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: $transition1;
        pointer-events: none;

        &:hover {
            // background: $white;
            cursor: pointer;

            span {
                opacity: 0.5;
            }
        }
        span {
            transition: $transition1;
            border: 20px solid rgba(0,0,0,0);
            border-left: 35px solid #fff;
            margin-left: 17px;
        }
    }
    &:hover {
        cursor: pointer;

        .play-button {
            // background: $white;

            & > span {
                opacity: 0.5;
            }
        }
    }
}

.single-collection-desc h3,
.video-text-container h4 {
    transition: $transition1;
}
.single-collection-desc h3:hover,
.video-text-container h4:hover {
    color: $red;
}
.single-collection-desc p {
	line-height: 1;
	margin-top: 10px;
}

.video-text-container a.byteacher:hover {text-decoration: none; color:#000 !important;}

.icon-star {
    width: 15px;
    height: 15px;
    background: url(../images/star.svg) no-repeat center center;
    display: inline-block;
}
.icon-black-star {
    width: 15px;
    height: 15px;
    background: url(../images/black-star.svg) no-repeat center center;
    display: inline-block;
}
.icon-small-star {
    width: 15px;
    height: 15px;
    background: url(../images/small-star.svg) no-repeat center center;
    display: inline-block;
    position: relative;
    top: -1px;
}
.single-video-item {
    margin-bottom: 54px;
    position: relative;

    .image-overlay {
        position: relative;
        width: 100%;
        padding-top: 56.25%;
        border-radius:8px;

        img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
}
.single-collection-desc {
    margin-top: 35px;
}
.single-collection-item {
    margin-bottom: 50px;
}
.single-collection-item .image-overlay::before {
    transition: $transition1;
}
.single-collection-item:hover .image-overlay::before {
    background: -webkit-gradient(linear, left bottom, left top, from(#222), to(rgba(0, 0, 0, 0)));
    background: linear-gradient(to top, #222, rgba(0, 0, 0, 0));
    opacity: 0.9;
}
.border-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border: 1px solid #F0F0F0;
    width: 130px;
    margin: 0 15px;
    height: 190px;

    & * {
        line-height: 1;
    }
    .border-box-cell {
        line-height: 1.6;
        text-align: center;
        font-size: 20px;
        height: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        align-items: center;
        font-size: 20px;
    }
}
.video-row {
    max-width: 1030px;
    margin: 0 auto;
}

.video-row .image-overlay img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
	width: 100%;
	height: 100%;
    object-fit: cover;
}
.video-row .image-overlay {
	width: 100%;
	height: 0;
	padding-top: 56%;
}
.live-badge {
	position: absolute;
	top: 30px;
	right: 30px;
}
.zindex-1 {
    z-index: 11;
    position: relative;
}
.single-teacher-item {
	border: 1px solid #F0F0F0;
	display: flex;
 	justify-content: center;
 	text-align: center;
    align-items: baseline;
	height: 100%;
    padding-top:60px; 
    padding-bottom:47px;
    border-radius:10px;


    &:hover{
        background: #F8F8F8;
        border-color: #F8F8F8;
    }
}
.teacher-panel {
	position: relative;
	display: flex;
	flex-direction: column;
    align-items: center;
	width: 250px;
    height: 250px;
	max-width: 100%;
}
.teacher-panel h3 {
    line-height:1.5;
    margin-top: 45px;
    padding-left: 15px;
    padding-right: 15px;
}
.teacherimg-wrap {
    position: relative;
    width: 56%;
    height: 0;
    padding: 56% 0 0;
    border-radius: 50%;
    overflow: hidden;
}
.teacher-img {
	
    min-width: 0;
    min-height: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;

    &.white-border {
        border: 5px solid #fff;
        border-radius: 50%;
    }
}
.teacher-badge {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
}
.credit-card-icon {
    width: 30px;
    margin-right: 20px;
}
.favs {
    display: block;
    width: 20px;
    height: 18px;
    margin-right: 30px;
    background: url(../images/heart.svg) no-repeat center center / cover;

    &:hover {
        background: url(../images/heart-full.svg) no-repeat center center / cover;
    }
}
.circle-arrow {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../images/circle-arrow.svg) no-repeat center center / cover;
    cursor: pointer;
    transition: all 0.25s ease-in-out 0s;

    &:hover {
        background: url(../images/circle-arrow-opened.svg) no-repeat center center / cover;
        transform: rotate(180deg);
    }
    &.opened:hover,
    &.opened {
        background: url(../images/circle-arrow-opened.svg) no-repeat center center / cover !important;
        transform: rotate(0deg);
    }
}

.playlist-wrapper {
    width: 1000%;
    max-width: 1000px;
    margin: 0 auto;
}
.playlist-img {
    position: relative;
    width:120px;
}
.single-user-playlist {
    margin-bottom: 40px;
}
.nrplaylists {
    border-top:1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    height: 52px;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.playlist-main-title {
    padding-top: 90px;
  padding-bottom: 95px;
}
.nrplaylists p {font-size: 12px; color: #969696;}

.add_class_to_playlist {
    display: block;
    width: 30px;
    min-width: 30px;
    height: 30px;
    margin-left: auto;
    background: url(../images/add_to_playlist.svg) no-repeat center center / cover;

    &:hover {
        background: url(../images/add_to_playlist_hover.svg) no-repeat center center / cover;
    }
}
.remove_class_from_playlist {
    display: block;
    width: 30px;
    height: 30px;
    margin-left: auto;
    background: url(../images/remove_from_playlist.svg) no-repeat center center / cover;

    &:hover {
        background: url(../images/remove_from_playlist_hover.svg) no-repeat center center / cover;
    }
}
.single-user-playlist-content {
	position: relative;
}
.handle {
	cursor: pointer;
}
.handle:hover {
	opacity: 0.4;
}
.playlist-class-container {
	background: #fff;
}

.playlists {
    width: 20px;
    height: 20px;
    background: url(../images/playlist-icon.svg) no-repeat center center / cover;

    &:hover {
     
    }
}
.watchlater {width: 20px; height: 20px; background: url(../images/clock-icon2.svg) no-repeat center center / cover;}

.playlist-option {
    position: absolute !important;
    bottom: 0;
    right: 15px;
}
.playlist-big-image {
	width: 300px;
	height: 300px;
	object-fit: cover;
	min-width: 300px;
	max-width: 300px;
	width: 100%;
}
.option-btn {
    cursor: pointer;
    display: block;
    width: 30px;
    height: 30px;

    &:hover svg g {
        stroke: #000
    }
}
.logged-in {
    display: none;
}
header.logged {
    .logged-in {
        display: flex;
    }
    .logged-out {
        display: none;
    }
}
.right-menu.logged-in a {margin-left: 25px;}
 
.right-menu.logged-out a {margin-left:20px;}
header .menu-button {display:none;}
.side-links li {
    margin-bottom: 18px !important;
    line-height: 1;

    a {
        font-weight: 400 !important;
        line-height: 1 !important;
        font-size: 12px !important;

        &.active {
            color: $midGray;
        }
        &:hover {
            color: $midGray;
        }
    }
}
.hide {
	display: none !important;
}
.upload-image img.no-img {
    opacity: 0.3;
    min-width: 20px !important;
    min-height: 20px !important;
    width: 33px !important;
    height: 40px !important;
    border-radius: 0;
}
.upload-image img.no-img {
    opacity: 0.3;
    min-width: 20px !important;
    min-height: 20px !important;
    width: 33px !important;
    height: 40px !important;
    border-radius: 0;
}
.upload-image .image_preview.no-img.has_image {
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    -o-object-fit: cover !important;
    object-fit: cover !important;
}
.upload-image {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
    border: 1px solid #F0F0F0;
    margin-right: 30px;
    overflow: hidden;
    cursor: pointer;
    width: 210px;
    height: 120px;
    position: relative;
}
.position-relative {
    position: relative !important;
}
.big-avatar {
	width: 120px;
	height: 120px;
	border-radius: 60px;
	overflow: hidden;
    position: relative;
}
.avatar150  {
	width: 150px;
	height: 150px;
	border-radius: 100px;
	overflow: hidden;
    position: relative;
    display: block;

    img {
        width: 150px;
        height: 150px;
        object-fit: cover;
    }
}
.avatar120 {
    width:120px; height:120px; border-radius: 100px; overflow: hidden; position: relative; display: block;
}

.initials {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
     border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: #fff;
    color: $black;
    text-transform: uppercase;
}
.avatar {
	width: 50px;
	height: 50px;
	border-radius: 30px;
	overflow: hidden;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}
.avatar40 {
	width: 40px;
	height: 40px;
	min-width: 40px;
	min-height: 40px;
	border-radius: 30px;
	overflow: hidden;
    position: relative;

    .img-fluid {
        width: 40px;
        height: 40px;
        object-fit: cover;
    }
}
.avatar25 {
	width: 25px;
	height: 25px;
	min-width: 25px;
	min-height: 25px;
	border-radius: 30px;
	overflow: hidden;
    position: relative;
}
.avatar70 {
	width: 70px;
	height: 70px;
	border-radius: 40px;
	overflow: hidden;
    position: relative;

    .initials {
        border: 2px solid #000;
        font-size: 18px;
        background: #000;
        color: #fff;
    }
}
.side-link:hover {
    color: $red;
}
.side-link.active{
    font-weight: 500;
    color: $red;
}
.sticky {
    position: sticky;
    top: calc(#{$header} + 20px)
}
.single-help-item {
    margin-bottom: 40px;

    h3 {
        margin-bottom: 20px;
    }
    &:last-of-type {
        margin-bottom: 0;
    }
}
/* SELECT 2 */
.select2-container--default .select2-selection--single {
	background-color: #fff !important;
	height: 56px !important;
	border-radius: 30px !important;
	padding: 0px 35px 0px 20px !important;
	border: 1px solid #F0F0F0 !important;
}
.select2-container--open .select2-dropdown--below {
	border-bottom-left-radius: 28px !important;
	border-bottom-right-radius: 28px !important;
	border-top: none !important;
}
.select2-dropdown {
	border: 1px solid #F0F0F0 !important;
}
.select2-results__option {
	font-size: 14px !important;
	padding: 10px 6px 10px 35px !important;
	background: none !important;
	position: relative;
    // white-space: nowrap;
}
.select2-results__option[aria-selected]::before {
	content: "";
	border: 1px solid #eaeaea;
	position: absolute;
	top: 9px;
	left: 10px;
	width: 16px;
	height: 16px;
}
.select2-results__option[aria-selected=true]::before {
	content: "";
    background: #000;
	border: none;
	position: absolute;
	top: 9px;
	left: 17px;
	width: 16px;
	height: 16px;
}
.select2-results__option .wrap {
    line-height: 1.4;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
	color: #000 !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	color: #000 !important;
	line-height: 56px !important;
}
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
	border-bottom-left-radius: 0 !important;
	border-bottom-right-radius: 0 !important;
	border-bottom: none !important;
}
.select2-container--default .select2-selection--single .select2-selection__clear {
	display: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
	padding-left: 20px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
	height: 7px !important;
	margin-left: -14px !important;
	width: 12px !important;
	border: none !important;
	background: url(../images/arrow-down.svg) no-repeat right center !important;
}
.select2-container {
	width: auto !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
	top: 13px !important;
}
.custom-select {
	margin-right: 20px;
}
.video-screen-holder {
	background: rgb(248, 248, 248) !important;
}
.single-video-item .image-overlay img {
    width: 100%;
	height: 285px;
    object-fit: cover;
}
.video-text-container h4 {
	line-height: 1.2;
}
.video-text-container h4 span {
	line-height: 20px;
}
.video-text-container h4 span.pr-2 {
	font-weight: 600 !important;
}
.no-rate {
    pointer-events: none !important;
    user-select: none !important;
}
.single-collection-item {
    .image-overlay {
        width: 100%;
        height: 0;
        padding-top: 59%;
        position: relative;

        img {
            height: 100%;
            width: 100%;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
}
#cover_image[type="file"], #image[type="file"] {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	left: 0;
	z-index: 1;
	opacity: 0;
	cursor: pointer;
}

body .mobile { display: none !important; }
body .mobile-flex { display: none !important; }
body .mobile-inline { display: none !important; }
body .desktop { display: block !important; }
body .desktop-flex { display: flex !important; }
body .desktop-inline { display: inline-block !important; }

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active{
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    filter: none !important;
}
.products_list {
	min-height: 500px;
    position: relative;
    justify-content: space-between;
}
.products_list .col-4 {width: 32.3%; flex: 32.3%; max-width: 32.3%;}
.products_list::after {content: ''; width: 32.3%;}
.products_list.courses-list .thumb-info {display:none;}
.load_more {
    // animation: 1s pulse infinite;
}
.single-ajax-class {
    animation: 1s stepIn forwards;
}
@keyframes pulse {
    0% {opacity: 0.3;}
    50% {opacity: 1;}
    100% {opacity: 0.3;}
}
@keyframes stepIn {
    0% {transform: translateY(50px)}
    100% {transform: translateY(0px)}
}
.teacher-panel .initials {
    min-width: 0;
    min-height: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:#f0f0f0;
    font-size: 70px;
  color: #fff;
}
.disabled {
    pointer-events: none;
    opacity: 0.5;
}
.watched {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 13;
	transform: translate(-50%, -50%);
	cursor: pointer;
    opacity: 1;

    &:hover {
        opacity: 0.5;
    }
}
.music-with-control {
	position: absolute;
	top: 20px;
	left: 20px;
	line-height: 25px;
	z-index: 13;
	cursor: pointer;
	width: 90px;
	text-align: center;
	background: #fff;
	color: #000 !important;
	justify-content: center;
    pointer-events: none;
	padding: 0 5px;
	font-size: 7px !important;
	font-weight: 600;
	text-align: center;
    margin-left: 25px;
}
.first-multi-row {
	opacity: 0.5;
	color: #871c1e !important;
}
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #000 !important;
}
.lightGray-bg + .select2-container--default.select2-container--open .select2-selection--single {
    background: #ffffff !important;
}
.lightGray-bg + .select2-container--default .select2-selection--single {
    background: #f8f8f8 !important;
}
.select2-container .select2-selection--single .select2-selection__rendered {
    padding-right: 0px !important;
}
.select-machines + .select2-container--default .select2-selection--single .select2-selection__rendered {
    width: 120px;
}
.select-difficulty + .select2-container--default .select2-selection--single .select2-selection__rendered {
    width: 125px;
}
.select-duration + .select2-container--default .select2-selection--single .select2-selection__rendered {
    width: 120px;
}
.select-teacher + .select2-container--default .select2-selection--single .select2-selection__rendered {
    width: 170px;
}
.select-body_parts + .select2-container--default .select2-selection--single .select2-selection__rendered {
    width: 130px;
}
.footer-hub-items {
    .col-3 {
        .footer-hub-item {
            height: 100px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #F0F0F0;
            transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;

            &.selected,
            &:hover {
                position: relative;
                z-index: 1;
                background: #F8F8F8;
                box-shadow: 0 0 0 1px #F8F8F8;
                border-left: 1px solid #F8F8F8;
            }
        }
        &:hover {
            position: relative;
            z-index: 1;
        }
        &:last-child .footer-hub-item{
            border-right: 1px solid #F0F0F0;

            &:hover {
                border-right: 1px solid #F8F8F8;
            }
        }
    }
}

/*    PLAYER      */
#airplay {
    background: rgba(0,0,0,0);
    position: absolute;
    bottom: 27px;
    right: 20px;
}
#airplay:hover {
    opacity: 0.6;
}
.castPosition {
    left: -21px !important
}
.fwdevp-time, .fwdevp-qaulity-button {
    font: 400 14px 'Graphik', sans-serif !important;
    margin-left: -10px !important;
}
.img-overflow-right {
    width: 57vw;
    max-width: none;
}
.mvp-skin-flat-light .mvp-player-loader {
    background-color: rgba(0, 0, 0, 0%) !important;
    width: 100% !important;
    animation: none !important;
    height: 100% !important;
    margin: 0 !important;
}
.mvp-skin-flat-light .mvp-player-loader::before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0, 0.35);
    z-index: 1;
}
.mvp-skin-flat-light .mvp-player-loader::after {
    content: '';
    display: block;
    width: 44px;
    height: 44px;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 2;
    margin-left: -22px;
    margin-top: -27px;
    border-radius: 50%;
    border: 5px solid #fff;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
.mvp-poster-holder.hidden_poster {
    opacity: 0;
}

.select2-results {
    display: block;
    padding: 10px;
}
.footer-hub-items {
	text-align: center;
	margin: 0;

    .footer-hub-item {
        display: flex;
        height: 80px;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #f0f0f0;
    }
}
.footer-hub-items,
.footer-hub-items * {
	text-align: center;
	margin: 0;
	font-size: 12px;
	font-weight: 600 !important;
	color: #000;
}
.single-class-row {
	display: flex;
	align-items: center;
	border-bottom: 1px solid #f0f0f0;
	height: 70px;

    &:last-of-type {
        border-bottom: none !important;
    }
    &:first-of-type {
        margin-top: -18px;
    }
    .class-row-left {
        width: 115px;
        font-weight: 300;
    }
    .class-row-right {
        font-weight: 500;
    }
}
body h1, body .h1, body h1 span, body .h1 span,
body h1.light, body .h1.light, body h1.light span, body .h1.light span,
body h1.normal, body .h1.normal, body h1.normal span, body .h1.normal span,
body h1.medium, body .h1.medium, body h1.medium span, body .h1.medium span,
body h2, body .h2, body h2 span, body .h2 span,
body h2.light, body .h2.light, body h2.light span, body .h2.light span,
body h2.normal, body .h2.normal, body h2.normal span, body .h2.normal span,
body h2.medium, body .h2.medium, body h2.medium span, body .h2.medium span,
body h4, body .h4, body h4 span, body .h4 span,
body h4.light, body .h4.light, body h4.light span, body .h4.light span,
body h4.normal, body .h4.normal, body h4.normal span, body .h4.normal span,
body h4.medium, body .h4.medium, body h4.medium span, body .h4.medium span {
    // font-family: 'Arquitecta' !important;
    font-weight: 600 !important;
    // letter-spacing: 0 !important;
    text-transform: uppercase !important;
}
.unlock-icon {
	filter: invert(1);
}
.single-class-row .class-row-left {
	min-width: 115px;
}
.single-class-row {
	min-height: 70px;
}
.single-class-row .class-row-left {
	font-size: 12px;
}
.single-class-row .class-row-right {
	font-weight: 500;
	font-size: 12px;
	line-height: 1.2;
	padding: 5px 0;
}
.single-class-row:first-of-type {
	margin-top: 0;
	border-top: 1px solid #f0f0f0;
}
.single-class-row:last-of-type {
	border-bottom: 1px solid #f0f0f0 !important;
}
.btn.btn-border:hover {
	color: #fff;
}
.mvp-skin-flat-light .mvp-big-play {
	background: rgba(255, 255, 255, 0) !important;
}
.mvp-skin-flat-light .mvp-big-play svg path {
	color: #fff;
	fill: #fff !important;
}
.mvp-big-play svg {
	z-index: 11;
}
.mvp-poster-holder::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to top, rgba(0,0,0,0.5), rgba(0,0,0,0));
	z-index: 1;
}
.subscription-option {
	border: 1px solid #f0f0f0;
	border-radius: 10px;
    max-width:500px;
}
.subscription-option:hover {
	/*box-shadow: 0 0 0 1px #000 inset;*/
    border: 1px solid #ddd !important;
    background:#FCFCFC;
}
.subscription-option.selected {
    border: 1px solid #000 !important;
    background:#FCFCFC;
}
.btn.f-10 {
    font-size: 8px !important;
    min-height: 30px !important;
    font-weight: 500 !important;
}
body .popup h2 {
	font-size: 20px !important;
}
.line-input.error {
	border-color: red !important;
}
// HELP BUTTON
.chat-holder {
	position: fixed;
	bottom: 0;
	right: 0;
    z-index: 1000;
}
.chat-button {
	background: #000;
	color: #fff;
	width: 160px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 14px;
	letter-spacing: 0.05em;
	text-align: left;
	color: #fff;
	cursor: pointer;
    transition: all 0.25s ease-in-out 0s;

    &:hover {
        background-color: #444 !important;
    }
}
.chat-window {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1111;
    width: 360px;
    background: #fff;
    box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
    opacity: 0;
    pointer-events: none;
    transition: all 0.25s cubic-bezier(.13,.56,.38,.89) 0s;

    &.active {
        opacity: 1;
        pointer-events: auto;
    }
    .chat-window-header {
        background: #000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30px;
        height: 50px;
        cursor: pointer;

        &:hover {
            background-color: #444 !important;
        }
        span {
            font-weight: bold;
            font-size: 13px;
            letter-spacing: 0.05em;
            text-align: left;
            color: #fff;
        }
    }
    .chat-window-body {
        background: #fff;
        padding: 30px;

        p {
            font-weight: 300;
            font-size: 14px;
            letter-spacing: 0.05em;
            line-height: 20px;
            text-align: left;
            color: #000;
        }
        textarea {
            height: 150px !important;
            min-height: 150px !important;
        }
    }
}
.line-dropdown {
	border: 1px solid #F0F0F0;
	position: absolute;
	top: calc(100% - 1px);
	width: 100%;
	background: #fff;
	z-index: 111;
	font-size: 14px;
    max-height: 250px;
    overflow: auto;
    display: none;
}
.line-dropdown li {
    line-height: 25px;
    padding: 0 10px;
    cursor: pointer;

    &:hover {
        background: #f0f0f0;
    }
}
.input-placeholder {
	position: absolute;
	top: 11px;
}
.input-container.with-currency::before {
    content: attr(data-currency);
    position: absolute;
    left: 0;
    font-size: 24px;
    line-height: 60px;
    color: #999;
}
.input-container.with-currency {
    margin: 0;
    padding-left: 30px;
}
.abs-center {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 1;
	transform: translate(-50%, -50%);
}
.earning-model-box {
	height: 300px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
    padding: 30px;
	border: 1px solid #707070 !important;
}
.earning-model-box.start-earning-box {
	height: 350px;
	border: 1px solid #F0F0F0 !important;
}
.plus-between::before {
	content: "+";
	position: absolute;
	top: calc(50% - 18px);
	right: -11px;
	font-size: 30px;
	font-weight: bold;
	line-height: 1;
	color: #000;
}
.price-big {
	font-size: 30px;
	font-weight: 300;
	margin: 30px 0 60px;
}
.small-box {
    width: 75px;
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}
h1.class_title {
    line-height: 1.4;
}
.with-comma {
	display: flex;
    flex-wrap: wrap;
}
.with-comma span {
  position: relative;
  margin-right: 5px;
  white-space: nowrap;
  line-height: 20px;
}
.with-comma span:last-child {
  margin-right: 0 !important;
}
.with-comma span:last-child::after {
  display: none;
}
.with-comma span::after {
  content: ",";
}
.video-text-container {
	text-align: left;
    position: relative;
}
.single-playlist-list-item .video-text-container {display: flex; width:100%;}
.info-plst {flex:1; padding-right: 15px;}
.info-plst a {}
.options-plst {flex:0 0 70px; text-align: right;display: flex; justify-content: space-between;align-items: center;}
.options-plst a {display: flex;}
.btn.delete-item-playlist {height: 35px; min-height: 0; width: 35px; padding: 0;}




// EARNINGS
.class-item {
	position: relative;
}
#video[type="file"] {
	opacity: 0;
	left: 0;
	top: 0;
    z-index: 111111;
	position: absolute;
	width: 100%;
	height: 100%;
}
#mob_cover_image[type="file"],
#cover_image[type="file"],
#image[type="file"] {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	left: 0;
	z-index: 1;
	opacity: 0;
	cursor: pointer;
}
.hide {
	display: none !important;
}
.upload-image .image_preview.no-img.has_image {
	opacity: 1 !important;
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
}
#progress-bar-status-show {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 12px;
	background: #000000;
	text-align: right;
	padding: 0;
	font-size: 10px;
	line-height: 12px;
	width: 0;
	color: #fff;
}
.before_upload {
	display: flex;
	align-items: center;
	flex-direction: column;
    position: relative;
    z-index: 1;
}
.video_placeholder {
    width: 100%;
    max-height: 70vh;
}
.after_upload {
    position: relative;
    left: 0;
    height: 100%;
    z-index: 0;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    display: block;
    max-height: 70vh;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active{
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    filter: none !important;
}
#main_form h3.mb-3 {
	font-size: 18px !important;
	font-weight: bold !important;
    text-transform: uppercase;
}
.tabs {
	height: 50px;
	display: flex;
	align-items: center;
	padding: 0 15px;
    border-bottom: $border;
    margin-bottom: 50px;

    .btn-tab {
        height: 50px;
        display: flex;
        align-items: center;
        transition: none !important;
        padding: 0 15px;
        font-size: 14px;
        border-bottom: 2px solid #fff;
        font-weight: 600;

        &.active {
            border-bottom: 2px solid #000;
        }
    }
}
/* END EARNINGS */
.shop-item {
    margin-bottom: 80px;
}
.shop-image {
    border: 1px solid #F0F0F0;
    padding: 70px 0;
    width: 100%;
    height: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
    padding-top: 80%;
    position: relative;

    img {
        max-width: 80%;
        object-fit: contain;
        max-height: 80%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }
}
.single-class-new-row {
	display: flex;
	width: 100%;
	align-items: center;
	padding: 17px 25px;
	border: 1px solid #F0F0F0;
    border-radius: 10px;
}
.single-class-new-row:not(:last-of-type) {margin-bottom: 20px;}
.single-class-new-row a.btn {background:#fdfdfd;}

/*New single page look*/
.single-wrapper .mvp-poster-holder {border-radius: 10px;}
.single-wrapper .music-with-control {display:none !important;}
.single-wrapper .single-title-wrap {margin-top: 43px;}
.single-wrapper .class-box-small {background:#fdfdfd;}
.single-wrapper .body-parts-left p {font-size: 12px; line-height: 1; margin-top: 10px;}
.single-wrapper .class-next, .single-wrapper .class-prev {display: none !important;}
.classes-elements {margin-top:30px; border:1px solid #f0f0f0; border-radius: 10px;}
.classes-elements .class-body-parts {border: none; border-bottom: 1px solid #f0f0f0; border-radius: 0;	padding: 0 25px; min-height: 84px; margin:0 !important;}
.classes-elements .class-body-parts:last-of-type {border-bottom: none;}
.equipment-popup .class-bdown-wrap {padding-bottom: 10px;} 
.equipment-popup .single-class-new-row {border: none; margin-bottom: 0 !important; padding-top: 0; padding-bottom: 20px;}
.equipment-popup .single-class-new-row:first-of-type {padding-top:30px;}
.equipment-popup .class-new-row-image img {width:66px; height:66px;}
.equipment-popup .single-class-new-row a.btn {background: #000; border-color: #000; color: #fff;}
.equipment-popup .single-class-new-row a.btn:hover {color:#000; background:#fff;}
.equip-popup-body {max-height: 380px; overflow-y: auto;}
.exercise-list-wrapper {max-height: 422px; overflow-y: auto;}
.single-page-title {margin-bottom: 0 !important; font-size: 20px !important; line-height: 1.3 !important;}
.terminology-popup .class-bdown-wrap {max-width: 800px; overflow-y: auto;}

.class-new-row-image {margin-right: 25px;}
.class-new-row-image img {
	width: 41px;
	height: 41px;
	min-width: 41px;
	min-height: 41px;
	object-fit: contain;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
}
.h30 {
	height: 30px !important;
	min-height: 30px !important;
}
.select_filter_shop {
	height: 50px;
	border: 1px solid #F0F0F0;
	width: 320px;
	padding: 0 20px;
}
.w320 {
    width: 100%;
    max-width: 320px;
}
.w200 {
    width: 100% !important;
    max-width: 200px !important;
}
.mega_new_collection .shop-image img, .mega_restored_collection .shop-image img {
	max-width: 60% !important;
}

// VIDEO PREVIEW
.video_preview_player {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 1;
    object-fit: cover;
    opacity: 0;
    // transition: all 0.2s ease-in-out 0s;
    transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
}
.video_preview_player:hover {
    transition: all 0.2s ease-in-out 0s;
	opacity: 1;
    border-radius:8px;
}
.single-video-item .image-overlay:hover {
	background: none !important;
    border-radius: 8px !important;
}
.watched, .duration {
    pointer-events: none;
}
.image-overlay::before {
	pointer-events: none;
}

// PLAYLISTS
.single-playlist-item .image-overlay {
	width: 100%;
	height: 0;
	padding-top: 100%;
	position: relative;
}
.single-playlist-item .image-overlay img {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.single-playlist-desc {
	margin-top: 20px;
}
.single-playlist-desc p {
	line-height: 1.5;
    margin-top: 10px;
}
.single-playlist-desc h3 {
    line-height: 22px;
    font-weight: 600 !important;
}
.single-playlist-item {
	margin-bottom: 80px;
}
.added-staff {
	position: absolute;
	top: 15px;
	left: 15px;
	background: #fff;
	color: #000;
	z-index: 111;
	font-size: 8px;
	font-weight: 600;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1;
	text-align: center;
	letter-spacing: 0;
	padding: 10px;
}
.single-playlist-list-item {
	display: flex;
	align-items: center;
    padding: 25px 0;
    margin: 0 0;

    > .video-container {
        width: 100%;
        max-width: 100px;
        height: 56px;
        margin-right: 15px;
        margin-bottom: 0px;

        .play-button span {
            border: 14px solid rgba(0, 0, 0, 0);
            border-left: 20px solid #fff;
        }
        img {
            width: 100%;
            max-width: 160px;
            object-fit: cover;
        }
    }
    .locked {
        bottom: 10px;
        right: 10px;
        width: 8px;
        height: 10px;
        background-size: cover;
    }
}
.single-playlist-list-item .video-text-container {align-items: center;} 
.popup.edit-playlist-popup,
.popup.add-to-playlist-popup {
	max-width: 400px;
}
.cursor {
    cursor: pointer;
}
.playlists-list {
	min-height: 0px;
	max-height: 60vh;
	overflow-y: auto;
    padding: 0 30px;
}
.playlists-list-item {
	display: flex;
	align-items: center;
	padding: 20px 0;
    border-bottom: 1px solid #F0F0F0;
    cursor: pointer;

    .checkbox-box {
        width: 20px;
        height: 20px;
        display: block;
        border: 1px solid #F0F0F0;
        margin-right: 15px;
        transition: all 0.15s ease-in-out 0s;
        border-radius: 4px;
    }
    &:hover .checkbox-box {
        border: 1px solid rgb(185, 185, 185);
    }
    &.checked .checkbox-box {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #000;
        }
    }
    .playlist-title {
        font-size: 12px;
        line-height: 20px;
    }
    .playlist-added {
        font-size: 12px;
        color: #969696;
        margin-left: auto;
        margin-right: 0px;
        height: 20px !important;
    }
    .playlist-private {
        margin-left: 20px;

        img {
            width: 11.5px;
            height: 15px;
        }
    }
}
.playlist-wrap {
    max-width: 800px;
    margin:47px auto 0;
}
.playlist-list {
    border: 1px solid #f0f0f0;
    border-radius: 10px;
}

.playlist-list .col-12 {
    padding-left:30px;
    padding-right: 30px;
}
.playlist-list .col-12:hover {
    background-color: #fcfcfc;
}
.playlist-list .single-playlist-list-item-wrap:not(:last-of-type) {
    border-bottom:1px solid #f0f0f0;
}
.single-user-playlist-item {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
	align-items: center;
	height: 100px;
}
.single-user-playlist:first-child .single-user-playlist-item {
    border-top: 1px solid #f0f0f0;
}
.single-user-playlist-content .playlist-option {
    right: 0;
    top: 50%;
    margin-top: -15px;
    height:30px;
}
.big-content-img {
    width: 280px;
    height: 280px;
    position: relative;
    min-width: 280px;
    min-height: 280px;

    img {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
    }
}
.playlist-list .playlist-option {
	bottom: auto;
	right: auto;
	position: relative;
}
.avatar-50 {
	width: 50px;
	height: 50px;
	min-width: 50px;
	min-height: 50px;
	border-radius: 50%;
	object-fit: cover;
	overflow: hidden;
}
.machine-image {
	position: relative;
    margin-bottom: 70px;
}
.the-micro {
	position: absolute;
	top: 50%;
	right: 120px;
	transform: translateY(-50%);
}
.video-wrap {
	position: relative;
	width: 100%;
	height: 0;
	padding-top: 56.25%;

    iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 111;
    }
}
.playlist-empty-title {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	color: #fff;
	text-transform: uppercase;
	font-weight: bold;
	z-index: 111;
	padding: 20px;
	width: 100%;
	height: 100%;
	line-height: 1.1;
	text-align: center;
}
.single-playlist-item {
    transition: all 0.25s ease-in-out 0s;

    img {
        transition: all 0.25s ease-in-out 0s;
    }
    &:hover img {
        opacity: 0.7;
    }
}
.video-text-container h4 {
    transition: all 0.25s ease-in-out 0s;
}
.video-text-container h4:hover {
    color: #666;
}
.single-ajax-class .video-container img {
    transition: all 0.25s ease-in-out 0s;
}
.single-ajax-class:hover .video-container img {
    opacity: 0.7;
}
.playlist-big-image-content {
	width: 100%;
	height: auto;
	padding-bottom: 0;
	max-width: 300px;
}
/* NOTIFICATIONS */
.notifications-icon {
    display: block;
    width: 16px;
    height: 22px;
    position: relative;
    background: url(../images/bell-icon2.svg) no-repeat center center / cover;

    &:hover {
    
    }
}
.red-dot {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	position: absolute;
	top: 0px;
	right: -3px;
	background: #DB1818;
    display: block;
}
.red-dot-small {
	width: 7px;
	height: 7px;
	border-radius: 50%;
	position: absolute;
	top: 8px;
	right: -5px;
	background: #DB1818;
}
.dropdown {
	position: relative;

    &.opened > .dropdown-menu.notifications,
    > .dropdown-menu.notifications {
        min-width: 360px;

        .drop-title {
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 10px 20px;
            font-size: 12px;
            font-weight: 600;
        }
        li {
            position: relative;

            .single-notification .link {
                font-size: 10px !important;
                font-weight: 400;
                line-height: 13px !important;
                margin: 10px 0 !important;
                text-transform: none;
                display: inline !important;
            }
        }
    }
}
.single-notification {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 22px;
    font-size: 12px;
    background: #f8f8f8;
    width: 100%;
    min-height: 80px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;

    &.seen {
        background: #fff;

        & + .remove-notif {
            color: #f0f0f0;
            &:hover {
                color: #999 !important;
            }
        }
        .red-dot-small {
            display: none;
        }

    }
    &:hover {
        background: #faf9f9;
        cursor: pointer;
    }
}
.dropdown-menu.notifications .single-notification {border-radius:0;border-bottom: none;}

.notif-icon {
	position: relative;
	width: 22px;
	height: 20px;
	margin-right: 22px;
	background: #000;
	display: flex;
	align-items: center;
	justify-content: center;
    background:url(../images/notifblack-new-icon.svg);
    background-repeat: no-repeat !important;
    background-size: contain !important;
    background-position: center !important;
    flex: 0 0 22px;

}
.single-notification.seen .notif-icon {background:url(../images/notifwhite-new-icon.svg);
}
.notif-icon img {
    width: 12px;
}
.notif-icon .red-dot-small {
	top: 1px;
	right: 1px;
}
.notif-title {
	display: flex;
	flex-direction: column;

    p {
        display: block;
        line-height: 1.4;
        font-size: 11px;
        margin-bottom: 5px;
        padding-right: 20px;
        text-transform: none;

        * {
            line-height: 1;
        }
    }
    * {
        text-transform: none;
    }
    span {font-size:11px;}

}
.remove-notif {
    font-size: 24px;
    margin-left: auto;
    cursor: pointer;
	position: absolute;
	top: 50%;
	right: 15px;
	transform: translateY(-50%);
    width:12px;

    &:hover {
        color: #999 !important;
    }
    &:hover img {
        filter: invert(1);
    }
}
.with-dots {
    padding-left: 15px;

    li {
	    position: relative;

        &::before {
            content: "â€¢";
            position: absolute;
            left: -10px;
        }
    }
}

.notifications-group-list {

    li {
        position: relative;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    .single-notification {
 
        & .notif-title p {
            font-size: 12px;
        }
        & + .remove-notif {
            font-size: 28px;
            right: 30px;
            font-weight: 300;
            line-height: 1;
        }
    }
}
/* CONVERSATIONS */
.conversation-list {
	padding:30px 10px 30px 15px !important; 

    .single-msg {
        display: flex;
        margin-bottom: 40px;
        flex-direction: row-reverse; 
        margin-bottom:30px;

        &:last-of-type {
            margin-bottom: 0 !important;
        }
        &.unread + .unread {
            margin-top: 0px;
            position: relative;

            &::after  {display: none;}
            &::before {display: none;}
        }
        &.unread {
            margin-top: 115px;
            position: relative;

            &::after {
                content: "";
                height: 1px;
                position: absolute;
                bottom: calc(100% + 55px);
                left: 0%;
                z-index: 2;
                width: 100%;
                background: #000;
            }
            &::before {
                content: attr(data-msg);
                height: auto;
                position: absolute;
                bottom: calc(100% + 45px);
                left: 50%;
                z-index: 3;
                transform: translateX(-50%);
                width: auto;
                background: #fff;
                padding: 5px 18px;
                font-size: 12px;
                display: inline-block;
                line-height: 1;
            }
            .avatar40::before {
                content: "";
                width: 10px;
                height: 10px;
                border-radius: 50%;
                position: absolute;
                top: 0px;
                right: 0px;
                background: #ff0403;
                display: block;
            }
        }
        .avatar40 {
            margin-left: 0;
            margin-right: 20px;
            overflow: visible;

            img {
                border-radius: 30px;
                overflow: hidden;
                width: 40px;
                height: 40px;
                object-fit: cover;
            }
        }
        &:not(.seb-msg) {
            flex-direction: initial;

            .avatar40 {
                margin-right: 0;
                margin-left: 15px;
            }
            .msg-area {
                margin-left:20px;
            }
            .msg-area .message {
                background: #fff !important;
                border: 1px solid #f0f0f0;
            }
        }
        .msg-area {
            max-width: calc(100% - 120px);
            min-width: 240px;
            margin-right:20px;
            max-width:750px;

            .message {
                padding: 15px 20px;
                line-height:20px;
                border: none;
                font-size: 12px;
                font-weight: 400;
                background: #f8f8f8;
                word-break: break-word;
                border-radius:10px;
            }
            .msg-useful {
                font-size: 10px;
                font-weight: 300;
                display: flex;
                align-items: center;
                margin-top: 10px;
                line-height: 1;
                font-weight: 300;

                img {
                    width: 12px;
                }
            }
        }
        .msg-date {
            line-height: 1;
            font-size: 10px;
            font-weight: 400;
            text-align: right;
            margin-top: 10px;
            color: #969696;
        }
    }
}
.container-askseb {max-width:750px; margin:0 auto; width:calc(100% - 20px);}/*dodati-novo*/

.conversation-list.bg--loading::after {
	top: 50% !important;
}
.conversation-write {
	width: 100%;
	border-bottom: none !important;
    padding:30px !important; 
    border-top:1px solid #f0f0f0;

    form {
        position: relative;

        textarea {
            min-height: 0; padding: 18px 0 0 20px; font-size: 14px; height:55px !important; border-radius:8px; border:1px solid #ddd; background:#fdfdfd; width: 100%;
        }
        button {
            position:relative; 
            top:initial; 
            right:initial; 
            display: flex; 
            margin-left: auto; 
            margin-top: 30px; 
            height:46px;
        }
    }
}
.ask-banner {
    background:#000 url("../images/askbg.jpg") center no-repeat; 
    background-size: cover; 
    width:100%; 
    border-top-left-radius: 10px;
    border-top-right-radius:10px;
    height: 150px;
    padding-left: 50px;
    display: flex;
    align-items: center;
}
.ask-banner h1 {
    color:#fff;
    font-size: 20px !important;     
}
.h80 {
	height: 80px;
}
.minH300 {
    min-height: 300px;
}
.subscribe-image  {
    height: 48px;
}
.check-code-buttom:hover {
    text-decoration: underline;
}
.check-code-buttom {
    position: absolute;
    top: 35px;
    right: 0;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
}
.code-container {
    position: relative;
}
.code-container .applycpn {
    position: absolute;
    right: 0;
    background: none !important;
    color: #000 !important;
}
.code-container .applycpn:hover {text-decoration: underline;}
.dashboard-page .video-container {
	margin-bottom: 25px;
}
.add_video {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}
.add_video_text {
	font-size: 12px;
	display: block;
}
.red_bar {
	color: #fff;
	text-align: center;
	font-size: 12px;
	font-weight: 500;
	position: relative !important;
	// position: absolute !important;
	// top: 0;
	// z-index: ********11;
	// left: 0;
	// width: 100%;
}
.announcement_bar {
	color: #fff;
	text-align: center;
	font-size: 12px;
	font-weight: 500;
	position: relative;
	z-index: 12;
}
.close_announcement {
	position: absolute;
	top: 0;
	right: 0;
	padding: 0 20px;
	font-size: 20px;
	z-index: 9;
	cursor: pointer;

    &:hover {
        opacity: 0.5;
    }
}
.custom-selectbox ul li.drop-separator {
    padding-left: 10px !important;
    color: #ccc;
    pointer-events: none;
}
.custom-selectbox ul li.select-machine-msg {
    padding-left: 0px !important;
    color: #000;
    text-align: center;
    pointer-events: none;
}
.custom-selectbox ul li.select-machine-msg::before,
.custom-selectbox ul li.drop-separator::before {
    display: none;
}
.mvp-music-toggle svg {
	height: 22px;
}
.mvp-volume-wrapper svg {
	height: 22px;
}
.mvp-btn.mvp-btn-volume-off,
.mvp-music-toggle.mvp-contr-btn.muted {
    opacity: 0.4;
}
.phone-buttons {
	margin-top: 0;
	gap: 15px;
	padding: 15px;
	font-size: 14px;
	font-weight: 600;
    border-bottom: 1px solid #F0F0F0;
    display: none !important;

    .phone-button {
        cursor: pointer;
        width: 50%;
        text-align: center;
        padding: 1px 10px;
        font-size: 10px;
        border: 1px solid #F0F0F0;

        &:not(.active) {
            color: rgba(0,0,0,0.3);
        }
    }
}
.show-popup .applying_filters {
    opacity: 1;
    visibility: visible;
}
.applying_filters {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 111;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
	text-align: center;
	font-size: 20px;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.abs-bottom-down {
	position: absolute;
	bottom: 40px;
	right: 40px;
	z-index: 11;
	height: 25px;
}
.textarea {
	width: 100%;
	border: 1px solid #ddd !important;
    padding: 13px 20px 0 !important;
    margin-bottom: 20px;
    border-color: #ddd !important;
    border-radius: 8px;
    background: #fdfdfd;

    textarea {
        border: none !important;
        font-size: 14px;
        width: 100%;
        line-height: 1.2 !important;
        padding: 4px 0 0;
        margin: 0;
    }
}
.single-comment {
	display: flex;
}
.single-comment-info {
	margin-left: 20px;
}
.comment-user, .single-comment * {
	line-height: 1 !important;
}
.comment-message {
	font-size: 12px;
	line-height: 20px !important;
	margin-top: 2px;
}
.reply-comments .single-comment {
	margin-top: 30px;
}
.reply-comments {
	padding-left: 60px;
}
.comments-container {
	margin-top: 0;
 }
.btn.btn-sm.h40 {
	min-height: 40px;
}
.single-comment-action {
	margin-top: 7px;
}
.reply-comments .single-comment-action {
	margin-top: 7px;
}
.class-next {
	position: absolute;
	top: 50%;
	right: 50px;
	transform: translateY(-50%);
    opacity: 0.3;
    transition: all 0.15s ease-in-out 0s;
}
.class-prev {
	position: absolute;
	top: 50%;
	left: 50px;
	transform: translateY(-50%);
    opacity: 0.3;
    transition: all 0.15s ease-in-out 0s;
}
.class-prev:hover,
.class-next:hover {
    opacity: 1;
}
.video-container.single-exercise {
	display: flex;
	align-items: center;
}
.single-exercise-image {
	width: 100px;
	height: 55px;
    position: relative;
    margin-right: 15px;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border: 1px solid transparent !important;
    }
}
.video-container .single-exercise-image .play-button {
	width: 100px;
	height: 55px;

    span {
        border: 10px solid rgba(0, 0, 0, 0);
        border-left: 18px solid #fff;
        margin-left: 18px;
    }
}
 
.single-comment-container:first-of-type {border-top:1px solid #f0f0f0; padding-top: 30px;}
.single-comment-container:last-of-type {margin-bottom: 30px;}

.reply-comments > .reply-comments {
	padding: 0;
}
.single-class-title {
    line-height: 1.3 !important;
    font-size: 24px !important;
}

/* SHOP */
.search-container {
    position: relative;
    height: 47px;
}
.search-form {
    position: absolute;
    top: 0;
    height: 47px;
    right: 0;
    z-index: 22;
}
.search-form.show .search-input,
.search-form:hover .search-input,
.search-form:active .search-input,
.search-form:focus .search-input,
.search-form .search-input:focus,
.search-form .search-input:active {
    width: 340px;
    color: #fff;
    background: #000;
    border-color: #000;
}
.search-form .search-input::placeholder {
    color: #fff;
    opacity: 1;
}
.search-form .search-input {
    height: 47px;
    border: 1px solid #F0F0F0;
    border-radius: 8px;
    width: 47px;
    background: #fff;
    padding: 0 20px;
    font-size: 14px;
    letter-spacing: 0;
    color: #fff;
    transition: all 0.25s ease-in-out 0s;
}
.search-form .search-button {
    background: rgba(0, 0, 0, 0);
    position: absolute;
    border: none;
    border-radius: 0px;
    top: 0;
    right: 0;
    z-index: 11;
    width: 47px;
    height: 47px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.search-form.show .search-button,
.search-form:focus .search-button,
.search-form:active .search-button,
.search-form:hover .search-button {
    // top: 1px;
    height: 47px;
    background: #000 !important;
    border: none;
}

.search-form.show .search-input + .search-button img,
.search-form:hover .search-input + .search-button img,
.search-form:active .search-input + .search-button img,
.search-form:focus .search-input + .search-button img,
.search-form:hover .search-input + .search-button:hover img,
.search-form:active .search-input + .search-button:hover img,
.search-form:focus .search-input + .search-button:hover img,
.search-form .search-input:hover + .search-button img,
.search-form .search-input:focus + .search-button img,
.search-form .search-input:active + .search-button img,
.search-form .search-input:hover + .search-button img,
.search-form .search-input:active + .search-button:hover img,
.search-form .search-input:focus + .search-button:hover img {
    filter: invert(1);
}
.search-form.show .search-button,
.search-form:hover .search-button {
    background: #000 !important;
}
.keyword {
    font-size: 30px;
    line-height: 50px;
}
.cart-button {
	position: relative;
}
span.cart_items_no {
	position: absolute;
	top: -7px;
	right: -6px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #000 !important;
	color: #fff;
	width: 15px;
	height: 15px;
	font-size: 10px;
	border-radius: 50%;
}
/* CART */
/* SHOP CART */
.counter {
    width: 150px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.counter input {
	width: 45px;
	border: 0;
	line-height: 45px;
	font-size: 14px;
	text-align: center;
	background: #000;
	color: #fff;
    border-radius: 0 !important;
	appearance: none;
	outline: 0;
	margin: 0 10px;
}
.counter span:hover {
    border: 1px solid #000;
}
.counter span {
	width: 45px;
	min-width: 45px;
	line-height: 45px;
    display: block;
    font-size: 25px;
    text-align: center;
    padding: 0 10px;
    cursor: pointer;
    color: #000;
    user-select: none;
	border: 1px solid #f0f0f0;
}
.cart-table {
	display: block;
	width: 100%;
}
.cart-table-header {
	display: flex;
	width: 100%;
	font-size: 13px;
	padding: 40px 0;
	border-top: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
}
.cart-table-body {
	width: 100%;
	font-size: 18px;
}
.cart-table-product {
	width: 70%;
	flex: 0 0 70%;
	max-width: 70%;
	display: flex;
	align-items: center;
}
.cart-table-qty {
    width: 20%;
	flex: 0 0 20%;
	max-width: 20%;
    display: flex;
    justify-content: center;
}
.cart-table-total {
    display: flex;
    justify-content: flex-end;
	width: 10%;
	flex: 0 0 10%;
	max-width: 10%;
}
.cart-table-single-product {
	display: flex;
	width: 100%;
	padding: 60px 0;
	align-items: center;
	border-bottom: 1px solid #f0f0f0;
}
.cart-table-image {
    display: block;
	width: 220px;
	height: 220px;
	margin-right: 50px;
	/* background: #f0f0f0; */
	border: 1px solid #f0f0f0;
}
.cart-table-image img {
	width: 100%;
	height: 100%;
    object-fit: contain;
}
.cart-table-product-name {
	display: flex;
	flex-direction: column;
}
.cart-table-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 50px 0;
    font-size: 18px;
	border-bottom: 1px solid #f0f0f0;
}
.cart-table-shipping {
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    height: 70px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.cart-table-grand-total {
	font-size: 28px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    padding: 70px 0;
}
// .single_checkout_button:hover img {
// 	filter: invert(1);
// }
.single-product-title {
	line-height: 1;
	text-transform: uppercase;
	font-size: 30px !important;
}
.single-product-price {
	color: #969696;
	line-height: 1;
	font-size: 18px !important;
}
.single-product-desc *,
.single-product-desc p,
.single-product-desc {
	font-size: 14px !important;
	line-height: 30px !important;
    text-align: justify;
}
.single-product-desc p:not(:last-of-type) {
	margin-bottom: 30px;
}
.link-redirect {
	font-size: 16px !important;
}
.event-desc.popup-event-desc a {
	color: #000 !important;
	text-decoration: underline !important;
	font-weight: bold !important;
}
body .center-locator.locator-info {
	display: none !important;
}
.btn.btn-tall {
	height: 56px;
	font-size: 14px !important;
}
.product-single-image {
	position: relative;
	border: 1px solid #f0f0f0;
}
.product-single-image-badge {
	position: absolute;
	top: 30px;
	left: 30px;
	z-index: 1;
}
.account-header {
	height: 300px;
	background: #000 url(../images/account-header-bg.jpg) no-repeat center center / cover;
	display: flex;
	align-items: center;
	padding: 0;
    max-width:100% !important;
    margin-bottom: 3px !important;
}
.account-btn {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: $border;
	border-radius: 50%;
    cursor: pointer;

    &:hover {
        border-color: $black;
    }
}
.account-btn + .dropdown-menu.drop-right.side-links {
	right: auto;
	left: 100%;
	margin-left: 15px;
	top: 0;
	margin-top: 0;
    min-width: 160px;
}
.table-row .video-container .play-button span,
.featured-row .video-container .play-button span {
	border: 12px solid rgba(0, 0, 0, 0);
	border-left: 20px solid #fff;
	margin-left: 16px;
}
@media(min-width: 1024px){
    .show-popup .applying_filters {
        font-size: 28px;
    }
}
.rename_device_item {
    padding: 7px 5px;
    margin-bottom: 0;
    position: relative;
    transform: translateX(-5px);
    white-space: nowrap;
    border: 1px solid #fff;
}
.rename_device_item:focus {
	border: 1px solid #dadada !important;
	width: 80%;
}
.rename_device_item:hover {
	border: 1px solid #f8f8f8;
	width: 80%;
}
.os-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    padding: 0;
    margin-right:20px !important;
}
.minH100 {
	min-height: 100px;
}
.current_video_state {
	display: block;
	position: absolute;
	bottom: 0;
	left: 0;
	height: 4px;
	background: red;
	z-index: 111;
}
.help-center-page .side-links li a {
	font-size: 14px !important;
	display: inline-block;
	padding: 3px 0;
}
.device-tooltip {
    position: absolute;
    top: 50%;
    display: block;
    width: 290px;
    font-size: 11px;
    line-height: 1.4;
    right: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    margin-top: -47px;
    background: #000;
    color: #fff;
    padding: 10px;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.25s ease-in-out 0s;

    &::before {
        content: "";
        position: absolute;
        top: 100%;
        right: 10px;
        border: 10px solid rgba(0, 0, 0, 0);
        border-top: 7px solid #000;
    }
}
[data-title] {
    position: relative;

    &::before {
        content: attr(data-title);
        position: absolute;
        bottom: calc(100% + 5px);
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        background: #000;
        color: #fff;
        font-size: 9px;
        line-height: 1;
        transition: all 0.15s ease-in-out 0s;
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
        padding: 4px 6px 3px 6px !important;
        letter-spacing: 0.3px;
    }
    // &::after {
    //     content: "";
    //     position: absolute;
    //     top: 110%;
    //     left: 50%;
    //     margin-top: -10px;
    //     transform: translateX(-50%);
    //     border: 7px solid rgba(0, 0, 0, 0);
    //     border-bottom: 5px solid #000;
    //     transition: all 0.25s ease-in-out 0s;
    //     visibility: hidden;
    //     opacity: 0;
    //     pointer-events: none;
    // }
    &:hover::before,
    &:hover::after {
        visibility: visible;
        opacity: 1;
    }
}
.device-tooltip-container:hover .device-tooltip {
    opacity: 1;
    visibility: visible;
}
span.link.no-hover:hover {
    text-decoration: none !important;
    color: #969696 !important;
}
.mid-title-machine {
	padding: 0;
    margin: 30px 0 80px 0;
	border-top: 1px solid #F0F0F0;
	border-bottom: 1px solid #F0F0F0;
	width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
	font-weight: 600;
    text-transform: uppercase;
    font-size: 18px;
    letter-spacing: 0.05em;
    line-height: 30px;
    text-align: left;
}
.single-ajax-class .video-container:hover .play-button {
	opacity: 0;
}
.popup-body ul {
	list-style: none;
	font-size: 12px;
	line-height: 25px;
	padding-left: 10px;
	font-weight: 300;

    li {
        line-height: 25px;
        position: relative;

        &::before {
            content: "";
            position: absolute;
            top: 12px;
            left: -11px;
            width: 2px;
            height: 2px;
            background: #000;
            border-radius: 3px;
        }
    }
}
.mob-height {
    max-height: 90vh;
}

.foo-bottom.py-120 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
  margin-top: 50px;
  border-top: 1px solid #1e1e1e;
}
.showmob {display:none !important;}
.footer-social a {margin: 0 0 0 20px;}
.footer-social.showmob {margin-left:auto; margin-right:10px;}
.footer-social.showmob a {display: inline-block; vertical-align: middle; margin-left:10px; margin-right: 0;}
.footer-menu-title {height: 35px !important; text-transform:uppercase;	font-size: 14px;color:#ffffff;}
.footer ul li a {font-size: 14px !important; line-height: 32px;font-weight: 400;color: #aaa !important;}
.foo-bottom .col-6 {padding-right: 0;}
.blockondesk {display:block;line-height: 16px;}
.chat-window-header .showdesk {display: flex;width: 100%;}
.chat-window-header .showdesk img {margin-left: auto;}
header .mainlink.subnav {height: auto; align-items: center; display: flex;}
.desknavdrop {justify-content: space-between !important; align-items: center !important; display: flex !important;}
.mobsubscribe {font: 500 10px "Graphik", sans-serif !important; letter-spacing:1px;}
.showipad {display:none !important;}
.bottom-nav {display:none;}
/*footer menu accordion*/
.foo-mobile .footcol-one h6 {
  width: 100%;
  display: block;
  float: left;
  border-top: 1px solid #272727;
  margin-top: 18px;
  padding-top: 28px;
  margin-bottom: 23px;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
  transition: transform .3s;
  padding-right: 0;
}

.foo-mobile .footcol-one:first-of-type h6 {
  margin-top: 0;
  border-top: none;
}

.foo-mobile .footcol-one h6.active {
  width: 100%;
  margin-bottom: 25px;
}

.foo-mobile .footcol-one h6:after {
  content: url(../images/foo-arrdown.svg);
  float: right;
}

.foo-mobile .footcol-one h6.active:after {
  content: url(../images/foo-arrup.svg);
  float: right;
}

.foo-mobile .foomenulist {
  list-style: none;
  display: none;
  float: left;
  margin-bottom: 5px !important;
}

.foo-mobile .foomenulist a {
  float: left;
  font-size: 13px;
  margin-top: 5px;
  text-align: justify;
  display: block;
  width: 100%;
}

.foo-mobile .foot-col {
  width: 100%;
  margin-bottom: 0;
}

.foo-mobile .foo-first {
  flex-direction: row-reverse;
  margin-bottom: 18px;
  border-bottom: none;
}

.foo-mobile .copyright-p.showmob a {
  float: right;
  text-decoration: underline;
  margin-left: 4px;
}

.foo-mobile .hublogo {
  height: 40px !important;
}
.hublogo {
  margin-right: 10px;
}
.foo-mobile .foo-first p {
  font-size: 14px !Important;
  line-height: 25px !important;
}

.foo-mobile .mr2.lagree-method-footer {
  margin-right: 0 !important;
}

.foo-mobile .foo-nav .footcol-one {
  width: 100% !Important;
  max-width: 100% !Important;
}

.foo-mobile .foo-nav .col-mob-6 {
  width: 100%;
  flex: 0 0 100%;
  max-width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.foo-mobile .foo-nav ul {
  padding-left: 0;
}

.foo-mobile .foo-first span {
  margin-right: auto;
}

.foo-mobile .foo-selling.col-4.flex.aic.jcr {
  padding-top: 35px;
}

.foo-mobile .py-100 {
  padding-bottom: 0px !important;
}

.foo-mobile .foo-bottom.hubcol {
  padding-top: 25px !important;
  padding-bottom: 0 !important;
}

.foo-mobile .foo-bottom.hubcol .col-6 {
  padding-bottom: 25px !important;
}

.foo-mobile .hubcol a {
  margin-left: auto !important;
  text-decoration: underline;
}

footer.foo-mobile .col-8.flex.aic.jcl.white,
footer.foo-mobile .col-4.flex.aic.jcr,
.foo-mobile .hubcol {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.foo-mobile .foo-first img {
  margin-right: 0 !important;
}

.foo-mobile .hubcol .col-6 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.mobprivacy {
  display: none;
  color: #ffffff;
  float: right;
}

.foo-mobile .mobprivacy {
  display: block;
}

.foo-mobile .copyspan {
  width: 100%;
  margin-right: 0 !important;
}

.foo-mobile .mobfoo-btn {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 18px;
  padding-top: 40px;
  padding-bottom: 10px;
}

.foo-mobile .mobfoo-btn a {
  width: 100%;
  border: 1px solid #ffffff !important;
}

.foo-mobile .container {
  padding-left: 30px;
  padding-right: 30px;
}

.foo-mobile .row {
  margin-left: 0;
  margin-right: 0;
}
.overlay .mini-popup.hub-popup, .show-popup .overlay .mini-popup.hub-popup.show {
	padding-top: 25px !important;
	top: 50% !Important;
	transform: translate(-50%, -50%) scale(1.0) !Important;
	overflow: hidden !Important;
	left: 50% !Important;
}
.mini-popup.hub-popup p.text-center.pb-2 {
  font-size: 14px !important;
}
/*-----*/
.single-ajax-class .music-with-control {
    display: none;
}
.single-course {
	position: relative;
    margin-bottom: 60px;

    .single-course-desc {
        position: absolute;
        z-index: 1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        letter-spacing: 0.05em;
        text-align: center;
        text-transform: uppercase;

        h3 {
            font-weight: bold;
            font-size: 28px;
        }
        p {
            font-weight: normal;
            font-size: 12px;
        }
    }
    img {
        width: 100%;
        max-height: 500px;
        object-fit: cover;
    }
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(rgba(0,0,0,0), #333);
        opacity: 0.8;
    }
}
.progress-outer {
	max-width: 350px;
	width: 100%;
	height: 5px;
	background: gray;
	position: relative;

    .progress-inner {
        position: absolute;
        top: 0;
        left: 0;
        height: 5px;
        background: red;
        transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
    }
}
.with-bullets.lh-25 li {
    line-height: 25px;
}
.with-bullets {
	list-style-type: disc;
	list-style-position: inside;
}
.start-course-banner {
    position: absolute;
    top: 80px;
    right: 0;
    width: 100%;
    height: 100%;
    max-width: 360px;
    .start-course-image {
        height: 200px;
        width: 100%;
        object-fit: cover;
    }
    .start-course-img {
        position: relative;
 
    }
    .start-course-img img {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid #212121;
        border-bottom: none;
    }
    .play-course {
        @include centerAbsPosition();
        cursor: pointer;

        &:hover{
            opacity: 0.7;
        }
    }
    .start-course-info {
        background: #fff;
        padding:30px 30px 20px;
        border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
    }
}
.single-course-item {
    border: 1px solid #f0f0f0;
    border-radius: 10px;
    margin-bottom: 15px;
}
:nth-last-child(1 of .single-course-item)  {margin-bottom: 0;}

.single-course-item.opened {border-bottom-left-radius: 0; border-bottom-right-radius: 0; margin-bottom: 0; border-color: #ddd; border-bottom: none;}
.single-course-accordion .single-course-item {border: none;}
.single-course-video {
     margin-bottom:0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px 0 !important;  
 
    p {
        line-height: 18px;
        width: 100%;

        &.playing {
            color: #DB1818 !important;
            font-weight: 600 !important;
        }

    }
    // &:hover {
    //     background: #f9f9f9;
    // }
    .check-status .img-fluid {
        width: 20px !important;
        height: 20px !important;
        min-width: 20px !important;
        min-height: 20px !important;
    }
    .text-status{
        margin-left: auto;
        font-size: 12px;
        line-height: 20px !important;
        text-transform: capitalize;
        color: #000;
        font-weight: 600;
        transition: none !important;

        &:hover {
            color: $midGray !important;
        }
        &.completed {
            // color: #52C15A !important;
            font-weight: 600 !important;
            border: none !important;
            font-size: 0 !important;
            line-height: 1 !important;
        }
        &.completed:before {
            content:"";
            width:70px; 
            height: 30px;
            background:url(../images/completed-new.svg);
            display: block;
        }
        &.playing {
            color: #fff !important;
            font-weight: 600 !important;
            background: #000;
            min-width: 70px;
            width: 70px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            font-size: 10px;
            text-transform: uppercase;
            user-select: none;
            pointer-events: none;
        }
        &:not(.completed){
            cursor: pointer;
        }
    }

}
.single-course-accordion  .single-course-video:first-of-type {padding-top:25px !important;}
/*.single-course-item.single-course-video:hover {background: #f8f8f8;}*/
.single-course-item.single-course-video:not(.rest_day) {font-weight:500;}
.single-course-title .check-status {border: 1px solid #ddd; padding:15px; border-radius: 50%; margin-left: 20px; position: relative;}
.single-course-item.single-course-video.rest_day .check-status:before {content:url(/images/icon-restday.svg); position: absolute; margin-top: -4px;}
.single-course-item.single-course-video.rest_day .check-status.video_watched :before {content:url(/images/checked.svg); position: absolute; margin-top: -4px;}
.single-course-video .text-status:not(.completed):not(.playing) {font-size: 10px; line-height: normal !important; text-transform: uppercase; color: #fff; background:#000; border:1px solid #000; padding:8px 18px; border-radius: 30px;}
.single-course-video .text-status:not(.completed):hover {color: #000 !important; background:#fff;}


.single-course-title.opened {
    border-bottom: none;
  }
.single-course-accordion {
    margin-top: 0;
    margin-bottom: 15px;
    border: 1px solid #ddd; 
    border-top:none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius:10px;
    padding-bottom: 24px;
}
.single-course-title {
    padding: 19px 25px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    transition: all 0.15s ease-in-out 0s;

    &:hover {
        color: $midGray !important;
    }
    h4 {
        line-height: 1;
    }
    .check-status img {
        transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        margin-left: -6px;
    }
    &.opened .check-status img {
        transform: rotate(180deg);
        margin-top: -4px;
    }
    &.opened {
        border-bottom: 1px solid #f0f0f0;
    }
    .course-video-count {
        font-weight: 400 !important;
        text-transform: none !important;
        color: $midGray;
        line-height: 1;
        white-space: nowrap;
        margin-left: 10px;
    }
    // &:hover {
    //     background: #f9f9f9;
    // }
}
.scacctxt {padding: 23px 25px 0 25px;}
.scacctxt:after {content:""; width:100%; height:1px; background:#f0f0f0; display: block; margin-top: 25px;}
.machine_first.top--50 {margin-top: -50px;}
.reversecols {flex-direction: row-reverse;}
.row-actions {display: none !important;}
.signup-foo p, .signup-foo p span {line-height:16px !Important;}

/*OnBoarding*/
.ob-body header {justify-content: center; position: relative !important;}
.ob-body header .left-menu, .ob-body header .right-menu .subscribedesk, .ob-body header .menu-button, .ob-body header .mobsubscribe {display:none !important;}
.ob-body header .right-menu {margin-left: auto !important;}
.ob-body header .logo {position:absolute;}
.ob-body footer, .ob-body .mid-nav {display:none !important;}
#onboarding-body {padding-top:0;}
#onboarding-body section {margin-bottom:20px;}
#onboarding-body .container {max-width:1030px;}
.black-banner {background: #000000; color:#ffffff; text-align:center; padding: 170px 20px 40px;}
.black-banner h1 {font-size:30px !important; line-height: 60px; margin-bottom: 5px; color:#fff;}
.black-banner p {color:#fff;}
.black-banner .stepof {color: #707070; font-size:14px; font-weight:600; margin-top:100px; display:block;}
.ob-title {text-align:center; width:100%; padding-top:75px; padding-bottom:70px; border-bottom:1px solid #f0f0f0; border-bottom:1px solid #f0f0f0;}
.ob-title.obtitle-video {padding-top: 53px; padding-bottom: 47px;}
.ob-title h4 {font-size:18px !important; line-height: 26px; margin-bottom:5px;}
.ob-title p {font-size:14px; color:#DB1818;}
#onboarding-body form {width:100%; }
#onboarding-body form .form-box {border: 1px solid #f0f0f0; padding:7px 0 7px 15px; width:100%; max-width:500px; margin:0 auto 15px; line-height: 0; cursor:pointer; border-radius: 8px;}
#onboarding-body form h5 {text-align:center; font-size:16px; font-weight:600; padding-top:46px; padding-bottom:48px;}
.checkbox-wrap {border:1px solid #F0F0F0; }
.form-box .squaredOne, .form-box span {display: inline-block; vertical-align: middle;}
.form-box span {font-size:12px; margin-left: 8px;}
.squaredOne {width: 20px; height: 20px; background: #ffffff; position: relative; border-radius: 4px;}
.squaredOne input  {position: absolute; cursor:pointer; top: 0; left: 0; width: 20px; height: 20px; margin: 0; border: none; z-index: 9999; opacity: 0;}
.squaredOne label {cursor: pointer; position: absolute;	width: 10px; height: 10px; left: 4px; top: 4px;	background: #ffffff; padding-left: 0 !important;}
.squaredOne label:after {opacity: 0; content: ''; position: absolute; width: 8px; height: 8px; background: #000000; top: 1px !important; left: 2px !important;}
.squaredOne > input[type=checkbox]:checked + label:after {opacity: 1;}
.squaredOne label:before {display:none !important;}
#onboarding-body hr {width:100%; height:1px; background:#f0f0f0; margin:60px 0 0;}
.checkbox-group {display:none;}
.lodselect {padding-left:20px; border: 1px solid #f0f0f0; width: 100%; max-width: 500px; margin: 0 auto; display: block; height: 50px; appearance: none; -webkit-appearance: none; background-size: 8px 5px; letter-spacing: 0.05em; background: url(/admin_assets_new/images/triangle-down.svg) no-repeat right 20px center; background-size: auto; font:12px 'Graphik'; font-weight:400;
margin-bottom:15px; border-radius: 0; color:#000000;}
.survey-btn {
	width: 100%;
	max-width: 500px;
	background: #000000;
	color: #ffffff;
	font-weight: 600;
	font-size: 14px;
	letter-spacing: 1.4px;
	display: flex;
	margin: 60px auto 25px;
	height: 54px;
	border: 1px solid #000000;
	cursor: pointer;
	font-family: "Graphik", sans-serif;
	align-items: center;
	justify-content: center;
    user-select: none;
    border-radius:50px;
}
.survey-btn:hover {background:#ffffff; color:#000000;}
.completesurvey {font-size:12px; color:#969696; text-align:center;}
.moretxt {display : none;}
.contid {font-weight:300; font-size:14px;}
#lipsum {margin-top:50px; border-bottom:1px solid #f0f0f0; padding-bottom: 47px;}
#lipsum a.lessText {display: none;}
#lipsum span.secondHalf {display: none;}
#lipsum p.summary, .moreText, .lessText {font-weight:400; font-size:14px; line-height:30px;}
.classdesc a.moreText, .classdesc a.lessText, .classdesc a.moreText span, .classdesc a.lessText span {margin-top: 0;}
.classdesc p {font-size: 14px; line-height: 25px;}
.classdesc p.read_more {margin-top:20px;}
.moreText, .lessText {color:#000000 !important;  cursor:pointer;}
.moreText span, .lessText span {display:block; color:#969696 !important; margin-top: 6px;}
.greytxt {color:#969696 !important;}
.videoWrapper {position: relative; padding-bottom: 56.22%; height: 0; border-radius: 10px; overflow: hidden;}
.videoWrapper iframe {position: absolute; top: 0; left: 0; width: 100%;	height: 100%;}
.videoplay {position:absolute; top:50%; left:50%; z-index:80;}
.videoWrapper .videoposter {position:absolute; top:0; left:0; z-index: 60; object-fit: cover; width: 100%; height: 100%;}
.video-opacity {background:rgba(0,0,0,0.2); position:absolute; top:0; left:0; z-index: 60;  width: 100%; height: 100%; z-index: 99;}
.videoWrapper .videoposter img {position:absolute; top:0; left:0; /*object-fit: cover;*/ width: 100%; height: 100%;}
.videoWrapper .playtriangle {position:absolute; top:50%; left:50%; z-index: 70; margin-top:-50px; margin-left:-40px; cursor:pointer; width:80px;}
.videoWrapper .playtriangle:hover {opacity:0.7;}
.video-spacer {display: block; height:60px;}
.firstHalf, .secondHalf {line-height: 25px;}
.watch-video {border-bottom:1px solid #f0f0f0;}
.watch-video .form-box {text-align: right; border: none; margin-top:15px; margin-bottom:15px;}
.greentxt {color:#52C15A !important;}
.graycheckbox, .checkbox-group .form-box {background:#F8F8F8;}
.typatiente {border-top: 1px solid #f0f0f0 !important; margin-top: 60px !important;}


/*JoinLagree*/
#onboarding-body.joinlagree-body .container {max-width:1030px;}
.black-banner.joinbanner {padding:260px 20px 174px;}
#tab-outer {position:relative; margin-top: 90px;}
#tab-wrapper {list-style:none; text-align: center; padding-bottom: 85px; border-bottom: 1px solid #f0f0f0;}
#tab-wrapper li {display: inline-block !important; margin-left: 32px; margin-right: 32px;}
#tab-wrapper li a {display:block; font-weight: 600; font-size: 18px; line-height:30px; text-decoration:none; color: #969696;}
#tab-wrapper li.active a {color:#000;}
.join-title {text-align: center; padding-top: 90px; padding-bottom: 90px;}
.join-title.top-border {margin-top: 100px;}
.join-title h4 {font-size: 24px !important; line-height: 34px; }
.join-title p {font-size: 16px; line-height: 35px !important; line-height: normal;margin-top: 25px;}
.joinform {width:100%; max-width: 570px; margin:0 auto 85px;}
.joinform label, .joinform input {display: block; width:100%;}
.joinform label {font-size: 11px; margin-bottom: 2px;}
.joinform input {height:40px; color:#000000; font-size: 14px; border-radius: 8px; padding-left:20px; border: 1px solid #ddd; margin-bottom: 16px; background: #fdfdfd;}
.joinform input:hover {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1) !important;}
.joinform select {width:100%; height:50px; font-size: 14px; border-radius: 8px; padding-left:20px; border: 1px solid #ddd; margin-bottom: 26px; appearance: none; -webkit-appearance: none; background-color: #ffffff; font-family: 'Graphik';letter-spacing: 0.05em; background: url(https://staging.lagreeod.com/admin_assets_new/images/triangle-down.svg) no-repeat right 20px center; color:#000000; background: #fdfdfd;}
.joinform ::-webkit-input-placeholder {color: #969696; opacity:1; font-family: 'Graphik';letter-spacing: 0.05em;}
.joinform :-ms-input-placeholder {color: #969696; opacity:1; font-family: 'Graphik';letter-spacing: 0.05em;}
.joinform ::placeholder {color: #969696; opacity:1; font-family: 'Graphik';letter-spacing: 0.05em;}
.form-ttl {text-align: center; font-weight: 600; padding-top:90px; padding-bottom: 90px;}
.birth-date {display: flex; justify-content: space-between;}
.birth-date select {max-width:178px; display:inline-block; margin-bottom: 0;}
.joinform button.survey-btn {max-width: 100%;}
.admin-submenu.currenturlbox {display:block !important;}
.hosttybox {text-align: center;	padding-top: 300px; padding-bottom: 195px;}
.hosttybox h5 {font-weight: 600; font-size: 24px !important;}
.hosttybox p {text-align: center; margin-top: 15px; margin-bottom: 50px; color:#969696;}
.hosttybox .custom-btn {font-size: 12px; padding: 13px 21px;}
.join-videowrap {position:relative;border-radius:10px; overflow: hidden;}
.join-videowrap:before {content:""; width:100%; height:100%; background:rgba(0,0,0,0.2); top:0; left:0; position: absolute; border-radius:10px;}
.playbtn {position: absolute; top:50%; left:50%; margin-top:-40px; margin-left: -38px; cursor:pointer;}
.join-videowrap .playbtn img {width:80px;}


// join lagree
.custom-selectbox-holder.h55 {
	height: 50px;
}
.h55 .select_val {
	height: 50px;
	line-height: 50px;
}
.nothing {appearance: none;}
.join-qu {text-align:center;}
.join-qu p {font-weight:300; border-top:1px solid #f0f0f0; padding-top:37px; padding-bottom:37px;}
.join-qu p:last-of-type {border-bottom:1px solid #f0f0f0;}
.help-contact {border-top: 1px solid #f0f0f0; margin-top: 85px; padding-top: 135px; padding-bottom: 140px;}
.radio_answer.selected,
.check_answer.selected {
	background: #000 !important;
	color: #fff !important;
}
.coll-title {margin-bottom: 95px;}
.bystaffhome {display: none !important;}
.search-wrapper {
	display: flex;
	align-items: center;
	height: 50px;
    z-index:49;

    .search-popup {
        .close_search {
            width: 17px;
            height: 18px;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 300;
        }
    }
    .search-form-container {
        position: absolute;
        top: 50%;
        height: 50px;
        width: calc(100% - 60px);
        z-index: 11;
        opacity: 0;
        transform: translateY(-50%);
        pointer-events: none;
        visibility: hidden;
        transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
    }
    .search-form-header button {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        position: absolute;
        top: 1px;
        right: 8px;
        border: none;
        z-index: 11;
    }
    &.opened {
        & .search-popup > img {
            display: none;
        }
        & .close_search {
            display: flex;
        }
        .search-form-container {
            opacity: 1;
            pointer-events: auto;
            visibility: visible;    
        }
    }
}
.position-relative-desktop {
    position: relative;
}
.search-wrapper.body-search .search-form-header button {right:0; width: 52px; height: 50px;}
.search-item {padding: 37px 0; border-bottom: 1px solid #f0f0f0;}
.searchsec {padding-top:95px; padding-bottom: 95px;}
.searchsec .container-fluid h1 {font-size:24px !important; margin-bottom: 15px !important;}
.searchsec h2 {padding-top: 47px; padding-bottom: 47px;}
.searchsec h5 {font-size: 14px !important; color:#969696;} 
.searchsec.pt-0.pb-0 {margin-top: 85px; margin-bottom: 85px;}
.noresfound {margin-bottom: 265px;}
.noresfound p {border-top:1px solid #f0f0f0; padding-top:35px;}
.searchsec .body-search {margin-right: initial; margin-left: auto; margin-top: 0; z-index:49; width: 100%;}
.searchres {max-width: 1765px !important; margin:0 auto !important;}
.searchsec .search-form-container {margin-right: 20px;}
.searchres {padding-top:0; padding-bottom: 0;}
/**/
.classesroot .container1030 .main-wrap {width: 100%; flex: 0 0 100%; max-width: 100%;}
.classesroot .container1030 .side-popup {display: none; }
.popup-container {border-radius: 10px;}
.class-body-parts {position:relative; border:1px solid #f0f0f0; border-radius:10px; padding: 23px 25px; min-height:84px; display: flex; justify-content: space-between; align-items: center;}
.body-parts-left {flex:1;}
.body-parts-right {flex: 0 0 80px; display: flex; justify-content: end; }

.class-body-parts:not(:last-of-type) {margin-bottom: 20px;}
.explvideo {font-size: 10px; font-weight: 600; color: #fff; border: 1px solid #000; background: #000; border-radius: 50px; padding: 8px 15px; line-height: normal; cursor:pointer; }
.explvideo:hover {border-color:#000; color:#000; background:#fff;}
.classesroot .comments-form.main-comment-form .textarea {padding: 13px 20px 0 !important; border-color:#ddd; border-radius:8px; background:#fdfdfd; margin-bottom: 0;} 
.classesroot .comments-form.main-comment-form .textarea textarea {min-height: 0px !important; height:38px; background:#fdfdfd;}
.classesroot .comments-form.main-comment-form .textarea.pb-2 { padding-bottom: 20px !important;}
.classesroot .comments-form.main-comment-form .textarea.pb-2.pb-0 { padding-bottom: 0px !important;}
.textarea.pb-2 { padding-bottom: 20px !important;}
.textarea.pb-2.pb-0 { padding-bottom: 0px !important;}
.side-popup {display:none; position: fixed; width: 100%; height: 100%; left: 0; top: 0; background: rgba(0,0,0,0.3); z-index: 999999; }
.side-popup .popup-container {background: #fff; width: 90%; max-width: 500px; padding-bottom: 1px; position: relative; left: 50%; transform: translate(-50%, -50%); top: 50%;;}
.side-popup .popup-container h3 {text-align: center; border-bottom: 1px solid #f0f0f0; padding-top: 28px; padding-bottom: 28px; margin-bottom: 30px; line-height: normal;}
.side-popup .popup-container .exercises-container {padding:0 30px; margin-bottom: 10px; max-height: 375px; overflow-y: auto;}
.side-popup .popup-container .exercises-container .image-overlay, .side-popup .popup-container .exercises-container .image-overlay img {border-radius: 4px;}
.side-popup .video-container {margin-bottom: 20px;}
.side-popup .popup-container .close_exercises {position: absolute; top: 0; right: 0; padding: 30px; cursor: pointer;}
.class-svc {justify-content:space-between; padding-top: 30px; padding-bottom: 30px;}
.class-box-small {width: 24%; flex: 0 0 24%; max-width: 100%; border: 1px solid #ddd; text-align: center; font-size: 10px !important; font-weight: 600 !important; height: 45px; background: #fff; border-radius: 50px;}
.class-box-small:hover {background: #f8f8f8;}
/*.class-box-small:hover a, .class-box-small:hover span {color: #fff !important;}
.class-box-small:hover img {filter: none !important;}
.class-box-small.playlistbox:hover img {filter: invert(1) !important;}
.class-box-small:hover i {filter: invert(1) !important;}*/
.class-box-small img {height:14px;}
.parts-left {margin-top: 52px; padding-top: 55px;}
.container800 {max-width: 830px; margin:0 auto; padding-left: 15px; padding-right: 15px;}
.container850 {max-width: 880px; margin:0 auto; padding-left: 15px; padding-right: 15px;}

.pagination {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 30px;
	padding: 50px 0;

    li {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #F0F0F0;
        margin: 0 5px;
        font-size: 12px;

        a {
            color: $midGray;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }

        &:hover,
        &.active {
            background-color: #000;
            border-color: #000;
            cursor: pointer;

            a {
                color: #fff;
            }
        }
    }
}
.homepage-wrap {margin-bottom:20px;}
.homepage-wrap section.last-home-section hr {background:transparent;}
.homepage-wrap .single-video-item .class-type  {display: none !important;}
.homepage-wrap .container-fluid .row .col-4:nth-of-type(4),
.homepage-wrap .container-fluid .row .col-4:nth-of-type(5),
.homepage-wrap .container-fluid .row .col-4:nth-of-type(6) {display: none !important;}
.homepage-wrap .row-title {padding-top: 67px; padding-bottom: 67px;}
.homepage-wrap .row-title h3 {text-align: left; font-size: 20px !important;}
/*.homepage-wrap .video-text-container .midGray.light {font-weight:400 !important;}*/
.homepage-wrap .ttl-btn {padding-top: 137px; padding-bottom: 140px;}
.homepage-wrap .ttl-btn h2 {font-size: 30px !important; margin-bottom: 3px;}
.homepage-wrap .ttl-btn .btn-54:hover {background: #fff !important; color: #000 !important; border: 1px solid #000;}
.btn-all {font-size: 12px; font-weight: 600; border: 1px solid #dddddd; padding: 0; height: 42px; line-height: 40px; flex: 0 0 104px; text-align: center; border-radius: 50px;} 
.btn-all:hover {background:#000; color:#fff; border-color: #000;} 
.row.three-items {justify-content: space-between;}
.row.three-items .col-4 {width: 32.3%; flex: 32.3%; max-width: 32.3%;}
.btn.btn-54 {height: 54px; padding-left: 30px; padding-right: 30px; letter-spacing: 1.4px !important; border:1px solid #000;}
#support_form .input-container, .payments-wrap .input-container.full-field, .account-form .input-container.full-field, .payments-wrap .code-container, .payments-wrap .panel.flex-row {max-width: 500px;}
#support_form .input-container textarea {height:275px; border-radius: 8px; padding-left:15px;}
.payments-wrap .row.max600 {max-width: 532px;}
.payments-wrap .code-container a.btn {position: absolute; background: none !important; color: #000 !important; right: 20px; font-size: 14px !important;
font-weight: 400 !important; text-transform: initial !important; padding: 0;}
.payments-wrap .code-container a.btn:hover {text-decoration: underline;}
.payments-wrap hr.my-6 {margin-top: 60px !important; margin-bottom: 58px !important;}
.account-save .top-border {margin-top: 28px; padding-top: 58px;}
.mbsec {margin-bottom: 145px !important;}
.support-wrap {margin-bottom: 140px;}
.card-info {display: flex; margin-left: -160px;}
.card-info .input-container {width:50px;}
.card-info .input-container input {border: none !important; background: none;}
.card-info .input-container input:hover {box-shadow: none !important;}
/**/
#accordion {list-style: none; float:left; width: 100%;}
#accordion > li {width: 100%; float: left; border-top:1px solid #EFEFEF;  margin-top:37px; padding-top:50px;}
#accordion > li:last-of-type {border-bottom:1px solid #EFEFEF;  margin-bottom:100px; padding-bottom:45px; }
#accordion li div {display: block; float:left; cursor: pointer;  font-size: 16px; text-transform:uppercase; width:100%;transition: transform .3s;}
#accordion li div.active, #accordion li div.active-acc {width: 100%;}
#accordion li div:after {content:""; background:url(../images/accarrow-down.png); float:right; width:15px; height:15px;background-size: contain; background-position: center;
background-repeat: no-repeat;}
#accordion li div.active:after, #accordion li div.active-acc:after {content:""; background: url(../images/accarrow-up.png); float:right; width:15px; height:15px;background-size: contain;background-position: center;	background-repeat: no-repeat; margin-top: 11px;}
#accordion ul {list-style: none; display: none;	float:left;	width:auto !important;}
#accordion ul li {float:left; width:auto !important; font-size: 16px; line-height:30px; margin-top: 15px; padding-right: 90px; text-align: justify;}
#accordion ul.f-18 li {font-size: 18px; line-height:35px;}
#accordion ul li a {}
.assemblyttl {margin-top: 13px; margin-bottom: 7px;}
#accordion.assembly-acc {margin-bottom: 50px;}
#accordion.assembly-acc ul {margin-top: 23px; margin-bottom: -25px;}
#accordion.assembly-acc div {font-weight:600;}
#accordion.assembly-acc div.active-acc {color:#969696;}
.lfmachines.toplftrgt.assemblyttl {align-items: flex-start !important;}
#accordion.assembly-acc ul li {padding-right: 0; position:relative; width: 31% !important; display: inline-block; float: none; vertical-align: top; margin-bottom: 2.6%;}
#accordion.assembly-acc ul li:nth-child(3n+2) {margin-left:3.5%; margin-right:3.5%;}
#accordion.assembly-acc ul li p {font-size: 12px; font-weight: 600; text-transform: uppercase; margin-top: 26px; text-align:center; line-height: 1.5 !important;}
#accordion.assembly-acc ul li section {position:relative; display:flex; padding: 0 !important; margin: 0 !important; max-width: 100% !important;}
#accordion.assembly-acc ul li section:after {content:""; background:url(../images/greyover.png); position:absolute; left:0; top:0; width:100%; height:100%;}
.playassemb {position: absolute; left: 50%; top: 50%; margin-top:-30px; margin-left:-30px; z-index: 5;}
#accordion.assembly-acc > li {padding-top: 38px;}
.assembly-ttl {padding-top: 146px; padding-bottom: 106px;} 
section.assembly-ttl h1 {font-size: 30px !important; margin:0 !important; line-height: normal !important;} 
.home-teachers {display:flex; flex-direction: row; flex-wrap: wrap; justify-content: space-between;}
.home-teachers.hidemob {border-bottom:1px solid #f0f0f0;}
.hometeacher {width: 11%; text-align: center; position:relative; margin-bottom:75px;}
.hometeacher img {border-radius:50%;}
.hometeacher:hover {opacity:0.7;}
.hometeacher a {position:absolute; width:100%; height:100%; left:0; top:0;}
.hometeacher p {font-size:14px; font-weight:600; text-transform:uppercase; line-height: 1.3; margin-top:35px;}
.swiper-slide .btn {padding-left:30px; padding-right:30px; border:none; border-radius:50px;}
.hearticon {
    border: 1px solid #ddd;
    border-radius: 50%;
    position: absolute;
    right: 0;
    margin-top: -6px;
    top: 0;
    cursor: pointer;
    height: 34px;
    width: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.hearticon .icon {
	width: 16px;
	position: relative;
	top: auto;
	left: auto;
	background: url(../images/heart-icon.svg) no-repeat center center;
	height: 14px;
    display: block;
}
.hearticon.liked.adding .icon::before {
	content: "";
    animation: ripples .65s ease-in-out forwards;
	width: 10px;
	height: 10px;
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 1;
	background: #db1818;
	transform: translate(-50%, -50%) scale(1);
	transform-origin: center center;
	border-radius: 50%;
    opacity: 0.3;
}
@keyframes ripples {
    0% {
        border: 1px solid transparent;
    }
    100% {
        border: 25px solid #db1818;
        opacity: 0
    }
}
.hearticon.liked .icon {
    // animation: pulse 0.5s forward 0s;
    background: url(../images/heart-icon-hover.svg) no-repeat center center;
}
.hearticon:hover .icon {
	background: url(../images/heart-icon-hover.svg) no-repeat center center;
}
.btn.filter_classes, .btn.sortby_list {background:#fdfdfd !important; color:#000 !important; border:1px solid #ddd; border-radius:50px; height:52px; margin-right: 20px; padding-left: 30px; padding-right: 30px;}
.btn.filter_classes {padding-right:26px;}
.sortby_list:after {content:url("/images/arrow-down.svg"); margin-left: 10px;}
.btn.filter_classes:hover, .btn.sortby_list:hover {background:#000 !important; color:#fff !important; border-color:#000;}
.btn.filter_classes:hover img, .btn.sortby_list:hover img {filter: brightness(0) invert(1);}
.sortby_list:hover:after {filter: brightness(0) invert(1);}

.btn.filter_classes img, .btn.sortby_list img {margin-right: 5px;}
.inner-title {padding-top:87px; padding-bottom:83px;}
.sort_by_title {color: #969696;}
.sortby-btn {border: 1px solid #ddd; border-radius: 50px; padding: 0 20px;}
.classby {line-height: 20px; display:block !important;}
.hearticon.loggedhearticon {display: flex; align-items: center; justify-content: center;padding: 0 15px; border-radius: 30px; width: auto;}
.hearticon.loggedhearticon img {position: relative; top: 0; left: 0; margin-right:5px;}
.hearticon.loggedhearticon span {font-size:12px; line-height: normal;}
.hearticon.loggedhearticon .icon {margin-right: 5px;}
h4.medium .hearticon span {font-weight:400 !important;}
.classdesc {background: #f8f8f8;  padding: 23px 30px !important; border-radius: 10px; margin-top:30px;}
.classdesc a.lessText {display: none;}
.classdesc span.secondHalf {display: none;}
.singlettl {margin-top:46px; margin-bottom: 46px;}
.comments-wrap {border:1px solid #f0f0f0; border-radius: 10px;padding: 0;}
.comments-wrap form {padding: 25px 25px 20px;}
.comments-wrap .single-comment-container {padding: 30px 25px 0;}
.logtocomment {display: block; text-align: center; margin-top:25px; padding-top: 27px; margin-bottom: 25px; padding-bottom: 3px; border-top:1px solid #f0f0f0;}
.logtocomment a {font-weight: 600; border: 1px solid #ddd; padding: 10px 15px; border-radius: 50px; letter-spacing: 0.1em; font-size: 10px;}
.logtocomment a:hover {border-color: #000; background:#000; color:#fff;}
.comment-notify {margin-top: 20px;}
.apply_filter {height: 46px; margin-top: 19px;} 
.sortby-btn img {width: 10px; margin-left: 5px;}
.popup-ttl {line-height: normal; margin-top: 27px; padding-left: 30px;}
.hovericon {display:none;}
.class-box-small.liked .hovericon {display:block;}
.class-box-small.added .hovericon {display:block;}
.class-box-small:hover .hovericon {display:block;}
.class-box-small.liked .nohovericon {display:none;}
.class-box-small.added .nohovericon {display:none;}
.class-box-small:hover .nohovericon {display:none;}
.hovericon, .nohovericon {margin-right:10px;}
.heartred {display:none;}
.hearticon:hover .heartred {display:block;}
.hearticon:hover .heartwhite {display:none;}
.body-search {border-radius: 50px; padding: 0; width: 52px; height: 52px; text-align: center; justify-content: end; position: relative;}
.body-search.opened {border: none;}
.body-search .search-form-container {width: 52px; opacity: 1; visibility: visible;transform: none !important; top:0;}
.body-search.opened .search-form-container {width:400px;} 
.body-search .search-form-container input {color:transparent;}
.body-search:not(.opened) ::-webkit-input-placeholder {color: transparent;}
.body-search:not(.opened) :-ms-input-placeholder {color: transparent;}
.body-search:not(.opened) ::placeholder {color: transparent;}
.body-search.opened  .search-form-container input {color:#000;}
.search-form-container .line-input {border-radius:50px; height:50px;}
.body-search .search-form-container .line-input {height:52px;border-color: #ddd !important;}
.search-wrapper.body-search .search-popup .close_search {display: none;}
.share-popup {max-width:400px !important;} 
.cancel_comment {background: none; font-weight: 600; text-transform: uppercase; margin-right: 10px;}
.cancel_comment:hover {color:#000;}
.cancel_comment, .btn.send_comment { font-size: 10px !important; letter-spacing: 0.1em;}
.login-popup .input-container {margin-bottom: 5px;}
.login-popup h2 {margin-bottom: 55px;  line-height:normal;}
.forgot-popup h2 {margin-bottom: 48px;}
.forgot-popup p {margin-bottom: 35px;}
.overlay .popup.login-popup, .overlay .popup.forgot-popup {padding:57px 50px 30px;}
.popup.login-popup .btn {max-height: 42px;}
.cust-supp-popupwrap {padding:0 30px 30px;}
.single-threebtns .class-box-small {flex: 0 0 33%; max-width: 215px;}
.single-threebtns .class-svc {padding-top: 30px; padding-bottom: 30px;}
.overlay .popup.need-help-popup {max-width: 400px;}
.overlay .popup .line-input {height:40px;}
.humanenter {display:flex; justify-content: end;}
.humanenter .line-input {max-height:45px; max-width: 90px; font-size: 12px !important;}
.human-div input {max-width:130px; float:right;}
.markcompleted span {background: #52C15A; color: #fff; height: 30px; border-radius: 30px; padding-left: 15px; padding-right: 15px;}
.markcompleted a span {background: #fff;color: #000;border: 1px solid #ddd;  }
.markcompleted a span:hover {background: $lightGray;}
.markcompleted .mark_text {display: flex; width: 100%; justify-content: center; align-items: center; letter-spacing: 0.1em;}
.markcompleted .mark_text img {margin-right: 10px;}
div.small-text p, div.big-text p {line-height: 25px;}
#overlay .forgot-popup  {max-width: 500px;}
#overlay .forgot-popup p {margin-bottom: 35px;}
.search-wrapper.searchmob {display:none;}
.search-wrapper.searchdesk {z-index: 500;}
.search-wrapper.searchdesk .search-form-container {max-width: 600px; transform: translate(-50%, -50%); left: 50%;}
.search-wrapper.searchdesk .close_search {display:none;}
#overlay .popup.need-help-popup h2 {margin-bottom: 20px !important; padding-top: 30px; padding-left: 30px; font-size: 14px !important; line-height: normal;}
hr.popup-divider {margin-top: 30px !important;margin-bottom: 28px !important;}
.popup .btn {padding: 15px 25px;}
.areyouhuman-box {justify-content: space-between;}
.title-filter {display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center;}
.mob-right-header {display:none;}
.mob-right-header .help-header-button img, .mob-right-header .non-logged-user-icon img {height:20px;}
.inner-title h1 {font-size: 24px !important;}
.clear_filter {
	width: 100%;
	display: block !important;
	border: 1px solid #ddd;
	background: #fdfdfd;
	border-radius: 50px;
	font-size: 12px !important;
	text-transform: uppercase;
	font-weight: 600;
	text-decoration: none !important;
	height: 46px;
	padding: 0 25px;
	display: flex !important;
	align-items: center;
	justify-content: center;
	color: #969696 !important;
}
.clear_filter:hover {background:#000; border-color: #000; color:#fff !important;}
.search-wrapper.body-search a.search-popup {border:none !important; position: absolute; width: 100%; height: 100%;}
.search-wrapper.body-search a.search-popup img {opacity:0;}
.main-course-desc {font-size: 14px; line-height:25px;}
.course-ttil {margin-top:45px; margin-bottom: 45px;}
.single-title-wrap {margin-top: 45px; margin-bottom: 45px;}
.media-desc {font-size: 14px; line-height: 1.4;}
.bdown-popup, .equipment-popup {display: none; position: fixed; width: 100%; height: 100%; left: 0; top: 0; background: rgba(0, 0, 0, 0.3); z-index: 999999;}
.terminology-popup, .bodypos-popup {display: none; position: fixed; width: 100%; height: 100%; left: 0; top: 0; background: rgba(0, 0, 0, 0.3); z-index: 999999;}
.terminology-content {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	padding: 30px 30px 20px;
    gap: 20px 10px;
    
    .terminology-item {
        text-align: center;

        .terminology-image {
            width: 140px;
            height: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border: 1px solid #F0F0F0;
            border-radius: 6px;
        }
    }
}
.bodypos-content {width: 90%; display: block; padding-top: 60px; padding-bottom: 60px; margin: 0 auto;}
.bodypos-content img {width: 100%; max-width: 400px; margin:0 auto;}
.class-bdown-wrap {background: #fff; width: 90%; max-width: 500px; padding-bottom: 17px; position: relative; left: 50%; transform: translate(-50%, -50%); top: 50%;}
.class-bdown-wrap.max400 {max-width: 400px;}
.class-bdown-wrap h3 {border-bottom: 1px solid #f0f0f0; padding-top: 28px; padding-bottom: 28px; margin-bottom: 15px; line-height: normal;} 
.close_bdown {position: absolute; top: 0; right: 0; color: #F0F0F0; line-height: 30px; font-size: 40px; padding: 30px; cursor: pointer;}
.close_bdown img:hover {filter: invert(1);}
.bdown-content {display: flex; padding: 0 30px 0; justify-content: space-between; max-height: 300px; overflow-y: auto;}
.bdown-content p {line-height: 30px;}
.bdown-name {font-size: 12px; }
.bdown-percentage {font-size: 12px; font-weight: 500;}
.title-filter .btn.greyborder {line-height:1; padding: 20px 30px;}
.watch-hist-wrap .wlater-box {margin-bottom: 40px;}
.watch-hist-wrap .wlater-box .video-container {margin-right: 30px; flex: 0 0 210px; height:118px; }
.watch-hist-wrap .wlater-box .video-container .image-overlay {border-radius: 6px;}
.watch-hist-section .pagination {border: 1px solid #f0f0f0; border-radius: 10px; padding: 35px 0;}
.watch-hist-section .pagination li {border-radius: 50%;}
.watchhistory-ttl {padding-top: 18px; padding-bottom:18px; margin-bottom:40px;}
.menu-button img.menucloseicon {display:none;}
.mid-nav, .mobmenu-sep {display:none;}
.mid-nav a {font-weight: 600; margin-left: 17.5px; margin-right: 17.5px;font-size: 12px; color: #000; letter-spacing: 0.1em;}
.right-menu a:hover, header .search-wrapper a.search-popup:hover {opacity:0.5;}
.cs-video .class-body-parts .mark-video {width: 100%;}
.watchlater-wrap section {max-width: 1130px !important; margin:0 auto;}
.w-hist-likes {flex:0 0 auto; justify-content: end;padding-left: 15px;}
.search-wrapper.body-search a.search-popup {display:block !important;}
.title-filter .landing-options .landing-filter, .title-filter .landing-options .body-search {position: relative;}
.search-type-dropdown {
	min-width: 80px;
    position: absolute;
    top: 15px;
    right: 50px;
    font-size: 10px;
    min-height: 20px;
    border: 1px solid #DDDDDD;
    line-height: 1;
    border-radius: 12px;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;

    .current-type-selected {
        position: relative;
        display: block;
        width: 100%;
        height: 20px;
        font-size: 10px;
        text-align: left;
        padding-left: 10px;
        line-height: 20px;
        color: #969696;
    }
    .search-type-dropdown-menu {
        position: relative;
        display: block;
        width: 100%;
        height: 0;
        padding: 0px;
        overflow: hidden;
    }
    &.opened > .search-type-dropdown-menu {
        height: 60px;
        padding: 10px;
    }
    .search-type-item {
        line-height: 1.3;
        text-align: left;
        margin-bottom: 10px;
        text-transform: uppercase;
        cursor: pointer;
        color: #969696;

        &:hover {
            color: #000;
        }
    }
    &::before {
        content: "";
        position: absolute;
        top: 7px;
        right: 9px;
        border: 3px solid rgba(0,0,0,0);
        border-top: 5px solid #969696;
    }
}
.search-options {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;

    .search-type-item {
        border-radius: 10px;
        height: 20px;
        width: 100%;
        border: 1px solid #F0F0F0;
        font-size: 10px;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        color: #969696;
        text-transform: uppercase;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        &.selected {
            border: 1px solid #000;
            background: #000;
            color: #fff;
        }
    }
}

.lghub {border:1px solid #272727; border-radius:50px; display: flex; padding: 10px 26px;align-items: center;}
.footcol-one .foomenulist.bababa {display:none !important;}
.big-gap.teachers-wrap [class*="col-"] {padding-left: 30px; padding-right: 30px; margin-bottom: 60px;}
.forgot-popup .forgot-form .row-vertical {margin-top:-10px;}

/*new account look*/
section.account-content {max-width:1330px; margin: 0 auto !important; padding-bottom: 150px !important;}
.ask-wrapper {padding-bottom:140px;}
section.account-content .btn {height:42px;}
.account-main-title {padding-top: 49px; padding-bottom: 45px;}
.account-btn {display:none;}
.accountmenu {opacity: 1; visibility: visible; width: 100% !important; padding: 50px 0 !important;}
.accountmenu li {display:block; margin-bottom:0 !important;}
.accountmenu li:last-of-type {margin-bottom:0 !important;}
.accountmenu li a {font-size:14px !important; line-height:normal;}  
.accountmenu li a.active {font-weight:600 !important; color: #000;}  
.accountmenu li {margin-bottom: 25px !important;}  
.account-page .dashboard-panel-item:last-of-type {margin-bottom: 0;}
.account-content .lodacc-menu {flex:0 0 130px;}  
section.account-content .container1100 {width:100%;}
.account-hero {max-width: 1040px; width: 100%; margin: 0 auto;}
.account-subttl {margin-top:47px; margin-bottom:47px;} 
section.account-content .acc-logout {color:#969696;}
section.account-content .payment-choose {border:1px solid #f0f0f0; padding:30px 40px 40px;}
section.account-content #change_subscription a.cancel-subsc {text-transform: initial !important; font-weight: 400 !important; float: right; margin-top: 45px; letter-spacing:0.05em !important;}
section.account-content #change_subscription a.cancel-subsc:hover {text-decoration:underline;}
section.account-content form#change_subscription {max-width:500px;}
section.account-content .subscription-option label {padding: 20px 30px 19px 25px !important; margin-right: 25px !important;}
.payments-wrap .panel.panel-cc {padding: 15px 25px !important; border-radius:8px; justify-content: space-between; margin-bottom: 15px;}  
.payments-wrap .panel.panel-cc:last-of-type {margin-bottom: 0;}
.notify-chekcbox {padding: 35px 30px;} 
#add-card-month, #add-card-year, #add-card-cvc {text-align:center; width:50px; padding-left: 2px;}
.account-content .image_preview.no-img {width:14px !important;}
.black-banner.flex.aic.jcc.flex-column {height:450px;} 
.black-banner.flex.aic.jcc.flex-column h1 {font-size:36px !important;} 
.account-showpass .reveal_password {margin-top:-7px; margin-right:15px;}
.contact-form textarea.line-input {padding-left:15px;}
.exer-popup .video-container {border-radius: 4px;}
.payments-wrapper .panel {border-radius: 8px;}
.pay-status .green, .pay-status .red {color:#fff !important; border-radius:50px; padding:10px 15px; font-size:12px; font-weight:500;}
.pay-status .green {background:#52C15A;}
.pay-status .red {background:#DB1818;}
.cancel-subs-txt {padding-left: 40px; padding-right: 40px;}
.sortbyimg {display: none;}
.newplaylist-btn {margin-top:-32px;}

.subsc-delete-buttons {display: flex; justify-content: center;}
.delete-subsc {height:42px; background: #000; color:#fff;margin-right: 15px;}
.goback-subsc {height:42px; background: #fff; color:#969696; border-color: #ddd;}
.delete-subsc:hover {background: #fff; color:#000;}
.goback-subsc:hover {background: #000; color:#fff; border-color: #000;}

::part(acsb-trigger) {
    bottom: 90px !important;
    right: 28px !important;
    z-index: 9999;
}
.overlay .add-to-playlist-popup .popup-header {
	padding: 20px 0;
}
.homecourses .thumb-info {display:none;}
.calendar-top {
	background-color: #000;
	display: flex;
	width: 100%;
	height: 90px;
	flex-wrap: wrap;
	justify-content: center;
}


/* RESPONSIVE mobile i ostaloo */
@media (max-width: 1920px) {
section:not(.classesroot), section.px-100, section.p-10 {max-width: 1780px; padding-left: 65px !important; padding-right: 65px !important; margin: 0 auto;} 
.container1030 {padding-left: 80px; padding-right: 80px;}
section.account-content {max-width:1170px;}

}


@include media(">small_desktop", "<desktop"){
    body {
        padding: 0;
    }
    // h1, .h1 { font-weight: normal; font-size: $fontSize * 3.2 !important; letter-spacing: 0.05em; line-height: 1.15; }
    // h2, .h2-big { font-weight: normal; font-size: $fontSize * 2.4 !important; letter-spacing: 0.05em; line-height: 1.15; }
    // h2, .h2 { font-weight: normal; font-size: $fontSize * 1.8 !important; letter-spacing: 0.05em; line-height: 1.15; }
    // h3, .h3 { font-weight: normal; font-size: $fontSize * 1.25 !important; }
    // h4, .h4 { font-weight: normal; font-size: $fontSize * 1.1 !important; }
    // h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
    // h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }
    section {
        padding: 120px;
    }
    .pr-150 { padding-right: 100px !important; }
    .pl-150 { padding-left: 100px !important; }
    .col-20 {
        padding-left: 15px;
        padding-right: 15px;
        width: 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }
}
@include media(">laptop", "<small_desktop"){
    body {
        padding: 0;
    }
    h1, .h1 { font-weight: normal; font-size: $fontSize * 3.2 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2-big { font-weight: normal; font-size: $fontSize * 2.4 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2 { font-weight: normal; font-size: $fontSize * 1.8 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h3, .h3 { font-weight: normal; font-size: $fontSize * 1.25 !important; }
    h4, .h4 { font-weight: normal; font-size: $fontSize * 1.1 !important; }
    h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
    h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }
    section {
        padding: 80px;
    }
    .pr-150 { padding-right: 75px !important; }
    .pl-150 { padding-left: 75px !important; }
    .col-20 {
        padding-left: 15px;
        padding-right: 15px;
        width: 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }
    header {
        padding: 0 20px;
    }
 
}
@include media(">tablet", "<laptop"){
    h1, .h1 { font-weight: normal; font-size: $fontSize * 2.6 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2-big { font-weight: normal; font-size: $fontSize * 2.2 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2 { font-weight: normal; font-size: $fontSize * 1.6 !important; letter-spacing: 0.05em; line-height: 1.15; }
    h3, .h3 { font-weight: normal; font-size: $fontSize * 1.25 !important; }
    h4, .h4 { font-weight: normal; font-size: $fontSize * 1.1 !important; }
    h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
    h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }
    body {
        padding: 0;
    }
    .position-relative-desktop {
        position: inherit !important;
    }
    .search-wrapper.searchdesk, .search-wrapper a.search-popup {display: none;}
    header .right-menu a.search-popup {
        display: none !important;
    }
    .max500 {
        max-width: 400px;
        min-width: 400px;
        width: 100%;
    }
    .px-120 {
        padding-left: 50px !important;
        padding-right: 50px !important;
    }
    .hero-container h1 {
        font-size: 30px !important;
        line-height: 30px;
    }
    .hero-container h3 {
        color: #fff;
        font-size: 16px !important;
    }
    header {
        padding: 0 20px;
    }

    section {
        padding: 80px 35px;
        position: relative;
    }
    
    header .menu-button rect {
        fill: #000;
    }
    body header .menu-button {
        margin-right: 0;
    }
    header a svg {
        display: block;
        
    }
    .pl-150 { padding-left: 50px !important; }
    .pr-150 { padding-right: 50px !important; }
    .pr-120 { padding-right: 50px !important; }
    .col-20 {
        padding-left: 15px;
        padding-right: 15px;
        width: 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }
    .search-wrapper.searchdesk {display:none;}
    .search-wrapper.searchmob {display:block;}
    


}
 
@media (max-width: 1300px) {
    footer .container {padding-left:40px; padding-right:40px;}
    header {padding: 0 30px;}
    .hometeacher {width: 14%;}
    .homepage-wrap .ttl-btn h2 {margin-bottom: 8px;}
    body.menu-opened .left-menu a {margin-left: 0;}
    /*mobile menu*/
    header .menu-button {display: flex !important; align-items:center;}
    header .left-menu {
        position: fixed;
        top: 90px;
        right: 0;
        z-index: -1;
        padding: 0 20px 40px;
        background: #ffffff;
        width: 100%;
        transition: all 0.25s ease-in-out 0s;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        overflow-y: auto;
        display: block;
        max-width:550px;

        a {
            color: #000;
            display: block !important;
            font-weight: 500 !important;
            margin: 0 0 25px 0;
            transform: translateY(-20px);
            opacity: 0;
            line-height: 1;
            animation-delay: calc(50ms * var(--animated_menu)) !important;
            font-size: 16px;
            letter-spacing: 0.05em;
            text-transform: initial;
        }
        .mainlink {
            transform: translateY(-20px);
            opacity: 0;
            animation-delay: calc(50ms * var(--animated_menu)) !important;
        }
    }
    header:not(.logged) .left-menu {padding-top: 30px;}

    body.menu-opened {
        .left-menu {
            visibility: visible;
            pointer-events: auto;
            opacity: 1;

            a{
                animation: slide-in 0.4s cubic-bezier(0.2, 0, 0.1, 1) forwards;
            }
            .mainlink {
                animation: slide-in 0.4s cubic-bezier(0.2, 0, 0.1, 1) forwards;
                height: auto;
                margin-bottom: 22px;
            }
        }
        .white-logo { display: none !important; }
        .black-logo { display: flex !important; }
    }
    @-webkit-keyframes slide-in {
        from {-webkit-transform: translateY(-30px);transform: translateY(-30px);opacity: 0;}
        to {-webkit-transform: translateY(0px);transform: translateY(0px);opacity: 1;}
    }

    @keyframes slide-in {
        from {-webkit-transform: translateY(-30px);transform: translateY(-30px);opacity: 0;}
        to {-webkit-transform: translateY(0px);transform: translateY(0px);opacity: 1;}
    }
    .js-arrow.navarrow.shop-nav + .desktopvanplus {display: flex;}
 
    header .left-menu .sub-menu {
        padding: 15px 0 0;
        position: relative;
        top: auto;
        left: auto;
        display: none;
        width: 100% !important;
        visibility: visible;
        opacity: 1;
        }
    header .left-menu .sub-menu a {
        margin-bottom: 0;
        line-height: 35px;
        font-size: 14px;

    }
    header .left-menu .sub-menu a:hover {color:#969696;}
    header .mainlink.subnav > div {
        width: 100%;
        height: 20px;
    }
    .left-menu .search-wrapper {
        border-bottom: 1px solid #f0f0f0 !important;
        padding-top: 25px;
        padding-bottom: 10px;
        display: block;
        margin-bottom: 30px;
        width: 100%;
        height: auto;
    }
    .sub-menu .subttl {
        display: none;
    }
    .search-wrapper.searchdesk, .search-wrapper a.search-popup {display:none !important;}
.left-menu .search-wrapper .search-form-container {
    position: relative;
    top: auto;
    height: 100%;
    width: 100% !important;
    right: auto !important;
    z-index: 20;
    opacity: 1;
    pointer-events: all;
    transform: translateY(0) !important;
    visibility: visible;
    transition: all 0.5s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
}

header .right-menu a.search-popup  {display: none !important;}
body.scrolled header .left-menu {top: 70px;}
.mid-nav {display:flex; margin:auto;}
.right-menu.logged-in a.watchlater {margin-left: 0;}
.left-menu .sbonmob {border-top:1px solid #f0f0f0; padding-top: 28px; margin-top: 30px;}
header .mainlink.subnav {flex-direction: column;}
.big-gap.teachers-wrap [class*="col-"] {padding-left: 15px; padding-right: 15px; margin-bottom:30px;}
.teacher-panel h3 {margin-top: 30px;}
.teacher-panel .initials {font-size: 46px;}
body:not(.menu-opened) .left-menu {display:none;}
}
@media (max-width: 992px) {
  .footer-top-row {
    padding-top:50px;
    padding-bottom:40px;
  }
  .foo-first, .foo-selling, .foo-bottom .col-6 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    justify-content: center !important;
  }
  .foo-bottom.py-120 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
    margin-top: 40px;
  }
  .foo-selling {
    border-top: 1px solid #1e1e1e;
    padding-top: 35px;
    margin-top: 35px;
  }
  .foo-hub {
    border-bottom: 1px solid #1e1e1e;
    padding-bottom: 40px;
    margin-bottom: 30px;
  }
  .foo-nav {
    padding-top: 55px;
  }
  .foo-second {
    margin-top: 30px;
    padding-bottom: 40px;
    padding-top: 40px;
    margin-bottom: 40px;
  }
  .foo-second .footbtm-left {
    margin-bottom: 0;
  }
  .foo-second .footbtm-right {
    border-bottom: 1px solid #272727;
    padding-bottom: 40px;
  }
  .foo-second {
    flex-direction: column-reverse;
  }
  .footbtm-left, .footbtm-right {
    width: 100% !important;
    justify-content: center;
    text-align: center;
  }
  .foot-lftrgt.foo-second {
    padding-bottom: 0;
    margin-bottom: 0;
}
.hosttybox {
	padding-top: 200px;
	padding-bottom: 100px;
}
.hosttybox p {
	margin-top: 15px;
	margin-bottom: 30px;
}
.help-contact {padding-top: 85px; padding-bottom: 90px;}
.footbtm-left {padding-top: 30px;}
.video-container {margin-bottom: 25px;}
.duration {bottom: 17px; left: 20px;}
.locked {bottom: 20px; right: 20px;}
/*mobile menu*/
 header .left-menu {max-width: 100%;}
.menu-button.active img.menucloseicon {display:block; width: 20px;}
.menu-button.active img.menuicon {display:none;}
.mid-nav a {margin-left: 10px; margin-right: 10px;}
/*end mobile menu*/
.single-teacher-item {
	padding-top: 40px;
	padding-bottom: 27px;
}
.playlist-main-title {
    padding-top: 67px;
  padding-bottom: 69px;
}
.row.products_list {
    flex-direction: row !important;

    .col-4 {
        width: 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
}
}


 .reverseonmob {
  display:flex;
  flex-direction: column-reverse;
 }
 .foot-lftrgt.foo-second {
  margin-bottom: 0;
}



@include media("<tablet"){
    h1, .h1 { font-weight: normal; font-size: 30px !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2-big { font-weight: normal; font-size: 26px !important; letter-spacing: 0.05em; line-height: 1.15; }
    h2, .h2 { font-weight: normal; font-size: 22px !important; letter-spacing: 0.05em; line-height: 1.15; }
    h3, .h3 { font-weight: normal; font-size: $fontSize * 1.25 !important; }
    h4, .h4 { font-weight: normal; font-size: $fontSize * 1 !important; }
    h5, .h5 { font-weight: normal; font-size: $fontSize !important; }
    h6, .h6 { font-weight: normal; font-size: $fontSize * 0.875 !important; }

    body .mobile { display: block !important; }
    body .mobile-flex { display: flex !important; }
    body .mobile-inline { display: inline-block !important; }
    body .desktop { display: none !important; }
    body .desktop-flex { display: none !important; }
    body .desktop-inline { display: none !important; }
    .position-relative-desktop {
        position: inherit !important;
    }
    
    .max500 {
        max-width: 100%;
        min-width: 0;
        width: 100%;
    }
    .with-comma span {
        margin-right: 0;
    }
    .announcement_bar {
        color: #fff;
        text-align: center;
        font-size: 11px;
        font-weight: 500;
        position: relative;
        z-index: 12;
        padding-right: 30px;
        line-height: 15px;
        padding: 10px 30px 10px 10px;
    }
    .close_announcement {
        position: absolute;
        top: 50%;
        right: -5px;
        padding: 0 20px;
        font-size: 20px;
        z-index: 9;
        cursor: pointer;
        transform: translateY(-50%);
    }
    .featured-row .col-4:last-of-type .single-video-item {
        margin-bottom: 0 !important;
    }
    section.p-10 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
      }
    .row {
        margin-right: -10px;
        margin-left: -10px;
    }
    .offset-1, .offset-2, .offset-3, .offset-4, .offset-5, .offset-6 { margin-left: 0% !important; }
    .btn.btn-60 {
        height: 50px;
    }
    body .f-10-mob {font-size: 10px !important;}
    body .f-12-mob, .btn.f-12-mob {font-size: 12px !important;}
    body .f-14-mob {font-size: 14px !important;}
    .f-18 {font-size: 15px !important;}
    .mb-3 {margin-bottom: 18px !important;}
    .mb-4 {margin-bottom: 25px !important;}
    .mb-5 {margin-bottom: 30px !important;}
    .mb-6 { margin-bottom: 30px !important; }
    .col-6.pl-0 { padding-left: 10px !important; }
    .ml-mob-0 {margin-left: 0px !important;}
    .ml-mob-1 {margin-left: 10px !important;}
    .mt-mob-0 {margin-top: 0px !important;}
    .mt-mob-05 {margin-top: 5px !important;}
    .mt-mob-1 {margin-top: 10px !important;}
    .mt-mob-2 {margin-top: 20px !important;}
    .mt-mob-3 {margin-top: 30px !important;}
    .mt-mob-4 {margin-top: 40px !important;}
    .mt-mob-5 {margin-top: 50px !important;}
    body .mt-mob-75 {margin-top: 75px !important;}
    .mb-mob-0 {margin-bottom: 0px !important;}
    .mb-mob-05 {margin-bottom: 5px !important;}
    .mb-mob-1 {margin-bottom: 10px !important;}
    .mb-mob-15 {margin-bottom: 15px !important;}
    .mb-mob-2 {margin-bottom: 20px !important;}
    .mb-mob-3 {margin-bottom: 30px !important;}
    .mb-mob-4 {margin-bottom: 40px !important;}
    .mb-mob-5 {margin-bottom: 50px !important;}
    .mb-mob-6 {margin-bottom: 60px !important;}
    .mr-mob-1 {margin-right: 10px !important;}
    .mr-mob-3 {margin-right: 30px !important;}
    .mr-mob-4 {margin-right: 40px !important;}
    body .mb-mob-75 {margin-bottom: 75px !important;}
    .pb-mob-5 {padding-bottom: 50px;}
    body .p-mob-0 {padding: 0px !important;}
    body .p-mob-2 {padding: 20px !important;}
    .pt-8 {padding-top: 50px !important;}
    .pt-100 {padding-top: 30px !important;}
    .pb-mob-4 {padding-bottom: 40px !important;}
    .px-mob-1 {padding-left: 10px !important;padding-right: 10px !important;}
    .px-mob-2 {padding-left: 20px !important;padding-right: 20px !important;}
    .px-mob-3 {padding-left: 25px !important;padding-right: 25px !important;}
    body .pt-mob-0 {padding-top: 0px !important;}
    .pt-mob-2 {padding-top: 20px !important;}
    body .pt-mob-4 {padding-top: 40px !important;}
    .pb-mob-2 {padding-bottom: 20px !important;}
    .pb-mob-3 {padding-bottom: 30px !important;}
    .pr-mob-1 {padding-right: 10px !important;}
    .pr-mob-15 {padding-right: 15px !important;}
    .pr-mob-0 {padding-right: 0px !important;}
    body .pb-mob-75 {padding-bottom: 75px !important;}
    .mb-100 {margin-bottom: 30px !important;}
    .mb-150 {margin-bottom: 75px !important;}
    .my-mob-5 {margin: 50px 0 !important;}
    body .my-mob-4 {margin: 40px 0 !important;}
    .my-mob-3 {margin: 30px 0 !important;}
    .py-6 {padding-top: 30px !important;padding-bottom: 30px !important;}
    .py-8 {padding-top: 50px !important;padding-bottom: 50px !important;}
    .pb-100 {padding-bottom: 60px !important;}
    body .px-120 { padding-left: 10px !important; padding-right: 10px !important;}
    body .py-mob-75 {padding-top: 75px !important; padding-bottom: 75px !important;}
    body .py-mob-3 {padding-top: 30px !important; padding-bottom: 30px !important;}
    body .px-mob-15 {padding-right: 15px !important; padding-left: 15px !important;}
    body .pt-mob-1 {padding-top: 10px !important;}
    /*body .px-mob-2 {padding-left: 15px !important; padding-right: 15px !important;}*/
    body .px-mob-0 {padding-right: 0px !important; padding-left: 0px !important;}
    .mr-mob-0 {margin-right: 0px !important;}
    .mb-mob-0 {margin-bottom: 0 !important;}
    .pb-mob-0 {padding-bottom: 0px !important;}
    .px-mob-0 {padding-left: 0px !important;padding-right: 0px !important;}
    .jcc-mob { justify-content: center !important;}
    .aic-mob { align-items: center !important; }

    .lightGray-bg.px-0.pt-mob-2 {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
    .w100-mob {
        width: 100%;
    }
    .mobile-flex-vertical {
        flex-direction: column;
        justify-content: flex-start;
        width: 100%;
    }
    .p-5.border.mb-4.w100.dashboard-panel.p-mob-2.px-mob-0 {
        border-left: none !important;
        border-right: none !important;
        padding-top: 40px !important;
        padding-bottom: 35px !important;
        margin-bottom: 0 !important;
    }
    .p-5.border.mb-4.w100.dashboard-panel.p-mob-2.px-mob-0.next-panel {
        border-left: none !important;
        border-right: none !important;
        padding-top: 40px !important;
        padding-bottom: 40px !important;
        border-top: none !important;
    }
    .my-100.mb-mob-5 {
        margin-bottom: 50px !important;
    }
    .pt-0.py-mob-75 {
        padding-top: 0 !important;
    }
    body.scrolled header {
        height: 60px;
    }
    body .mob-middle-img {
        width: 90%;
        margin: 0 auto 30px;
        max-width: 280px;
    }
    body .h3-mob {font-size: 18px !important;}
    .available-on-mob {max-width: 70%;margin: 0 auto}
    body main {
        position: relative;
        padding-top: 70px;
    }
    .px-100 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    .big-gap [class*="col-"] {
        padding-left: 10px;
        padding-right: 10px;
    }
    .big-big-gap [class*="col-"] {
        padding-left: 10px;
        padding-right: 10px;
    }
    h3, .h3 {
        font-size: 16px !important;
    }
    p {
        line-height: 25px;
    }
    body {
        padding: 0;
        overflow-x: hidden;
        font-size: 15px;
        line-height: 25px;
    }
    .non-logged-user-icon, .menu-button {
        height: 22px;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    .col-9.flex.aic.jcl {
        flex-direction: column;
    }
    .footer-hub-items {
        display: none;

        .col-3 {
            .footer-hub-item {
                justify-content: flex-start;
                border-left: none;
                padding-left: 30px;
                border-top: 1px solid #F0F0F0;

                &.selected,
                &:hover {
                    position: relative;
                    z-index: 1;
                    background: #fff;
                    box-shadow: 0 0 0 0px #F0F0F0;
                    border-left: none;
                    border-top: 1px solid #F0F0F0;
                }
            }
            &:hover {
                position: relative;
                z-index: 1;
            }
            &:last-child .footer-hub-item{
                border-right: none;

                &:hover {
                    border-top: 1px solid #F0F0F0;
                }
            }
        }
    }
    .footer-hub-title {
        height: 100px;
        display: flex;
        align-items: center;
        padding: 30px;
        font-weight: 600;
        justify-content: center;
    }
    .with-line::before {
        content: "";
        width: 40px;
        height: 2px;
        background: $red;
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-20px);
    }
    .swiper.homepage-collection {
        padding-bottom: 100px;
    }
    .my-100 {
        margin-top: 30px !important;
        margin-bottom: 30px !important;
    }
    .row.my-100.big-gap .col-6 {
        margin-bottom: 30px;
    }
    .mob-f-18 {
        font-size: 18px !important;
    }
    hr.my-100 {
        margin-top: 45px !important;
        margin-bottom: 45px !important;
    }
    footer {
        text-align: left;
        // padding-bottom: 40px;
    }
    footer.pt-100 {
        padding-top: 75px !important;
    }
    footer ul:last-of-type {
        margin-bottom: 30px !important;
    }
    footer ul li a {
        // font-size: 12px;
        color: #aaa;
    }
    .footer-menu-title {
        margin-bottom: 15px;
    }
    footer .flex.aic.jcr {
        justify-content: center !important;
        margin-top: 10px;
    }
    .footer-logo.mr-3.mt-mob-3 {
        margin-right: 0 !important;
        width: 180px;
        margin-bottom: 30px;
        margin-top: 40px;
    }
    .check-code-buttom {
        position: absolute;
        top: 0px;
        right: 0;
        font-size: 14px;
        height: 40px;
        line-height: 40px;
    }
    .sub-check.check-code-buttom {top: 29px !important;}
    #register_subscribe .reveal_password {top: 40px;}

    .order-mob-1 { order: 1}
    .order-mob-2 { order: 2}
    .order-mob-3 { order: 3}

    .homepage-sponsors-pagination {
        bottom: 30px !important;
    }
    .swiper.homepage-sponsors {
        padding-bottom: 40px;
    }
    .img-panel-content {
        padding: 20px;
    }
    .homepage-collection .swiper-slide {
        height: auto;
    }
    .py-100 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }
    .light.text-center.flex.aic.jcc.px-100 {
        padding: 0 !important;
        flex-direction: column !important;
        width: 100%;
    }
    .mt-mob-4 {
        margin-top: 40px;
    }
    .homepage-teachers .img-fluid, .homepage-collection .img-fluid {
        height: 100%;
    }
    .homepage-teachers .img-panel-content {
        padding-bottom: 40px;
    }
    .swiper.homepage-teachers .swiper-pagination.homepage-teachers-pagination {
        bottom: 10px;
    }
    .homepage section {
        text-align: center;
    }
    section {
        padding: 50px 10px;
        position: relative;
        overflow-x: hidden;
    }
    .row {
        flex-direction: column;
    }
    .avatar {
        width: 35px;
        height: 35px;
        min-width: 35px;
        min-height: 35px;
    }
    .pr-150 { padding-right: 10px !important; }
    .pl-150 { padding-left: 10px !important; }
    header .menu-button {
        margin: 0;
        padding-left: 0;
    }
    .favs {
        margin-right: 25px;
    }
    body header a svg path,
    body header a svg rect {
        color: #000 !important;
        fill: #000 !important;
    }
    body.transparent header a svg path,
    body.transparent header a svg rect {
        color: #fff !important;
        fill: #fff !important;
    }
    header {
        padding: 0 20px;
        height: 70px;
    }
    body header .white-logo {
        margin: 0;
    }
    .my-35 {
        margin-top: 35px;
        margin-bottom: 35px;
    }
    a.btn.btn-badge.btn-border.ml-2.favs_icon {
        width: 50px;
        margin: 0 !important;
        min-height: 50px;
        border-radius: 50% !important;
    }
    .border-box-cell .line-height-small {
        font-size: 16px;
    }
    .border-box .border-box-cell {
        line-height: 1.3;
    }
    .mob-devices-single-class {
        width: 200px;
    }

    
    
    .hero-container{
        padding: 0px 40px 0 40px;

        h1 {
            font-size: 30px !important;
            line-height: 1.2;
            margin-bottom:7px;
        }
        h3 {
            font-size: 18px !important;
            line-height: 1.5;
        }
    }
    .btn.btn-tall {
        height: 50px;
        font-size: 14px;
    }
    .btn.btn-wide {
        padding: 0 40px;
    }
    .hero-badge {
        position: absolute;
        bottom: auto;
        right: 25px;
        top: 25px;
        z-index: 10;
        width: 50px;
        height: 50px;
    }
    hr {
        margin: 75px 0;

    }
    .overlay {
        overflow: hidden;
        overflow-y: auto;
    }
    .overlay .popup {
        padding: 50px 30px;
        height: 100vh;
        overflow-y: auto;
    }
    .login-popup hr {
        margin-top: 35px !important;
        margin-bottom: 25px !important;
    }
    .line-input {
        padding-right: 0;
    }
    #card-month-year {
        text-align: center;
    }
    #card-cvc {
        text-align: center !important;
    }
    .btn {
        min-height: 44px;
        padding: 0 25px;
        font-size: 12px !important;
    }
    .input-container {
        margin-bottom: 18px;
    }
    .classes-page .image-overlay img {
        height: 250px;
    }
    .classes-page .video-container .image-overlay img {
        height: 200px;
    }
    .video-container .play-button {
        width: 80px;
        height: 80px;
        // border: 1px solid #fff;
    }
    .video-container .play-button span {
        border: 15px solid rgba(0, 0, 0, 0);
        border-left: 22px solid #fff;
        border-radius: 3px;
    }
    .single-video-item p {
        font-size: 14px !important;
    }
    .single-video-item p.f-12 {
      font-size: 12px !important;
    }
    img[src*="large-play.png"], img[src*="large-play-over.png"] {
        width: 50px !important;
        height: 50px !important;
        margin: 10px 0 0 25px !important;
    }
    .border-box {
        width: 31%;
        margin: 0 2%;
        height: 190px;
        padding: 10px;
        max-width: 120px;

        &:first-child {
            margin-left: 0 !important;
        }
        &:last-child {
            margin-right: 0 !important;
        }
    }
    .col.flex.p-0.ml-auto {
        margin: 30px 0 0 0;
    }
    .single-featured-collection {
        margin-bottom: 20px;
    }
    .single-video-item .image-overlay img {
        height: auto;
        min-height: 100px;
    }
    .msg-popup {
        top: auto;
        bottom: 0px;
        max-width: 90vw;

        &.showw {
            top: 30px;
            bottom: auto;
            padding: 30px 20px;
        }
        p{
            text-align: center;
            font-size: 13px;
        }
    }
    .col-mob-6 {
        width: 50%;
        flex:0 0 50%;
        max-width: 50%;
    }
    .filters {
        flex-direction: column;
        width: 90%;
    }
    .custom-selectbox-holder {
        height: 46px;
    }
    .select2-container {
        width: 100% !important;
    }
    .custom-select {
        margin-right: 0;
        margin-top: 10px;
    }
    .filters-switch {
        font-size: 14px;
    }
    .sort_by_title {
        white-space: nowrap;
    }
    .featured-pagination {
        bottom: auto !important;
        top: 20px !important;
        left: auto !important;
        right: 20px !important;
        width: auto !important;
        position: absolute;
        z-index: 1;
    }
    .swiper.featured-collection .swiper-pagination-bullets.featured-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
        background: #fff !important;
    }
    .swiper.featured-collection .swiper-pagination-bullets.featured-pagination .swiper-pagination-bullet {
        background: rgba(255, 255, 255, 0.3) !important;
        opacity: 1 !important;
    }
    .footer-image {
        max-width: 80%;
        margin: 0 10% !important;
    }
    .pr-75 {
        padding-right: 10px !important;
    }
    #register_subscribe {
        margin: 0 -20px;
    }
    .panel {
        padding: 25px 15px;
    }
    .f-18 [type="radio"]:not(:checked) + label, [type="radio"]:checked + label {
        font-size: 15px;
    }
    .col-6.pr-75 {
        padding-left: 10px !important;
        padding-right: 10px!important;
    }
    .f-10 {
        font-size: 10px !important;
    }
    .panel.big-padding {
        padding: 40px 20px;
    }
    .btn.btn-sm {
        min-height: 28px;
        padding: 0 15px;
        line-height: 1;
    }
    .homepage-teachers .swiper-slide a, .homepage-teachers .swiper-slide .image-overlay {
        height: 100%;
    }
    .homepage-teachers .swiper-slide {
        height: 220px;
    }
    .single-collection-desc {
        margin-top: 20px;
    }
    .single-collection-item {
        margin-bottom: 20px;
    }
    body .small-header-image {
        min-height: 40vh !important;
        height: 60vh !important;
        padding: 0 !important;
    }
    body .small-header-image .image-overlay img {
        min-height: 100% !important;
        max-height: 60vh !important;
    }
    body .border-boxes {
        margin-left: auto !important;
        margin-top: 0 !important;
        padding: 0 10px !important;
        width: 100%;
        margin-right: auto !important;
        justify-content: center;
    }
    .video-rate {
        width: 50px;
        font-size: 13px;
    }
    .icon-star {
        position: relative;
        top: 1px;
    }
    .row.video-row {
        margin-left: -35px;
        margin-right: -35px;
    }
    .py-mob-100 {
        padding-top: 30px !important;
        padding-bottom: 30px !important;
    }
    .single-collection-item img {
        height: auto;
    }
    .overlay .popup .popup-body {
        padding: 0 30px;
    }
    .popup.add-credit-card,
    .popup.share-popup,
    .popup.rate-popup {
        width: 90vw;
        height: auto;
    }
    .rate-class {
        margin: 30px 0;
    }
    .close_page {
        position: absolute;
        top: 0;
        right: 0;
        color: #F0F0F0;
        line-height: 20px;
        font-size: 30px;
        padding: 17px;
        cursor: pointer;
    }
    .popup-header p {
        text-align: left !important;
        padding-left: 15px;
    }
    .share-social a svg {
        width: 20px;
        height: 20px;
    }

    .single-teacher-item {
        margin: 0 !important;
        height: auto;
        padding: 60px 0;
    }
    .zindex-1 {
        z-index: 11;
        position: relative;
    }
     .teacher-badge {
        position: absolute;
        top: 0;
        right: auto;
        z-index: 1;
        left: calc(50% + 40px);
    }
    .sticky {
        position: relative;
        top: auto;
    }
    .side-links li {
        margin-bottom: 20px;

        .side-links {
            margin-bottom: 50px;
        }
    }
    .single-help-item h3 {
        margin-bottom: 10px;
    }
    .single-help-item {
        margin-bottom: 25px;
    }
    .h4-mob {
        font-size: 16px !important;
    }
    // account
    .mobile-menu-button {
        width: 50px !important;
    }
    .big-avatar {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }
    .image_options + span, .image_options + span + span {
        font-size: 12px !important;
        line-height: 1.8;
    }
    .image_options.mobile {
        margin-top: 30px;
    }
    .upload-image.big-avatar {
        width: 28vw;
        height: 28vw;
    }
    .my-80 {
        margin-top: 40px !important;
        margin-bottom: 40px !important;
    }
    .mb-80 {
        margin-bottom: 40px !important;
    }
    .account-page {
        .input-label {
            display: none;
        }
    }
    .container640 .panel.p-2 {
        padding: 10px 15px !important;
        font-size: 13px;
    }
    .account-page .credit-card-inputs.input-container input,
    .account-page .credit-card-inputs.input-container + .input-container input,
    .account-page .credit-card-inputs.input-container + .input-container + .input-container input,
    .account-page .credit-card-inputs.input-container + .input-container + .input-container + .input-container input {
        font-size: 14px;
    }
    .account-page .credit-card-inputs.input-container,
    .account-page .credit-card-inputs.input-container + .input-container,
    .account-page .credit-card-inputs.input-container + .input-container + .input-container,
    .account-page .credit-card-inputs.input-container + .input-container + .input-container + .input-container {
        margin-bottom: 18px;
        padding: 0 !important;
    }
    .panel.p-3 {
        padding: 15px !important;
        font-size: 12px;
    }
    .btn-mob-tall {
        height: 50px;
        padding: 0 20px !important;
    }
    .favs {
        position: relative;
        top: -2px;
    }
    .input-label {
        top: -15px;
        font-size: 11px;
    }
    .reveal_password {
        top: 0;
    }
    .col-6.order-mob-1.pr-0.text-right.overflow {
        display: none;
    }
    body .small-subtitle {
        font-size: 11px !important;
        margin-bottom: 10px !important;
    }
    .featured-collection h3 {
        font-size: 14px !important;
        line-height: 1.4;
    }
    .single-featured-collection p {
        margin: 0 !important;
        font-size: 12px;
    }
    // .col-4:last-child .single-video-item {
    //     margin-bottom: 0;
    // }
    .btn.btn-badge {
        width: 50px;
    }
    .initials {
        font-size: 13px; 
    }
    .hero-banner {
        position: relative;
        margin-top: 70px;
        height: 71vh;
    }
    .homepage-collection .swiper-pagination-bullet {
        background: #fff !important;
        opacity: 1 !important;
        border: 1px solid #f0f0f0 !important;
        width: 40px !important;
        height: 40px !important;
    }
    .homepage-collection .swiper-pagination-bullet-active {
        background: #000 !important;
        border: none !important;
    }
    section > .px-150 {
        padding: 0 15px !important;
    }
    body {
        counter-reset: section;
    }
    .homepage-collection .swiper-pagination-bullet::before {
        counter-increment: section;
        content: counter(section);
        color: #000;
        line-height: 40px;
        text-align: center;
        font-size: 13px;
    }
    .homepage-collection .swiper-pagination-bullet-active::before {
        color: #fff;
    }
    footer.pt-100.px-150 {
        padding: 30px 0 0 !important;
    }
    .footer-hub-item img {
        height: 13px;
    }
    footer ul li {
        margin-bottom: 5px;
        line-height: 25px;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        right: 15px !important;
    }
    .single-video-item {
        margin-bottom: 45px;
    }
    .col {
        width: 100%;
    }
    .white.flex.ail.jcc.w100 {
        align-items: center !important;
    }
    [src="images/lock.svg"] {
        height: 20px;
    }
    .col.flex-vertical.p-0.ml-auto {
        width: 100%;
    }
    .custom-select:nth-child(1){ z-index: 5000; position: relative;}
    .custom-select:nth-child(2){ z-index: 4000; position: relative;}
    .custom-select:nth-child(3){ z-index: 3000; position: relative;}
    .custom-select:nth-child(4){ z-index: 2000; position: relative;}
    .custom-select:nth-child(5){ z-index: 1000; position: relative;}
    .custom-selectbox-holder {
        width: 100% !important;
    }
    .py-120 {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
    }
    .col-6.flex.aic.jcr.px-mob-3 {
        justify-content: flex-start !important;
    }
    // sup {
    //     top: -11px;
    // }
    .overlay .popup.mini-popup {
        left: 5%;
        height: auto;
        overflow: hidden;
    }
    .hub-badge {
        position: relative;
        top: 0px;
        width: 120px;
        height: 120px;
        background: #fff;
        border-radius: 50%;
        -webkit-box-shadow: 0 0 0 5px #fff;
        box-shadow: 0 0 0 5px #fff;
        margin-bottom: 30px;
        margin-top: 30px;
    }
    .footer-hub-items {
        display: block;
    }
    .mini-popup sup {
        top: -5px;
    }
    footer .col-4.flex.aic.jcr {
        justify-content: flex-start !important;
        padding-left: 25px !important;
    }
    footer .col-8.flex.aic.jcl.white {
        padding-left: 25px;
    }
    .lagree-method-footer {
        width: 50px !important;
    }
    .col-6:last-of-type .single-collection-item {
        margin-bottom: 0 !important;
    }
    .notifications-checkbox label {
        font-size: 14px !important;
        line-height: 1.5 !important;
        display: block;
    }
    .single-class-row {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        height: 70px;
        padding: 10px 0;
        height: auto !important;

        &:last-of-type {
            border-bottom: 1px solid #f0f0f0 !important;
            margin-bottom: 50px;
        }
        &:first-of-type {
            margin-top: 25px;
        }
        .class-row-left {
            width: 105px;
            font-weight: 300;
            font-size: 12px;
        }
        .class-row-right {
            font-size: 12px;
        }
    }
    .btn.btn-badge {
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        padding: 0 10px;
    }
    .order-3 {
        order: 3;
        margin-left: 30px;
    }
    .btn.btn-sm.red-bg.white.f-10.ml-auto.mr-5 {
        padding: 0 5px;
        min-height: 25px;
    }
    .chat-window {
        width: 100vw;
    }
    .homepage-slider .swiper-pagination-bullet {
        width: 50px;
    }
    .col.left-border.pl-75 {
        border: none !important;
        padding-left: 10px !important;
    }
    .subscription-option label.flex.aic img {
        width: 50px;
    }
    .mob-text-center {
        text-align: center;
    }
    .overlay .popup {
        top: 50px;
        width: 96%;
        height: auto;
    }
    .show-popup .overlay .popup.show {
        transform: translate(-50%, 0%) scale(1) !important;
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        max-width: 100%;
    }
    .buy-class .subscription-option label.flex.aic {
        font-size: 14px;
        padding-right: 40px !important;
    }
    .mob-wrap {
        flex-wrap: wrap;
    }
    .class-item > .light.mr-3 {
        margin-right: 10px !important;
    }
    .class-item .class-item-img {
        width: 70px !important;
        min-width: 70px !important;
        max-width: 70px !important;
        height: 70px !important;
    }
    .class-item * {
        font-size: 11px !important;
    }
    .most-title.earn-title {
        line-height: 1.4;
        margin-bottom: 10px;
    }
    .mob-w100 {
        width: 100%;
    }
    .class-item > div {
        padding-right: 0 !important;
    }
    .before_upload {
        justify-content: center;
        text-align: center;
        padding: 23px;
    }
    .before_upload .h4 {
        font-size: 13px !important;
        line-height: 1.4;
    }
    .upload-zone {
        min-height: 250px !important;
    }
    .mt-80.mb-4 {
        margin-top: 30px !important;
    }
    .flex-column-mob {
        flex-direction: column;
    }
    .user-upload-info {
        padding: 30px !important;
    }
    .small-box {
        margin-bottom: 25px;
    }
    .user-upload-info .f-16.semibold {
        font-size: 14px !important;
        margin-bottom: 20px;
    }
    .user-upload-info .f-14.lh-25 li {
        margin-bottom: 10px;
        line-height: 1.4 !important;
    }
    .user-upload-info .f-14.lh-25 {
        font-size: 13px !important;
        list-style: decimal;
        padding-left: 20px;
    }
    .checkbox label.f-16 {
        font-size: 13px !important;
        padding-left: 35px !important;
    }
    .btn.btn-wide {
        padding: 0 30px;
    }
    .ail-mob {
        align-items: flex-start !important;
    }
    .dashboard-panel.p-mob-0 {
        padding: 0px !important;
    }
    .dashboard-panel.in_the_numbers .dashboard-panel-item > span,
    .dashboard-panel.next-panel .dashboard-panel-item > span,
    .dashboard-panel.p-mob-0 .dashboard-panel-item > span {
        padding-right: 0;
        line-height: 30px;
        margin-bottom: 0;
    }
    .dashboard-panel-item > span + span {
        white-space: nowrap;
        line-height: 20px;
    }
    .dashboard-panel-item {
        font-size: 14px !important;
    }
    .dashboard-panel-item > span:first-of-type {
        padding-right: 20px;
        line-height: 20px;
        margin-bottom: 10px;
    }
    .py-150 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }
    .w320 {
        width: 100%;
        max-width: 100%;
    }
    .shop-item {
        margin-bottom: 40px;
    }
    .shop-title-price .f-16 {
        font-size: 14px !important;
    }
    .shop-button .btn.btn-border {
        padding: 0 15px !important;
    }
    .single-class-new-row {
        padding: 20px;
    }
    .shop-page .mob-flex-left {
        align-items: flex-start !important;
    }
    .shop-page section > hr {
        margin-left: 10px;
        width: calc(100% - 20px);
    }
    .shop-page .col-8.py-150.pl-0 {
        padding-left: 10px !important;
    }
    .shop-page .col-4.px-0.flex.air.jcr {
        padding-right: 10px !important;
        padding-left: 10px !important;
    }
    .avatar-50 {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }
    .the-micro {
        right: 20px;
        width: 60px;
    }
    body .f-16 {
        font-size: 14px !important;
    }
    .text-mob-center {
        text-align: center;
    }
    .single-playlist-list-item {padding: 20px 0;}
    .single-playlist-list-item > .video-container .play-button span {
        border: 9px solid rgba(0, 0, 0, 0);
        border-left: 15px solid #fff;
    }
    .big-content-img {
        width: 100%;
        height: 300px;
        max-width: 300px;
    }
    .d-block-mob {
        display: block;
    }
    .machine-image {
        margin-bottom: 40px;
    }
    .pb-mob-45 {
        padding-bottom: 45px !important;
    }
    .mb-mob-45 {
        margin-bottom: 45px !important;
    }
    .mt-mob-4 {
        margin-top: 40px !important;
    }
    .pt-mob-3 {
        padding-top: 30px !important;
    }
    .playlists-page .col-20 {
        width: 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
    .flex-wrap-mob {
        flex-wrap: wrap;
        flex-direction: row;
    }
    .single-playlist-item {
        margin-bottom: 40px;
    }
    .video-row .single-playlist-list-item .image-overlay {
        width: 100%;
        height: 0;
    }
    .playlist-header .playlist-option {
        position: absolute !important;
        bottom: 36px;
        right: 10px;
        z-index: 11;
    }
    .overlay .popup .popup-header p {
        line-height: 1.2;
    }
    .overlay .popup .popup-header {
        padding: 30px 20px 30px 0;
    }
    .playlist-big-image {
        max-width: 100%;
        min-width: 100px;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        object-fit: cover;
    }
    .added-staff {
        display: none;
    }
    .col-mob-6 {
        width: 50%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
    .single-playlist-list-item {
        margin: 0;
    }
    .playlist-big-image-content {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        max-width: 100%;
    }
    .dropdown.opened > .dropdown-menu.notifications, .dropdown > .dropdown-menu.notifications {
        min-width: 80%;
        position: fixed;
        width: 300px;
        left: 10%;
        top: 60px;
        transform: none;
    }
    .single-notification {
        padding: 10px 30px 10px 15px;
    }
    .notifications-group-list .single-notification + .remove-notif {
        right: 15px;
    }
    .conversation-write form textarea {
        width: 100%;        
        max-width: 100%;
        height: 50px !important;
    }
    .conversation-list .single-msg .avatar40 {
        margin-left: 0;
        margin-right: 10px;
        overflow: visible;
    }
    .conversation-list .single-msg:not(.seb-msg) .avatar40 {
        margin-right: 0;
        margin-left: 15px;
    }
    .conversation-list .single-msg .msg-area {
        max-width: calc(100% - 100px);
        min-width: 200px;
    }
    .text-mob-left {
        text-align: left !important;
    }
    .mvp-btn.mvp-btn-volume-off::before, .mvp-music-toggle.mvp-contr-btn.muted::before {
        content: "";
        position: absolute;
        top: 19px;
        left: 17px;
        width: 30px;
        height: 2px;
        background: #333;
        z-index: 11;
        transform: rotate(35deg);
        box-shadow: 0 1px 0 1px rgba(255, 255, 255, 0.9);
    }
    .phone-buttons {
        display: flex !important;
        padding-top: 0;
        padding-bottom: 0;
    }
    .single-class-title {
        font-size: 16px !important;
    }
    .row.video-row {
        margin-left: -20px;
        margin-right: -20px;
    }
    .abs-bottom-down {
        position: absolute;
        bottom: 25px;
        right: 25px;
        z-index: 11;
        height: 15px;
    }
    // .mvp-player {
    //     height: auto !important;
    // }
    #native-video {
        display: block;
    }
    .phone-button.play_voice_button {
        margin: 0 !important;
        border-top: none !important;
        border-left: none !important;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .phone-button.play_music_button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
        border-right: none !important;
        border-top: none !important;
        border-left: none !important;
        margin: 0 !important;
    }
    .phone-buttons.mt-2.flex.aic.jcsb {
        background: #fff;
    }
    .phone-buttons {
        border-bottom: none;
    }
    .h60 {
        height: 50px !important;
    }
    .single-exercise-image {
        width: 140px;
        height: 75px;
        position: relative;
        margin-right: 15px;
    }
    .video-container.single-exercise {
        flex-direction: column;
        align-items: first baseline;
        margin-bottom: 30px;
    }
    .col-8.pr-mob-1.right-border.pr-4 {
        border: none !important;
    }
    .exercises-container-overhidden.mt-4 {
        margin: 0 -16px 20px;
        height: 110px;
        overflow: hidden;
    }
    .exercises-container {
        margin: 0 0;
        overflow-x: auto;
        padding-left: 15px;
    }
    .exercises-container-scroll {
        display: flex;
    }
    .single-comment .avatar40 .initials {
        font-size: 10px !important;
    }
    .single-comment .avatar40 {
        min-width: 25px !important;
        min-height: 25px !important;
        height: 25px !important;
        width: 25px !important;
    }
    .single-comment-info {
        margin-left: 10px;
    }
    .comment-message {
        font-size: 12px;
        line-height: 20px !important;
     }
    .reply-comments {
        padding-left: 30px;
    }
    .class-next, .class-prev {
        display: none;
    }
    .single-course .single-course-desc {
        width: 80%;
    }
    .single-course .single-course-desc h3 {
        line-height: 1.2;
    }
    .single-course .single-course-desc p {
        font-size: 10px;
    }
    .single-course:last-of-type {
        margin-bottom: 0;
    }
    .h450px {
        height: auto;
    }
    .start-course-banner {
        position: relative;
        top: auto;
        left: auto;
        width: auto;
        height: auto;
        margin-left: 10px;
        margin-right:10px;
    }
    .black-bg.no-mob-black {
        background: none !important;
    }
    .black-bg.no-mob-black::before {
        content: "";
        background: #000;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 400px;
        z-index: 0;
    }
    .row.full-row {
        width: calc(100% + 30px);
        display: block !important;
        padding: 50px 0 0;
    }
    .main-course-title, .main-course-desc {
        padding: 0 30px !important;
        text-align: center;
    }
    .main-course-desc {
        margin-bottom: 50px !important;
    }
    .main-course-title {
        font-size: 18px !important;
    }
    .start-course-banner .start-course-image {
        height: auto;
    }
    .single-course-title h4 {
        line-height: 1.2;
    }
    .ml-15.f-14.semibold.mark_text {
        line-height: 1.4;
    }
    .main-course-desc + .progress-wrap {
        padding: 0 50px 30px;
        margin-top: -20px;
    }
    .progress-wrap .white.f-12.text-uppercase {
        font-size: 10px !important;
    }
    .progress-wrap.w200.text-right {
        width: 45% !important;
    }
    .playlist-wrap {margin-top: 30px;}
    body section.all-teachers-wrap, body section.all-teachers-wrap .container-fluid {padding-left:0 !important; padding-right: 0 !important;}
    body section.all-teachers-wrap {margin-bottom: 50px;}
    .row.teachers-wrap {
        flex-direction: row;
        border-top: 1px solid #f0f0f0;
    }
    .row.teachers-wrap .col-3 {
    width: 50%;
    max-width: 50%;
    flex: 0 0 50%;
    margin: 0;
    padding: 0;
    }
    .single-teacher-item {
    border-radius: 0;
    border-top: none;
    }
    .row.teachers-wrap .col-3:nth-of-type(odd) .single-teacher-item {border-left:none;}
    .row.teachers-wrap .col-3:nth-of-type(even) .single-teacher-item {border-left:none; border-right:none;}   
    .single-teacher-item {padding-top: 30px; padding-bottom: 20px;} 
    .teacher-panel {width:70%; height:auto;}
    .teacher-panel h3 {margin-top: 15px;}
    .teacher-panel p {text-transform: lowercase;}

    .homepage-wrap .container-fluid .row .col-4:nth-of-type(4) {
        display: block !important;
    }
    .homepage-wrap .container-fluid .row.homecourses .col-4:nth-of-type(3),
    .homepage-wrap .container-fluid .row.homecourses .col-4:nth-of-type(4) {
        display: none !important;
    }
}
@media(max-width: 360px){
    .f-12.light.media-desc.with-comma.line-height-small {
        font-size: 11px !important;
    }
}
.mob-search-button {
	display: none;
}
.single-product-desc ul {
	padding-left: 15px;
	line-height: 30px;
	margin-bottom: 20px;
    list-style: disc;
}
.single-product-desc p a, .single-product-desc ul li a, .single-product-desc ol li a {
	font-weight: 500;
	text-decoration: underline;
}

@media(max-width: 960px){
    section:not(.classesroot), section.px-100, section.p-10 {padding-left: 25px !important; padding-right: 25px !important; }
    .container1030 {padding-left: 40px; padding-right: 40px;}
    .row.three-items .col-4 {
     width: 33%;
     flex: 33%;
     max-width: 33%;
    }
    .homepage-wrap .ttl-btn h2 {
        font-size: 30px !important;
    }
    .assembly-ttl {padding-top: 93px; padding-bottom: 56px;}
    #accordion.assembly-acc {margin-bottom: 0;}
#accordion.assembly-acc ul li p {margin-top: 15px;}
.playassemb {max-height: 42px; margin-top: -21px; margin-left: -21px;}
.container850 {padding-left: 40px; padding-right: 40px;}
.hometeacher p {margin-top: 20px;}
.home-teachers:not(.hidemob) .hometeacher {margin-bottom:50px;}
section.account-content {padding-bottom: 100px !important;}
.ask-wrapper {padding-bottom:90px;}


}

@media(max-width: 767px){
    section:not(.classesroot), section.px-100, section.p-10 {padding-left: 30px !important; padding-right: 30px !important; }

    .py-mob-2 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }
    .cart-table-header {
        display: none;
    }
    .cart-table-single-product {
        padding: 30px 0 30px;
        display: block;
    }
    .cart-table-qty {
        width: 50%;
        flex: 0 0 100%;
        max-width: 100%;
        display: inline-block;
        justify-content: center;
        margin-top: 30px;
    }
    .cart-table-shipping {
        font-size: 13px;
    }
    .cart-table-footer {
        padding: 30px 0;
    }
    .cart-table-grand-total {
        font-size: 24px;
        padding: 30px 0;
    }
    .py-100 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }
    body .cart-info {
        font-size: 14px !important;
        text-align: left;
        line-height: 1.4 !important;
        margin-bottom: 30px;
        text-align: center;
    }
    body .single_checkout_button {
        font-size: 12px !important;
    }
    .cart-table-total {
        display: inline-block;
        justify-content: flex-end;
        width: 48%;
        flex: 0 0 100%;
        max-width: 48%;
        text-align: right;
    }
    .cart-table-product-name {
        display: flex;
        flex-direction: row;
        width: 100%;
        align-items: flex-start;
        justify-content: space-between;
    }
    .cart-table-product {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
        display: block;
    }
    .cart-table-image {
        width: 100%;
        height: calc(100vw - 30px);
        margin-right: 0;
        margin-bottom: 30px;
    }
    .cart-table-body {
        border-top: 1px solid #f0f0f0;
    }
    .product-single-image-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 1;
        width: 60px;
    }
    .pt-mob-15 {
        padding-top: 2px !important;
    }
    .pb-8 {
        padding-bottom: 50px !important;
    }
    hr.my-6 {
        margin-top: 30px !important;
        margin-bottom: 30px !important;
    }
	.single-product-title {
		font-size: 20px !important;
	}
	.single-product-price {
		font-size: 16px !important;
	}
	.single-product-desc {
		font-size: 14px !important;
	}
	.link-redirect {
		font-size: 14px !important;
	}
    .col-5.pl-120.pl-mob-10 {
        padding-top: 40px;
    }
    .pl-mob-10 {
        padding-left: 10px !important;
    }
    .pl-mob-2 {
        padding-left: 20px !important;
    }
    .keyword {
        font-size: 22px;
        line-height: 50px;
    }
    .mob-search-button {
        position: absolute;
        top: 0;
        right: 0;
        width: 47px;
        height: 47px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1px solid #F0F0F0;
        z-index: 11;
    }
    .search-form.show .search-input, .search-form:hover .search-input, .search-form:active .search-input, .search-form:focus .search-input, .search-form .search-input:focus, .search-form .search-input:active {
        width: calc(100vw - 80px);
    }
    .mb-mob-0 {
        margin-bottom: 0px !important;
    }
    .sub-menu {
        pointer-events: auto;
    }
    .shopmore {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }
    .single-product-desc table {
        width: 100% !important;
    }
    .pb-mob-3 {
        padding-bottom: 30px !important;
    }
    .account-header {
        height: 300px;
        background: #000 url(../images/mob-acc-header.jpg) no-repeat center center / cover;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .avatar150 {
        width: 120px;
        height: 120px;
    }
    .f-24 {
        font-size: 18px !important;
    }
    .account-header .f-12 {
        font-size: 10px !important;
    }
    .account-header .initials {
        font-size: 20px;
    }
    .container750, .container800 {
        margin: auto;
        max-width: 780px;
        width: 100%;
        padding-left: 10px;
        padding-right: 10px;
    }
    .account-btn + .dropdown-menu.drop-right.side-links {
        right: 50px;
        left: auto;
    }
    .featured-row .col-4 {
        width: 40vw !important;
        flex: 0 0 40vw;
        max-width: 40vw;
    }
    .featured-row {
        flex-wrap: nowrap;
        overflow: auto;
        flex-direction: row;
    }
    .featured-row .single-video-item .image-overlay img {
        min-height: 50px !important;
    }
    .dashboard-page .single-video-item p {
        font-size: 10px !important;
        line-height: 1.3;
    }
    .dashboard-page .video-container {
        margin-bottom: 15px;
    }
    .f-14.text-uppercase.mob-balance {
        font-size: 12px !important;
        line-height: 1.8;
    }
    .my-6 {
        margin-top: 40px !important;
        margin-bottom: 40px !important;
    }
    .min50w {
        min-width: 50px;
        text-align: right;
    }
    .rename_device_item {
        white-space: normal;
    }
    .f-18.flex.aic.jcsb.mob-w100.mb-mob-0.line-height-small {
        font-size: 16px !important;
    }
    .account-page .table-row .video-container .play-button span, .featured-row .video-container .play-button span {
        border: 9px solid rgba(0, 0, 0, 0) !important;
        border-left: 15px solid #fff !important;
        margin-left: 16px !important;
    }
    .account-page .dashboard-panel-item {
        margin-bottom: 8px;
    }
    .account-page .dashboard-panel-item > span:first-of-type {
        margin-bottom: 0;
    }
    .right-menu .avatar {
        min-width: 30px;
        min-height: 30px;
    }
    .f-14.last_activity {
        font-size: 12px !important;
    }
    .side-links li:last-child {
        margin: 0 !important;
    }
    .mid-title-machine {
        margin: 20px 0 50px 0;
        height: 80px;
        font-size: 16px;
    }
    .mid-title-machine.mt-0 {
        margin-top: 0 !important;
    }
    .mob-height {
        max-height: calc(90vh - 100px);
    }
    .close_page.close {
        padding: 30px 20px !important;
    }
	.nomaripad {
        margin:0 !important;
    }

    .mobsubscribe {margin-left:auto;}
    .mobsubscribe a {margin-right:12px;}
    .foo-bottom.py-120 {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
        margin-top: 30px;
    }

    footer .foo-bottom .col-6.aic, footer .col-6.flex.aic.jcr.px-mob-3 {
        justify-content: center !important;
    }
    footer .foo-bottom .col-6.aic:first-of-type {
        border-bottom:1px solid #272727;
        padding-bottom: 40px;
    }
    .foo-first.col-8.aic.jcl {
        justify-content: center !important;
    }
    .foo-selling.col-4.flex.aic.jcr {
        border-top: 1px solid #272727;
        border-bottom: none;
        padding-top: 40px;
        padding-bottom: 0;
        text-align: center;
        justify-content: center !important;
    }
    .showipad {display:block !Important;}

    footer .foo-bottom .col-6.aic, footer .col-6.flex.aic.jcr.px-mob-3 {
        justify-content: center !important;
    }
    footer .foo-bottom .col-6.aic:first-of-type {
        border-bottom:1px solid #272727;
        padding-bottom: 40px;
    }
    .foo-first.col-8.aic.jcl {
        justify-content: center !important;
    }
    .foo-selling.col-4.flex.aic.jcr {
        border-top: 1px solid #272727;
        border-bottom: none;
        padding-top: 40px;
        padding-bottom: 0;
        text-align: center;
        justify-content: center !important;
    }
    .showipad {
    display:block !Important;
    }
    .pbi20 {
    padding-bottom: 20px !important;
    }
    .mobsubscribe {margin-left:auto;}
    .mobsubscribe a {margin-right:12px;}
    .foo-bottom.py-120 {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
        margin-top: 35px;
    }

    footer .foo-bottom .col-6.aic, footer .col-6.flex.aic.jcr.px-mob-3 {
        justify-content: center !important;
    }
    footer .foo-bottom .col-6.aic:first-of-type {
        border-bottom:1px solid #272727;
        padding-bottom: 40px;
    }
    .foo-first.col-8.aic.jcl {
        justify-content: center !important;
    }
    .foo-selling.col-4.flex.aic.jcr {
        border-top: 1px solid #272727;
        border-bottom: 1px solid #272727;
        padding-top: 40px;
        padding-bottom: 40px;
        text-align: center;
        justify-content: center !important;
    }
    .showipad {display:block !important;}
    .foo-nav {border-top:none; padding-top: 15px;}
    .foo-first {padding-bottom: 35px;}
    .footbtm-left {padding-top:30px;}
    /*OnBoarding*/
    .ob-body .help-header-button {margin-left: auto;}
    .ob-body header .logo {position: relative;}
  .black-banner {padding-top: 120px;}
  .black-banner .stepof {margin-top: 60px;}
  .black-banner h1 {font-size: 30px !important;	line-height: 50px;}
  .black-banner.joinbanner {padding: 200px 20px 110px;}
  .class-type {margin-top: -10px;}
  .hosttybox h5 {font-size: 20px !important;}
  .playbtn {width: 60px; margin-top: -30px; margin-left: -30px;}
  #tab-outer {margin-top: 65px;}
#tab-wrapper {padding-bottom: 60px;}
.join-title {padding-top: 65px; padding-bottom: 65px;}
.join-title.top-border {margin-top:75px; padding-bottom: 50px;}
.joinform {margin-bottom: 50px;}
.video-spacer {height:40px;}
section.sec_100 {padding-top: 70px; padding-bottom: 65px;}
.help-contact {margin-top: 65px; padding-top: 60px; padding-bottom: 60px;}
.coll-title {margin-bottom: 45px;}
.searchsec.pt-0.pb-0 {margin-top: 60px;	margin-bottom: 70px;}
.popup-container .video-container.single-exercise {flex-direction: initial; align-items: center; margin-bottom: 20px;}
.parts-left {margin-top: 0; padding-top: 40px;}
.row.three-items {flex-direction:row;}
.row.three-items .col-4 {width: 50%; flex: 50%; max-width: 50%;}
.homepage-wrap .ttl-btn {padding-top: 70px; padding-bottom: 75px;}
.homepage-wrap .ttl-btn h2 {font-size: 22px !important;}
.homepage-wrap .row-title {padding-top: 40px; padding-bottom: 40px;}
.account-save .top-border {margin-top: 42px;}
.mbsec {margin-bottom: 55px !important;}
.support-wrap {margin-bottom: 55px;}
.card-info {margin-left: -135px;}
.card-info .input-container {width: 40px;}
#accordion > li {border-top:1px solid #EFEFEF;  margin-top:35px; padding-top:40px;}
#accordion > li:last-of-type {margin-bottom:75px; padding-bottom:35px;}
#accordion.assembly-acc ul li {width:48% !important;}
#accordion.assembly-acc ul li:nth-child(3n+2) {margin-left: initial; margin-right: initial;}
#accordion.assembly-acc ul li:nth-child(odd) {margin-right:4%;}
#accordion.assembly-acc ul {margin-top: 25px;}
#accordion.assembly-acc > li {padding-top: 30px;}
#accordion.assembly-acc ul {margin-bottom: 0;}
section.assembly-ttl h1 {font-size: 24px !important;} 
.assembly-ttl {padding-top: 68px; padding-bottom: 34px;}
.teachersbio {padding-left: 20px;}
.container1080 {padding-left: 20px; padding-right: 20px;}
.scroll-row {white-space: nowrap; -webkit-overflow-scrolling: touch; overflow-y: hidden; overflow-x: scroll; width: 100% !important; flex-direction: row; flex-wrap: initial; display: block;} 
.home-teachers:not(.hidemob) {border-bottom:1px solid #f0f0f0;}
.row.three-items.scroll-row .col-4 {width: 90%; flex: 90%; max-width: 90%;white-space: initial; display: inline-block; vertical-align: top;}
.hidemob {display:none;}
.hometeacher {width: 43%;}
.hometeacher p {font-size:12px;}
.btn-all {font-size: 10px; height: 35px; line-height: 34px; flex: 0 0 75px;}
.inner-title {padding-top:45px; padding-bottom:45px;}
.searchsec h2 {padding-top: 37px; padding-bottom: 37px;}
.search-item {padding: 27px 0;} 
.searchsec .container-fluid h1 {font-size: 18px !important;margin-bottom: 10px !important;}
.search-item h3.f-14 {font-size:12px !important;}
p.in-p {font-size: 12px !important;}
.noresfound {margin-bottom: 140px;}
.mob-right-header {display:flex; margin-left:auto;}
.inner-title h1 {font-size: 24px !important;}
.start-course-banner {max-width: 100%;}
.start-course-banner .start-course-info {padding-bottom: 30px;}
.single-threebtns .class-svc {display: none !important;}
.single-threebtns {margin-bottom: 30px;}
.title-filter .landing-options .col-12 {justify-content: end !important;}
.title-filter .landing-options {width: 100%; min-width: 100%; position: absolute; right: 10px;}
.title-filter .landing-options .landing-filter, .title-filter .landing-options .body-search {margin-right:initial;}
.products_list .col-4 {width: 100%; flex: 100%; max-width: 100%;}
.searchsec {padding-top: 60px; padding-bottom: 60px;}
/*mobile menu*/
.mob-right-header a, .right-menu.logged-in a {margin-left: 15px;}
header .left-menu {top:70px;}
body.scrolled header .left-menu {top: 60px;}
.mid-nav {display:none;}
.right-menu {margin-left:auto;}
body.menu-opened .right-menu .dropdown, body.menu-opened .right-menu a:not(.menu-button), body.menu-opened .mob-right-header a:not(.menu-button) {display: none;}
.menu-button.active img.menucloseicon {width: auto;} 
/*end mobile menu*/
.subscribe_steps_wrapper {padding: 38px 20px 30px;}
#register_subscribe .cardinfo input.card_year{
    border-right: none !important;
    border-left: none !important;
}
#register_subscribe .cardinfo input.card_month {
    border-right: none !important;
}
.subscribe-title {margin-top:47px; margin-bottom: 45px;}
.payment-part .coupon-form {margin-top: 30px; padding-top: 35px; padding-bottom: 30px;}
.subscribe_steps_wrapper .line-input[name=sum] {width:90px; float:right;}
.subsc-human-box {margin-top: 10px; margin-bottom: 50px;}
.teacherimg-wrap {width: 80px; padding: 80px 0 0;}
.account-page .default_submit.account-form .reveal_password {line-height: 50px;}
.watch_later_item .image-overlay img {position: absolute; top: 0; width: 100%; left: 0; height: 100%; object-fit: cover;}
/*new account look*/
.account-content .lodacc-menu {width: 100%; overflow: hidden; max-width: 100%; width:initial;} /*obrisati iz scss sve*/
.mr-mob-2 {margin-right: 20px !important;}
.pb-mob-1 {padding-bottom: 10px !important;}
section.account-content {padding-left:40px !important;}
.account-subttl {margin-top:37px; margin-bottom:37px;} 
.accountmenu {padding-top:40px !important;}
section.account-content #change_subscription a.cancel-subsc {float: none; margin-top: 30px; min-height: 0; line-height: 1;}
section.account-content .payment-choose {padding: 20px 20px 30px;}
.notify-chekcbox {padding: 25px 20px;} 
#add-card-month, #add-card-year, #add-card-cvc {padding-left: 0px;}
section.account-content .big-avatar {width: 80px; height: 80px;}
section.account-content {padding-bottom: 75px !important;}
.ask-wrapper {padding-bottom:70px;}
section.account-content .subscription-option label {padding-left:20px !important; padding-right:20px !important; margin-right: 20px !important;}
#support_form .input-container textarea {height:150px;}
.radius-mob-6 {border-radius:6px;}
.conversation-list .single-msg .msg-area {width: 100%; max-width:100%;}
.payments-wrap .panel.panel-cc {font-size: 14px;}
.credit-card-icon {margin-right:10px;}
.payments-wrap .panel.panel-cc {padding: 18px 12px !important;}
.f-mob-12 { font-size: 12px !important; }
.f-mob-24 { font-size: 24px !important; }
.f-mob-18 { font-size: 18px !important; }
.lh-mob-1-3 {line-height: 1.3;}
.black-banner.flex.aic.jcc.flex-column {height:350px;} 
.h-mob-46 {height:46px !important;}
.cancel-subs-txt {padding-left: 30px !important; padding-right: 0;}
#overlay .popup.mob-middle {top: 50% !important; height: auto !important; transform: translate(0, -50%) scale(1) !important; left: 0 !important; width: 100% !important; border-radius: 0 !important; margin-top: -50px;}
#overlay .popup.mob-middle .btn {width: auto !important; margin-bottom: 0 !important;}
#overlay .popup.mob-middle .popup-header p {text-align: center !important; margin-top: 5px !important;}
}


@media(max-width: 600px){
    section:not(.classesroot), section.px-100, section.p-10 {padding-left: 10px !important; padding-right: 10px !important; }
    .container1030 {padding-left: 20px; padding-right: 20px;}

    .homepage .py-mob-75 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }

    .homepage .mb-mob-75 {
        margin-bottom: 50px !important;
    }

    .nomartopmob {
        margin-top: 50px !important;
    }

    .mob-mb-50 {
        margin-bottom: 50px !important;
    }
    body.menu-opened .black-logo.showdesk {
        display: none !important;
    }
    footer .foo-bottom .col-6.aic,
    footer .col-6.flex.aic.jcr.px-mob-3,
    .foo-selling.col-4.flex.aic.jcr,
    .foo-first.col-8.aic.jcl {
        justify-content: flex-start !important;
    }
	.homepage .py-mob-75 {padding-top: 50px !important;	padding-bottom: 50px !important;}
.homepage .mb-mob-75 {margin-bottom: 50px !important;}
.nomartopmob {margin-top: 50px !important;}
.mob-mb-50 {margin-bottom: 50px !important;}
footer .foo-bottom .col-6.aic, footer .col-6.flex.aic.jcr.px-mob-3, .foo-selling.col-4.flex.aic.jcr, .foo-first.col-8.aic.jcl {justify-content: flex-start !important;}
.showdesk {display:none !important;}
.showmob {display:block !important;}
#overlay .login-popup, #overlay .forgot-popup, .filters, .need-help-popup, .class-bdown-wrap, .side-popup .popup-container, #overlay .cancel-subscribtion-popup, #overlay .add-credit-card, .edit-playlist-popup, .sortby-popup, #overlay .need-help-popup, #overlay .edit-playlist-popup {transform: none !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; padding: 0; position: fixed; z-index: 9999999999999; border-radius: 0 !important; max-width: 100% !important;}
#overlay .popup h2 {margin-bottom:0 !important;padding-top: 27px; padding-left: 20px;}
#overlay .popup.need-help-popup h2, .popup-ttl {padding-left: 20px; }
#overlay .login-popup .col-12.mt-2.aic.jcsb {display:block !Important; margin-top: 12px !Important;}
#overlay .login-popup a.btn {width:100%; font-size: 12px !important; letter-spacing: 1.2px !important;}
#overlay .login-popup a.link.f-14 {font-size: 12px !important; text-align: center; width: 100%; display: block;  margin-top:28px !important; font-weight:400 !important;}
#overlay .login-popup hr {margin-top: 27px !important; margin-bottom: 10px !important;}
#overlay .login-popup .input-container span svg {margin-top:27px;}
#overlay .login-popup ::-webkit-input-placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
#overlay .login-popup :-ms-input-placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
#overlay .login-popup ::placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
/*popup - Forgot Password popup*/
#overlay .forgot-popup p.light.mb-5 {letter-spacing: 0.6px; line-height:20px !important; font-size: 12px; margin-bottom: 15px !important;}
#overlay .forgot-popup .col-12.mt-2.aic.jcsb {display:block !Important; margin-top: 12px !Important;}
#overlay .forgot-popup .mt-2.aic.jcc p {font-size: 12px !important; text-align: center; font-weight: 400 !important; letter-spacing: 0.6px !important;}
#overlay .forgot-popup hr.popup-divider {margin-top: 30px !important; margin-bottom: 5px !important;}
#overlay .forgot-popup ::-webkit-input-placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
#overlay .forgot-popup :-ms-input-placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
#overlay .forgot-popup ::placeholder {color: #969696; font-weight: 400; font-size: 14px !important; letter-spacing: 0.7px !important;}
#overlay .popup:not(.need-help-popup) .row-vertical {padding-left: 50px; padding-right: 50px;}
.cust-supp-popupwrap {padding: 0 50px 50px;}
.filter-fields {padding: 0 50px 50px;}
/*Support chat*/
.chat-button {display:none;}
.chat-holder {z-index: 9999999999999;}
.chat-window .chat-window-body {padding: 50px 55px 70px 55px;}
.chat-window .chat-window-body p.f-12.mb-3 {margin-bottom:35px !important; font-size:12px !important;}
.chat-window .chat-window-body form#help-form input, .chat-window .chat-window-body form#help-form textarea {font-size: 12px !important; letter-spacing: 0.6px !important;  }
.chat-window .chat-window-body form .submit-box {border-top: 1px solid #F0F0F0; padding-top: 30px !important; margin-top: 20px !important;}
.chat-window .chat-window-body .human-box {margin-top: 20px;}
.chat-window .chat-window-body form ::-webkit-input-placeholder {color: #969696; font-weight: 400; font-size: 12px !important; letter-spacing: 0.6px !important;}
.chat-window .chat-window-body form :-ms-input-placeholder {color: #969696; font-weight: 400; font-size: 12px !important; letter-spacing: 0.6px !important;}
.chat-window .chat-window-body form ::placeholder {color: #969696; font-weight: 400; font-size: 12px !important; letter-spacing: 0.6px !important;}
.chat-window .chat-window-body form button.btn {letter-spacing: 1.2px !important;}
.chat-window .chat-window-body .input-container.mb-2 {margin-bottom:10px !Important;}
.chat-holder .chat-window {height: 100vh;padding-top: 60px;position: fixed !important; top:0 !important;}
.chat-window .chat-window-header {background: #ffffff; padding-left: 55px; height: auto; display: block;}
.chat-window .chat-window-header span {color: #000000; font-size: 24px;}
.chat-window .chat-window-header:hover {background: none !important;}
.closesupport {position:absolute; top:30px; right:30px;}
.chat-window .chat-window-body p.f-14.mb-3 {margin-bottom:20px !important;}
.col-6.mob-half.humantxt {width: 67%; max-width: 67%; flex: 0 0 67%;}
.col-6.mob-half.humanenter {width: 33%; max-width: 33%; flex: 0 0 33%;}
.blockondesk {display:inline;}
.blockonmob {display:block;}
/*mobile nav logo and subscribe*/
.showmob .logo.black-logo {display:flex;}
.bottom-nav {display:flex; align-items:center; position:fixed; bottom:0; width:100%; background:#ffffff;text-align: center;z-index: 99; padding-bottom: 20px;}
.b-nav-box {width:25%; padding-top:10px; padding-bottom:5px; border-top:1px solid #F0F0F0;}
.b-nav-box img {display:block; width: 40px;}
.b-nav-box p {display:block;line-height: 16px; margin-top: 3px;}
.b-nav-box a {font: 400 9px "Graphik", sans-serif !important; display: flex; flex-direction: column; align-items: center;}
#bnavwrap a img.activeicon {display:none;}
#bnavwrap a.b-nav-active img.activeicon {display:block;}
#bnavwrap a.b-nav-active img.noactiveicon {display:none;}
.noclick {pointer-events: none; cursor: pointer;}
footer {padding-bottom:20px;}
.classes-page .dropdown.d-inline-block, .exercises-page .dropdown.d-inline-block {display:none;}
body.menu-opened .black-logo.showdesk {display:none !important;}
body.menu-opened .black-logo {display: flex !important;}
footer .container {padding-left:30px; padding-right:30px;}
.foo-nav {padding-top: 0; border-top: 1px solid #1e1e1e;}
.footbtm-left {
	justify-content: flex-start;
}
.footer-top-row {
	padding-bottom: 0;
  padding-top: 0 !important;
}
.foot-lftrgt.foo-second {
	padding-bottom: 0;
  padding-top: 0;
}
.foo-first {
  padding-top: 50px;
}
.footbtm-left a {
  font-size: 12px;
}
.foo-bottom.py-120 {
	margin-top: 30px;
}
.foo-second .footbtm-left a {
	font-size: 12px;
}
.lghub {
	border: none; 
	padding: 0;
	width: 100%;
}
/*OnBoarding*/
.black-banner h1 {font-size: 22px !important;	line-height: 40px; margin-bottom:10px;}
.black-banner p {font-size: 14px;}
.black-banner {padding-top: 70px;}
.black-banner .stepof {margin-top: 20px;}
.ob-title {padding-top: 45px;	padding-bottom: 42px;}
.ob-title.obtitle-video {padding-top: 45px; padding-bottom: 42px;}
.ob-title h4 {font-size: 16px !important;	line-height: 22px;margin-bottom: 6px;}
#onboarding-body form h5 {font-size: 14px !important; line-height: 20px; padding-top: 24px; padding-bottom: 26px;}
#onboarding-body hr {margin-top:50px;}
#onboarding-body form button {margin-top:50px;}
#onboarding-body section {margin-bottom:0;}
.videoWrapper .playtriangle {width: 50px; margin-top: -25px; margin-left: -25px;}
button.survey-btn {margin-top:50px; height: 50px;}
#lipsum {margin-top: 40px; padding-bottom: 37px;}
/*JoinLagree*/
.black-banner.joinbanner {padding: 70px 20px 74px;}
#tab-outer {margin-top: 40px;}
#tab-wrapper li {margin-left: 5px; margin-right: 5px;}
#tab-wrapper li a {font-size: 14px;}
#tab-wrapper {padding-bottom: 35px;}
.join-title {padding-top: 45px;padding-bottom: 40px;}
.join-title h4 {font-size: 16px !important; line-height:22px; }
.join-title p {font-size: 14px; line-height: 30px !important;margin-top: 15px;}
.form-ttl {padding-top:38px; padding-bottom: 38px;}
.birth-date select {max-width: 31%; }
.black-banner.joinbanner {padding: 150px 20px 70px;}
.hosttybox {padding-top: 160px;	padding-bottom: 90px;}
.hosttybox .custom-btn {font-size: 12px; padding: 13px 21px;}
.playbtn {width: 50px; margin-top: -25px; margin-left: -25px;}
.join-title.top-border {margin-top: 50px; padding-bottom: 32px;}
.joinform {margin-bottom: 45px;}
.join-qu p {padding-top:25px; padding-bottom:25px; font-size: 14px;}
.typatiente {margin-top: 50px !important;}
section.sec_100 {padding-top: 50px; padding-bottom: 40px;}
.help-contact {margin-top: 45px; padding-top: 45px; padding-bottom: 45px;}
.searchsec.pt-0.pb-0 {margin-top: 35px;	margin-bottom: 40px;}
.classesroot .class-svc {flex-wrap: wrap;}
.classesroot .class-box-small {width: 49%; flex: 0 0 49%;}
.classesroot .class-svc .class-box-small:first-child, .classesroot .class-svc .class-box-small:nth-child(2) {margin-bottom: 10px;}
.homepage-wrap .row-title h3 {font-size: 16px !important;}
#accordion > li {margin-top: 25px;	padding-top: 25px;}
#accordion > li:last-of-type {margin-bottom:50px; padding-bottom:25px;}
#accordion ul li, #accordion li div, #accordion ul.f-18 li {font-size:14px; line-height:25px;}
#accordion ul li {padding-right: 25px;}
#accordion.assembly-acc ul li {width:100% !important; margin-bottom: 20px;}
#accordion.assembly-acc ul li:nth-child(odd) {margin-right:0;}
#accordion.assembly-acc ul {margin-top: 15px;}
#accordion.assembly-acc > li {padding-top: 25px;}
.assembly-ttl {padding-top: 45px; padding-bottom: 24px;}
section.assembly-ttl h1 {font-size:18px !important;} 
#accordion li div.active:after, #accordion li div.active-acc:after {margin-top: 7px;}
.teachersbio {padding-left: 0;}
.container850 {padding-left: 20px; padding-right: 20px;}
.btn.filter_classes {margin-right: 0; font-size: 0 !important; padding: 0; width: 46px; height: 46px;}
.btn.filter_classes span {display:none;}
.btn.filter_classes img {margin-right: 0; width:18px;}
/*.show-popup .overlay .popup.show {transform: translate(0%, 0%) scale(1) !important;}*/
.login-popup .btn {flex: 0 0 100%;}
.mob-flex-col {flex-direction: column;}
.popup .btn {width:100%; margin-bottom: 30px;}
body .popup h2 {font-size: 14px !important;}
#overlay .popup hr.popup-divider {margin-bottom: 29px !important;}
#overlay .popup.forgot-popup hr.popup-divider {margin-bottom: 22px !important;}
#overlay .forgot-popup p {margin-bottom: 25px; text-align: center;}
.inner-title h1 {font-size: 18px !important;}
.single-video-item {margin-bottom: 25px;}
.title-filter .landing-options .body-search.opened {width:calc(100% - 37px); position:absolute;}
.body-search.opened .search-form-container {width: 100%;}
.searchsec .body-search.opened {width: calc(100% - 17px);}
.searchsec .body-search.opened .search-form-container {width: calc(100vw - 40px);}
#search_input {color:#969696;}
.body-search .search-form-container input#search_input {color: transparent;}
.body-search.opened .search-form-container input#search_input {color: #000;}
.hearticon, .hearticon.loggedhearticon {border-radius: 0; border: none;}
.hearticon {padding-right: 10px;}
.hearticon.loggedhearticon {padding:0;}
.main-course-title {font-size: 16px !important;}
.main-course-desc {font-size: 12px; line-height: 20px;}
.course-wrap {padding-top: 45px !important;}
.main-course-desc {margin-bottom: 45px !important;}
.scacctxt {padding:15px 20px 0 20px;}
.scacctxt::after {margin-top: 15px;}
.single-course-title {padding: 20px;}
.course-ttil {margin-top:30px; margin-bottom:30px;}
.classdesc {padding: 17px 20px 16px 12px !important;}
.classdesc p, .classdesc p span {font-size: 12px !important; line-height: 20px;}
.class-body-parts {padding: 18px 20px; min-height: auto;}
.class-body-parts:not(:last-of-type) {margin-bottom: 15px;}
.singlettl {margin-top: 30px; margin-bottom: 30px;}
.single-title-wrap {margin-top: 25px; margin-bottom: 25px;}
.media-desc {font-size: 12px; line-height: 1;}
.single-class-new-row:not(:last-of-type) {margin-bottom: 15px;}
.single-class-new-row .class-new-row-image {display: none;}
.equipment-popup .single-class-new-row .class-new-row-image {display: block;}
.comments-wrap form {padding: 20px;}
.title-filter .btn.greyborder {padding: 15px 20px;}
div.small-text p, div.big-text p {line-height: 20px;}
.homepage-wrap {margin-bottom: 10px;}
.body-search .search-form-container, .body-search .search-form-container .line-input {height:46px; margin-top: 0px;}
.body-search:not(.opened) .search-form-container {width: 46px;}
.search-wrapper.body-search .search-form-header button {width: 46px; height: 46px;}
.search-wrapper.body-search a.search-popup img {width:17px;}
.search-wrapper.body-search .search-form-header button img {margin-top: -4px;}
.searchsec {padding-top: 50px; padding-bottom: 50px;}
.wlater-box {flex-wrap:wrap;}
.wlater-box .w160px {width: 100%; height: 220px;}
.watch-hist-wrap .wlater-box .video-container {margin-right: 0; margin-bottom: 25px !important; flex: 100%; height: 100%;}
.watch-hist-wrap .wlater-box .video-container .image-overlay {padding-top: 56.25%;} 
.watch-hist-wrap .wlater-box {margin-bottom: 25px;}
.watchhistory-ttl {margin-bottom: 30px;}
.single-video-item .image-overlay, .single-video-item .image-overlay:hover, .video_preview_player:hover {border-radius: 6px !important;}
.single-notification, .classdesc, .class-body-parts, .single-class-new-row, .single-course-item, .subscription-option, .panel.subsc-right {border-radius: 6px;}
.popup-container {border-radius: 0;}
.side-popup .popup-container .exercises-container {padding: 0 20px; max-height: 86%;}
.wlater-box {align-items: start !important;}
.subscribe-title h1 {font-size:16px !important;}
.subsc-right .continue_button_wrap button {font-size: 12px !important;}
.subscribe-createacc .w100 {padding-top: 36px; margin-top: 30px; margin-bottom: 27px;}
.subscribe-whats-box {padding: 25px 30px 10px 30px;}
.subsc-plans h4 {margin-bottom: 35px;}
.subsc-plans .radio-button label {padding-left: 20px !important; padding-right: 20px; margin-right: 20px;}
.code-field {border-radius: 4px;}
.subscribe_steps_wrapper .payment-part h4 {margin-bottom: 30px;}
.step-two-codefields .code-field {padding-top: 4%; padding-bottom: 4%; font-size: 16px;}
.searchsec .search-form-container {margin-right: 10px;}
.title-filter .landing-options .landing-filter, .title-filter .landing-options .body-search {height: 46px;}
// .searchsec .body-search {margin-top: -72px;}
.filters.show {overflow-y: auto;}
.bdown-content {padding-left:20px !important; }
.bdown-content, .exercise-list-wrapper, .equip-popup-body {max-height:86% !important;}
.teacher-panel .initials {font-size: 46px;}
.big-gap.teachers-wrap [class*="col-"] {margin-bottom: 20px;}
.forgot-popup .forgot-form .row-vertical {margin-top:0;}
/*new account look*/
section.account-header {height:200px; margin-bottom: 0 !important;padding-left:30px !important;}
.accountmenu {overflow-x: scroll; width: 100% !important; overflow-y: hidden; margin-left: 0 !important; white-space: nowrap; padding-top: 25px !important; padding-bottom: 30px !important; }
.accountmenu li {display:inline-block; margin-right:10px;}
.accountmenu li a {text-transform:uppercase; font-size:12px !important; font-weight:600 !important;}
.side-links.accountmenu li a.active {color:#969696;}
section.account-content .acc-logout {color:#000;}
section.account-content {flex-direction:column;}
.py-mob-15 {padding-top:15px !important; padding-bottom:15px !important;}
.avatar120 {width:80px; height:80px;}
section.account-content {padding-left:10px !important; padding-right:10px !important;}
section.account-content .notifications-checkbox label {font-size:12px !important;}
.top-border-mob {border-top:1px solid #f0f0f0;}
.side-links.accountmenu li {margin-bottom: 0 !important;}
.account-content .lodacc-menu {flex: auto; padding-left: 10px !important;}
.acc-logout {color:#000;}
.conversation-list {padding: 20px 5px 30px !important;}
.conversation-list .single-msg {margin-bottom: 30px;}
.conversation-write {padding: 20px 20px 30px !important;}
.conversation-write form textarea {height:50px !important; padding-top:13px;}
.conversation-list .single-msg {justify-content: space-between;}
.conversation-list .single-msg .msg-area {margin-right:20px;}
.conversation-list .single-msg:not(.seb-msg) .msg-area {margin-right:15px;}
.conversation-list .single-msg .msg-area {margin-left:15px;}
.black-banner.flex.aic.jcc.flex-column {height:250px;} 
.black-banner.flex.aic.jcc.flex-column h1 {font-size:24px !important;} 
.account-showpass .reveal_password {margin-top:-9px;}
.search-form.show .search-input, .search-form:hover .search-input, .search-form:active .search-input, .search-form:focus .search-input, .search-form .search-input:focus, .search-form .search-input:active {width: calc(100vw - 40px);}
.popup.survey-popup .btn {margin-bottom: 0;}
.btn.sortby_list {font-size: 0 !important; padding: 0; width: 46px; height: 46px;}
.btn.sortby_list img {margin-right: 0;}
.btn.filter_classes {margin-right: 10px;}
.btn.sortby_list {margin-right: 5px;}
body.show-popup .sortby-popup .custom-selectbox.single-select ul {padding-left: 50px; padding-right: 50px;}
.sortby-popup .sortby-actions {padding-left: 50px;padding-right: 50px;}
.sortbyimg {display: block;}
.sortby_list::after {display:none;}
.single-wrapper .single-title-wrap {margin-top: 23px;}
.bodypos-content {padding-top: 30px;}
.single-page-title {}
.account-main-title {padding-bottom: 35px;}
.sortby-popup ul {padding: 0 50px 50px;}
.ask-banner {background-position: right 30% center;}
.single-user-playlist {margin-bottom: 25px;}
.playlist-main-title {padding-top: 40px; padding-bottom: 45px;}
.nrplaylists {margin-bottom: 25px;}
.options-plst {flex: 0 0 60px;}
.playlist-list .col-12 {padding-left:20px; padding-right: 20px;}
.video-ttl span {white-space: nowrap; overflow: hidden;}
.single-video-item .hearticon {display:none;}
/**/
.mob-medium {font-weight:500 !important;}
.video-container {margin-bottom: 15px;}
.single-video-item .video-text-container a h4 span {font-weight:500 !important; text-transform: initial !important;}
.products_list .watched img {width: 35px;}
.duration {font-size: 12px;}
.products_list .play-button, .products_list .video-tags, .three-items .play-button, .three-items .video-tags, 
.with-comma .thumb-duration, .with-comma .thumb-difficulty, .with-comma .thumb-bposition  {display: none !important;}
.thumb-info span.with-comma, .thumb-info span.classby {display:inline !important; line-height:1 !important;}
.thumb-info {line-height: 1.4 !important;}
.video-ttl span {line-height: 1.6 !important;}
.with-comma span {line-height:18px; white-space: initial;}
.thumb-info .byteacher {white-space: initial; display: inline !important;}
.homepage-slider {border-bottom: 1px solid #f0f0f0;}
.homepage .three-items.homecourses .col-4 .single-video-item .image-overlay {padding-top:100%;}
.products_list.courses-list .single-video-item .image-overlay {padding-top:100%;}
.products_list.courses-list .coursedesc, .homecourses .coursedesc {display:none;}
.products_list.courses-list .thumb-info, .homecourses .thumb-info {display:block;}
.pr-600-0 {padding-right:0 !important;}
.calendar-top {	 
	height: 70px;
}
}

@media(max-width: 480px){
.panel.subsc-right {padding: 40px 25px;}    
#register_subscribe .cardinfo input {margin-top: 31px;}
.homepage-wrap .ttl-btn {padding-top: 48px; padding-bottom: 52px;} 
.hero-container {position: relative; top: initial; min-height: 150px; transform:translate(-50%, -0%); padding-top: 45px; padding-bottom: 50px;}
.hero-banner .image-overlay.h100vh {height: 250px;}
.hero-container h1 {font-size: 18px !important; color:#000;}
.hero-container h3 {font-size: 12px !important; color:#000; margin: 0 auto 26px; max-width: 280px;}
.hero-banner {height: auto;}
.homepage-slider .swiper-pagination.homepage-slider-pagination {bottom: 247px;}
.swiper-slide .btn {background:#000 !important; color:#fff !important; border:1px solid #000;}
body .swiper-slide .btn:hover {background:#fff !important; color:#000 !important;}
.swiper-slide .btn span {font-size: 12px;}
.btn-all {flex: 0 0 50px;}  
.btn-all span {display: none;}
.homepage-wrap .row-title {padding-top:28px; padding-bottom: 28px;}
.comment-notify button.send_comment {padding: 0 15px; height: 30px; min-height: 0;}
.comment-notify .cancel_comment {margin-right: 0;}
.comment-notify label {padding-left: 30px !important;}
section.account-content {padding-bottom: 50px !important;}
// .searchsec .body-search {margin-top: -55px;}
.red_bar {
	color: #fff;
	text-align: center;
	font-size: 12px;
	font-weight: 500;
	position: fixed !important;
	bottom: 0;
	left: 0;
	width: 100%;
	z-index: ********;
}
}
@media(max-width: 370px){
    .foo-nav .col-mob-6 {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
        text-align: center;
    }
}

@media(max-width: 768px) and (max-height: 700px) {
    .terminology-popup .class-bdown-wrap {
        transform: translate(-50%, 0%);
        top: 0;
        height:100%;
    }
} 