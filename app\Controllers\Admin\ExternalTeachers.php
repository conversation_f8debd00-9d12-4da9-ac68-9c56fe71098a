<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class ExternalTeachers extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('TeachersModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_teachers'] = $this->model->all_noncertified_teachers(0, session('per_page'));
        $data['teachers_count'] = count($this->model->query("SELECT * FROM `teachers` WHERE deleted_at IS NULL AND (certified = 0 OR certified IS NULL)")->getResultArray());
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;
        $data['external'] = 1;

        echo view('admin/teachers/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_teachers'] = $this->model->all_noncertified_teachers(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['teachers_count'] = count($this->model->query("SELECT * FROM `teachers` WHERE deleted_at IS NULL AND (certified = 0 OR certified IS NULL)")->getResultArray());
        $data['sort_by'] = "Ascending";
        $data['page'] = $page;
        $data['external'] = 1;

        echo view('admin/teachers/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_teachers'] = $this->model->like('title', $data['search_term'])->findAll();
        $data['all_teachers'] = $this->model->query("SELECT  teachers.*, COALESCE(x.cnt,0) AS classes_count
                                                                    FROM  teachers
                                                                    LEFT JOIN (SELECT teacher, count(*) as cnt FROM classes GROUP BY teacher) x on x.teacher = teachers.id
                                                                    WHERE teachers.deleted_at IS NULL
                                                                    AND (teachers.firstname LIKE '%" . $data['search_term'] . "%' OR teachers.lastname LIKE '%" . $data['search_term'] . "%')
                                                                    AND (certified = 0 OR certified IS NULL)
                                                                    ORDER BY teachers.created_at desc")->getResultArray();
        $data['teachers_count'] = count($this->model->query("SELECT * FROM `teachers` WHERE deleted_at IS NULL AND firstname LIKE '" . $data['search_term'] . "' AND (certified = 0 OR certified IS NULL)")->getResultArray());
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;
        $data['external'] = 1;

        echo view('admin/teachers/index_view', $data);
    }

    public function sort_by($type = 'teachers.firstname', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_teachers'] = $this->model->all_noncertified_teachers(0, session('per_page'), '', $type . " " . $direction);
        $data['teachers_count'] = count($this->model->query("SELECT * FROM `teachers` WHERE deleted_at IS NULL AND (certified = 0 OR certified IS NULL)")->getResultArray());
        $types = array(
            "teachers.created_atdesc" => "Date Joined",
            "teachers.firstnameasc" => "Ascending",
            "teachers.firstnamedesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
        $data['external'] = 1;
		echo view('admin/teachers/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_teachers'] = $this->model->findAll();
		$data['usertypes'] = $usertypes->orderBy('title', 'asc')->findAll();

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL')->getResultArray();
		$data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/teachers');
        };

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/teachers/edit_view', $data);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        if($data['password'] == ""){
            $rules['password'] = "if_exist";
            unset($data['password']);
        }
        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/teachers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/teachers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/teachers/' . $name, 98);
				$data['image'] = 'uploads/teachers/' . $name;
                $webp = webpImage($data['image'], $_ENV['webP_quality']);
                $data['webp_image'] = str_replace('\\', '/', $webp);
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/teachers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/teachers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/teachers/' . $name, 98);
				$data['cover_image'] = 'uploads/teachers/' . $name;
                $webp = webpImage($data['cover_image'], $_ENV['webP_quality']);
                $data['webp_cover_image'] = str_replace('\\', '/', $webp);
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/teachers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/teachers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/teachers/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/teachers/' . $name;
                $webp = webpImage($data['mob_cover_image'], $_ENV['webP_quality']);
                $data['webp_mob_cover_image'] = str_replace('\\', '/', $webp);
			}
            if(!isset($data['machine'])){ $data['machine'] = ''; }
            if(!isset($data['difficulty'])){ $data['difficulty'] = ''; }

            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if($data['cover_image_removed'] == 1){
                $data['cover_image'] = "";
            }
            unset($data['cover_image_removed']);

            if($data['mob_cover_image_removed'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);

            if($data['id'] == 0){
                if(!isset($data['status']) OR $data['status'] != 0){
                    $notification_data = array(
                        'content'   => 'New teacher <span class="text-underline">' . $data['firstname'] . ' ' . $data['lastname'] . '</span> joined LagreeOD.',
                        'link'      => base_url() . '/teachers/' . $data['slug'],
                        'author'    => 'system',
                        'type' => 'new_teacher_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_saved'] = $NotificationsModel->save($notification_data);
                }
            }

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}