<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Subscribers extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_subscribers'] = $this->model->all_subscribers(0, session('per_page'));
        $data['subscribers_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/subscribers/index_view', $data);
    }

    public function non_subscribers()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_subscribers'] = $this->model->all_subscribers(0, session('per_page'));
        $data['subscribers_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/subscribers/index_view', $data);
    }

    public function all_subscribers()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_subscribers'] = $this->model->all_subscribers(0, session('per_page'));
        $data['subscribers_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/subscribers/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['all_subscribers'] = $this->model->all_subscribers(($page * session('per_page')) - session('per_page'), session('per_page'), NULL, session('subscribers_sort_by') != NULL ? session('subscribers_sort_by') : '');
        $data['subscribers_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/subscribers/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_subscribers'] = $this->model->like('firstname', $data['search_term'])->like('lastname', $data['search_term'])->findAll();
        $data['all_subscribers'] = $this->model->query("SELECT *
                                        FROM subscribers
                                        WHERE deleted_at IS NULL
                                        AND
                                        (
                                            MATCH(firstname) AGAINST('+{$data['search_term']}*' IN BOOLEAN MODE)
                                            OR
                                            MATCH(lastname) AGAINST('+{$data['search_term']}*' IN BOOLEAN MODE)
                                            OR
                                            MATCH(email) AGAINST('+{$data['search_term']}*' IN BOOLEAN MODE)
                                        )
                                        ORDER BY firstname asc")->getResultArray();

        $data['subscribers_count'] = count($data['all_subscribers']);
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/subscribers/index_view', $data);
    }

    public function sort_by($type = 'subscribers.title', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['all_subscribers'] = $this->model->all_subscribers(0, session('per_page'), NULL, $type. " " . $direction);

        // $data['all_subscribers'] = $this->model->query("SELECT subscribers.*
        //                                 FROM subscribers
        //                                 WHERE subscribers.deleted_at IS NULL
        //                                 ORDER BY " . $type. " " . $direction . "")->getResultArray();
        $data['subscribers_count'] = count($this->model->findAll());
        $types = array(
            "subscribers.created_atdesc" => "Date Joined",
            "subscribers.firstnameasc" => "Ascending",
            "subscribers.firstnamedesc" => "Descending",
        );
        session()->set('subscribers_sort_by', $type.' '.$direction);
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/subscribers/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/subscribers');
        }
        $teacher = $teachers_model->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND email = '" . (isset($data['current']['email']) ? $data['current']['email'] : '') . "'")->getFirstRow('array');

        $data['current']['is_teacher'] = isset($teacher['id']) ? 1 : 0;

		return view('admin/subscribers/edit_view', $data);
    }

    public function reset_pass()
    {
		$data = $this->request->getPost();
		if (isset($data['password']) AND $data['password'] <> ''){
			$data['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))));
		}

        $response['success'] = $this->model->save($data);

		return $this->respond($response);
    }

    public function login_as() //<EMAIL>
    {
        $stripe_model = model('StripeModel');
        $SubscribersModel = model('SubscribersModel');
		$data = $this->request->getPost();

        $user = $SubscribersModel->where(['email' => $data['email']])->first();
        $subscription = !empty($user['stripe_subscription']) ? $stripe_model->retrieve_subscription($user['stripe_subscription']) : NULL;
        $status = isset($subscription['subscription']['status']) ? $subscription['subscription']['status'] : NULL;

        session()->set('user', $user['id']);
        session()->set('subscription', $status);
        session()->set('subscription_plan', $subscription['subscription_info']['plan']['interval']);

        return redirect()->to('/');
    }

    public function save()
    {
        $teachers_model = model('TeachersModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        unset($rules['password']);
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/subscribers', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/subscribers/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/subscribers/' . $name, 98);
				$data['image'] = 'uploads/subscribers/' . $name;
			}
            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $data[$key] = json_encode($single_field);
                }
            }
            $teacher = $teachers_model->query("SELECT * FROM teachers WHERE deleted_at IS NULL AND status = 0 AND email = '" . $data['email'] . "'")->getFirstRow('array');
            if ($data['is_teacher'] == 1){
                if(!isset($teacher['id'])){
                    $teacher_data = array('firstname' => $data['firstname'], 'lastname' => $data['lastname'], 'slug' => 'techer_' . time(), 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', 'lagreeod')))), 'email' => $data['email'], 'status' => 0);
                    $response['new_teacher_data'] = $teacher_data;
                    $response['new_teacher_saved'] = $teachers_model->save($teacher_data);
                }
            }else{
                if(isset($teacher['id'])){
                    $teacher_data = array('id' => $teacher['id'], 'status' => 1);
                    $response['teacher_updated'] = $teacher_data;
                    $response['teacher_updated'] = $teachers_model->save($teacher_data);
                }
            }

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}