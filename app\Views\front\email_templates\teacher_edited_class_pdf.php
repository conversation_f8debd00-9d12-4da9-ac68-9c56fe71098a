<!DOCTYPE html>
<html lang="en-GB">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title></title>
<style>
    * { margin: 0; padding: 0; font-size: 100%; font-family: "Helvetica", Helvetica, Arial, sans-serif; line-height: 1.65; }
    img { max-width: 100%; }
    html, body {background: #ffffff; padding: 0; margin: 0; color: #000}
    .body-wrap { max-width: 840px; width: 100% !important; margin: 0 auto !important; }
    a { color: #71bc37; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .text-center { text-align: center; }
    .text-right { text-align: right; }
    .text-left { text-align: left; }
    .button { display: inline-block; color: white; background: #2B7CB5; border: solid #2B7CB5; border-width: 10px 20px 8px; font-weight: bold; border-radius: 4px; }
    .button:hover { text-decoration: none; }
    .mark-text { display: inline-block; color: #333333; padding: 10px; background: #eeeeee; border-style: none; font-weight: regular; border-radius: 1px; }
    h1, h3, h4, h5, h6 { margin-bottom: 20px; line-height: 1.25; color: #000!important;}
    h1 { font-size: 32px; }
    h2 { font-size: 14px; font-weight: 700; background: #F6F9FB; border-radius: 0;padding: 10px 20px; text-transform: uppercase;color: #66717C}
    h3 { font-size: 24px; }
    h4 { font-size: 20px; }
    h5 { font-size: 16px; }
    td, th { padding: 5px 20px; }
    p, ul, ol { font-size: 16px; font-weight: normal; margin-bottom: 20px; }
    .container { max-width: 840px !important; }
    .container table { width: 100% !important; border-collapse: collapse; }
    .logohead { padding: 0; }
    .container .content { background: white; padding: 50px 50px 20px; }
    .container .content.footer { background: none; padding: 30px 0 0; }
    /* NEW */
    .container .content .meeting-desc { font-size: 14px; line-height: 25px; margin-top: 40px; margin-bottom: 50px;}
    .container .content .meeting-title .column h4 {font-size: 20px; margin-bottom: 5px; margin-top: 10px;line-height: 30px; color: #000; font-weight: bold}
    .container .content .meeting-title .column p {font-size: 14px; margin-bottom: 40px;line-height: 25px; color: #969696; font-weight: bold}
    .container .content .small-table {font-size: 12px; margin-bottom: 30px;}
    .container .content .small-table tr {font-size: 12px; height: 50px; border-bottom: 1px solid #EDF2F5;}
    .container .content .small-table-desc {border-bottom: 1px solid #EDF2F5; padding: 0 0 25px 0; font-size: 12px; }
    .container .content .recap-avg-score {font-size: 16px; font-weight: 700; }
    .container .content .recap-score-big {background-color: #1BC3A7; font-size: 16px; color: #ffffff; display: inline-block; border-radius: 33px; margin-right: 5px; margin-top: 15px; width: 40px; height: 40px; text-align: center; line-height: 40px; font-weight: 700}
    .footer-desc {font-size: 10px;}
</style>
</head>
<body>
<div class="logohead" style="text-align:center;">
    <!-- <img src="https://www.lagreeod.com/images/email-logo.png" style="height: 21px;width: 321px;margin: auto;" height="21" width="321"> -->
</div>
<table style="width: 100%;font-family: Helvetica" bgcolor="#ffffff" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td>
            <table class="body-wrap">
                <tr>
                    <td class="container">
                        <table>
                            <tr>
                                <td class="content">
                                    <div class="meeting-title">
                                        <div class="column">
                                            <h4><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h4>
                                            <p>Submited on <?php echo date("d/m/Y \@ H:i"); ?> by: <?php echo (isset($single['teach_full_name']) AND $single['teach_full_name'] != '') ? $single['teach_full_name'] : '/'; ?></p>
                                        </div>
                                    </div>
                                    <h2>CLASS INFO</h2>
                                    <table border="0" width="100%" style="font-size: 12px;" class="small-table">
                                        <tr>
                                            <td align="left"><strong>Machine</strong></td>
                                            <td align="right"><?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Difficulty</strong></td>
                                            <td align="right"><?php echo (isset($single['diff']) AND $single['diff'] != '') ? $single['diff'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Body Parts</strong></td>
                                            <td align="right"><?php echo (isset($single['all_body_parts']) AND $single['all_body_parts'] != '') ? $single['all_body_parts'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Class Accessories</strong></td>
                                            <td align="right"><?php echo (isset($single['all_custom_class_accessories']) AND $single['all_custom_class_accessories'] != '') ? $single['all_custom_class_accessories'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Bungee Tension</strong></td>
                                            <td align="right"><?php echo (isset($single['all_class_tensions']) AND $single['all_class_tensions'] != '') ? $single['all_class_tensions'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Tempo Count</strong></td>
                                            <td align="right"><?php echo (isset($single['all_class_tempo']) AND $single['all_class_tempo'] != '') ? $single['all_class_tempo'] : '/'; ?></td>
                                        </tr>
                                        <tr>
                                            <td align="left"><strong>Spring Load</strong></td>
                                            <td align="right"><?php echo (isset($single['all_class_springs']) AND $single['all_class_springs'] != '') ? $single['all_class_springs'] : '/'; ?></td>
                                        </tr>
                                    </table>
                                    <?php if(isset($single['exercises']) AND is_Array($single['exercises']) AND count($single['exercises']) > 0){ ?>
                                    <h2>CLASS ROUTINE</h2>
                                    <table border="0" style="font-size: 12px;" class="small-table">
                                        <?php 
                                        $total = 0;
                                        foreach($single['exercises'] as $exercise){ 
                                            if(isset($exercise['custom_duration']) AND is_numeric($exercise['custom_duration']) AND $exercise['custom_duration'] != '' AND $exercise['custom_duration'] != 'NaN' AND $exercise['custom_duration'] != 0){
                                                $dur = $exercise['custom_duration'];
                                            }else{
                                                $dur = 0;
                                            };
                                            $total = $total + $dur;
                                        ?>
                                        <tr>
                                            <td align="left"><strong><?php echo (isset($exercise['orientation']) AND $exercise['orientation'] != '') ? '(' . $exercise['orientation'] . ') ' : ''; ?><?php echo (isset($exercise['title']) AND $exercise['title'] != '') ? $exercise['title'] : ''; ?></strong></td>
                                            <td align="right"><?php echo (isset($exercise['custom_duration']) AND $exercise['custom_duration'] != 0) ? duration_standard($exercise['custom_duration']) : ((isset($exercise['duration']) AND is_numeric($exercise['duration']) AND $exercise['duration'] != '' AND $exercise['duration'] != 'NaN') ? duration_standard($exercise['duration']) : ''); ?></td>
                                        </tr>
                                        <?php 
                                        }
                                        ?>                                        
                                        <tr style="background: #FCFCFD;height: 30px;">
                                            <td align="left" style="color: #66717C;"><strong>TOTAL DURATION</strong></td>
                                            <td align="right"><?php echo duration_standard($total); ?></td>
                                        </tr>
                                    </table>
                                    <?php } ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>