<?php namespace App\Models;

use CodeIgniter\Model;

class ExercisesModel extends Model
{
    protected $table = 'exercises';
	protected $allowedFields = ['parent_id', 'title', 'aka', 'slug', 'image', 'video_preview', 'video', 'video_thumb', 'video_encrypted_path', 'duration', 'content', 'difficulty', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'notification_sent', 'type', 'reason'/* , 'exercise_type','range_of_motion','direction','tension' */, 'hidden_in_finder', 'language', 'additional_desc'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[exercises.slug,id,{id}]',
        // 'content'     => 'required',
        // 'video'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        // 'body_parts'     => 'required',
        // 'accessories'     => 'required',
        // 'springs'     => 'required',
    ];
    protected $validationMessages = [
        'slug' => [
            'required'  => 'The Page URL field is required.',
            'is_unique' => 'The Page URL field must be unique!'
        ]
    ];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_exercises($start = 0, $limit = 10, $search_term = "0", $order = "exercises.created_at DESC", $status = NULL){
        $status_sql = ($status != NULL) ? " AND exercises.status IN (" . $status . ")" : " AND exercises.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        // if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
        //     $words = explode(" ", $search_term);
        //     $string = array_map(function($word){
        //         return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
        //     }, $words);

        //     $term = implode(" ",$string);
        //     $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        // }else{
        //     $search = "";
        // };

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.id,exercises.image,exercises.title,exercises.slug,exercises.duration,exercises.video_thumb,exercises.type,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS exerciseRate,
                            difficulty.title as diff,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(body_parts.title, ' ', '_')) SEPARATOR ' ') AS body_parts_exercises,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                            LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
                        
        return $data;
    }

    public function all_exercises_search($start = 0, $limit = 10, $search_term = "0"){
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        // if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
        //     $words = explode(" ", $search_term);
        //     $string = array_map(function($word){
        //         return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
        //     }, $words);

        //     $term = implode(" ",$string);
        //     $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        // }else{
        //     $search = "";
        // };

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.id,exercises.image,exercises.title,exercises.slug,exercises.duration,exercises.video_thumb,exercises.type,
                            IF((SELECT count(id) FROM exercises_rate WHERE exercise_id = exercises.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                            (SELECT count(rate > 3) as rate FROM exercises_rate WHERE exercise_id = exercises.id GROUP BY exercise_id) as likeCount,
                            difficulty.title as diff,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS tempo
                            FROM exercises
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY exercises.created_at DESC
                            " . $limit_size . "
                        ")->getResultArray();
                        
        return $data;
    }

    public function all_exercises_front($start = 0, $limit = 10, $search_term = "0", $order = "exercises.created_at DESC", $status = NULL){
        $status_sql = ($status != NULL) ? " AND exercises.status IN (" . $status . ")" : " AND exercises.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        // if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
        //     $words = explode(" ", $search_term);
        //     $string = array_map(function($word){
        //         return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
        //     }, $words);

        //     $term = implode(" ",$string);
        //     $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        // }else{
        //     $search = "";
        // };

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
            // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.id,exercises.image,exercises.title,exercises.slug,exercises.duration,exercises.video_thumb,exercises.type,
                            difficulty.title as diff,
                            video_state.video_time as video_state
                            FROM exercises
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();

        if(count($data) > 0){
            foreach($data as $key => $single){
                // Machines
                $machines_query = $this->query("SELECT 
                                                    GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises, 
                                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                                                    FROM exercises_machine 
                                                    LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['machines_exercises'] = $machines_query['machines_exercises'];
                // Body Parts
                $body_parts_query = $this->query("SELECT GROUP_CONCAT(DISTINCT LOWER(REPLACE(body_parts.title, ' ', '_')) SEPARATOR ' ') AS body_parts_exercises 
                                                    FROM exercises_body_parts 
                                                    LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['body_parts_exercises'] = $body_parts_query['body_parts_exercises'];
                // Tempo
                $tempo_query = $this->query("SELECT GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo
                                                    FROM exercises_tempo 
                                                    LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['all_exercise_tempo'] = $tempo_query['all_exercise_tempo'];
                // Springs
                $springs_query = $this->query("SELECT GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids
                                                    FROM exercises_springs 
                                                    LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['all_exercise_springs_ids'] = $springs_query['all_exercise_springs_ids'];
            }
        }
                        
        return $data;
    }

    public function all_home_exercises($start = 0, $limit = 10, $search_term = "0", $order = "exercises.created_at DESC", $status = NULL){
        $status_sql = ($status != NULL) ? " AND exercises.status IN (" . $status . ")" : " AND exercises.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";

        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.id,exercises.title,exercises.slug,exercises.image,exercises.duration,exercises.video_preview,exercises.video_thumb,exercises.difficulty,exercises.status,exercises.type,exercises.language,exercises.created_at,
                            COALESCE(y.rate,0) AS exerciseRate,
                            -- COALESCE(s.sum_price,0) AS exerciseEarned,
                            difficulty.title as diff,
                            video_state.video_time as video_state,
                            -- GROUP_CONCAT(DISTINCT LOWER(REPLACE(body_parts.title, ' ', '_')) SEPARATOR ' ') AS body_parts_exercises,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                            -- GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            -- LEFT OUTER JOIN (SELECT exercise_id, SUM(seller_earning) as sum_price FROM subscribers_exercises GROUP BY exercise_id) s on s.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            -- LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                            -- LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            -- LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            -- LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            " . $status_sql . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_exercises_for_routines($start = 0, $limit = 10, $search_term = "0", $order = "exercises.created_at DESC", $status = NULL){
        $status_sql = ($status != NULL) ? " AND exercises.status IN (" . $status . ")" : " AND exercises.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS exerciseRate,
                            difficulty.title as diff,
                            video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(body_parts.title, ' ', '_')) SEPARATOR ' ') AS body_parts_exercises,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                            LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function load_more_exercises($start = 0, $limit = 100, $filter = ''){
        $search = '';
        $machines_sql = '';
        $body_parts_sql = '';
        $join = '';
        $machines_group_title = '';

        if($filter != ''){
            $filter_data = json_decode($filter, TRUE);
            $search_term = $filter_data['search_term'];
            $machines = (isset($filter_data['machines']) AND count($filter_data['machines']) > 0) ? $filter_data['machines'] : [];
            $body_parts = (isset($filter_data['body_parts']) AND count($filter_data['body_parts']) > 0) ? $filter_data['body_parts'] : [];

            // if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            //     $words = explode(" ", trim($search_term));
            //     $string = array_map(function($word){
            //         return (strlen($word) > 3 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word));
            //     }, $words);
    
            //     $term = implode(" ",$string);
            //     $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE)) AND exercises.title LIKE '%{$term}%'";
            // }else{
            //     $search = "";
            // };
            if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
                $words = explode(" ", $search_term);
                $string = array_map(function($word){
                    return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
                }, $words);
    
                $search = 'AND (' . implode(" AND ",$string) . ')';
                // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
            }else{
                $search = "";
            };

            if(count($machines) > 0){
                // $machines_group_title = " GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines";
                $machines_sql = "AND exercises_machine.exercise_machine IN (" . implode(',', $machines) . ")";
                // $join .= " LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
                //         LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine";
            }
            if(count($body_parts) > 0){
                $body_parts_sql = "AND exercises_body_parts.exercise_body_parts IN (" . implode(',', $body_parts) . ")";
                $join .= " LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id 
                          LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts";
            }
        }

        // $sql = "SELECT exercises.id, exercises.title, exercises.status,
        //                     difficulty.title as diff,
        //                     GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids,
        //                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
        //                     FROM exercises
        //                     LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
        //                     LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
        //                     LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
        //                     LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
        //                     LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
        //                     LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
        //                     LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
        //                     LEFT JOIN difficulty on difficulty.id = exercises.difficulty
        //                     WHERE exercises.deleted_at IS NULL
        //                     AND exercises.status = 0
        //                     " . $search . "
        //                     " . $machines_sql . "
        //                     " . $body_parts_sql . "
        //                     GROUP BY exercises.id
        //                     ORDER BY exercises.created_at DESC
        //                     LIMIT $start, $limit";
        // echo '<pre>';
        // print_r($sql);
        // die();
        
        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $sql = "SELECT exercises.id, exercises.title, exercises.status, exercises.video_preview,
                            difficulty.title as diff,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids                            
                            FROM exercises
                            " . $join ."
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            " . $search . "
                            " . $machines_sql . "
                            " . $body_parts_sql . "
                            GROUP BY exercises.id
                            ORDER BY exercises.title ASC
                            LIMIT $start, $limit
                        ";
        // echo '<pre>';
        // print_r($sql);
        // die();

        $data = $this->query("SELECT exercises.id, exercises.title, exercises.status, exercises.video_preview,
                            difficulty.title as diff,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids                            
                            FROM exercises
                            " . $join ."
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            " . $search . "
                            " . $machines_sql . "
                            " . $body_parts_sql . "
                            GROUP BY exercises.id
                            ORDER BY exercises.title ASC
                            LIMIT $start, $limit
                        ")->getResultArray();
        return $data;
    }

    public function load_more_exercises_count($start = 0, $limit = 0, $filter = ''){
        $search = '';
        $machines_sql = '';
        $body_parts_sql = '';
        $join = '';
        $machines_group_title = '';

        if($filter != ''){
            $filter_data = json_decode($filter, TRUE);
            $search_term = $filter_data['search_term'];
            $machines = (isset($filter_data['machines']) AND count($filter_data['machines']) > 0) ? $filter_data['machines'] : [];
            $body_parts = (isset($filter_data['body_parts']) AND count($filter_data['body_parts']) > 0) ? $filter_data['body_parts'] : [];

            if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
                $words = explode(" ", $search_term);
                $string = array_map(function($word){
                    return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
                }, $words);
    
                $search = 'AND (' . implode(" AND ",$string) . ')';
            }else{
                $search = "";
            };

            if(count($machines) > 0){
                $machines_sql = "AND exercises_machine.exercise_machine IN (" . implode(',', $machines) . ")";
            }
            if(count($body_parts) > 0){
                $body_parts_sql = "AND exercises_body_parts.exercise_body_parts IN (" . implode(',', $body_parts) . ")";
                $join .= " LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id 
                          LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts";
            }
        }

        // $sql = "SELECT COUNT(exercises.id) as count                        
        //                     FROM exercises
        //                     " . $join ."
        //                     LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
        //                     LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
        //                     LEFT JOIN difficulty on difficulty.id = exercises.difficulty
        //                     LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
        //                     LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
        //                     WHERE exercises.deleted_at IS NULL
        //                     AND exercises.status = 0
        //                     " . $search . "
        //                     " . $machines_sql . "
        //                     " . $body_parts_sql . "
        //                     GROUP BY exercises.id
        //                 ";

        // echo '<pre>';
        // print_r($sql);
        // die();
        

        $data_count = $this->query("SELECT exercises.id, exercises.title, exercises.status,
                            difficulty.title as diff,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            GROUP_CONCAT(DISTINCT springs.id SEPARATOR ', ') AS all_exercise_springs_ids                            
                            FROM exercises
                            " . $join ."
                            LEFT OUTER JOIN exercises_springs ON exercises_springs.exercise_id = exercises.id
                            LEFT OUTER JOIN springs ON springs.id = exercises_springs.exercise_springs
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            " . $search . "
                            " . $machines_sql . "
                            " . $body_parts_sql . "
                            GROUP BY exercises.id
                            ORDER BY exercises.title ASC
                            
                        ")->getResultArray();
        $data = count($data_count);
        // foreach($data_count as $single){
        //     $data += $single['count'];
        // }
        return $data;
    }

    public function load_more_bulk_exercises($start = 0, $limit = 30, $filter = ''){
        $search = '';
        $machines_sql = '';
        $body_parts_sql = '';
        $join = '';

        if($filter != ''){
            $filter_data = json_decode($filter, TRUE);
            $search_term = $filter_data['search_term'];
            $machines = (isset($filter_data['machines']) AND count($filter_data['machines']) > 0) ? $filter_data['machines'] : [];
            $body_parts = (isset($filter_data['body_parts']) AND count($filter_data['body_parts']) > 0) ? $filter_data['body_parts'] : [];

            // if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            //     $words = explode(" ", trim($search_term));
            //     $string = array_map(function($word){
            //         return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            //     }, $words);

            //     $term = implode(" ",$string);
            //     $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
            // }else{
            //     $search = "";
            // };
            if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
                $words = explode(" ", $search_term);
                $string = array_map(function($word){
                    return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
                }, $words);
    
                $search = 'AND (' . implode(" AND ",$string) . ')';
                // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
            }else{
                $search = "";
            };
            if(count($body_parts) > 0){
                $body_parts_sql = "AND exercises_body_parts.exercise_body_parts IN (" . implode(',', $body_parts) . ")";
                $join .= " LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id 
                          LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts";
            }
            if(count($machines) > 0){
                $machines_sql = "AND exercises_machine.exercise_machine IN (" . implode(',', $machines) . ")";
                $join .= " LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id 
                        LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine";
            }
        }

        // $sql = "SELECT exercises.id, exercises.title
        //                             FROM exercises
        //                             " . $join . "
        //                             WHERE exercises.deleted_at IS NULL
        //                             AND exercises.status = 0
        //                             " . $search . "
        //                             " . $machines_sql . "
        //                             " . $body_parts_sql . "
        //                             GROUP BY exercises.id
        //                             ORDER BY exercises.created_at DESC
        //                             LIMIT $start, $limit
        //                 ";
        //                 echo '<pre>';
        //                 print_r($sql);
        //                 die();
                        
                        
        $data = $this->query("SELECT exercises.id, exercises.title, exercises.video_preview, exercises.image, exercises.video_thumb
                                    FROM exercises
                                    " . $join . "
                                    WHERE exercises.deleted_at IS NULL
                                    AND exercises.status = 0
                                    " . $search . "
                                    " . $machines_sql . "
                                    " . $body_parts_sql . "
                                    GROUP BY exercises.id
                                    ORDER BY exercises.title ASC
                                    LIMIT $start, $limit
                        ")->getResultArray();
        return $data;
    }

    public function all_micro_exercises($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, GROUP_CONCAT(DISTINCT machines.id SEPARATOR ',') AS exercise_machines_ids,
                            video_state.video_time as video_state,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            DATE_ADD(MAX(subscribers_exercises.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_exercises.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND exercises_machine.exercise_machine = 1
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_micro_exercises_home($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            video_state.video_time as video_state,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(subscribers_exercises.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND exercises.type = 0
                            AND exercises_machine.exercise_machine = 1
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_mega_exercises($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            video_state.video_time as video_state,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            DATE_ADD(MAX(subscribers_exercises.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_exercises.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND (exercises_machine.exercise_machine = 2 OR exercises_machine.exercise_machine = 13)
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_mega_exercises_home($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            video_state.video_time as video_state,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(subscribers_exercises.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.type = 0
                            AND exercises.status = 0
                            AND (exercises_machine.exercise_machine = 2 OR exercises_machine.exercise_machine = 13)
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_mini_exercises($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        // $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return (strlen($word) > 2 ? "+" : "") . str_replace('\'', '\'\'', str_replace('\'', '\'\'', $word)) . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            video_state.video_time as video_state,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(subscribers_exercises.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS purchased,
                            DATE_ADD(MAX(subscribers_exercises.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND (exercises_machine.exercise_machine = 3 OR exercises_machine.exercise_machine = 4)
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function filter_exercises($data){

        $limit_size =  ($data['limit'] != 0) ? " LIMIT " . $data['start'] . ", " . $data['limit'] : "";
		$sql_add = '';
		$join = '';
        $sql_selector = '';

		//WHERE
        $real_durations = [
            '0' => '< 600',
            '1' => '> 600 AND exercises.duration < 1500',
            '2' => '> 1500'
        ];
        if( (isset($data['machine']) AND $data['machine'] != '') OR
            (isset($data['accessories']) AND $data['accessories'] != '') OR
            (isset($data['body_parts']) AND $data['body_parts'] != '') OR
            (isset($data['language']) AND $data['language'] != '') OR
            (isset($data['difficulty']) AND $data['difficulty'] != '') OR
            (isset($data['duration']) AND $data['duration'] != '')
        ){
            $sql_add .=  "WHERE exercises.deleted_at IS NULL AND exercises.status = 0 ";
            if(isset($data['machine']) AND $data['machine'] != ''){
                $sql_add .= " AND exercises_machine.exercise_machine IN (" . ((isset($data['machine']) AND is_array($data['machine'])) ? implode(',', $data['machine']) : 0) . ") ";
            }
            if(isset($data['accessories']) AND $data['accessories'] != ''){
                $sql_add .= " AND exercises_accessories.exercise_accessories IN (" . ((isset($data['accessories']) AND is_array($data['accessories'])) ? implode(',', $data['accessories']) : 0) . ") ";
                $join .= 'LEFT JOIN exercises_accessories ON exercises_accessories.exercise_id = exercises.id ';
            }
            if(isset($data['body_parts']) AND $data['body_parts'] != ''){
                $sql_add .= " AND exercises_body_parts.exercise_body_parts IN (" . ((isset($data['body_parts']) AND !empty($data['body_parts']) AND is_array($data['body_parts'])) ? implode(',', $data['body_parts']) : 0) . ") ";
                $join .= 'LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id ';
            }
            if(isset($data['language']) AND $data['language'] != ''){
                $sql_add .=  " AND exercises.language IN (" . ((isset($data['language']) AND !empty($data['language']) AND is_array($data['language'])) ? implode(',', $data['language']) : 0) . ")";
                $join .= 'LEFT JOIN languages on languages.id = exercises.language ';
            }
            if(isset($data['difficulty']) AND $data['difficulty'] != ''){
                $sql_add .=  " AND exercises.difficulty IN (" . ((isset($data['difficulty']) AND !empty($data['difficulty']) AND is_array($data['difficulty'])) ? implode(',', $data['difficulty']) : 0) . ")";
            }
            if(isset($data['duration']) AND $data['duration'] != ''){
                $sql_add .=  " AND (exercises.duration ";
                if(is_array($data['duration'])){
                    foreach($data['duration'] as $key => $value){
                        $sql_add .= ' OR exercises.duration ' . $real_durations[$value];
                    }
                    $sql_add .= ")";
                }else{
                    $sql_add .= $real_durations[$data['duration']] . ")";
                }
            }
        }else{
            $sql_add .=  "WHERE exercises.deleted_at IS NULL AND exercises.status = 0 ";
        }
        if($data['order'] == 'countView desc'){
            $join = 'LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id ';
            $sql_selector .= 'COALESCE(x.cnt,0) AS countView, ';
        }

        // $sql = "SELECT exercises.id,exercises.image,exercises.title,exercises.slug,exercises.duration,exercises.video_thumb,exercises.video_preview,exercises.type,exercises.type,
        //                             GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises, 
        //                             GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
        //                             GROUP_CONCAT(DISTINCT body_position.title SEPARATOR ', ') AS body_position,
        //                             difficulty.title as diff,
        //                             IF((SELECT count(id) FROM exercises_rate WHERE exercise_id = exercises.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
        //                             (SELECT count(rate > 3) as rate FROM exercises_rate WHERE exercise_id = exercises.id GROUP BY exercise_id) as likeCount,
        //                             " . $sql_selector . "
        //                             video_state.video_time as video_state
        //                             FROM exercises
        //                             " . $join . "
        //                             LEFT JOIN exercises_body_position ON exercises_body_position.exercise_id = exercises.id
        //                             LEFT JOIN body_position ON body_position.id = exercises_body_position.exercise_body_position
        //                             LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
        //                             LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
        //                             LEFT JOIN difficulty on difficulty.id = exercises.difficulty
        //                             LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
        //                             " . $sql_add . "
        //                             GROUP BY exercises.id
        //                             ORDER BY " . $data['order'] . "
        //                             " . $limit_size . "
        //                         ";
        // echo '<pre>';
        // print_r($sql);
        // die();
        
        $result = $this->query("SELECT exercises.id,exercises.image,exercises.title,exercises.slug,exercises.duration,exercises.video_thumb,exercises.video_preview,exercises.type,exercises.type,
                                    GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises, 
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                                    GROUP_CONCAT(DISTINCT body_position.title SEPARATOR ', ') AS body_position,
                                    difficulty.title as diff,
                                    IF((SELECT count(id) FROM exercises_rate WHERE exercise_id = exercises.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                    (SELECT count(rate > 3) as rate FROM exercises_rate WHERE exercise_id = exercises.id GROUP BY exercise_id) as likeCount,
                                    " . $sql_selector . "
                                    video_state.video_time as video_state
                                    FROM exercises
                                    " . $join . "
                                    LEFT JOIN exercises_body_position ON exercises_body_position.exercise_id = exercises.id
                                    LEFT JOIN body_position ON body_position.id = exercises_body_position.exercise_body_position
                                    LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                    LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                    LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                                    LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'exercises')
                                    " . $sql_add . "
                                    GROUP BY exercises.id
                                    ORDER BY " . $data['order'] . "
                                    " . $limit_size . "
                                ")->getResultArray();

        // echo '<pre>';
        // print_r($result);
        // die();
                        
        if(count($result) > 0){
            foreach($result as $key => $single){
                // Machines
                // $machines_query = $this->query("SELECT 
                //                                     GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_exercises, 
                //                                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                //                                     FROM exercises_machine 
                //                                     LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                //                                     WHERE exercise_id = " . $single['id'] . "
                //                                 ")->getRowArray();
                // $data[$key]['machines_exercises'] = $machines_query['machines_exercises'];
                // Body Parts
                $body_parts_query = $this->query("SELECT GROUP_CONCAT(DISTINCT LOWER(REPLACE(body_parts.title, ' ', '_')) SEPARATOR ' ') AS body_parts_exercises 
                                                    FROM exercises_body_parts 
                                                    LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['body_parts_exercises'] = $body_parts_query['body_parts_exercises'];
                // Tempo
                $tempo_query = $this->query("SELECT GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo
                                                    FROM exercises_tempo 
                                                    LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                                                    WHERE exercise_id = " . $single['id'] . "
                                                ")->getRowArray();
                $data[$key]['all_exercise_tempo'] = $tempo_query['all_exercise_tempo'];
            }
        }

        return $result;
    }
    public function filter_exercises_admin($start = 0, $limit = 10, $search_term = NULL, $order = "exercises.created_at DESC", $filter_data = NULL){

        $sql_add = '';

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
            // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };


        $sql_add .=  "WHERE exercises.deleted_at IS NULL \nAND exercises.status IN (0,1) \nAND exercises.type = 0 ";

        if($filter_data != NULL){
            $real_durations = [
                '0' => 'exercises.duration < 600',
                '1' => '(exercises.duration > 600 AND exercises.duration < 1200)',
                '2' => '(exercises.duration > 1200 AND exercises.duration < 1800)',
                '3' => '(exercises.duration > 1800 AND exercises.duration < 2400)',
                '4' => '(exercises.duration > 2400 AND exercises.duration < 3000)',
                '5' => '(exercises.duration > 3000 AND exercises.duration < 3600)',
                '6' => 'exercises.duration > 3600'
            ];
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= "\n AND exercises_machine.exercise_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= "\n AND exercises_body_parts.exercise_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= "\n AND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  "\n AND exercises.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  "\n AND exercises.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            if(is_array($filter_data['duration'][0]) AND count($filter_data['duration'][0]) > 0){
                $sql_add .=  "\n AND (";
                $durations = json_decode($filter_data['duration'][0], TRUE);
                if(is_array($durations)  AND count($durations) > 0){
                    foreach($durations as $key => $value){
                        $sql_add .= $real_durations[$value] . '' . ((count($durations) == ($key+1)) ? '' : ' OR ');
                    }
                }else{
                    $sql_add .= "1 = 1";
                }
                $sql_add .= ") AND exercises.duration != '' AND exercises.duration != 'NaN' AND exercises.duration IS NOT NULL ";
            }
        }
        
        // $result_query = "SELECT exercises.id, exercises.image, exercises.video_thumb, exercises.status, exercises.type, exercises.title, exercises.aka, exercises.duration, exercises.created_at,
        //                     COALESCE(x.cnt,0) AS countView,
        //                     COALESCE(y.rate,0) AS exerciseRate,
        //                     COALESCE(s.sum_price,0) AS classEarned,
        //                     CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                     difficulty.title as diff,
        //                     teachers.slug  as teach_slug,
        //                     GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
        //                     FROM exercises
        //                     LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
        //                     LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
        //                     LEFT OUTER JOIN (SELECT exercise_id, SUM(seller_earning) as sum_price FROM subscribers_exercises GROUP BY exercise_id) s on s.exercise_id = exercises.id
        //                     LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
        //                     LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
        //                     LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
        //                     LEFT JOIN difficulty on difficulty.id = exercises.difficulty
        //                     LEFT JOIN languages on languages.id = exercises.language
        //                     LEFT JOIN teachers on teachers.id = exercises.teacher
        //                     " . $sql_add . "
        //                     " . $search . "
        //                     GROUP BY exercises.id
        //                     ORDER BY " . $order . "
        //                     LIMIT " . $start . ", " . $limit . "
        //                 ";

        // echo '<pre>';
        // print_r($result_query);
        // die();
        
        $result = $this->query("SELECT exercises.id, exercises.image, exercises.video_thumb, exercises.video_preview, exercises.status, exercises.type, exercises.title, exercises.aka, exercises.duration, exercises.created_at,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS exerciseRate,
                            COALESCE(s.sum_price,0) AS classEarned,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            difficulty.title as diff,
                            teachers.slug  as teach_slug,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, SUM(seller_earning) as sum_price FROM subscribers_exercises GROUP BY exercise_id) s on s.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN languages on languages.id = exercises.language
                            LEFT JOIN teachers on teachers.id = exercises.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            LIMIT " . $start . ", " . $limit . "
                        ")->getResultArray();

        // echo "<pre>";
        // echo $result_query;
        // var_dump($result);
        // die();

        return $result;
    }
    public function filter_exercises_admin_count($start = 0, $limit = 10, $search_term = NULL, $order = "exercises.created_at DESC", $filter_data = NULL){

        $sql_add = '';

        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ", $string) . ')';
            // $search =  "AND (MATCH(exercises.title) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        };


        $sql_add .=  "WHERE exercises.deleted_at IS NULL \nAND exercises.status IN (0,1) \nAND exercises.type = 0 ";

        if($filter_data != NULL){
            $real_durations = [
                '0' => 'exercises.duration < 600',
                '1' => '(exercises.duration > 600 AND exercises.duration < 1200)',
                '2' => '(exercises.duration > 1200 AND exercises.duration < 1800)',
                '3' => '(exercises.duration > 1800 AND exercises.duration < 2400)',
                '4' => '(exercises.duration > 2400 AND exercises.duration < 3000)',
                '5' => '(exercises.duration > 3000 AND exercises.duration < 3600)',
                '6' => 'exercises.duration > 3600'
            ];
            if(isset($filter_data['machine']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) != ""){
                $sql_add .= "\n AND exercises_machine.exercise_machine IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['machine']))) . ") ";
            }
            if(isset($filter_data['body_parts']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) != ""){
                $sql_add .= "\n AND exercises_body_parts.exercise_body_parts IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['body_parts']))) . ") ";
            }
            if(isset($filter_data['teacher']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) != ""){
                $sql_add .= "\n AND teachers.id IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['teacher']))) . ") ";
            }
            if(isset($filter_data['difficulty']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) != ""){
                $sql_add .=  "\n AND exercises.difficulty IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['difficulty']))) . ")";
            }
            if(isset($filter_data['language']) AND str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) != ""){
                $sql_add .=  "\n AND exercises.language IN (" . str_replace("[", "", str_replace("]","", implode(',', $filter_data['language']))) . ")";
            }
            if(is_array($filter_data['duration'][0]) AND count($filter_data['duration'][0]) > 0){
                $sql_add .=  "\n AND (";
                $durations = json_decode($filter_data['duration'][0], TRUE);
                if(is_array($durations)  AND count($durations) > 0){
                    foreach($durations as $key => $value){
                        $sql_add .= $real_durations[$value] . '' . ((count($durations) == ($key+1)) ? '' : ' OR ');
                    }
                }else{
                    $sql_add .= "1 = 1";
                }
                $sql_add .= ") AND exercises.duration != '' AND exercises.duration != 'NaN' AND exercises.duration IS NOT NULL ";
            }
        }
        
        $result = $this->query("SELECT COUNT(exercises.id) as count
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, SUM(seller_earning) as sum_price FROM subscribers_exercises GROUP BY exercise_id) s on s.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN languages on languages.id = exercises.language
                            LEFT JOIN teachers on teachers.id = exercises.teacher
                            " . $sql_add . "
                            " . $search . "
                            GROUP BY exercises.id
                        ")->getResultArray();

        return $result;
    }

    public function similar_exercises($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc", $id = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "AND exercises.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT exercises.*,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS exerciseRate, difficulty.title as diff,
                            DATE_ADD(MAX(subscribers_exercises.date), INTERVAL 1 DAY) as expiry_rent_date,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                            IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_exercises.purchase_type = 'buy' OR subscribers_exercises.purchase_type IS NULL)
                                    ) as exercises_purchased
                            ), 1, 0) as purchased,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_exercises.purchase_type = 'rent' AND DATE_ADD(subscribers_exercises.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as exercises_rented
                            ), 1, 0) as rented,
                            IF(exercises.id IN (
                                    SELECT * FROM (
                                            SELECT exercise_id FROM subscribers_exercises_watched  WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as exercises_watched
                            ), 1, 0) as watched
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN subscribers_exercises ON (subscribers_exercises.exercise_id = exercises.id)
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND exercises.id != " . $id . "
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function current($slug = ''){
        $data = $this->query("SELECT exercises.*, difficulty.title as diff,
                                IF((SELECT count(id) FROM exercises_rate WHERE exercise_id = exercises.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                IF((SELECT count(id) FROM subscribers_favs WHERE class_id = exercises.id AND type = 'exercises' AND subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as added_to_watch_later,
                                (SELECT count(rate > 3) as rate FROM exercises_rate WHERE exercise_id = exercises.id GROUP BY exercise_id) as likeCount,
                                COALESCE(x.cnt,0) AS countView,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT body_position.title SEPARATOR ', ') AS all_body_position,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_exercise_accessories,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_exercise_accessories_shopify,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_exercise_springs,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                                GROUP_CONCAT(DISTINCT tensions.title ORDER BY tensions.sort ASC SEPARATOR ', ') AS all_exercise_tensions,
                                GROUP_CONCAT(DISTINCT terminology.title ORDER BY terminology.sort ASC SEPARATOR ', ') AS all_exercise_terminology,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_exercise_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_exercise_machines_short
                                FROM exercises
                                LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x ON x.exercise_id = exercises.id
                                LEFT JOIN exercises_accessories ON  exercises_accessories.exercise_id = exercises.id
                                LEFT JOIN accessories ON accessories.id = exercises_accessories.exercise_accessories
                                LEFT JOIN exercises_tensions ON  exercises_tensions.exercise_id = exercises.id
                                LEFT JOIN tensions ON tensions.id = exercises_tensions.exercise_tensions
                                LEFT JOIN exercises_terminology ON  exercises_terminology.exercise_id = exercises.id
                                LEFT JOIN terminology ON terminology.id = exercises_terminology.exercise_terminology
                                LEFT JOIN exercises_springs ON  exercises_springs.exercise_id = exercises.id
                                LEFT JOIN springs ON springs.id = exercises_springs.exercise_springs
                                LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                                LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                                LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                                LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                LEFT JOIN exercises_body_position ON exercises_body_position.exercise_id = exercises.id
                                LEFT JOIN body_position ON body_position.id = exercises_body_position.exercise_body_position
                                LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                WHERE exercises.deleted_at IS NULL
                                AND exercises.status = 0
                                AND exercises.slug = '" . $slug . "'
                            ")->getRowArray();
        return $data;
    }

    public function single($id = ''){
        $data = $this->query("SELECT exercises.*, difficulty.title as diff,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS exerciseRate,
                                GROUP_CONCAT(DISTINCT body_position.title SEPARATOR ', ') AS all_body_position,
                                GROUP_CONCAT(DISTINCT terminology.title SEPARATOR ', ') AS all_terminology,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_exercise_accessories,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_exercise_accessories_shopify,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_exercise_springs,
                                GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_exercise_tempo,
                                GROUP_CONCAT(DISTINCT tensions.title ORDER BY tensions.sort ASC SEPARATOR ', ') AS all_exercise_tensions,
                                GROUP_CONCAT(DISTINCT tension.title ORDER BY tension.sort ASC SEPARATOR ', ') AS all_exercise_tension,
                                GROUP_CONCAT(DISTINCT range_of_motion.title ORDER BY range_of_motion.sort ASC SEPARATOR ', ') AS all_exercise_range_of_motion,
                                GROUP_CONCAT(DISTINCT direction.title ORDER BY direction.sort ASC SEPARATOR ', ') AS all_exercise_direction,
                                GROUP_CONCAT(DISTINCT exercise_type.title ORDER BY exercise_type.sort ASC SEPARATOR ', ') AS all_exercise_exercise_type,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_exercise_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_exercise_machines_short
                                FROM exercises
                                LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x ON x.exercise_id = exercises.id
                                LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y ON y.exercise_id = exercises.id
                                LEFT JOIN exercises_accessories ON  exercises_accessories.exercise_id = exercises.id
                                LEFT JOIN accessories ON accessories.id = exercises_accessories.exercise_accessories
                                LEFT JOIN exercises_tensions ON  exercises_tensions.exercise_id = exercises.id
                                LEFT JOIN tensions ON tensions.id = exercises_tensions.exercise_tensions
                                LEFT JOIN exercises_springs ON  exercises_springs.exercise_id = exercises.id
                                LEFT JOIN springs ON springs.id = exercises_springs.exercise_springs
                                LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                                LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                                LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                                LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                LEFT JOIN exercises_body_position ON exercises_body_position.exercise_id = exercises.id
                                LEFT JOIN body_position ON body_position.id = exercises_body_position.exercise_body_position
                                LEFT JOIN exercises_terminology ON exercises_terminology.exercise_id = exercises.id
                                LEFT JOIN terminology ON terminology.id = exercises_terminology.exercise_terminology

                                LEFT JOIN exercises_direction ON exercises_direction.exercise_id = exercises.id
                                LEFT JOIN direction ON direction.id = exercises_direction.exercise_direction

                                LEFT JOIN exercises_range_of_motion ON exercises_range_of_motion.exercise_id = exercises.id
                                LEFT JOIN range_of_motion ON range_of_motion.id = exercises_range_of_motion.exercise_range_of_motion

                                LEFT JOIN exercises_tension ON exercises_tension.exercise_id = exercises.id
                                LEFT JOIN tension ON tension.id = exercises_tension.exercise_tension

                                LEFT JOIN exercises_exercise_type ON exercises_exercise_type.exercise_id = exercises.id
                                LEFT JOIN exercise_type ON exercise_type.id = exercises_exercise_type.exercise_exercise_type

                                LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                WHERE exercises.deleted_at IS NULL
                                AND exercises.status = 0
                                AND exercises.id = '" . $id . "'
                            ")->getRowArray();
        return $data;
    }

    public function cron(){
        $data = $this->query("SELECT exercises.*,
                                FROM exercises
                                WHERE exercises.deleted_at IS NULL
                                AND exercises.status = 0
                                AND exercises.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

    function prev_next($id = 0){
		$result = array('prev' => NULL, 'next' => NULL,);

		if($id != 0){
            $tmp = $this->query("SELECT * FROM exercises WHERE status = 0 AND deleted_at IS NULL ORDER BY created_at desc")->getResultArray();

			if(count($tmp) > 1)
			{
				$total = count($tmp);
				$index_list = array_column($tmp, 'id');
				$index_id = array_search($id, array_column($tmp, 'id'));
				if($index_id !== FALSE){
					if($index_id < $total - 1){
						$result['next'] = $this->where(['id' => $index_list[$index_id + 1]])->first();
					}else{
						$result['next'] = $this->where(['id' => $index_list[0]])->first();
					}

					if($index_id > 0){
						$result['prev'] = $this->where(['id' => $index_list[$index_id - 1]])->first();
					}else{
						$result['prev'] = $this->where(['id' => $index_list[$total - 1]])->first();
					}
				}
			}
		}

        // echo "<pre>";
        // var_dump($result);
        // die();

		return $result;
	}

    public function all_micro_exercises_finder($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ",$string) . ')';
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*
                            FROM exercises
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND exercises_machine.exercise_machine = 1
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    public function all_mega_exercises_finder($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ",$string) . ')';
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*
                            FROM exercises
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND (exercises_machine.exercise_machine = 2 OR exercises_machine.exercise_machine = 13)
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    public function all_mini_exercises_finder($start = 0, $limit = 0, $search_term = NULL, $order = "exercises.updated_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(exercises.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ",$string) . ')';
        }else{
            $search = "";
        };

        $data = $this->query("SELECT exercises.*
                            FROM exercises
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            WHERE exercises.deleted_at IS NULL
                            AND exercises.status = 0
                            AND (exercises_machine.exercise_machine = 3 OR exercises_machine.exercise_machine = 4)
                            " . $search . "
                            GROUP BY exercises.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }	protected function prepare_data(array $data)
	{
		return $data;
	}
    
}