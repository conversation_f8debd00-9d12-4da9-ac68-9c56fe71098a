<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<link rel="stylesheet" type="text/css" href="css/mvp.css" />
<link rel="stylesheet" type="text/css" href="css/flat.css?3" />
<link href="css/videojs.custom.css" rel="stylesheet">
<style>
.video-row iframe {
	width: 100%;
}
</style>
</head>
<body class="collection-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <!-- <div class="px-100 small-header-image" style="background: url(images/single-collection-header-bg.jpg) no-repeat center center / cover"> -->
    <section class="pt-5 pb-5 px-120 pb-mob-2 pt-mob-0">
        <div class="container1530">
            <div class="row big-gap video-row">
                <div class="col-6">
                    <!-- <script type="text/javascript" src="https://www.bigmarker.com/conferences/639f99ee2b8b/widget/live.js"></script>
                    <iframe id="bigmarker_embed_conference_room" width="720" height="405" allowfullscreen="allowfullscreen" frameborder="0" style="width: 100%;"></iframe> -->
                    <?php echo $current['stream_url']; ?>

                    <div class="my-4 desktop">
                        <a href="javascript:;" class="flex aic jcl link link-black black f-14 semibold text-uppercase" data-popup="share-popup" title="Share"><img src="images/share-icon.svg" alt="" class="img-fluid mr-1" width="16" height="11" style="filter: invert(1)"> Share</a>
                    </div>
                </div>
                <div class="col-6 px-mob-3 mt-mob-4">
                    <!-- <h1 class="h2 mb-3 mobile"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?></h1> -->
                    <div class="row">
                        <div class="col-12">
                            <div class="mobile-flex-vertical">
                                <div class="w100">
                                    <h1 class="h3 mb-3 line-height-small"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?></h1>
                                    <div class="flex aic">
                                        <div class="avatar mr-2 mr-mob-1">
                                            <a href="teachers/sebastien-lagree">
                                                <img src="uploads/teachers/1637159490_4e9da733185a15ba9032.jpg" alt="" class="img-fluid">
                                            </a>
                                        </div>
                                        <div class="f-14 light media-desc midGray">with <a href="teachers/sebastien-lagree" class="link link-black black text-underline">Sebastien Lagree</a></div>
                                        <span class="flex aic f-14 light ml-0 desktop-flex">
                                            <span class="desktop-inline mr-05">, </span> In: <?php echo (isset($current['location']) AND $current['location'] != '') ? $current['location'] : ''; ?>,
                                            <?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y', strtotime($current['date'])) : ''; ?>
                                            at <?php echo (isset($current['time']) AND $current['time'] != '') ? $current['time'] : ''; ?>
                                            <small class="local_time gray"></small>
                                        </span>
                                    </div>
                                    <span class="flex aic f-14 light mobile-flex mt-3">
                                        In <?php echo (isset($current['location']) AND $current['location'] != '') ? $current['location'] : ''; ?>,
                                        <?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y', strtotime($current['date'])) : ''; ?>
                                        at <?php echo (isset($current['time']) AND $current['time'] != '') ? $current['time'] : ''; ?>
                                        <small class="local_time gray"></small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="mobile mb-mob-3 mt-mob-3">
                    <div class="row row-vertical ail nowrap pt-mob-0 mb-3">
                        <!-- <div class="col-12">
                            <p class="light">
                                In <?php echo (isset($current['location']) AND $current['location'] != '') ? $current['location'] : ''; ?>,
                                <?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y', strtotime($current['date'])) : ''; ?>
                                at <?php echo (isset($current['time']) AND $current['time'] != '') ? $current['time'] : ''; ?>
                                <small class="local_time gray"></small>
                            </p>
                        </div> -->
                        <div class="col-12">
                            <p class="f-14 light mt-3 mt-mob-0 mb-mob-0 lh-30"><?php echo (isset($current['content']) AND $current['content'] != '') ? $current['content'] : ''; ?></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 flex-vertical mb-5 mb-mob-0">
                            <?php if(isset($shopify_machines) AND count($shopify_machines) > 0){ ?>
                                <!-- <hr class="mt-0 mb-3" /> -->
                                <h3 class="f-1 bold mb-3">MACHINE USED:</h3>
                                <?php foreach($shopify_machines as $key => $single){ ?>
                                    <div class="single-class-new-row">
                                        <span class="class-new-row-image"><img src="<?php echo $single['product']['image']['src']; ?>" alt="" class="img-fluid" width="70" /></span>
                                        <span class="flex flex-column jcc">
                                            <span class="f-1 line-height-small mb-1 medium"><?php echo ($shopify_machines_titles[$key] == 'Microformer') ? 'The Micro' : $shopify_machines_titles[$key]; ?></span>
                                            <span class="f-1 line-height-small flex midGray"><?php echo ($shopify_machines_titles[$key] == 'Microformer') ? "STARTING AT" : ''; ?> $<?php echo number_format($single['product']['variants'][0]['price'], 0) ?></span>
                                        </span>
                                        <a href="https://www.shopmaximumfitness.com/products/<?php echo $single['product']['handle']; ?>" class="btn btn-xs btn-border ml-auto h30" title="BUY NOW" target="_blank">BUY</a>
                                    </div>
                                <?php } ?>
                            <?php } ?>
                            <?php if(isset($shopify_accessories) AND count($shopify_accessories) > 0){ ?>
                                <h3 class="f-1 bold line-height-small my-3">ACCESSORIES USED:</h3>
                                <?php foreach($shopify_accessories as $key => $single){ ?>
                                    <div class="single-class-new-row">
                                        <span class="class-new-row-image"><img src="<?php echo $single['product']['image']['src']; ?>" alt="" class="img-fluid" width="70" /></span>
                                        <span class="flex flex-column jcc">
                                            <span class="f-1 line-height-small mb-1 medium"><?php echo $single['product']['handle']; ?></span>
                                            <span class="f-1 line-height-small flex midGray">$<?php echo number_format($single['product']['variants'][0]['price'], 0) ?></span>
                                        </span>
                                        <a href="https://www.shopmaximumfitness.com/products/<?php echo $single['product']['handle']; ?>" class="btn btn-xs btn-border ml-auto h30" title="BUY NOW" target="_blank">BUY</a>
                                    </div>
                                <?php } ?>
                            <?php } ?>
                            <!-- <hr class="mt-5 mb-0" /> -->
                            <!-- <div class="single-class-row">
                                <span class="class-row-left">Machine:</span>
                                <span class="class-row-right"><?php echo (isset($current['all_class_machines_short']) AND $current['all_class_machines_short'] != '') ? $current['all_class_machines_short'] : ''; ?></span>
                            </div> -->
                            <hr class="mt-0 mb-3" />
                            <h3 class="f-1 bold mb-3">MACHINE USED:</h3>
                            <div class="single-class-new-row">
                                <span class="class-new-row-image"><img src="https://cdn.shopify.com/s/files/1/0342/7952/2442/products/Micro1A2048x2048.jpg?v=1635447969" alt="" class="img-fluid" width="70"></span>
                                <span class="flex flex-column jcc">
                                    <span class="f-1 line-height-small mb-1 medium">The Micro</span>
                                    <span class="f-1 line-height-small flex midGray">STARTING AT $890</span>
                                </span>
                                <a href="https://www.shopmaximumfitness.com/products/the-micro" class="btn btn-xs btn-border ml-auto h30" title="BUY NOW" target="_blank">BUY</a>
                            </div>
                            <hr class="mt-5 mb-0">
                            <?php if(isset($current['all_class_springs']) AND $current['all_class_springs'] != ''){ ?>
                            <div class="single-class-row">
                                <span class="class-row-left">Spring Load:</span>
                                <span class="class-row-right"><?php echo (isset($current['all_class_springs']) AND $current['all_class_springs'] != '') ? $current['all_class_springs'] : ''; ?></span>
                            </div>
                            <?php } ?>
                            <?php if(isset($current['all_body_parts']) AND $current['all_body_parts'] != ''){ ?>
                            <div class="single-class-row">
                                <span class="class-row-left">Body Parts:</span>
                                <span class="class-row-right"><?php echo (isset($current['all_body_parts']) AND $current['all_body_parts'] != '') ? $current['all_body_parts'] : ''; ?></span>
                            </div>
                            <?php } ?>
                            <div class="single-class-row">
                                <span class="class-row-left">Duration:</span>
                                <span class="class-row-right"><?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?></span>
                            </div>
                            <div class="single-class-row">
                                <span class="class-row-left">Difficulty:</span>
                                <span class="class-row-right"><?php echo (isset($current['diff']) AND $current['diff'] != '') ? $current['diff'] : ''; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
<?php if(!empty($logged_user) AND $logged_user['stripe_subscription'] != NULL AND $started){ ?>
<script type="text/javascript" src="js/new.js"></script>
<script type="text/javascript">
jQuery(document).ready(function($) {
    var settings = {
        sourcePath: "",
        instanceName: "player1",
        activePlaylist: ".playlist-video",
        activeItem: 0,
        volume: 1,
        autoPlay: false,
        autoPlayAfterFirst: true,
        randomPlay: false,
        loopingOn: true,
        mediaEndAction: false,
        aspectRatio: 1, // 1
        facebookAppId: "",
        youtubeAppId: "AIzaSyDQXQJhoY1TBwY_0NtKlXDDICXzsKLVv9I",
        playlistOpened: false,
        useKeyboardNavigationForPlayback: true,
        truncatePlaylistDescription: true,
        rightClickContextMenu: "custom",
        playlistItemContent: "thumb,title, description,duration",
        vrInfoImage: "data/360.png",
        playlistScrollTheme: "dark",
        elementsVisibilityArr: [
            {
                width: 500,
                elements: [
                    "play",
                    "next",
                    "seekbar",
                    "fullscreen",
                    "volume",
                ]
            }
        ],
        skin: "flat-light",
        playlistPosition: "no-playlist",
        playerType: "normal",
        playlistScrollType: "perfect-scrollbar",
        useSearchBar: false,
        liveui:true
    }
    $("#lod-video").mvp(settings);
});
function iOS() {
  return [
    'iPad Simulator',
    'iPhone Simulator',
    'iPod Simulator',
    'iPad',
    'iPhone',
    'iPod'
  ].includes(navigator.platform)
  // iPad on iOS 13 detection
  || (navigator.userAgent.includes("Mac") && "ontouchend" in document)
}
// if(iOS()){
//     $('#lod-video').html('<video type="application/x-mpegURL" poster="<?php // echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ''; ?>" controls style="width: 100%"><source src="<?php // echo (isset($current['stream_url']) AND $current['stream_url'] != '') ? $current['stream_url'] : ''; ?>""></source></video>')
//     console.log('IPHONE');
// }
</script>
<?php } ?>
<script>
    // var big_date = '<?php echo date('Y-m-d\TH:i:s', strtotime($event_time)); ?>' + '-08:00';
    // $('.local_time').text('(' + new Date(big_date).toLocaleString() + ' local time)');
</script>
</body>
</html>