<?php namespace App\Models;

use CodeIgniter\Model;

class SurveysQuestionsModel extends Model
{
    protected $table = 'surveys_questions';
	protected $allowedFields = ['title', 'survey_id', 'type', 'mandatory'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function single_survey($id = ''){
        $SurveysModel = model('SurveysModel');

        $data = $SurveysModel->where(['id' => $id])->first();
        if(!empty($data)){
            $data['questions'] = $this->query("SELECT id, title, type, mandatory
                                            FROM surveys_questions
                                            WHERE deleted_at IS NULL
                                            AND survey_id = " . $id . "
                                            ORDER BY sort desc, id asc
                                        ")->getResultArray();

            if(!empty($data['questions'])){
                foreach($data['questions'] as $key => $single){
                    $answer = $this->query("SELECT id, title, type, question_id FROM surveys_answers WHERE deleted_at IS NULL AND parent_id = 0 AND question_id = " . $single['id'] . " ORDER BY id ASC")->getResultArray();
                    if(!empty($answer)){
                        foreach($answer as $key2 => $single_answer){
                            $data['questions'][$key]['answer'][$key2]['id'] = $single_answer['id'];
                            $data['questions'][$key]['answer'][$key2]['title'] = $single_answer['title'];
                            $data['questions'][$key]['answer'][$key2]['type'] = $single_answer['type'];
                            $data['questions'][$key]['answer'][$key2]['question_id'] = $single_answer['question_id'];
                            $answer_options = $this->query("SELECT id, title, question_id, type FROM surveys_answers WHERE deleted_at IS NULL AND parent_id = " . $single_answer['id'] . " ORDER BY id ASC")->getResultArray();
                            if(!empty($answer_options)){
                                foreach($answer_options as $key3 => $single_answer_options){
                                    $data['questions'][$key]['answer'][$key2]['answer_options'][$key3]['id'] = $single_answer_options['id'];
                                    $data['questions'][$key]['answer'][$key2]['answer_options'][$key3]['title'] = $single_answer_options['title'];
                                    $data['questions'][$key]['answer'][$key2]['answer_options'][$key3]['type'] = $single_answer_options['type'];
                                    $data['questions'][$key]['answer'][$key2]['answer_options'][$key3]['question_id'] = $single_answer_options['question_id'];
                                }
                            }else{
                                $data['questions'][$key]['answer'][$key2]['answer_options'] = NULL;
                            }
                        }
                    }
                }
            }
        }

        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}
