<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Welcome <?php echo $logged_user['firstname']; ?>!</h1>
                <div class="ml-auto">
                    <a href="admin/classes/edit" class="btn black-bg white" title="Upload">Upload</a>
                    <a href="admin/classes/multi" class="btn btn-border white-bg black ml-2 ml-mob-5" title="Bulk">Bulk</a>
                </div>
            </div>
        </div>
        <div class="container pb-100">
            <div class="row big-gap dashboard-wrapper">
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 f-14">In The Numbers</h3>
                        <div class="dashboard-widget-list f-14">
                            <div class="flex aic jcsb mb-1">
                                <span class="normal"><a href="admin/classes" class="link link-red black">Classes</a></span>
                                <span class="red"><a href="admin/classes" class="link link-red black"><?php echo number_format($all_classes); ?></a></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal"><a href="admin/collections" class="link link-red black">Collections</a></span>
                                <span class="red"><a href="admin/collections" class="link link-red black"><?php echo number_format(count($all_collections)); ?></a></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal"><a href="admin/exercises" class="link link-red black">Exercises</a></span>
                                <span class="red"><a href="admin/exercises" class="link link-red black"><?php echo number_format($all_exercises); ?></a></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal"><a href="admin/subscribers" class="link link-red black">Subscribers</a></span>
                                <span class="red"><a href="admin/subscribers" class="link link-red black"><?php echo number_format(count($all_subscribers)); ?></a></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal"><a href="admin/teachers" class="link link-red black">Teachers</a></span>
                                <span class="red"><a href="admin/teachers" class="link link-red black"><?php echo number_format(count($all_teachers)); ?></a></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 f-14">Earnings</h3>
                        <div class="dashboard-widget-list f-14">
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Total earnings</span>
                                <span class="red">$0<?php // echo number_format($earnings['total'] / 100, 2, '.', ',') ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Last month</span>
                                <span class="red">$0<?php // echo number_format($earnings['month'] / 100, 2, '.', ',') ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">&nbsp;</span>
                                <span class="red">&nbsp;</span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">This week (<?php echo date('m/d', strtotime("-1 week")) ?> - <?php echo date('m/d') ?>)</span>
                                <span class="red">$0<?php // echo number_format($earnings['week'] / 100, 2, '.', ',') ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Today</span>
                                <span class="red">$0<?php // echo number_format($earnings['today'] / 100, 2, '.', ',') ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 f-14">Classes Statistics</h3>
                        <div class="dashboard-widget-list f-14">
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Total views</span>
                                <span class="red"><?php echo number_format($views) ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Last month</span>
                                <span class="red"><?php echo number_format($last_month) ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">&nbsp;</span>
                                <span class="red">&nbsp;</span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">This week (<?php echo date('m/d', strtotime("-1 week")) ?> - <?php echo date('m/d') ?>)</span>
                                <span class="red"><?php echo number_format($last_week) ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Today</span>
                                <span class="red"><?php echo number_format($today) ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 f-14">Subscribers</h3>
                        <div class="dashboard-widget-list f-14">
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Total Subscriptions</span>
                                <span class="red">0<?php // echo $subscribers['total'] ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Last Month</span>
                                <span class="red">+0<?php // echo $subscribers['last_month'] ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Montly subscriptions</span>
                                <span class="red">0<?php // echo $subscribers['month'] ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Annual subscriptions</span>
                                <span class="red">0<?php // echo $subscribers['year'] ?></span>
                            </div>
                            <div class="flex aic jcsb mb-1">
                                <span class="normal">Not subscribed</span>
                                <span class="red">0<?php // echo $not_subscribed ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 pb-05 f-14">Most Viewed Classes</h3>
                        <div class="dashboard-widget-list">
<?php foreach($most_viewed_classes as $single){ ?>
                            <div class="class-item flex aic mt-2 pb-1">
                                <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" style="max-width: 130px;width: 130px; height: 73px; object-fit: cover" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-1"><?php echo (isset($single['countView']) AND $single['countView'] != '') ? number_format($single['countView']) : ''; ?> views</span>
                                </div>
                            </div>
<?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-5">
                    <div class="dashboard-widget">
                        <h3 class="dashboard-widget-title mb-4 pb-05 f-14">Best Rated Classes</h3>
                        <div class="dashboard-widget-list">
<?php foreach($best_rated_classes as $single){ ?>
                            <div class="class-item flex aic mt-2 pb-1">
                                <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" style="max-width: 130px;width: 130px; height: 73px; object-fit: cover" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-1"><?php echo (isset($single['classRate']) AND $single['classRate'] != '') ? number_format($single['classRate'], 1) : ''; ?> <i class="icon-star" style="position: relative;top: 1px;"></i></span>
                                </div>
                            </div>
<?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="logdesk-popup">
      <div class="login-desktop">
        <img src="admin_assets_new/images/icon-desktop960.svg">
		<p>For the best user experience, continue with admin management on desktop device.</p>
      </div> 
    </div>

</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>



<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>