<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
	top: 0;
	margin-top: 40px;
	left: -26px;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 25px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 155px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
	flex: 1;
	max-width: 30px;
	margin-left: auto !important;
	align-self: center;
}
.upload-image.big-uplad-image {
	width: 290px;
	height: 150px;
}
#main_form h4,
#main_form h3.h4 {
	font-size: 15px !important;
}
.ck.ck-editor__main > .ck-editor__editable {
	border-color: var(--ck-color-base-border);
	min-height: 200px;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container pt-100">
            <div class="flex aic jcsb">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Page</h1>
                <a href="admin/pages" class="btn btn-border white-bg black ml-2" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-80 mb-4">
        </div>
        <form action="admin/pages/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <!-- <p class="midGray mb-5">Select or upload a photo that is your main photo.</p> -->
            <div class="row mb-5">
                <div class="col-8">
                    <h4 class="mb-2">Featured Photo</h4>
                    <div class="image_container flex aic">
                        <div class="upload-image big-uplad-image" id="image_container">
                            <input type="file" name="image" id="image">
                            <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                        </div>
                        <div class="midGray f-12">
                        <span>Max. file size is 300kb. Supported formats: JPG.<br>Desirable size: 1920px x 650px.</span>
                            <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                                <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                                <a href="javascript:;" class="link link-midGray midGray text-underline remove_image">Remove</a>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <h4 class="mb-2">Date published</h4>
                    <div class="input-container" id="date_container">
                        <input type="text" class="line-input datepicker" placeholder="Date"  name="date" value="<?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y', strtotime($current['date'])) : ''; ?>" />
                        <span class="calendar-icon"></span>
                    </div>
                    <h4 class="mb-2 mt-5">Template</h4>
                    <div class="input-container" id="template_container" style="text-transform: capitalize">
                        <select name="template" id="template" class="line-select" style="text-transform: capitalize">
                            <option value="" style="text-transform: capitalize">Select template</option>
                            <?php
                                $dir = '../app/Views/front/pages';
                                $files1 = scandir($dir);
                                foreach($files1 as $single){
                                    if($single != '.' AND $single != '..'  AND $single != 'old' AND $single != 'single_view.php'){
                            ?>
                                <option value="<?php echo substr($single, 0, -9); ?>" <?php echo (isset($current['template']) AND $current['template'] == substr($single, 0, -9)) ? "SELECTED " : ""; ?> style="text-transform: capitalize"><?php echo ($single == 'index_view.php') ? 'Homepage' : str_replace('-', ' ', str_replace('_', ' ', substr($single, 0, -9))); ?></option>
                            <?php
                                    }
                                }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row mb-5">
                <div class="col-8">
                    <h4 class="mb-2">Page Title <small class="f-10 midGray light ml-1">(up to 100 characters)</small></h4>
                    <div class="input-container mb-0" id="title_container">
                        <input type="text" name="title" maxlength="100" class="line-input f-3 bold black red make_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                    <div class="input-container mb-0" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" style="padding-left: 225px;border: none">
                        <span class="base_url">www.lagreeod.com/pages/</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-8">
                    <h4 class="mb-2">Page Description <small class="f-10 midGray light ml-1">(up to 240 characters)</small></h4>
                    <div class="input-container mb-0" id="content_container">
                        <!-- <div class="line-input border" id="content" style="height: 150px"><?php echo isset($current['content']) ? $current['content'] : '' ?></div>
                        <input type="hidden" name="content" id="content" value=""> -->
                        <textarea type="text" name="content" class="line-input cke_insert" style="min-height: 150px" id="content" placeholder="Describe Your Page"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row mx-0">
                <div class="col-12 black bold pl-0 mb-5 text-uppercase f-14 flex aic jcsb">
                    SEO
                </div>
                <div class="col-12 pl-0">
                  <h5 class="mb-1 f-11">SEO TITLE</h5>
                    <div class="input-container mb-3 pb-05" id="section_title_container">
                        <input type="text" name="seo_title" maxlength="255" class="line-input" placeholder="Enter" value="<?php echo (isset($current['seo_title']) AND $current['seo_title'] != '') ? $current['seo_title'] : '' ?>">
                    </div>
                    <h5 class="mb-1 f-11">SEO DESCRIPTION</h5>
                    <div class="input-container mb-0" id="description_container">
                        <textarea type="text" name="seo_description" class="line-input" maxlength="160" style="min-height: 275px" id="description" placeholder="Enter"><?php echo (isset($current['seo_description']) AND $current['seo_description'] != '') ? $current['seo_description'] : '' ?></textarea>
                        <p class="midGray normal f-12 mt-1">Max 160 characters</p>
                    </div>
                </div>                
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6 for_submit">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <div class="default-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? 'style="display: none;"' : '';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH PAGE</button>
                        <button type="submit" class="btn btn-wide btn-tall btn-border white-bg black ml-2" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="https://cdn.ckeditor.com/ckeditor5/34.0.0/classic/ckeditor.js"></script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script> -->
<!-- <script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script> -->
<!-- <script src="admin_assets_new/js/pages.js?v=<?php echo $_ENV['version']; ?>"></script> -->
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }
}
$(document).ready(function(){
    insert_cke();
});

const date = "<?php echo date('Y-m-d'); ?>";
if($('.sortable').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".sortable").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/pages/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}
</script>
</body>
</html>