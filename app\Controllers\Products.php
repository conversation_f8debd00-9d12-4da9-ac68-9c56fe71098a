<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Products extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ProductsModel');
        $controller = 'products';
	}

    public function index()
    {
        $WooCommerceModel = model('WooCommerceModel');

        $response = $WooCommerceModel->all_products_json();

        echo '<pre>';
        print_r($response['allProducts']);
        die();
        
        // echo view('front/products/index_view', $data);
    }

    public function slug($slug = '')
    {
		$data['main_menu'] = $this->main_menu;
		$data['title'] = 'Single product';
        $data['nums'] = create_session_nums();
		$data['current'] = $this->model
					->asArray()
					->where(['slug' => $slug])
					->first();
		$data['gallery'] = $this->model->get_gallery($data['current']['id']);
		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/products/single_view', $data);
    }
}