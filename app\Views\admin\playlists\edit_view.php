<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.single-selected-exercises .handle,
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
  margin-top: 37px;
  right: 35px;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.ajax-class {
	position:relative;
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 25px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 155px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
	flex: initial;
max-width: 35px;
margin-left: auto !important;
align-self: center;
height: 35px;
color: #969696 !important;
background: #fff !important;
border: 1px solid #f0f0f0;
width: 35px;
position: absolute;
right: 5px;
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto:hover {
  color: #fff !important;
  background: #000 !important;
  border: 1px solid black !important;
}
.upload-image.big-uplad-image {
	width: 280px;
	height: 280px;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Playlist</h1>
                <a href="admin/playlists" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/playlists/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom new_playlist_upload" id="main_form">
            <h5 class="mb-1 f-14 semibold">FEATURED PHOTO</h5>
            <p class="midGray mb-45 f-14">Select or upload a photo that shows what's in your playlist.</p>
            <div class="image_container flex aic">
                <div class="upload-image big-uplad-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 800px x 800px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="mt-5 mb-45">
            <div class="row mb-2">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">PLAYLIST NAME</h5>
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" maxlength="30" class="line-input black make_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold top-border pt-45 mt-05">PAGE URL</h5>
                    <h5 class="mb-1 f-11">URL</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" style="padding-left: 230px;">
                        <span class="base_url">www.lagreeod.com/playlists/</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                <h5 class="mb-4 f-14 semibold top-border pt-45 mt-05">PLAYLIST SHORT DESCRIPTION</h5>
                <h5 class="mb-1 f-11">DESCRIPTION *</h5>
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input" placeholder="Enter" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">500</span></p>
                    </div>
                </div>
            </div>
            <hr class="mt-2 mb-45">
            <!-- <div class="row">
                <div class="col-12">
                    <h3 class="mb-3">Teacher</h3>
                    <div class="row w100">
<?php
// $c=0;
// $teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
// foreach($all_teachers as $single){
// $c++;
?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container" style="<?php // echo ($single['certified'] == 0) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php // echo $c; ?>" <?php // echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php // echo $c; ?>" class="f-16"><?php // echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>

<?php
// }
?>
                    </div>
                </div>
            </div> -->
            <!-- <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">CREATED BY STAFF</h3>
                    <div class="checkbox mb-15">
                        <input type="checkbox"  id="author_check" <?php // echo $current['author'] == 1 ? 'checked' : (!isset($current['id']) ? 'checked' : '') ?> onchange="$(this).is(':checked') ? $('#author').val(1) : $('#author').val(0)">
                        <label for="author_check" class="f-16">Yes</label> -->
                        <input type="hidden" name="author" id="author" value="1">
                    <!-- </div>
                </div>
            </div> -->
            <!-- <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">PRIVATE?</h3>
                    <div class="checkbox mb-15">
                        <input type="checkbox"  id="private_check" <?php // echo $current['private'] == 1 ? 'checked' : '' ?> onchange="$(this).is(':checked') ? $('#private').val(1) : $('#private').val(0)">
                        <label for="private_check" class="f-16">Yes</label>
                        <input type="hidden" name="private" id="private" value="<?php // echo $current['private']; ?>">
                    </div>
                </div>
            </div> -->
            <!-- <hr class="my-5"> -->
            <input type="hidden" name="image_removed" id="image_removed" value="0">
            <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
            <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
        </form>


        <div class="container" style="<?php echo isset($current['id']) ? '' : 'display: none'; ?>">
            <div class="row big-gap reversecols">
                <div class="col-6">
                    <h5 class="mb-2 pb-45 f-14 ml-1 semibold flex aic jcsb bottom-border bordertopmob">ALL VIDEOS</h5>
                    <div class="row selected_clases sortable">
                    <?php
                    foreach($selected_classes_for_selection as $single){
                    ?>
                        <div class="col-12 pl-0 pr-2 single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : ($single['class'] == 'exercises_class' ? 'exercises' : 'howto'); ?> white-bg " data-rowid="<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="<?php echo (isset($single['type']) AND $single['type'] != '') ? $single['type'] : ''; ?>" data-sort="<?php echo (isset($single['sort']) AND $single['sort'] != '') ? $single['sort'] : ''; ?>">
                            <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
                            <div class="single-class ml-2 aic">
                                <div class="single-class-image">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                                </div>

                                <div class="single-class-rest">
                                    <div class="single-class-title f-14 medium" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></div>
                                    <div class="single-class-desc normal">
                                      <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                      <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''; ?>
                                      <?php echo (isset($single['diff']) AND $single['diff'] != '') ? ', ' . $single['diff'] : ''; ?>
                                      <?php echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                    </div>
                                    <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : ($single['class'] == 'exercises_class' ? 'exercises' : 'howto'); ?>').remove();remove_class_from_selected(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>, <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>, '<?php echo $single['class'] == 'class_class' ? 'Classes' : ($single['class'] == 'exercises_class' ? 'Exercises' : 'Howto'); ?>')">Remove</span>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    </div>
                </div>
                <div class="col-6">
                    <h5 class="mb-3 pb-45 f-14 semibold flex aic jcsb bottom-border">PLAYLIST VIDEOS
                        <div class="flex aic jcr classes_videos_show">
                            <div class="checkbox mr-2 mr-mob-10 small-checkbox">
                                <input type="checkbox" class="classes_show" id="classes_show" checked>
                                <label for="classes_show" class="f-12 normal">Classes</label>
                            </div>
                            <div class="checkbox mr-2 mr-mob-10 small-checkbox">
                                <input type="checkbox" class="videos_show" id="videos_show" checked>
                                <label for="videos_show" class="f-12 normal">Videos</label>
                            </div>
                            <div class="checkbox m-0 small-checkbox">
                                <input type="checkbox" class="exercises_show" id="exercises_show" checked>
                                <label for="exercises_show" class="f-12 normal">Exercises</label>
                            </div>
                        </div>
                    </h5>
                    <!--<div class="machines mb-2">
                        <div class="flex aic jcl classes_machines_show" style="flex-wrap: wrap;">
<?php
foreach($machines as $single){
    if($single['short_name'] != 'Proformer'){
?>
                            <div class="checkbox mr-2 small-checkbox">
                                <input type="checkbox" class="machines_show" id="machine<?php echo $single['id']; ?>" data-machine_class="<?php echo str_replace(' ', '_', strtolower($single['short_name'])); ?>">
                                <label for="machine<?php echo $single['id']; ?>" class="f-10 text-uppercase"><?php echo $single['short_name']; ?></label>
                            </div>
<?php
    }
}
?>
                        </div>
                    </div>-->
                    <div class="search-container mb-2">
                        <div class="ajax-search-classes search-form show ml-0 px-0">
                            <input type="text" class="seach-input search-wide search_classes" placeholder="Search (enter at least 2 characters)...">
                            <button type="button" class="search-button" style="right:7px; top: 7px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" /></button>
                        </div>
                    </div>
                    <div class="search-ajax-classes">
<?php
foreach($all_classes as $single){
    $added = FALSE;
    if($single['status'] == 0){
        foreach($selected_classes_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class classes-class <?php echo ($single['machines_classes']) ?> <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2">
                                <span class="f-14 medium" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500; margin-bottom: 1px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #969696;font-size: 12px;display: inline-block;line-height:20px;font-weight: 400;">
                                    <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?><br>
                                    <?php echo (isset($single['diff']) AND $single['diff'] != '') ? '<br>Difficulty: ' . $single['diff'] : ''; ?>
                                    <?php echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto">+</span>
                        </div>
<?php
    }
}
?>
                        <hr class="my-2 videos-title top-border">
                        <p class="f-1 videos-title px-2 semibold pt-2">VIDEOS</p>
                        <hr class="my-2">
<?php
foreach($all_howto as $single){
    $added = FALSE;
    if($single['status'] == 0){
        foreach($selected_howto_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class videos-class <?php echo $single['machines_classes']; ?> <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2">
                                <span class="f-14" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500;margin-bottom: 3px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #999;font-size: 12px;display: inline-block;line-height:20px;font-weight: 400;">
                                    <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?><br>
                                    <?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] : ''; ?>, <br>
                                    <?php echo (isset($single['teach']) AND $single['teach'] != '') ? 'by: ' . $single['teach'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto howto_add">+</span>
                        </div>
<?php
    }
}
?>
                        <hr class="my-2 videos-title top-border">
                        <p class="f-1 exercises-title px-2 medium pt-2">EXERCISES</p>
                        <hr class="my-2">
<?php
foreach($all_exercises as $single){
    $added = FALSE;
    if(isset($single['status']) AND $single['status'] == 0){
        foreach($selected_exercises_for_selection as $single2){
            if($single['id'] == $single2['id']){ $added = TRUE; }
        }
?>
                        <div class="ajax-class exercises-class <?php echo $single['machines_exercises']; ?> <?php echo $added ? 'added' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-class-image">
                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" />
                            </div>
                            <span class="pr-2">
                                <span style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500;margin-bottom: 3px; font-size:14px;"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                <span style="color: #999;font-size: 12px;display: inline-block;line-height: 20px;font-weight: 400;">
                                    <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>
                                    <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', Duration: ' . duration_standard($single['duration']) : ''; ?><br>
                                    <?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] : ''; ?>, <br>
                                    <?php echo (isset($single['teach']) AND $single['teach'] != '') ? 'by: ' . $single['teach'] : ''; ?>
                                </span>
                            </span>
                            <span class="btn btn-xs red-bg white f-1 add_button ml-auto exercises_add">+</span>
                        </div>
<?php
    }
}
?>
                    </div>
                </div>
            </div>
            <hr class="mt-4 mb-5 hide-mob">
            <div class="row">

    </div>

    </div>
        <!-- <hr class="mt-4 mb-6"> -->
        <div class="container">
        <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
            <div class="default-buttons centerbtns flex aic mt-mob-25">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                <a href="/admin/playlists" class="cancel-link ml-2" title="Cancel">Cancel</a>
                <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="playlists" data-popup="delete-popup" title="Cancel">DELETE PLAYLIST</a>
            </div>
        <?php }else{ ?>
            <div class="default-buttons centerbtns flex aic mt-mob-25">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                <a href="/admin/playlists" class="cancel-link" title="Cancel">Cancel</a>
            </div>
        <?php } ?>
        </div>
        </div>
</main>

<!-- CLASS TEMPLATE -->
<div id="class-template" style="display: none">
    <div class="col-12 single-selected-class" data-id="0" data-rowid="0" data-type="0">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>
<!-- HOW TO CLASS TEMPLATE -->
<div id="howto-template" style="display: none">
    <div class="col-12 single-selected-howto" data-id="0" data-rowid="0" data-type="0" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>
<!-- EXERCISES CLASS TEMPLATE -->
<div id="exercises-template" style="display: none">
    <div class="col-12 single-selected-exercises" data-id="0" data-rowid="0" data-type="0" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">Remove</span>
            </div>
        </div>
    </div>
</div>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
const playlist_id = <?php echo (isset($current['id']) AND $current['id'] > 0) ? $current['id'] : 0; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
if($('.sortable').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".sortable").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/playlists/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}
$('.classes_videos_show input').on('change', function(){
    var type = $(this).attr('class');
    console.log(type);
    if($(this).is(':checked')){
        console.log('checked');
        if(type == 'classes_show'){
            $('.classes-class').show();
        }
        if(type == 'videos_show'){
            $('.videos-class').show();
            $('.videos-title').show();
        }
        if(type == 'exercises_show'){
            $('.exercises-class').show();
            $('.exercises-title').show();
        }
    }else{
        console.log('NOT checked');
        if(type == 'classes_show'){
            $('.classes-class').hide();
        }
        if(type == 'videos_show'){
            $('.videos-class').hide();
            $('.videos-title').hide();
        }
        if(type == 'exercises_show'){
            $('.exercises-class').hide();
            $('.exercises-title').hide();
        }
    }
    console.log('------------');
});
</script>
</body>
</html>