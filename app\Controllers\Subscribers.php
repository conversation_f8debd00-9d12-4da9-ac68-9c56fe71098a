<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Subscribers extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
        $controller = 'subscribers';
	}

    public function index()
    {
		$data['main_menu'] = $this->main_menu;
		$data['title'] = 'List of Live Events';
		$data['current'] = NULL;
		echo view('front/subscribers/index_view', $data);
    }

    public function slug($slug = '')
    {
		$data['main_menu'] = $this->main_menu;
		$data['title'] = 'Single page';
		$data['current'] = $this->model
					->where(['slug' => $slug])
					->first();
		// $data['gallery'] = $this->model->get_gallery($data['current']['id']);
		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('front/subscribers/single_view', $data);
    }
}