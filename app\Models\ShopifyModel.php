<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class ShopifyModel extends Model
{
    function __construct()
    {
        parent::__construct();
		$this->shopify_config = array(
			'api_key' => $_ENV['shopify_api_key'],
			'password' => $_ENV['shopify_password'],
			'shopUrl' => $_ENV['shopify_ShopUrl']
		);
    }

    function single_collection($id = NULL)
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $result['success'] = FALSE;
		$result['message'] = '';
		// try {
            \PHPShopify\ShopifySDK::config($config);
            $shopify = new \PHPShopify\ShopifySDK;
            $response = $shopify->Collection($id)->Product->get();

			$tmp = json_decode(json_encode($response), TRUE);
			$result['success'] = TRUE;
			$result['products'] = $tmp;
		// } catch(\PHPShopify\ShopifySDK $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			// $result['message'] = $e->getError();
		// }
		return $result;
	}
    function all_products()
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $result['success'] = FALSE;
		$result['message'] = '';
		// try {
            \PHPShopify\ShopifySDK::config($config);
            $shopify = new \PHPShopify\ShopifySDK;
            $response = $shopify->Product()->get();

			$tmp = json_decode(json_encode($response), TRUE);
			$result['success'] = TRUE;
			$result['products'] = $tmp;
		// } catch(\PHPShopify\ShopifySDK $e) {
			// Since it's a decline, \Stripe\Exception\CardException will be caught
			// $result['message'] = $e->getError();
		// }
		return $result;
	}

    function single_product($id = NULL)
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $result['success'] = FALSE;
		$result['message'] = '';
		// try {
            \PHPShopify\ShopifySDK::config($config);
            $shopify = new \PHPShopify\ShopifySDK;
            $response = $shopify->Product($id)->get();

			$tmp = json_decode(json_encode($response), TRUE);
			$result['success'] = TRUE;
			$result['product'] = $tmp;
		// } catch(\PHPShopify\ShopifySDK $e) {
		// 	// Since it's a decline, \Stripe\Exception\CardException will be caught
		// 	$result['message'] = $e->getError();
		// }
		return $result;
	}

}