<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Playlists extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('PlaylistsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_playlists'] = $this->model->all_playlists(0, session('per_page'));
        $data['all_playlists'] = $this->model->all_playlists(0, session('per_page'), NULL, "sort, playlists.updated_at desc");
        $data['playlists_count'] = count($this->model->all_playlists());
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/playlists/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();

        $data['all_playlists'] = $this->model->all_playlists(($page * session('per_page')) - session('per_page'), session('per_page'), NULL, "sort, playlists.updated_at desc", 1);
        $data['playlists_count'] = count($this->model->all_playlists());
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/playlists/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_playlists'] = $this->model->all_playlists(0, 9, $data['search_term'], "sort, playlists.updated_at desc", 1);
        $data['playlists_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/playlists/index_view', $data);
    }

    public function sort_by($type = 'playlists.title', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $teachers_model = model('TeachersModel');
		// $data = $this->request->getPost();
        $data['all_playlists'] = $this->model->all_playlists(0, session('per_page'), NULL, $type. " " . $direction, 1);
        // $data['all_playlists'] = $this->model->query("SELECT playlists.*
        //                                 FROM playlists
        //                                 WHERE playlists.deleted_at IS NULL
        //                                 ORDER BY " . $type. " " . $direction . "")->getResultArray();
        $data['playlists_count'] = count($this->model->all_playlists());
        $types = array(
            "playlists.created_atdesc" => "Date Added",
            "playlists.titleasc" => "Ascending",
            "playlists.titledesc" => "Descending"
        );
        $data['all_teachers'] = $teachers_model->findAll();
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/playlists/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $classes_model = model('ClassesModel');
        $howto_model = model('HowtoModel');
        $exercises_model = model('ExercisesModel');
        $teachers_model = model('TeachersModel');
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_teachers'] = $teachers_model->findAll();

		$data['machines'] = $this->model->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
        $data['all_classes'] = $classes_model->all_classes(0,0);
        $data['all_howto'] = $howto_model->all_howto(0,0);
        $data['all_exercises'] = $exercises_model->all_exercises(0,0);

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/playlists');
        };

        $data['selected_classes_for_selection'] = $this->model->classes_for_playlist($edit_id);
        $data['selected_howto_for_selection'] = $this->model->howto_for_playlist($edit_id);
        $data['selected_exercises_for_selection'] = $this->model->exercises_for_playlist($edit_id);

        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('admin/playlists/edit_view', $data);
    }

    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Data successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['image'] = 'uploads/playlists/' . $name;
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['cover_image'] = 'uploads/playlists/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/playlists/' . $name;
			}
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            // if($data['cover_image_removed'] == 1){
            //     $data['cover_image'] = "";
            // }
            // unset($data['cover_image_removed']);

            // if($data['mob_cover_image_removed'] == 1){
            //     $data['mob_cover_image'] = "";
            // }
            // unset($data['mob_cover_image_removed']);

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            if($data['id'] == 0){
                $notification_data = array(
                    'content'   => 'New playlist by staff is added.',
                    'link'      => base_url() . '/playlists/',
                    'author'    => 'system',
                    'type' => 'new_staff_playlist_notif',
                    'date'    => date('Y-m-d H:i:s')
                );
                $response['notification_saved'] = $NotificationsModel->save($notification_data);
            }

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('playlists_' . $key);
                    $builder->delete(['playlists_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'playlists_id' => $response['inserted_id'],
                            'playlist_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function sort_classes_table()
    {
        $PlaylistClassesModel = model('PlaylistClassesModel');
        $PlaylistHowtoModel = model('PlaylistHowtoModel');
        $PlaylistExercisesModel = model('PlaylistExercisesModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                if($single['type'] == 'classes'){
				    $PlaylistClassesModel->save($single);
                }
                if($single['type'] == 'videos'){
				    $PlaylistHowtoModel->save($single);
                }
                if($single['type'] == 'exercises'){
				    $PlaylistExercisesModel->save($single);
                }
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function save_videos_in_playlist()
    {
        $PlaylistHowtoModel = model('PlaylistHowtoModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['success'] = $PlaylistHowtoModel->save($data);
        $result['csc_id'] = $PlaylistHowtoModel->getInsertID();
        $result['type'] = 'videos';

        if($result['success']){
            $result['class'] = $this->get_howto_info($data['playlist_selected_howto']);
        }

		return $this->respond($result);
    }

    public function save_exercises_in_playlist()
    {
        $PlaylistExercisesModel = model('PlaylistExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['success'] = $PlaylistExercisesModel->save($data);
        $result['csc_id'] = $PlaylistExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['playlist_selected_exercises']);
        }

		return $this->respond($result);
    }

    public function save_class_in_playlist()
    {
        $PlaylistClassesModel = model('PlaylistClassesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $result['success'] = $PlaylistClassesModel->save($data);
        $result['csc_id'] = $PlaylistClassesModel->getInsertID();
        $result['type'] = 'classes';

        if($result['success']){
            $result['class'] = $this->get_class_info($data['playlist_selected_classes']);
        }

		return $this->respond($result);
    }

    public function delete_record($record_id = 0, $ajax = FALSE)
    {
		if ($record_id > 0)
		{
			$response['success'] = $this->model->delete($record_id);
		}
		return redirect()->to(site_url('admin/playlists'));
    }
}