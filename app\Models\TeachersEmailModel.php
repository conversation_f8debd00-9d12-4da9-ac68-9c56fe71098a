<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class TeachersEmailModel extends Model
{
    protected $table = 'teachers_emails';
	protected $allowedFields = ['teachers_id', 'teachers_names', 'teachers_cc', 'all', 'subject', 'description'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    protected $validationRules    = [
        'teachers_id'     => 'required',
        'subject'     => 'required',
        'description'     => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_teachers_emails($start = 0, $limit = 100000, $search_term = NULL, $order = "created_at DESC")
    {
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? "HAVING (teachers_emails.date LIKE '%$search_term%' OR teachers_emails.created_at LIKE '%$search_term%' OR teacher LIKE '%$search_term%' OR model LIKE '%$search_term%')" : "";

                                    
        $data = $this->query("SELECT teachers_emails.id, teachers_emails.teachers_id, teachers_emails.status, teachers_emails.teachers_cc, teachers_emails.created_at, teachers_emails.all, teachers_emails.subject, teachers_emails.description
                                    FROM teachers_emails 
                                    WHERE teachers_emails.deleted_at IS NULL
                                    " . $search . "
                                    ORDER BY " . $order . "
                                    " . $limit_size . "
                        ")->getResultArray();

        if(count($data) > 0){
            foreach($data as $key => $single){
                $names = $this->query("SELECT GROUP_CONCAT(teachers.firstname, ' ', teachers.lastname SEPARATOR ', ') as teacher_names FROM teachers WHERE teachers.id IN (" . $single['teachers_id'] . ")")->getRowArray();
                $data[$key]['teachers_names'] = $names['teacher_names'];
            }
        }
        // echo '<pre>';
        // print_r($data);
        // die();
        
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}