<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('admin/templates/head.php'); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<style>
.routinevideo {border-top: none; display: flex; align-items: center; justify-content: space-between; padding-top: 38px; padding-bottom: 40px;}
.teach-quest {border-top:1px solid #f0f0f0; padding-top:40px; margin-top:40px;}
.teach-quest p {font-size:18px; font-weight:500;}
.btnmob {display:none;}

@media screen and (max-width: 960px) {
.routinevideo {flex-direction:column; align-items: baseline; padding-top: 31px;}
.routinevideo h6 {margin-bottom:15px !important;}
.teach-quest {padding-top: 30px; margin-top: 30px;}
.teach-quest p {font-size: 16px;}
}
@media screen and (max-width: 600px) {
.routinevideo {padding-top: 40px;}
.routinevideo h6 {display:none;}   
.btnmob {display: flex; width: 100%;}
.btndesk {display:none;}
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">DASHBOARD</h1>
                <a href="javascript:;" data-popup="report-issue-popup" class="btn btn-border white-bg black ml-auto" title="REPORT AN ISSUE">REPORT AN ISSUE</a>
            </div>
        </div>
        <div class="container pb-100">
            <div class="dashboard-wrapper">
                <div class="t-dash-intro">
                    <h6>LagreeOD Trainer Guidelines</h6>
                    <p>Effective as of September 25, 2024.</p>
                </div>
                <div class="t-dash-intro routinevideo">
                    <h6 class="mb-0">Routine builder - Video tutorial</h6>
                    <a href="https://youtu.be/Elwu7ksW_Vs" data-fancybox="" class="btndesk btn btn-tall red-bg white">WATCH THE VIDEO</a>
                    <a href="https://youtu.be/Elwu7ksW_Vs" data-fancybox="" class="btnmob btn btn-tall red-bg white">ROUTINE BUILDER (WATCH THE VIDEO)</a>

                </div>

                <div class="t-dash-text">
                <p><span class="semibold">Trainers must wear black, no exceptions</span><br>
                For consistency in our content and branding, it is required that our trainers wear black for filming (unless the team requested otherwise prior to filming)</p>

                <p><span class="semibold">Trainers must act professionally on set</span><br>
                · Please refrain from eating while on the microphone<br>
                · Please avoid using your phone during filming (unless you’re using it for the purpose of keeping track of time with a stopwatch app)<br>
                · Please stand, kneel, or take a seat while off-camera, but avoid laying down<br>            
                · Please treat the staff, team, and models with respect</p>


                <p><span class="semibold">Trainers must be proficient in each of the machines and its accessories. Additionally, trainers must use the correct name for each accessory</span><br>
                · Machines and how they’re used can be found at <a href="https://www.lagreeshop.com/category/machines" target="_blank"><u>Lagree Shop</u></a><br>
                · Accessories and their names can be found at <a href="https://www.lagreeshop.com/category/accessories" target="_blank"><u>Lagree Shop</u></a><br>
                · If you need a personal walkthrough of the machine or its accessories, please reach out to Sebastien directly, and he’d be happy to schedule time with you</p>

                <p><span class="semibold">Trainers must start and end filming on time</span><br>
                For example, if the trainer is scheduled from 1pm to 4pm, the trainer is expected to be ready to start recording at 1pm. We recommend arriving a few minutes earlier to get your microphone set up and if you need to use the restroom. Additionally, recording will end at the scheduled time of 4pm so we are mindful of the team, staff, and models.</p>

                <p><span class="semibold">Trainers must submit their routine using the Routine Builder on LOD at least 24 hours before filming</span><br>
                · In the Routine Builder, select the length of the routine. You are able to add exercises and transition times within the length you selected. For example, if you select 40 minutes, the routine builder will only allow you to create a routine within 40 minutes and you will not be able to go beyond the selected 40 minutes. Watch the tutorial <a href="https://youtu.be/Elwu7ksW_Vs" data-fancybox><u>here</u></a><br>
                · Routine submission supports our team and staff have everything ready for your shoot (i.e. equipment and accessories) while ensuring that the video is queued up for the website with little to no lag time.     
                </p>

                <p><span class="semibold">Trainers must be mindful of transition time</span><br>
                To keep the routine on time, be sure to add transition time between exercises and keep track of your time as you film. We recommend using a stopwatch</p>

                <p><span class="semibold">Trainers must be clear and concise in their queuing</span><br>
                Guiding your audience/clients with ease from exercise to exercise is a skillset and talent that you have! We recommend stating the following:</p>

                <p>· Name of exercise<br>
                · Spring load<br>
                · Direction<br>


                
                · Set up queue<br>
                · Movement <br>
                · Tempo is a 4-count for beginner, and slower for intermediate and advance classes<br>
                · Activated muscles<br>
                · Tips/Corrections</p>

                <p><span class="semibold">Trainers must use the exercises from the exercise library found in the Routine Builder</span><br>
                We love your creativity! If you have an exercise you’d like to teach, please submit them to Sebastien for review and approval before adding it to your routine. Our intention is to create consistency for the website and keep our audience/clients engaged with little-to-no confusion</p>

                <p><span class="semibold">Helpful steps for using the Routine Builder</span><br>
                 · Make sure to select the routine duration<br>
                · In your search use "reverse giant" instead of "giant reverse"<br>
                · You can delete the entire routine by clicking on “delete all”<br>
                · In the search list, exercises are listed in alphabetical order<br>
                · If you import multiple routines, the system will list them all one after the other, it won't substitute one routine for the next<br>
                · When you search for the runner's lunge to type "runner" only. Do not type "Runner's"</p>
                
                <p><span class="semibold">Trainers must use a minimum of 40 minutes per hour scheduled</span><br>
                If you are scheduled from 1pm - 4pm, we expect you to be actively teaching for at least 120 minutes. For example: From 1pm-2pm, in that one-hour time window, you can teach four (4) 10-minute routines or one (1) 40-minute routine or two (2) 25-minute routines. A 40-minute routine must end on time</p>

                <p><span class="semibold">Trainers can model their own classes for up to two (2) hours, otherwise, a model is required for one (1) of the hours</span><br>
                The Lagree method is meant to be challenging and to support your well-being and the quality of the content, we will provide you with a model or you can bring your own for one (1) of the three (3) hours</p>

                <p><span class="semibold">If trainers provide their own models, the model must be extremely proficient with the method and not an in-progress student</span><br>
                If the video and edit team do not feel that a class was effective in demonstrating the exercise and the method, we will not publish it</p>
                </div>
            </div>
            <div class="teach-quest"><p>For any questions, please email Nitar at <a href="mailto:<EMAIL>"><u><EMAIL></u></a>.</p></div>
        </div>
    </div>

    <div class="logdesk-popup">
      <div class="login-desktop">
        <img src="admin_assets_new/images/icon-desktop960.svg">
		<p>For the best user experience, continue with admin management on desktop device.</p>
      </div> 
    </div>

</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>



<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>