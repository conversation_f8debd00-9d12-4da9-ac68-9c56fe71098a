<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.pad-60-25 {
    padding: 60px 25px;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container dashb-container mb-150">
            <div class="flex aic jcsb minH45 bottom-border page-title">
                <h1 class="h3">hall of fame</h1>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">MOST VIEWED CLASSES</span>
                </div>
                <?php foreach($most_viewed_classes as $single){ ?>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                    <span class="f-14 medium"><?php echo number_format($single['countView'], 0); ?> views</span>
                </div>
                <?php } ?>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">MOST VIEWED EXERCISES</span>
                </div>
                <?php foreach($most_viewed_exercises as $single){ ?>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                    <span class="f-14 medium"><?php echo number_format($single['countView'], 0); ?> views</span>
                </div>
                <?php } ?>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">MOST VIEWED VIDEOS</span>
                </div>
                <?php foreach($most_viewed_videos as $single){ ?>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                    <span class="f-14 medium"><?php echo number_format($single['countView'], 0); ?> views</span>
                </div>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="logdesk-popup">
      <div class="login-desktop">
        <img src="admin_assets_new/images/icon-desktop960.svg">
		<p>For the best user experience, continue with admin management on desktop device.</p>
      </div> 
    </div>

</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>



<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>