<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundlmin.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="account-page logged dashboard-page">

<?php echo view('front/templates/header.php'); ?>
<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white semibold text-uppercase pb-1 mb-05 mb-mob-0">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
    <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                            DEVICES
                        </h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12 pt-4 pb-4 pt-mob-2 pb-mob-2 top-border pl-0">
                    <p class="f-14 f-12-mob line-height-big">
                        <?php if(count($devices) < 3){ ?>
                            You have <?php echo count($devices); ?>/3 active devices on your account.
                        <?php }else{ ?>
                            You've reached maximum active devices (<?php echo count($devices); ?> of 3).
                        <?php } ?>
                        <br>
                        <span class="device_msg midGray lh-25">You can replace one device per month.</span> <?php // echo $diff; ?><br>
                    </p>
                </div>
<?php foreach($devices as $single){ ?>
                <div class="col-12 px-0" data-device="<?php echo $single['id']; ?>">
                    <div class="w100 col-12 py-2 py-mob-2 flex aic jcsb border px-3 px-mob-2 mb-2 radius-10 radius-mob-6">
                        <div class="flex aic w100 mt-05 mb-05 mt-mob-0 mb-mob-0">
                            <span class="mr-2 os-icon"><img src="images/<?php echo $single['os_name'] == 'Mac OS' ? 'ios' : strtolower($single['os_name']); ?>-icon.svg" alt="" class="img-fluid" /></span>
                            <div class="flex flex-column w100">
                                <p class="f-12 semibold text-uppercase line-height-small rename_device_item" contenteditable><?php
                                        if($single['title'] != '' AND $single['title'] != NULL){
                                            $title = $single['title'];
                                        }else{
                                            if($single['device_model'] != 'undefined' AND $single['device_type'] != 'undefined' AND $single['device_vendor'] != 'undefined'){
                                                $title = $single['device_vendor'] . ' ' . $single['device_model'] . ', ' . $single['browser_name'] . ' ' . $single['browser_version'];
                                            }else{
                                                $title = $single['os_name'] . ' ' . $single['os_version'] . ', ' . $single['browser_name'] . ' ' . $single['browser_version'];
                                            }
                                        }
                                        echo $title;
                                    ?></p>
                                <p class="f-12 line-height-normal last_activity midGray">Last Activity: <?php echo date('m/d/Y, h:iA', strtotime($single['last_activity'])); ?></p>
                            </div>
                        </div>
                        <?php if((int)$diff > 30 OR $diff == 'NO DELETED DEVICES') { ?>
                            <p class="mt-mob-1 min50w"><a href="javascript:;" class="link link-black black f-12"  title="Delete" data-popup="delete-device-popup" onclick="$('.delete_device_item').data('delete-id', <?php echo $single['id']; ?>).attr('data-delete-id', <?php echo $single['id']; ?>)">Delete</a></p>
                        <?php }else{ ?>
                            <p class="mt-mob-1 min50w device-tooltip-container" style="display:none;">
                                <span class="link link-midGray midGray no-hover f-12" title="Delete">Delete</span>
                                <span class="device-tooltip">
                                <?php if((int)$diff <= 30) {?>
                                    You have already removed one device, you can remove another one at: <?php echo date("m/d/Y H:iA", strtotime("+30 day", strtotime($deleted_device['deleted_at']))); ?>
                                <?php } ?>
                                </span>
                            </p>
                        <?php } ?>
                    </div>
                </div>
<?php } ?>
                <div class="col-12 pl-0">
                    <div class="mt-05 pt-2 pt-mob-0">
                        <p class="f-14 f-12-mob lh-25 midGray">
                            <?php if(!empty($deleted_device) AND count($deleted_device) > 0){ ?>
                            Last removed device: <?php echo date('m/d/Y \a\t H:iA', strtotime($deleted_device['deleted_at'])); ?>
                            <br>
                            <?php } ?>
                            Need help? <a href="/account/support" class="link link-black black text-underline" style="display: inline-block;line-height: 25px;">Contact our support</a>
                        </p>
                    </div>
                </div>
             </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/cookie.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>