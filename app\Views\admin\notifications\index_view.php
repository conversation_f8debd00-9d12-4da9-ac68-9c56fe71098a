<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Notifications</h1>
                <div class="ml-auto">
                    <a href="admin/notifications/edit" class="btn black-bg white" title="Upload Class">New notification</a>
                </div>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $notifications_count == 1 ? $notifications_count . ' Notification' : $notifications_count . ' Notifications'; ?></h5>

                <div class="dropdown d-inline-block ml-auto">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/notifications/sort_by/notifications.created_at/desc" class="link midGray" title="">Date Added</a></li>
                        <li><a href="admin/notifications/sort_by/notifications.title/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/notifications/sort_by/notifications.title/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>

                <div class="search-container">
                    <form action="admin/notifications/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            <hr class="mt-2 mb-2">
            <!-- <pre>
                <?php // print_r($all_notifications); ?>
            </pre> -->
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="notifications" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>
                <div class="col-name mr-2 hidden">
                  <p class="f-12 medium midGray mr-2">ROLE</p>
                </div>
            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders noimg-table">
<?php
foreach($all_notifications as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <div class="flex flex-column">
                                    <a href="admin/notifications/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title f-12 medium" style="margin-bottom:3px;"><?php echo (isset($single['content']) AND $single['content'] != '') ? $single['content'] : ''; ?></a>
                                    <span class="midGray f-12">Date: <?php echo (isset($single['date']) AND $single['date'] != '') ? date('m/d/Y', strtotime($single['date'])) : ''; ?></span>
                                    <div class="row-actions f-1 red">
                                        <a href="admin/notifications/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 hide_record" data-popup="hide-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="notifications">Delete</a>-->
                                    </div>
                                </div>
                                <?php if($single['author'] == "admin"){ ?>
                                <div class="flex flex-column ml-auto text-right f-12">
                                    <!--<span class="btn  btn-xs f-12 btnadmin ml-4 normaladmin">Admin</span>-->
                                </div>
                                <?php } ?>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($notifications_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_notifications); ?><span class="midGray mx-1">of <?php echo $notifications_count; ?></span>
                    <a href="admin/notifications/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/notifications/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_notifications) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_notifications)) == $notifications_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
</script>
</body>
</html>