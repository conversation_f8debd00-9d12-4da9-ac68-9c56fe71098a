<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

<style>
    .add-course-video .popup-body {
        max-height: 86vh;
        overflow-y: auto;
    }
    .single-course-title h4::after {
        content: "(" attr(data-videos) ")";
        color: #999;
        margin-left: 6px;
    }
    .single-selected-exercises .handle,
    .single-selected-howto .handle,
    .single-selected-class .handle {
        position: absolute;
        top: 0;
        margin-top: 40px;
        left: -26px;
    }
    .handle:hover {
        cursor: pointer;
    }
    .ajax-class > * {
        flex: 1;
        display: flex;
    }
    .search-ajax-courses {
        display: flex;
        flex-direction: column;
    }
    .ajax-class .single-class-image {
        min-width: 120px;
        width: 120px;
        height: 70px;
        min-height: 70px;
        margin-right: 25px;
        flex: 1;
        max-width: 120px;
    }
    #slug {
        width: 100%;
        margin-left: 5px;
        height: 40px;
        font-size: 14px;
        border: none;
        position: relative;
        top: 0px;
        opacity: 0.6;
    }
    .single-class-image + span {
        flex: 1;
        flex-direction: column;
        margin-left: 0;
        max-width: calc(100% - 155px - 10px);
    }
    .btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
        flex: 1;
        max-width: 30px;
        margin-left: auto !important;
        align-self: center;
    }
    .audio_item:hover {
    background: #f0f0f0;
    }
    .audio_item {
    padding: 0 !important;
    margin: 0 !important;
    cursor: pointer;
    }
    .audio_item a{
    padding: 8px 20px !important;
    margin: 0 !important;
    }
    .audio_item.selected {
        font-weight: 700;
    }
    .dropdown > .dropdown-menu {
    padding: 25px 25px 25px 25px;
    }
    .upload-zone_audio,
    .upload-zone {
        background: #fff;
        border: none;
        min-height: 500px;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .upload-zone_audio::before,
    .upload-zone::before {
        content: "";
        position: absolute;
        border: 1px solid #ddd;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 10px;
        background: #f8f8f8;
    }
    .upload-zone.dragOver::before {
        content: "Drop your video file here";
        font-size: 24px;
        color: rgb(0 0 0);
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        border: 10px solid #f0f0f0;
        top: -5px;
        bottom: -5px;
        left: -5px;
        right: -5px;
    }
    .upload-zone_audio.dragOver::before {
        content: "Drop your audio file here";
        font-size: 24px;
        color: rgb(0 0 0);
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        border: 10px solid #f0f0f0;
        top: -5px;
        bottom: -5px;
        left: -5px;
        right: -5px;
    }
    .upload-zone_audio.dragOver,
    .upload-zone.dragOver {
        background: #f8f8f8;
        border: none;
    }
    .upload-zone_audio.no-border::before,
    .upload-zone_audio.no-border {
        background: #fff;
        border: none;
    }
    #main_form h3.mb-3 {
        font-size: 18px !important;
    }
    .bottom-fixed-buttons {
        position: fixed;
        bottom: 0;
        z-index: 11;
        padding: 20px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: calc(100vw - 440px);
        max-width: 1170px;
        background: rgba(255, 255, 255, 1);
        border-top: 1px solid #F0F0F0;
    }
    .popup.add-course-video {
        max-width: 600px;
    }
    .upload-zone_audio, .upload-zone {
        min-height: 270px;
    }


/* Nestable list */
.dd {
    position: relative;
    display: block;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 13px;
    line-height: 20px;
  }
  .dd-list {
    display: block;
    position: relative;
    margin: 0 0 0 -15px;
    padding: 0;
    list-style: none;
  }
  .dd-list .dd-list {
    padding-left: 35px;
    padding-bottom: 25px;
  }
  .dd-collapsed .dd-list {
    display: none;
  }
  .dd-item,
  .dd-empty,
  .dd-placeholder {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    min-height: 20px;
    font-size: 13px;
    line-height: 20px;
  }

  .coursecontent-box {
    border-top:1px solid #f0f0f0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
} 
  .coursecontent-box > .dd-item.dd3-item {
    border-left:1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
    padding-bottom: 22px;
    padding-left: 23px;
    padding-right: 20px;
    position:relative;
}
.coursecontent-box > .dd-item.dd3-item:after {
    content:"";
    width:100%;
    height:1px;
    background:#f0f0f0;
    position:absolute;
    left:30px;
    width: calc(100% - 60px);
}
.coursecontent-box > .dd-item.dd3-item:first-of-type {height:4000px;}
.coursecontent-box > .dd-item.dd3-item:last-of-type:after {display:none;}
.coursecontent-box > .dd-item.dd3-item > .dd-handle.dd3-handle {margin-left:23px;}
.coursecontent-box  .dd-item .dd-handle svg {margin-top:-2px;}

.coursecontent-box > .first-nested:last-of-type {
    border-bottom:1px solid #f0f0f0;
    margin-bottom:0;
    padding-bottom:0;
    border-bottom-left-radius:10px;
    border-bottom-right-radius:10px;
}
.coursecontent-box :nth-child(1 of .first-nested) {
    padding-top:27px; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
 
  .dd3-flex {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100%;
    padding-left: 0;
  }
  .dd-handle {
    display: block;
    margin: 5px 0;
    padding: 0;
    color: #333;
    text-decoration: none;
    border: none;
    background: #fff;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
  .dd-handle span {
    font-weight: bold;
  }
  .dd-handle:hover {
    /* background: #fff; */
    cursor: pointer;
    font-weight: bold;
  }
  .dd-item > button {
    display: block;
    position: relative;
    cursor: pointer;
    float: left;
    width: 25px;
    height: 45px;
    margin: 0px 0;
    padding: 0;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: 0;
    background: transparent;
    font-size: 12px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
  }
  .dd-item > button:before {
    content: '+';
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    text-indent: 0;
  }
  .dd-item > button[data-action="collapse"] {display:none !important;}
  .dd-item > button[data-action="collapse"]:before {
    content: '-';
  }
  #nestable2 .dd-item > button {
    font-family: FontAwesome;
    height: 34px;
    width: 33px;
    color: #c1c1c1;
  }
  #nestable2 .dd-item > button:before {
    content: "\f067";
  }
  #nestable2 .dd-item > button[data-action="collapse"]:before {
    content: "\f068";
  }
  .dd-placeholder,
  .dd-empty {
    margin: 5px 0;
    padding: 0;
    min-height: 30px;
    background: #fff;
    border: 1px dashed #b6bcbf;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
  .dd-empty {
    border: 1px dashed #bbb;
    min-height: 100px;
    background-color: #e5e5e5;
    background-image: -webkit-linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff), -webkit-linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff);
    background-image: -moz-linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff), -moz-linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff);
    background-image: linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff), linear-gradient(45deg, #ffffff 25%, transparent 25%, transparent 75%, #ffffff 75%, #ffffff);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
  }
  .dd-dragel {
    position: absolute;
    z-index: 9999;
    pointer-events: none;
  }
  .dd-dragel > .dd-item .dd-handle {
    margin-top: 0;
  }
 
  /* .dd-dragel .dd-handle {
    -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
  } */
  /**
  * Nestable Extras
  */
  .nestable-lists {
    display: block;
    clear: both;
    padding: 30px 0;
    width: 100%;
    border: 0;
    border-top: 2px solid #ddd;
    border-bottom: 2px solid #ddd;
  }
  #nestable-menu {
    padding: 0;
    margin: 10px 0 20px 0;
  }
  #nestable-output,
  #nestable2-output {
    width: 100%;
    font-size: 0.75em;
    line-height: 1.333333em;
    font-family: open sans, lucida grande, lucida sans unicode, helvetica, arial, sans-serif;
    padding: 5px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
  #nestable2 .dd-handle {
    color: inherit;
    /* border: 1px dashed #e7eaec; */
    /* background: #f3f3f4; */
    padding: 10px;
  }
  #nestable2 span.label {
    margin-right: 10px;
  }
  #nestable-output,
  #nestable2-output {
    font-size: 12px;
    padding: 25px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
  /**
   * Nestable Draggable Handles
   */

  .dd3-content {
    display: block;
    height: 40px;
    margin: 0;
    padding: 5px 10px 5px 35px;
    color: #333;
    text-decoration: none;
    /* font-weight: bold; */
    border: 0px solid #d0d0d0;
    background: #fafafa;
    background: -webkit-linear-gradient(top, #fafafa 0%, #eee 100%);
    background: #fff;
    background: linear-gradient(top, #fafafa 0%, #eee 100%);
    -webkit-border-radius: 3px;
    border-radius: 0px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
  }
  .dd3-content:hover { color: #000; background: #fff; }

  .dd-dragel > .dd3-item > .dd3-content { margin: 0; }

 
  .dd3-handle {
        position: absolute;
        margin: 0;
        left: 0;
        cursor: pointer;
        width: 30px;
        text-indent: 100%;
        white-space: nowrap;
        overflow: hidden;
        border: none;
        /* background: #ddd; */
        /* background: -webkit-linear-gradient(top, #ddd 0%, #bbb 100%); */
        /* background: #fff; */
        /* background: linear-gradient(top, #ddd 0%, #bbb 100%); */
        /* border-top-right-radius: 0; */
        /* border-bottom-right-radius: 0; */
        height: 43px;
        display: flex;
        align-items: center;
        justify-content: center;
  }
  /*.dd3-handle::before {
  content: '≡';
  display: block;
  position: absolute;
  left: 5px;
  top: 12px;
  width: 100%;
  text-align: center;
  text-indent: 0;
  color: #000;
  font-size: 24px;
  font-weight: normal;
  }*/
       
  /* .dd3-handle:hover { background: #fff; } */

  /*Dropdown checkboxes*/ 
.dropdown-container {position: relative;}
.dropdown-label {font-size:14px; color:#969696;}
.dropdown-label:after {content: ""; width:8px; height:5px; background: url(/admin_assets_new/images/triangle-down.svg) no-repeat center center/cover; position: absolute; right: 15px; top: 50%; margin-top: -3px;}
/*.dropdown-container.is-active .dropdown-label:after {content: "\25B2";}*/
.dropdown-button {cursor: pointer;  border: 1px solid #f0f0f0; background: white; display: flex; flex-flow: row wrap;}
.dropdown-quantity {flex: 1;display: flex; flex-flow: row wrap; flex-basis: 100%; }
.dropdown-list {position: absolute; overflow-y: auto; z-index: 9999999; top: 55px; width: 100%; max-height: 250px; padding: 15px 20px 0 20px; border: 1px solid #ddd !important; border-top: 0; background: white; display: none; max-width: 500px; box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);}
.dropdown-container.is-active .dropdown-list {display: block; border-radius:10px;}
.dropdown-list input[type="search"] {padding: 5px; display: block; width: 100%;}
.dropdown-list ul {padding: 0; padding-top: 10px; list-style: none;}
.dropdown-list li {padding: 0.24em 0;}
input[type="checkbox"] {margin-right: 5px;}
.dropdown-container .is-hidden { display: none; }



@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
}
.custom-selectbox:not(.with-checkboxes) ul li:before {
    display: none !important;
}
.custom-selectbox:not(.with-checkboxes) ul li {
    padding: 8px 0px 0px 10px !important;
}
.custom-selectbox:not(.with-checkboxes) ul {
	margin: 8px 0;
}
.custom-selectbox:not(.with-checkboxes) ul li {
	padding: 6px 10px 6px 20px !important;
	margin-bottom: 0;
}
.custom-selectbox:not(.with-checkboxes) ul li:hover {
	background: #fbfbfb;
}
.custom-select.error .custom-selectbox:not(.with-checkboxes) {
	border-color: red !important;
}
.graycheckbox, .checkbox-group .form-box {
	background: #fff;
}
.custom-selectbox:not(.with-checkboxes) ul {
    overflow-y: auto !important;
}
.custom-selectbox:not(.with-checkboxes) ul li:last-child {
	margin-bottom: 0;
}

</style>
<script src="https://cdn.tiny.cloud/1/yf1kz5c9m4nsr4ly6jwcpk1z4hejqt6hx6mgkhamw01cn2gw/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'Upload' ?> Course <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                <span class="btn btn-xs red-bg white f-1 ml-auto" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                <!--<a href="javascript:;" data-anchor="#courses" class="btn black-bg white ml-auto" title="Courses">Courses</a>-->
                <a href="admin/courses" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
        </div>
        <div class="container"><hr class="mt-0 mb-45"></div>
        <form action="admin/courses/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom courses_form mb-mob-0" id="main_form">
            <h5 class="mb-45 f-14 semibold">CUSTOM THUMBNAIL</h5>
            <!--<p class="midGray mb-55 f-14">Select or upload a photo that shows what's in your video. A good thumbnail stands out and draws viewers' attention.</p>-->
            <div class="image_container flex aic">
                <div class="upload-image small-uplad-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="mt-5 mb-45">
            <!--<h5 class="mb-45 f-14 semibold">COVER PHOTO</h5>-->
            <!--<p class="midGray mb-55 f-14">Select or upload a photo that shows what's in this course.</p>-->
            <!--<div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image cover_image_size" id="image_container">
                    <input type="file" name="cover_image" id="cover_image">
                    <img src="<?php // echo empty($current['cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['cover_image']; ?>" alt="" class="image_preview <?php // echo empty($current['cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo empty($current['cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php // echo empty($current['cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>-->
            <!--<hr class="mt-5 mb-45">-->

            <h5 class="mb-4 f-14 semibold">COURSE INFO</h5>

            <div class="row">
                <div class="col-12">
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container" style="position: relative;">
                        <input type="text" name="title" class="line-input black make_slug" placeholder="Enter" data-slug_target="#slug" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />

                    </div>
                </div>
            </div>

            <h5 class="mb-1 f-11">CATEGORY *</h5>
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div id="content_container" style="position: relative;z-index: 110;width: 100%">
                    <div class="custom-select">
                        <div class="custom-selectbox-holder w100 mb-0">
                            <div class="custom-selectbox">
                                <span class="select_val" style="<?php echo (isset($current['category']) AND $current['category'] != '') ? 'color: #000' : 'color: #969696'; ?>"><?php echo (isset($current['category']) AND $current['category'] != '') ? $current['category'] : 'Select'; ?></span>
                                <ul style="display: none;">
                                    <li data-val="Intro Series" onclick="$('[name=category]').val('Intro Series');$(this).parent().prev().css('color','#000')" class="">Intro Series</li>
                                    <li data-val="Conditioning series" data-duration="Conditioning series" onclick="$('[name=category]').val('Conditioning series');$(this).parent().prev().css('color','#000')" class="">Conditioning series</li>
                                </ul>
                                <input type="hidden" name="category" class="select-custom" value="<?php echo (isset($current['category']) AND $current['category'] != '') ? $current['category'] : ''; ?>" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h5 class="mb-1 f-11">PAGE URL</h5>
                    <div class="input-container" id="title_container">
                    <input type="text" name="slug" class="line-input" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" style="padding-left: 279px;">
                    <span class="base_url">https://www.lagreeod.com/courses/</span>
                    </div>
                </div>
            </div>


            <h5 class="mb-1 f-11">MACHINE *</h5>
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'Selected (' . count($current_machines) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
                    foreach($machines as $single){
                    ?>
                    <div class="checkbox mb-15" id="machine_container">
                        <input type="checkbox" class="available_machines machinecheck" name="machine[]" data-name="<?php echo $single['title']; ?>" id="machine_select<?php echo isset($single['id']) ? $single['id'] : 0; ?>" <?php echo (isset($single['id']) AND in_array($single['id'], $curr_machines)) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>                        
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if(count($curr_machines)){
                        foreach($machines as $single){
                            foreach($curr_machines as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="machine_select<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                ?>                    
                </div>
            </div>

            <h5 class="mb-1 f-11">TEACHERS *</h5>
            <?php 
                $teach = [];
                foreach($all_teachers as $single) {
                    $teach[$single['id']] = ((isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : '');
                }
                $curr_teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0; 
            ?>
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['teacher']) AND $current['teacher'] != '') ? (isset($teach[$current['teacher']]) ? 'black' : '') : ''; ?>"><?php echo (isset($current['teacher']) AND $current['teacher'] != '' AND $current['teacher'] != 0) ? (isset($teach[$current['teacher']]) ? $teach[$current['teacher']] : 'Select') : 'Select'; ?></div>
                </div>
                <div class="col-12 dropdown-list">
                    <div class="w100">
                <?php
                $c=0;
                $teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
                foreach($all_teachers as $single){
                $c++;
                    if(isset($current['type']) AND $current['type'] == 1){
                ?>
                        <div class="checkbox mb-15" id="teacher_container" style="<?php echo ($single['id'] != $teacher) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-12"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>
                        <?php
                        }else{
                        ?>
                        <div class="checkbox mb-15" id="teacher_container" style="<?php echo ($single['certified'] == 0) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-12"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>

                        <?php
                        }
                        ?>
                    <?php
                    }
                    ?>
                    </div>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if($curr_teacher > 0){
                        foreach($all_teachers as $single){
                            if($single['id'] == $curr_teacher){
                    ?>
                    <span class="dropdown-sel"><?php echo ((isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''); ?><span class="remove_tag" data-uncheck="teacher<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                ?>                    
                </div>
            </div>

            <div class="row all_courses" <?php echo (isset($current['id']) AND $current['id'] > 0) ? '' : 'style="display: block"'; ?>>
                <div class="col-8">
                <h5 class="mb-1 f-11">COURSE DURATION</h5>
                    <div class="input-container mb-0" id="title_container">
                        <input type="text" name="course_duration" id="course_duration" placeholder="Enter total course duration eg. '6-weeks'" class="line-input" value="<?php echo isset($current['course_duration']) ? $current['course_duration'] : '' ?>">
                    </div>
                </div>
            </div>

            <hr class="mt-5 mb-45">
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">DESCRIPTION</h5>
                    <h5 class="mb-1 f-11">SHORT DESCRIPTION *</h5>
                    <div class="input-container" id="short_desc_container">
                        <textarea type="text" name="short_desc" class="line-input" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500" placeholder="Enter" style="min-height: 275px;"><?php echo isset($current['short_desc']) ? $current['short_desc'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">500</span></p>
                    </div>
                </div>
            </div>
 
            <div class="row mt-2">
                <div class="col-12">
                    <div class="input-container" id="content_container">
                        <h5 class="mb-1 f-11">INTRODUCTION *</h5>
                        <textarea type="text" name="content" class="line-input" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="1000" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">1000</span></p>
                    </div>
                </div>
            </div>
            <hr class="mt-2 mb-0">

            <!--<hr class="mt-4 mb-55 all_courses" <?php echo (isset($current['id']) AND $current['id'] > 0) ? '' : 'style="display: none"'; ?>>-->

            <div class="all_courses" id="courses" <?php echo (isset($current['id']) AND $current['id'] > 0) ? '' : 'style="display: none"'; ?>>
                <div class="col-12 flex jcsb aic px-0 mt-4 pb-4 cc-threebtn-box" style="position: sticky;top: 0;background: #fff;z-index: 11;">
                    <h4 class="f-14 mb-mob-1">CONTENT</h4>
                    <h4 class="cc-threebtn">
                        <a href="javascript:;" title="add title" class="btn black-bg f-12 white btn-xs ml-auto" data-popup="add-course-video-title" data-id="0" style="font-size: 10px !important;">ADD TITLE</a>
                        <a href="javascript:;" title="add break" class="btn black-bg f-12 white btn-xs ml-1" data-popup="add-course-video-break" data-id="0" style="font-size: 10px !important;">ADD REST DAY</a>
                        <a href="javascript:;" title="add video" class="btn black-bg f-12 white btn-xs ml-1" data-popup="add-course-video" data-id="0" style="font-size: 10px !important;">ADD VIDEO</a>
                    </h4>
                </div>
                
                <div class="col-12 pr-0">
                    <div class="dd" id="nestable3">
                        <ol class="dd-list coursecontent-box">
                            <li>
                            <?php
                            $c=0;
                            if(isset($all_course_videos) AND count($all_course_videos) > 0){
                                $lastKey = array_search(end($all_course_videos), $all_course_videos);
                                foreach ($all_course_videos as $key => $single){
                                    $c++;
                                    if($single['type'] == 'title'){
                            ?>
                            <?php if($c > 1){ ?>
                                    </li>
                                </ol>
                            <?php } ?>
                            <li class="dd-item dd3-item first-nested" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                                <span class="dd-handle dd3-handle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="15" viewBox="0 0 17 15">
                                <g id="Group_13496" data-name="Group 13496" transform="translate(-1711 -18)">
                                    <rect id="Rectangle_1337" data-name="Rectangle 1337" width="17" height="2" transform="translate(1711 18)"/>
                                    <rect id="Rectangle_1338" data-name="Rectangle 1338" width="17" height="2" transform="translate(1711 24.5)"/>
                                    <rect id="Rectangle_1339" data-name="Rectangle 1339" width="17" height="2" transform="translate(1711 31)"/>
                                </g>
                                </svg>
                                </span>
                                <div class="dd3-content">
                                    <div class="dd3-flex">
                                        <h4 class="f-14 semibold"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h4>
                                        <span class="edit-course-video ml-auto" data-popup="add-course-video-<?php echo $single['type']; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">Edit</span>
                                        <span class="remove-course-video delete_record" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="CoursesVideos" data-popup="delete-popup">Delete</span>
                                    </div>
                                </div>
                                <ol class="dd-list">
                                    <?php
                                    }else if($single['type'] == 'video' OR $single['type'] == 'break'){
                                    ?>
                                    <li class="dd-item dd3-item" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                                        <span class="dd-handle dd3-handle">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="15" viewBox="0 0 17 15">
                                        <g id="Group_13496" data-name="Group 13496" transform="translate(-1711 -18)">
                                            <rect id="Rectangle_1337" data-name="Rectangle 1337" width="17" height="2" transform="translate(1711 18)"/>
                                            <rect id="Rectangle_1338" data-name="Rectangle 1338" width="17" height="2" transform="translate(1711 24.5)"/>
                                            <rect id="Rectangle_1339" data-name="Rectangle 1339" width="17" height="2" transform="translate(1711 31)"/>
                                        </g>
                                        </svg>

                                        </span>
                                        <div class="dd3-content">
                                            <div class="dd3-flex">
                                                <p class="f-12 normal"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></p>
                                                <span class="edit-course-video ml-auto" data-popup="add-course-video<?php echo $single['type'] == 'break' ? '-break' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">Edit</span>
                                                <span class="remove-course-video delete_record" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="CoursesVideos" data-popup="delete-popup">Delete</span>
                                            </div>
                                        </div>
                                    </li>
                            <?php if($key === $lastKey){ ?>
                                </ol>
                            <?php } ?>
                        <?php
                                }
                        ?>
                            </li>
                        <?php
                            }
                        }
                        ?>
                        </ol>
                    </div>
                    <input type="hidden" id="nestable-output" />
                </div>
            </div>
 
            <div class="row w100 mt-mob-25">
                <div class="col-12 pl-3">
                    <input type="hidden" name="type" id="type" value="<?php echo isset($current['type']) ? $current['type'] : 0 ?>">
                    <input type="hidden" name="prev_status" id="prev_status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <!-- <input type="hidden" name="created_at" id="created_at" value="<?php // echo isset($current['created_at']) ? $current['created_at'] : 0 ?>"> -->
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">


                    <?php if(isset($prev) OR isset($next)){ ?>
                        <div class="bottom-fixed-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? 'style="display: none;"' : '';?>>
                            <?php if(isset($prev)){ ?>
                                <a href="admin/courses/edit/<?php echo isset($prev['id']) ? $prev['id'] : 0; ?>" class="link link-black black text-underline f-14 mr-2">Previous Course</a>
                            <?php } ?>
                            <?php if(isset($next)){ ?>
                                <a href="admin/courses/edit/<?php echo isset($next['id']) ? $next['id'] : 0; ?>" class="link link-black black text-underline f-14">Next Course</a>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="container w100 px-0 mt-5 mt-mob-15">
                <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                <div class="default-buttons flex aic">
                    <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                    <a href="/admin/courses" class="cancel-link ml-2" title="Cancel">Cancel</a>
                    <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="courses" data-popup="delete-popup" title="Cancel">DELETE COURSE</a>
                </div>
                <?php }else{ ?>
                <div class="default-buttons flex aic">
                    <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                    <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                    <a href="/admin/courses" class="cancel-link" title="Cancel">Cancel</a>
                </div>
                <?php } ?>
            </div>
        </form>
    </div>
</main>



<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/courses.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/jquery.nestable.js"></script>
<script>
var course_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var course_item_sort = <?php echo (isset($current['id']) AND count($all_course_videos) > 0) ? count($all_course_videos) : 0; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
var statuss = 0;
$(document).on('change', '#lower_body', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="2"]').prop('checked', true);
    }else{
        $('[data-group="2"]').prop('checked', false);
    }
});
$(document).on('change', '#upper_body', function(e){
    $('#full_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="1"]').prop('checked', true);
    }else{
        $('[data-group="1"]').prop('checked', false);
    }
});
$(document).on('change', '#full_body', function(e){
    if($(this).is(':checked')){
        $('#lower_body').prop('checked', false);
        $('#upper_body').prop('checked', false);
        $('[data-group]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});

$(document).on('click', '[data-popup="add-course-video"]', function(){
    var id = $(this).data('id');
    $.ajax({
        type: 'POST',
        url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            $('.popup-html').html("");
            $('.popup-html').html(data.html);
            // setTimeout(function(){
            //     console.log('select_machine()');
            //     select_machine();
            // }, 500);
        },
        error: function (request, status, error) {
            console.log('Error');
        }
    });
});
$('[data-popup="add-course-video"]').on('click', function(){
    var id = $(this).data('id');
    $.ajax({
        type: 'POST',
        url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            $('.popup-html').html("");
            $('.popup-html').html(data.html);
            // setTimeout(function(){
            //     console.log('select_machine()');
            //     select_machine();
            // }, 500);
        },
        error: function (request, status, error) {
            console.log('Error');
        }
    });
});
$(document).on('click', '[data-popup="add-course-video-title"]', function(){
    var id = $(this).data('id');
    if(id != 0){
        $('#course_item_title_id').val(id);
        $.ajax({
            type: 'POST',
            url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('#course_item_title').val(data.title);
                $('#course_item_text').val(data.description);
                $('#course_item_sort').val(data.sort);
                course_item_sort++;
            },
            error: function (request, status, error) {
                console.log('Error');
            }
        });
    }else{
        $('#add-course-video-title').trigger('reset');
        $('#add-course-video-title').find('#course_item_title_id').val('');
        $('#course_item_sort').val(course_item_sort+1);
    }
});
$('[data-popup="add-course-video-title"]').on('click', function(){
    var id = $(this).data('id');
    if(id != 0){
        $('#course_item_title_id').val(id);
        $.ajax({
            type: 'POST',
            url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('#course_item_title').val(data.title);
                $('#course_item_text').val(data.description);
                $('#course_item_sort').val(data.sort);
                course_item_sort++;
            },
            error: function (request, status, error) {
                console.log('Error');
            }
        });
    }else{
        $('#add-course-video-title').trigger('reset');
        $('#add-course-video-title').find('#course_item_title_id').val('');
        $('#course_item_sort').val(course_item_sort+1);
    }
});
$(document).on('click', '[data-popup="add-course-video-break"]', function(){
    var id = $(this).data('id');
    if(id != 0){
        $('#course_item_break_id').val(id);
        $.ajax({
            type: 'POST',
            url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('#course_item_title').val(data.title);
                $('#course_break_sort').val(data.sort);
                course_item_sort++;
            },
            error: function (request, status, error) {
                console.log('Error');
            }
        });
    }else{
        $('#add-course-video-break').trigger('reset');
        $('#add-course-video-break').find('#course_item_break_id').val('');
        $('#course_break_sort').val(course_item_sort+1);
    }
});
$('[data-popup="add-course-video-break"]').on('click', function(){
    var id = $(this).data('id');
    if(id != 0){
        $('#course_item_break_id').val(id);
        $.ajax({
            type: 'POST',
            url: 'admin/courses/get_course_video_item_ajax/' + id + '/' + course_id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('#course_item_break').val(data.title);
                $('#course_break_sort').val(data.sort);
                course_item_sort++;
            },
            error: function (request, status, error) {
                console.log('Error');
            }
        });
    }else{
        $('#add-course-video-title').trigger('reset');
        $('#add-course-video-title').find('#course_item_break_id').val('');
        $('#course_break_sort').val(course_break_sort+1);
    }
});
/* COURSE VIDEO TITLE FORM */
$('#add-course-video-title').on('submit', function(e){
	console.log('add-course-video-title submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');

    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Course item saved', 'success');
                close_all();
                button.removeClass('btn--loading');
                if(data.new_item){
                    $('.dd > .dd-list').append('<li class="dd-item dd3-item" data-id="' + data.course_video_item.id + '">'+
                                                    '<span class="dd-handle dd3-handle"></span>'+
                                                    '<div class="dd3-content">'+
                                                        '<div class="dd3-flex">'+
                                                            '<h4 class="f-14 semibold">' + data.course_video_item.title + '</h4>'+
                                                            '<span class="edit-course-video ml-auto" data-popup="add-course-video" data-id="' + data.course_video_item.id + '">Edit</span>'+
                                                            '<span class="remove-course-video delete_record" data-id="' + data.course_video_item.id + '" data-table="CoursesVideos" data-popup="delete-popup">Delete</span>'+
                                                        '</div>'+
                                                    '</div>'+
                                                '</li>'
                    );
                }else{
                    $('.dd-item[data-id="' + data.course_video_item.id + '"]').find('h4').text(data.course_video_item.title);
                };
                form.trigger('reset');
                // read_videos();
                custom_sort_courses();
                // window.location.reload();
            }else{
                console.log('NO SUCCESS');
                app_msg("Please check all required fields", 'danger');
                button.removeClass('btn--loading');
                // read_videos();
                custom_sort_courses();
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
            button.removeClass('btn--loading');
        }
    });
});
/* COURSE VIDEO FORM */
$(document).on('submit', '#add-course-video', function(e){
	console.log('add-course-video submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');

    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Course item saved', 'success');
                close_all();
                button.removeClass('btn--loading');
                if(data.new_item){
                    $('.dd > .dd-list').append('<li class="dd-item dd3-item" data-id="' + data.course_video_item.id + '">'+
                                                    '<span class="dd-handle dd3-handle"></span>'+
                                                    '<div class="dd3-content">'+
                                                        '<div class="dd3-flex">'+
                                                            '<p class="f-14 normal">' + data.course_video_item.title + '</p>'+
                                                            '<span class="edit-course-video ml-auto" data-popup="add-course-video" data-id="' + data.course_video_item.id + '">Edit</span>'+
                                                            '<span class="remove-course-video delete_record" data-id="' + data.course_video_item.id + '" data-table="CoursesVideos" data-popup="delete-popup">Delete</span>'+
                                                        '</div>'+
                                                    '</div>'+
                                                '</li>'
                    );
                }else{
                    $('.dd-item[data-id="' + data.course_video_item.id + '"]').find('p').text(data.course_video_item.title);
                }
                // read_videos();
                custom_sort_courses();
                // window.location.reload();
            }else{
                console.log('NO SUCCESS');
                app_msg("Please check all required fields", 'danger');
                button.removeClass('btn--loading');
                // read_videos();
                custom_sort_courses();
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
            button.removeClass('btn--loading');
        }
    });
});
/* COURSE VIDEO BREAK FORM */
$('#add-course-video-break').on('submit', function(e){
	console.log('add-course-video-break submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');

    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Course item saved', 'success');
                close_all();
                button.removeClass('btn--loading');
                if(data.new_item){
                    $('.dd > .dd-list').append('<li class="dd-item dd3-item" data-id="' + data.course_video_item.id + '">'+
                                                    '<span class="dd-handle dd3-handle"></span>'+
                                                    '<div class="dd3-content">'+
                                                        '<div class="dd3-flex">'+
                                                            '<h5 class="f-14 normal">' + data.course_video_item.title + '</h5>'+
                                                            '<span class="edit-course-video ml-auto" data-popup="add-course-video-break" data-id="' + data.course_video_item.id + '">Edit</span>'+
                                                            '<span class="remove-course-video delete_record" data-id="' + data.course_video_item.id + '" data-table="CoursesVideos" data-popup="delete-popup">Delete</span>'+
                                                        '</div>'+
                                                    '</div>'+
                                                '</li>'
                    );
                }else{
                    $('.dd-item[data-id="' + data.course_video_item.id + '"]').find('p').text(data.course_video_item.title);
                };
                form.trigger('reset');
                // read_videos();
                custom_sort_courses();
                // window.location.reload();
            }else{
                console.log('NO SUCCESS');
                app_msg("Please check all required fields", 'danger');
                button.removeClass('btn--loading');
                // read_videos();
                custom_sort_courses();
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
            button.removeClass('btn--loading');
        }
    });
});

function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
// if($('.single-course-items').length){
    /* var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".single-course-items").sortable({
        connectWith: '.sub_courses',
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("id");
                // var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    // type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/courses/sort_courses_videos_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        // app_msg('Order Saved', 'success', 1000);
                        read_videos();
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection(); */
    var updateOutput = function (e) {
        console.log('updateOutput');
        console.log(e);
        var list = e.length ? e : $(e.target),
        output = list.data('output');
        console.log(list.nestable('serialize'));
        if (window.JSON) {
            output.val(window.JSON.stringify(list.nestable('serialize')));
            setTimeout(function(){
                save_order();
                console.log('auto saved order');
            }, 50);
        } else {
            output.val('JSON browser support required!');
        }
    };
    $('#nestable3').nestable({group: 3}).on('change', updateOutput);

    // output initial serialised data
    updateOutput($('#nestable3').data('output', $('#nestable-output')));
    console.log(updateOutput($('#nestable3').data('output', $('#nestable-output'))));

    $('#nestable-menu').on('click', function (e) {
        var target = $(e.target),
        action = target.data('action');
        if (action === 'expand-all') {
            $('.dd').nestable('expandAll');
        }
        if (action === 'collapse-all') {
            $('.dd').nestable('collapseAll');
        }
    });
// }
function save_order() {
	current_order = $('#nestable-output').val();
	console.log(current_order);

    // SAVE ORDER
    var post_data = {
        "sorting": []
    };
    $('.dd3-item').each(function(i) {
        var section_id = $(this).data("id");
        var pom = {
            id: section_id,
            sort: i + 1
        };
        post_data.sorting.push(pom);
    });
    console.log(post_data);
    $.ajax({
        type: "POST",
        url: "admin/courses/sort_courses_videos_table",
        data: post_data,
        dataType: "json",
        success: function (data) {
            console.log(data);
            if (data.success) {
                console.log('SUCCESS');
                // app_msg('Order Saved', 'success', 1000);
            } else {
                console.log('NO SUCCESS');
            }
        },
        error: function (result) {
            console.log('ERROR WITH PHP');
        }
    });
};
function custom_sort_courses(){
    var post_data = {
        "sorting": []
    };
    $(".dd3-item").each(function(i) {
        var section_id = $(this).data("id");
        var pom = {
            id: section_id,
            sort: i + 1
        };
        post_data.sorting.push(pom);
    });
    console.log(post_data);
    $.ajax({
        type: "POST",
        url: "admin/courses/sort_courses_videos_table",
        data: post_data,
        dataType: "json",
        success: function (data) {
            console.log(data);
            if (data.success) {
                console.log('SUCCESS');
                // app_msg('Order Saved', 'success', 1000);
                // read_videos();
            } else {
                console.log('NO SUCCESS');
            }
        },
        error: function (result) {
            console.log('ERROR WITH PHP');
        }
    });
}
/* function countElementsByClassAfterTitles(titleClass, elementClass) {
    const targetTitles = document.getElementsByClassName(titleClass);
    if (targetTitles.length === 0) {
        throw new Error(`No title elements with class "${titleClass}" found.`);
    }

    const elementCounts = [];
    for (let i = 0; i < targetTitles.length; i++) {
        const targetTitle = targetTitles[i];
        let elementCount = 0;
        let sibling = targetTitle.nextElementSibling;
        while (sibling) {
            if (sibling.classList.contains(elementClass)) {
                elementCount++;
            }else if (sibling.classList.contains(titleClass)) {
                break;
            }
            sibling = sibling.nextElementSibling;
        }
        elementCounts.push(elementCount);
    }

    return elementCounts;
}
function read_videos(){
    const titleClass = 'single-course-title';
    const elementClass = 'single-course-video';
    const elementCounts = countElementsByClassAfterTitles(titleClass, elementClass);
    elementCounts.forEach((count, index) => {
        console.log(count);
        $('.single-course-title:eq(' + index + ') h4').attr('data-videos', count);
    });
} */
$(document).ready(function(){
    // read_videos();
});
</script>

<script>//Checkboxes inside dropdwon
const $dropdown = $('.dropdown-container'); // Cache all;

function UI_dropdown() {
    const $this = $(this);
    const $btn = $('.dropdown-button', this);
    const $list = $('.dropdown-list', this);
    const $li = $('li', this);
    const $search = $('.dropdown-search', this);
    const $ckb = $(':checkbox, :radio', this);
    const $qty = $('.dropdown-quantity', this);

    $btn.on('click', function() {
        $dropdown.not($this).removeClass('is-active'); // Close other
        $this.toggleClass('is-active'); // Toggle this
    });

    $search.on('input', function() {
        const val = $(this).val().trim();
        const rgx = new RegExp(val, 'i');
        $li.each(function() {
        const name = $(this).text().trim();
        $(this).toggleClass('is-hidden', !rgx.test(name));
        });
    });

    $ckb.on('change', function(elem) {
        const names = $ckb.get().filter(el => el.checked).map(el => {
            return `<span class="dropdown-sel">${el.dataset.name.trim()}<span class="remove_tag" data-uncheck="${el.id.trim()}">×</span></span>`;
        });
        var num = $(elem.target).closest('.dropdown-container').find(':checked').length;
        setTimeout(function(){
            if(num > 0){
                $(elem.target).closest('.dropdown-container').find('.input-container').removeClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
            }else{
                $(elem.target).closest('.dropdown-container').find('.input-container').addClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').removeClass('black').text('Select');
            }
        }, 50);
        $qty.html(names.join(''));
        select_machine();
    });
}

$dropdown.each(UI_dropdown); // Apply logic to all dropdowns

// Dropdown - Close opened 
$(document).on('click', function(ev) {
    const $targ = $(ev.target).closest('.dropdown-container');
    if (!$targ.length) $dropdown.filter('.is-active').removeClass('is-active');
});
</script> 
  
<script>//**************SHOW-HIDE***************
$('.hide_bottom').click(function() { //CUSTOM THUMBNAIL SECTION
    $(this).parent().next().slideToggle();
    $(this).text() == 'SHOW' ? $(this).text('HIDE') : $(this).text('SHOW');
});
$('html, body').on('click', function(e){
    $('.custom-selectbox').closest('.custom-select').removeClass('active');
});
$('.custom-selectbox:not(.with-checkboxes) li').on('click', function(e){
    e.stopPropagation();
    var xx = $(this);
    var parent = $(this).closest('.custom-selectbox');
    var val = $(this).data("val");
    var text = $(this).text();

    parent.find('li').removeClass('checked');
    xx.addClass('checked');
    parent.find('input').val(val);
    parent.find('.select_val').html(text + ' <span class="select_count"></span>');

    $(this).closest('.custom-select').removeClass('open_top active');
    $('.custom-selectbox').find('ul').slideUp(200);
    $('.custom-selectbox').removeClass('opened');

    $(this).closest('.single_question').find('h5 span').remove();
    $(this).closest('.custom-select').removeClass('error');
});
$(document).on('click', '.custom-selectbox:not(.with-checkboxes) li', function(e){
    e.stopPropagation();
    var xx = $(this);
    var parent = $(this).closest('.custom-selectbox');
    var val = $(this).data("val");
    var text = $(this).text();

    parent.find('li').removeClass('checked');
    xx.addClass('checked');
    parent.find('input').val(val);
    parent.find('.select_val').html(text + ' <span class="select_count"></span>');

    $(this).closest('.custom-select').removeClass('open_top active');
    $('.custom-selectbox').find('ul').slideUp(200);
    $('.custom-selectbox').removeClass('opened');

    $(this).closest('.single_question').find('h5 span').remove();
    $(this).closest('.custom-select').removeClass('error');
});
</script>
</body>
</html>
