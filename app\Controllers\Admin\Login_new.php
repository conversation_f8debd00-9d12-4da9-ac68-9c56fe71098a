<?php namespace App\Controllers\Admin;

use App\Models\NewsModel;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\IncomingRequest;

class Login extends Controller
{
    public function index()
    {
		echo view('admin/login_view');
    }

    public function validate_login()
    {
		$request = service('request');
		$response['post'] = $request->getPost();
		$response['files'] = $this->request->getFiles();
		
		if ($response['files']['avatar']->isValid())
		{
			$file = $response['files']['avatar'];
			$name = $file->getRandomName();
			$file->move(ROOTPATH . 'public/uploads', $name);
		}
		var_dump($this->request->getFiles());
		//var_dump($response);
		//echo json_encode($response, JSON_PRETTY_PRINT);
    }
	
	public function create()
	{
		$model = new NewsModel();

		if (! $this->validate([
			'email' => 'required|min_length[3]|max_length[255]',
			//'body'  => 'required'
		]))
		{
			echo view('templates/header', ['title' => 'Create a news item']);
			echo view('news/create');
			echo view('templates/footer');
		}
		else
		{
			echo '<pre>';
			$request = service('request');
			var_dump($request->getPost());
			
			/*
			//Multi
			if($imagefile = $this->request->getFiles())
			{
			   foreach($imagefile['uploadedFile'] as $img)
			   {
				  if ($img->isValid() && ! $img->hasMoved())
				  {
					$newName = $img->getRandomName();
					$img->move(WRITEPATH.'uploads', $newName);
					var_dump($img->getSize('mb'));
					var_dump($img->getExtension());
					var_dump(WRITEPATH.'uploads');
					var_dump($img->getClientMimeType());
				  }
			   }
			}
			*/

			//Single
			$files = $this->request->getFiles();
			var_dump($files);
				if ($files['avatar']->isValid())
				{
					$file = $files['avatar'];

					// Generate a new secure name
					$name = $file->getRandomName();

					// Move the file to it's new home
					$file->move(ROOTPATH . 'public/uploads', $name);
					var_dump($file->getSize('mb'));
					var_dump($file->getExtension());
					var_dump(ROOTPATH . 'public/uploads');
					var_dump($file->getClientMimeType());
				}

			
			/*
			//Single
			$path = $this->request->getFile('uploadedFile')->store('head_img/', 'user_name.jpg');
			var_dump($path);
			*/
		}
	}
	
	public function snimi()
	{
        $model = new NewsModel();

		$data = [
			'id' => 18,
			'firstname' => '2aasdasd',
			'lastname' => 'anmim34234e',
			'email'    => '<EMAIL>',
			'mrt'    => array('jedan', 'dva', 'tri', 'cetiri', 'Pet'),
		];

		$tmp = $model->save($data);
		var_dump($tmp);
		if ($tmp === false)
		{
			var_dump($model->errors());
		}
	}
	
	public function vidi($id = false)
	{
        $model = new NewsModel();

        $data['news'] = $model->find($id);
		var_dump($data['news']);
	}
	
	public function brisi($id = false)
	{
        $model = new NewsModel();
		$tmp = $model->delete($id);
		var_dump($tmp);
	}
}