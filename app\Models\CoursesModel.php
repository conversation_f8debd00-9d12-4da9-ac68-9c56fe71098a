<?php namespace App\Models;

use CodeIgniter\Model;

class CoursesModel extends Model
{
    protected $table = 'courses';
	protected $allowedFields = ['parent_id', 'title', 'description', 'slug', 'image', 'category', 'cover_image', 'content', 'short_desc', 'teacher', 'difficulty', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'notification_sent', 'type', 'course_duration'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[courses.slug,id,{id}]',
        // 'content'     => 'required',
        // 'video'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        // 'body_parts'     => 'required',
        // 'accessories'     => 'required',
        // 'springs'     => 'required',
        // 'teacher'     => 'required',
    ];
    protected $validationMessages = [
        'slug' => [
            'required'  => 'The Page URL field is required.',
            'is_unique' => 'The Page URL field must be unique!'
        ]
    ];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_courses($start = 0, $limit = 10, $search_term = "0", $order = "courses.title DESC", $status = "0,1"){
        $status_sql = ($status != NULL) ? " AND courses.status IN (" . $status . ")" : " AND courses.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "+" . $word . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(courses.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND courses.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT courses.*,
                            COALESCE(x.cnt,0) AS countVideos,
                            IF((SELECT count(id) FROM courses_likes WHERE course_id = courses.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                            (SELECT count(rate > 3) as rate FROM courses_likes WHERE course_id = courses.id GROUP BY course_id) as likeCount,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_courses,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines
                            FROM courses
                            LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_videos WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x on x.course_id = courses.id
                            LEFT JOIN courses_videos_machine ON courses_videos_machine.course_id = courses.id
                            LEFT JOIN machines ON machines.id = courses_videos_machine.course_machine
                            LEFT JOIN teachers on teachers.id = courses.teacher
                            -- LEFT JOIN courses_videos on courses_videos.course_id = courses.id
                            -- LEFT JOIN video_state on (video_state.video_id = courses.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                            WHERE courses.deleted_at IS NULL
                            AND courses.type = 0
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY courses.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function courses_home($ids = ''){
        $data = $this->query("SELECT courses.*,
                            COALESCE(x.cnt,0) AS countVideos,
                            IF((SELECT count(id) FROM courses_likes WHERE course_id = courses.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                            (SELECT count(rate > 3) as rate FROM courses_likes WHERE course_id = courses.id GROUP BY course_id) as likeCount,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_courses,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines
                            FROM courses
                            LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_videos WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x on x.course_id = courses.id
                            LEFT JOIN courses_videos_machine ON courses_videos_machine.course_id = courses.id
                            LEFT JOIN machines ON machines.id = courses_videos_machine.course_machine
                            LEFT JOIN teachers on teachers.id = courses.teacher
                            -- LEFT JOIN courses_videos on courses_videos.course_id = courses.id
                            -- LEFT JOIN video_state on (video_state.video_id = courses.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                            WHERE courses.deleted_at IS NULL
                            AND courses.type = 0
                            AND courses.id IN (" . $ids . ")
                            GROUP BY courses.id
                        ")->getResultArray();
        return $data;
    }

    public function all_lagree_courses($start = 0, $limit = 10, $search_term = "0", $order = "courses.created_at DESC", $status = "0"){
        $status_sql = ($status != NULL) ? " AND courses.status IN (" . $status . ")" : " AND courses.status = 0";
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != "0" AND $search_term != ""){
            $words = explode(" ", $search_term);
            $string = array_map(function(&$word){
                return "+" . $word . "*";
            }, $words);

            $term = implode(" ",$string);
            $search =  "AND (MATCH(courses.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND courses.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT courses.*,
                            COALESCE(x.cnt,0) AS countView,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            teachers.slug  as teach_slug,
                            teachers.id  as teach_id,
                            -- video_state.video_time as video_state,
                            GROUP_CONCAT(DISTINCT LOWER(REPLACE(machines.short_name, ' ', '_')) SEPARATOR ' ') AS machines_courses,
                            GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines
                            FROM courses
                            LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_videos_views WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x on x.course_id = courses.id
                            LEFT JOIN courses_videos_machine ON courses_videos_machine.course_id = courses.id
                            LEFT JOIN machines ON machines.id = courses_videos_machine.course_machine
                            LEFT JOIN teachers on teachers.id = courses.teacher
                            -- LEFT JOIN video_state on (video_state.video_id = courses.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                            WHERE courses.deleted_at IS NULL
                            AND courses.type = 0
                            " . $status_sql . "
                            " . $search . "
                            GROUP BY courses.id
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    public function current($slug = '')
    {
        $CoursesVideosModel = model('CoursesVideosModel');

        $data = $this->query("SELECT courses.*, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image,
                                IF((SELECT count(id) FROM courses_likes WHERE course_id = courses.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                (SELECT count(rate > 3) as rate FROM courses_likes WHERE course_id = courses.id GROUP BY course_id) as likeCount,
                                video_state.video_time as video_state,
                                COALESCE(x.cnt,0) AS countVideos,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                CONCAT('teacher/', teachers.slug) as teach_url,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_course_accessories,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_course_springs,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_course_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_course_machines_short
                                FROM courses
                                LEFT JOIN (SELECT course_id, count(*) as cnt FROM courses_videos WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x ON x.course_id = courses.id
                                LEFT JOIN courses_videos_accessories ON courses_videos_accessories.course_id = courses.id
                                LEFT JOIN accessories ON accessories.id = courses_videos_accessories.course_accessories
                                LEFT JOIN courses_videos_springs ON  courses_videos_springs.course_id = courses.id
                                LEFT JOIN springs ON springs.id = courses_videos_springs.course_springs
                                LEFT JOIN courses_videos_body_parts ON courses_videos_body_parts.course_id = courses.id
                                LEFT JOIN body_parts ON body_parts.id = courses_videos_body_parts.course_body_parts
                                LEFT JOIN courses_machine ON courses_machine.course_id = courses.id
                                LEFT JOIN machines ON machines.id = courses_machine.course_machine
                                LEFT JOIN teachers ON teachers.id = courses.teacher
                                LEFT JOIN video_state on (video_state.video_id = courses.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                                WHERE courses.deleted_at IS NULL
                                AND courses.status = 0
                                AND courses.slug = '" . $slug . "'
                            ")->getRowArray();
        if(!empty($data) AND isset($data['id']) AND $data['id'] > 0){
            $data['videos'] = $CoursesVideosModel->query("SELECT * ,
                                                            IF(courses_videos.id IN (
                                                                    SELECT * FROM (
                                                                            SELECT course_video_id FROM courses_videos_views WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                                    ) as classes_watched
                                                            ), 1, 0) as watched
                                                            FROM courses_videos
                                                            WHERE course_id = " . $data['id'] . "
                                                            AND courses_videos.deleted_at IS NULL
                                                            ORDER BY sort asc
                                                        ")->getResultArray();
            if(count($data['videos']) > 0){
                foreach($data['videos'] as $key => $single){
                    if($single['type'] == 'video' AND $single['watched'] == 0){
                        $data['first_slug'] = $single['slug'];
                        break;
                    }
                }
            }
        }

        return $data;
    }
    public function course_info($id = 0)
    {
        $CoursesVideosModel = model('CoursesVideosModel');

        $data = $this->query("SELECT courses.*, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image,
                                (SELECT count(rate > 3) as rate FROM courses_likes WHERE course_id = courses.id GROUP BY course_id) as likeCount,
                                video_state.video_time as video_state,
                                COALESCE(x.cnt,0) AS countVideos,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                CONCAT('teacher/', teachers.slug) as teach_url,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_course_accessories,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_course_springs,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_course_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_course_machines_short
                                FROM courses
                                LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_videos WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x ON x.course_id = courses.id
                                LEFT JOIN courses_videos_accessories ON courses_videos_accessories.course_id = courses.id
                                LEFT JOIN accessories ON accessories.id = courses_videos_accessories.course_accessories
                                LEFT JOIN courses_videos_springs ON  courses_videos_springs.course_id = courses.id
                                LEFT JOIN springs ON springs.id = courses_videos_springs.course_springs
                                LEFT JOIN courses_videos_body_parts ON courses_videos_body_parts.course_id = courses.id
                                LEFT JOIN body_parts ON body_parts.id = courses_videos_body_parts.course_body_parts
                                LEFT JOIN courses_machine ON courses_machine.course_id = courses.id
                                LEFT JOIN machines ON machines.id = courses_machine.course_machine
                                LEFT JOIN teachers ON teachers.id = courses.teacher
                                LEFT JOIN video_state on (video_state.video_id = courses.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                                WHERE courses.deleted_at IS NULL
                                AND courses.status = 0
                                AND courses.id = '" . $id . "'
                            ")->getRowArray();

        $data['videos'] = $CoursesVideosModel->query("SELECT * ,
                                                        IF(courses_videos.id IN (
                                                                SELECT * FROM (
                                                                        SELECT course_video_id FROM courses_videos_views WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                                ) as classes_watched
                                                        ), 1, 0) as watched
                                                        FROM courses_videos
                                                        WHERE course_id = " . $data['id'] . "
                                                        AND courses_videos.deleted_at IS NULL
                                                        ORDER BY sort asc
                                                    ")->getResultArray();
        if(count($data['videos']) > 0){
            foreach($data['videos'] as $key => $single){
                if($single['type'] == 'video' AND $single['watched'] == 0){
                    $data['first_slug'] = $single['slug'];
                    break;
                }
            }
        }

        return $data;
    }
    // public function cron(){
    //     $data = $this->query("SELECT courses.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teacher_name
    //                             FROM courses
    //                             LEFT JOIN teachers ON teachers.id = courses.teacher
    //                             WHERE courses.deleted_at IS NULL
    //                             AND courses.status = 0
    //                             AND courses.notification_sent = 0
    //                             LIMIT 1
    //                         ")->getRowArray();
    //     return $data;
    // }

    function all_course_videos($course_id = 0)
	{
        $CoursesVideosModel = model('CoursesVideosModel');

        $result = NULL;
		if($course_id != 0){
            $result = $CoursesVideosModel->where("course_id", $course_id)->orderBy('SORT', 'ASC')->findAll();
        }

        return $result;
    }
    public function filter_courses($data){

        $limit_size =  ($data['limit'] != 0) ? " LIMIT " . $data['start'] . ", " . $data['limit'] : "";
		$sql_add = '';
		$join = '';
        $sql_selector = ' ';

		//WHERE
        // $real_durations = [
        //     '0' => 'classes.duration < 600',
        //     '1' => '(classes.duration > 600 AND classes.duration < 1200)',
        //     '2' => '(classes.duration > 1200 AND classes.duration < 1800)',
        //     '3' => '(classes.duration > 1800 AND classes.duration < 2400)',
        //     '4' => '(classes.duration > 2400 AND classes.duration < 3000)',
        //     '5' => '(classes.duration > 3000 AND classes.duration < 3600)',
        //     '6' => 'classes.duration > 3600'
        // ];
        if( (isset($data['machine']) AND $data['machine'] != '') OR
            // (isset($data['accessories']) AND $data['accessories'] != '') OR
            // (isset($data['body_parts']) AND $data['body_parts'] != '') OR
            // (isset($data['tempo']) AND $data['tempo'] != '') OR
            (isset($data['teacher']) AND $data['teacher'] != '')
            // (isset($data['language']) AND $data['language'] != '') OR
            // (isset($data['difficulty']) AND $data['difficulty'] != '') OR
            // (isset($data['duration']) AND $data['duration'] != '')
        ){
            $sql_add .=  "WHERE courses.deleted_at IS NULL AND courses.status = 0 ";
            if(isset($data['machine']) AND $data['machine'] != ''){
                $sql_add .= " AND courses_machine.course_machine IN (" . ((isset($data['machine']) AND is_array($data['machine'])) ? implode(',', $data['machine']) : 0) . ") ";
            }
            // if(isset($data['accessories']) AND $data['accessories'] != ''){
            //     $sql_add .= " AND classes_accessories.class_accessories IN (" . ((isset($data['accessories']) AND is_array($data['accessories'])) ? implode(',', $data['accessories']) : 0) . ") ";
            //     $join .= ' LEFT JOIN classes_accessories ON classes_accessories.class_id = classes.id ';
            // }
            // if(isset($data['body_parts']) AND $data['body_parts'] != ''){
            //     $sql_add .= " AND classes_body_parts.class_body_parts IN (" . ((isset($data['body_parts']) AND !empty($data['body_parts']) AND is_array($data['body_parts'])) ? implode(',', $data['body_parts']) : 0) . ") ";
            //     $join .= ' LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id ';
            // }
            // if(isset($data['tempo']) AND $data['tempo'] != ''){
            //     $sql_add .= " AND classes_tempo.class_tempo IN (" . ((isset($data['tempo']) AND !empty($data['tempo']) AND is_array($data['tempo'])) ? implode(',', $data['tempo']) : 0) . ") ";
            //     // $join .= ' LEFT JOIN classes_tempo ON classes_tempo.class_id = classes.id ';
            // }
            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $sql_add .= " AND teachers.id IN (" . ((isset($data['teacher']) AND !empty($data['teacher']) AND is_array($data['teacher'])) ? implode(',', $data['teacher']) : 0) . ") ";
            }
            // if(isset($data['language']) AND $data['language'] != ''){
            //     $sql_add .=  " AND classes.language IN (" . ((isset($data['language']) AND !empty($data['language']) AND is_array($data['language'])) ? implode(',', $data['language']) : 0) . ")";
            //     $join .= ' LEFT JOIN languages on languages.id = classes.language ';
            // }
            // if(isset($data['difficulty']) AND $data['difficulty'] != ''){
            //     $sql_add .=  " AND classes.difficulty IN (" . ((isset($data['difficulty']) AND !empty($data['difficulty']) AND is_array($data['difficulty'])) ? implode(',', $data['difficulty']) : 0) . ")";
            // }
            // if(isset($data['duration']) AND $data['duration'] != ''){
            //     $sql_add .=  " AND (";
            //     if(is_array($data['duration'])){
            //         foreach($data['duration'] as $key => $value){
            //             $sql_add .= $real_durations[$value] . '' . ((count($data['duration']) == ($key+1)) ? '' : ' OR ');
            //         }
            //         $sql_add .= ") AND classes.duration != '' AND classes.duration != 'NaN' AND classes.duration IS NOT NULL ";
            //     }else{
            //         $sql_add .= $real_durations[$data['duration']] . ")";
            //     }
            // }
        }else{
            $sql_add .=  " WHERE courses.deleted_at IS NULL AND courses.status = 0 ";
        }
        if($data['order'] == 'countView/desc'){
            $join = ' LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_views GROUP BY class_id) x on x.course_id = courses.id ';
            $sql_selector .= ' COALESCE(x.cnt,0) AS countView, ';
        }
        $result = $this->query("SELECT courses.*,
                                    IF((SELECT count(id) FROM courses_likes WHERE course_id = courses.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                    (SELECT count(rate > 3) as rate FROM courses_likes WHERE course_id = courses.id GROUP BY course_id) as likeCount,
                                    teachers.slug  as teach_slug,
                                    " . $sql_selector . "
                                    CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines
                                    FROM courses
                                    " . $join . "
                                    LEFT OUTER JOIN (SELECT course_id, count(*) as cnt FROM courses_videos WHERE type = 'video' AND deleted_at IS NULL GROUP BY course_id) x ON x.course_id = courses.id
                                    LEFT JOIN courses_videos_accessories ON courses_videos_accessories.course_id = courses.id
                                    LEFT JOIN accessories ON accessories.id = courses_videos_accessories.course_accessories
                                    LEFT JOIN courses_videos_springs ON  courses_videos_springs.course_id = courses.id
                                    LEFT JOIN springs ON springs.id = courses_videos_springs.course_springs
                                    LEFT JOIN courses_videos_body_parts ON courses_videos_body_parts.course_id = courses.id
                                    LEFT JOIN body_parts ON body_parts.id = courses_videos_body_parts.course_body_parts
                                    LEFT JOIN courses_machine ON courses_machine.course_id = courses.id
                                    LEFT JOIN machines ON machines.id = courses_machine.course_machine
                                    LEFT JOIN teachers ON teachers.id = courses.teacher
                                    " . $sql_add . "
                                    GROUP BY courses.id
                                    ORDER BY " . $data['order'] . "
                                    " . $limit_size . "
                                ")->getResultArray();
        // $result = "SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug,
        //                             DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
        //                             " . $sql_selector . "
        //                             CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
        //                             GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
        //                             IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
        //                                     ) as classes_purchased
        //                             ), 1, 0) as purchased,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
        //                                     ) as classes_rented
        //                             ), 1, 0) as rented,
        //                             IF(classes.id IN (
        //                                     SELECT * FROM (
        //                                             SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
        //                                     ) as classes_watched
        //                             ), 1, 0) as watched
        //                             FROM classes
        //                             " . $join . "
        //                             LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
        //                             LEFT JOIN machines ON machines.id = classes_machine.class_machine
        //                             LEFT JOIN subscribers_classes ON subscribers_classes.class_id = classes.id
        //                             LEFT JOIN teachers on teachers.id = classes.teacher
        //                             LEFT JOIN difficulty on difficulty.id = classes.difficulty
        //                             " . $sql_add . "
        //                             GROUP BY classes.id
        //                             ORDER BY " . $data['order'] . "
        //                             " . $limit_size . "
        //                         ";
        return $result;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}