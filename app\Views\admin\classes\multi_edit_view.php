<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.upload-zone_audio,
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
    position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver::before {
    content: "Drop your audio file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.upload-zone_audio.no-border::before,
.upload-zone_audio.no-border {
	background: #fff;
	border: none;
}
</style>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container pt-100">
            <div class="flex aic jcsb minH45">
                <h1 class="h3 mb-05">Bulk edit Classes</h1>
                <div class="ml-auto mb-05">
                    <a href="admin/classes" class="btn btn-border white-bg black ml-2" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
                </div>
            </div>
        </div>
        <div class="container mt-100 tabs">
            <div class="flex ail w100">
                <div class="flex aic w100 mr-3" style="flex-wrap: wrap;">
                <?php
                $c=0;
                foreach($current_classes as $k => $v){
                    $c++;
                ?>
                    <span class="tab <?php echo $c == 1 ? 'active' : ''; ?>" data-tab="<?php echo $v['id'] ?>"><img src="<?php echo (isset($v['image']) AND $v['image'] != '') ? $v['image'] : ((isset($v['video_thumb']) AND $v['video_thumb'] != '') ? $v['video_thumb'] : ''); ?>" style="height: 5.5vw; width: 100%; object-fit: cover" /></span>
                <?php } ?>
                </div>
                <a href="javascript:;" class="btn red-bg white ml-auto publish_all">Publish All</a>
            </div>
        </div>
        <?php
        $c=0;
        foreach($current_classes as $k => $current){
        $c++;
        ?>
        <div class="tab_content" data-tab="<?php echo $current['id']; ?>" <?php echo $c != 1 ? 'style="display: none"' : ''; ?>>
            <div class="container mt-3">
                <form action="admin/classes/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 410px;"' : '' ?>>
                    <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                    <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                        <span class="f-16 semibold mb-1">DRAG AND DROP VIDEO HERE</span><span class="f-14 midGray video_choose">or <u>select a file</u></span>
                    </div>
                    <div class="video_placeholder">
                        <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                    </div>
                    <span id="video-is-uploading"></span>
                    <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                    <span id="progress-bar-status-show"></span>
                    <span id="toshow" style="display: none;"></span>
                </form>

                <div class="flex aic jcsb mt-1">
                    <span id="remove_video<?php echo $current['id']; ?>" class="link link-red red text-underline f-14 remove_video_bulk" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                    <!-- <div class="duration-container ml-auto">
                        <div class="flex aic jcr f-14 no-wrap" hidden>
                            <p class="mr-1">Duration</p>
                            <input type="text" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                            <span class="get_duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                        </div>
                    </div> -->
                    <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                </div>
            </div>
            <div class="container"><hr class="my-4"></div>
            <div class="container pt-1">
                <form action="admin/classes/audio_upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone_audio" id="audio_container" style="min-height: 150px;">
                    <input type="file" name="audio" id="audio" ondragover="dragOverAudio()" ondragleave="dragLeaveAudio()" ondrop="dragLeaveAudio()" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? 'display: none' : ''; ?>">
                    <div class="before_upload_audio" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? 'display: none' : ''; ?>">
                        <span class="f-16 semibold mb-1">DRAG AND DROP AUDIO FILES</span><span class="f-14 midGray audio_choose">or <u>select a file</u></span>
                    </div>
                    <div class="audio_placeholder" style="display: none;">
                        <div class="audio-icon" style="display: none;"><img src="images/music.svg" alt="" class="img-fluid" /></div>
                        <audio id="my_audio" controls class="after_upload_audio" src="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none'; ?>"></audio>
                    </div>
                    <span id="audio-is-uploading"></span>
                    <!-- <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas> -->
                    <span id="progress-bar-status-show_audio"></span>
                    <span id="toshow_audio" style="display: none;"></span>
                </form>
                <div class="flex aic jcsb">
                    <span id="remove_audio" class="link link-red red text-underline f-14" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none'; ?>">Remove audio</span>
                    <!-- <div class="duration-container ml-auto">
                        <div class="flex aic jcr f-14 no-wrap" hidden>
                            <p class="mr-1">Duration</p>
                            <input type="text" id="duration" class="line-input small" value="<?php //echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                            <span class="duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                        </div>
                    </div> -->
                    <!-- <input type="hidden" id="duration" class="line-input small" value="<?php //echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" /> -->
                </div>
                <hr class="my-5">
            </div>
            <form action="admin/classes/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom classes_form_publish" id="main_form">
                <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
                <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>
                <h3 class="mb-3">Custom Thumbnail</h3>
                <p class="midGray mb-5">Select or upload a photo that shows what's in your video. A good thumbnail stands out and draws viewers' attention.</p>
                <div class="image_container flex aic">
                    <div class="upload-image" id="image_container">
                        <input type="file" name="image" id="image">
                        <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                    </div>
                    <div class="midGray f-12">
                    <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                        <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                            <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                            <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                        </div>

                    </div>
                </div>
                <hr class="my-5">
                <div class="row mb-5">
                    <div class="col-8">
                        <h3 class="mb-3">Class Name</h3>
                        <div class="input-container" id="title_container" style="position: relative;">
                            <input type="text" name="title" class="line-input h3 red" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                        </div>
                    </div>
                </div>
                <input type="hidden" name="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>">
                <div class="row">
                    <div class="col-6">
                        <h3 class="mb-3">Description</h3>
                        <div class="input-container" id="content_container">
                            <textarea type="text" name="content" class="line-input" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        </div>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-6">
                        <h3 class="mb-3">Machine</h3>
                        <?php
                        $curr_machines = (isset($current_machines[$current['id']]) AND $current_machines[$current['id']] != '') ? $current_machines[$current['id']] : array();
                        foreach($machines as $single){
                        ?>
                        <div class="checkbox mb-2" id="machine_container">
                            <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="machine_select<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
                        <?php } ?>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-6">
                        <h3 class="mb-3">Teacher</h3>
                        <?php
                        $c=0;
                        $teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
                        foreach($all_teachers as $single){
                        $c++;
                        ?>
                        <div class="checkbox mb-2" id="teacher_container">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>_<?php echo $current['id']; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>_<?php echo $current['id']; ?>" class="f-16"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>
                        <?php } ?>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-6">
                        <h3 class="mb-3">Language</h3>
    <?php
    $current_language = (isset($current['language']) AND $current['language'] != '') ? $current['language'] : 0;
    foreach($languages as $single){
    ?>
                        <div class="checkbox mb-2" id="language_container">
                            <input type="radio" class="" name="language" id="language<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_language ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="language<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
    <?php } ?>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-6">
                        <h3 class="mb-3">Difficulty</h3>
                        <?php
                        $current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
                        foreach($difficulty as $single){
                        ?>
                        <div class="checkbox mb-2" id="difficulty_container">
                            <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="difficulty<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
                        <?php } ?>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-12">
                        <h3 class="mb-3">Body Parts</h3>
                    </div>
                    <div class="col-12 flex">
                        <div class="mr-150">
                            <?php
                            $c=0;
                            $curr_body_parts = (isset($current_body_parts[$current['id']]) AND $current_body_parts[$current['id']] != '') ? $current_body_parts[$current['id']] : array();
                            foreach($body_parts as $single){
                            $c++;
                            ?>
                            <div class="checkbox mb-2" id="body_parts_container">
                                <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                <label for="body_parts<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                            </div>
                            <?php
                            if($c == 8){
                                echo '</div><div class="mr-150">';
                            }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-12 bottom-border pb-5 mb-5">
                        <h3 class="mb-3">Accessories</h3>
                        <?php
                        $curr_accessories = (isset($current_accessories[$current['id']]) AND $current_accessories[$current['id']] != '') ? $current_accessories[$current['id']] : array();
                        foreach($accessories as $single){
                        ?>
                        <div class="checkbox mb-2" id="accessories_container" data-machine="<?php echo $single['machine']; ?>">
                            <input type="checkbox" class="" name="accessories[]" id="accessories<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="accessories<?php echo $single['id']; ?>_<?php echo $current['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
                        <?php
                        }
                        ?>
                        <?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
                    </div>
                    <div class="col-12 bungee_tension" style="<?php echo ($current_tensions != 0 AND in_array(41, $curr_accessories)) ? '' : 'display: none'; ?>">
                        <h3 class="mb-3 flex aic jcsb">Bungee Tension <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h3>
<?php
$curr_tensions = (isset($current_tensions) AND $current_tensions != '') ? $current_tensions : array();
foreach($tensions as $single){
?>
                            <div class="checkbox mb-2" id="tensions_container" data-class-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>" data-id="<?php echo $single['id']; ?>">
                                <input type="checkbox" class="" name="tensions[]" id="tensions<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_tensions) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                <label for="tensions<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                            </div>
<?php
}
?>
                    </div>
                </div>
                <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3 flex aic jcsb">Spring Load <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h3>
                        <?php
                        $curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array();
                        foreach($springs as $single){
                        ?>
                        <div class="checkbox mb-2" id="springs_container" data-machine="<?php echo $single['machine']; ?>">
                            <input type="checkbox" class="" name="springs[]" id="springs<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="springs<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
                        <?php
                        }
                        ?>
                        <?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
                </div>
            </div>
                <hr class="my-5">
                <div class="row">
                    <div class="col-12">
                        <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                        <input type="hidden" name="video_preview" value="0" class="video_preview">
                        <input type="hidden" name="status" id="status" value="0">
                        <input type="hidden" name="image_removed" id="image_removed" value="0">
                        <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                        <input type="hidden" name="video_encrypted_path" id="video_encrypted_path" value="<?php echo isset($current['video_encrypted_path']) ? $current['video_encrypted_path'] : 0 ?>">

                        <!-- <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">Publish Class</button>
                        <button type="submit" class="btn btn-wide btn-tall btn-border white-bg black ml-2" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">Save as Draft</button> -->
                    </div>
                </div>
            </form>
        </div>
        <?php
        }
        ?>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script> -->
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var statuss = 0;
function save_status(xx){
    statuss = xx;
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    // console.log("status: " + status);
    setTimeout(function(){
        $('#main_form').submit();
    }, 200);
}
$('.tab').on('click', function(){
    var xx = $(this).data('tab');
    console.log('tab');
    $('.tab').removeClass('active');
    $('.tab_content').hide();
    $(this).addClass('active');
    $('.tab_content[data-tab="' + xx + '"]').show();
});
$('.publish_all').on('click', function(){
    $('.classes_form_publish').each(function(){
        $(this).submit();
    });
});
$(function() {
    $(window).on('scroll', function(){
        if($(window).scrollTop() > 245) {
            $('.tabs').addClass('sticky_tabs');
        }else{
            $('.tabs').removeClass('sticky_tabs');
        }
    });
});
</script>

</body>
</html>