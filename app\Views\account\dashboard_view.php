<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="py-0">
        <div class="container">
            <div class="row">
                <div class="col-12 py-4 bottom-border flex aic jcsb flex-column-mob ail-mob">
                    <h2 class="h3 flex aic jcsb mob-w100 mb-mob-3 line-height-small">
                        ACCOUNT
                        <span class="btn btn-border btn-badge mobile-flex mobile-menu-button"><img src="images/arrow-down.svg" alt="" class="img-fluid" /></span>
                    </h2>
                    <span class="text-right text-mob-left">
                        <a href="javascript:;" data-popup="payout-popup" class="btn black-bg white">REQUEST PAYOUT</a>
                        <p class="text-right"><small class="f-1 flex aic jcr line-height-small mt-1">Minimum payout amount is <?php echo $teacher['country'] == "US" ? '$50' : ($teacher['country'] == "CA" ? 'CAD 50' : '50€'); ?></small></p>
                    </span>
                </div>
            </div>
            <div class="acc-menu mobile">
                <div class="acc-menu-container" style="display: none;">
                    <p class="mb-100"></p>
                    <?php echo view('front/templates/account-left-menu.php'); ?>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-6">
        <div class="container">
            <div class="row ail nowrap">
                <div class="col pr-150 sticky desktop">
                    <?php echo view('front/templates/account-left-menu.php'); ?>
                </div>
                <div class="col left-border pl-75" style="width: 100%">
                    <h3 class="mb-0 f-16 bold text-uppercase line-height-small">DASHBOARD</h3>
                    <!-- <hr class="mt-6 mb-75 mb-mob-3 mt-mob-3">
                    <pre>
                        <?php // print_r($account_balace); ?>
                    </pre> -->
                    <hr class="mt-6 mb-75 mb-mob-3 mt-mob-3">
                    <p class="f-14 text-uppercase mb-75 mb-mob-3">Hi <?php echo $teacher['firstname']; ?>, <br class="mobile">you have
                    <span class="users_stripe_balance">
                        <?php echo $account_balace['balance']['available'][0]['currency'] == 'usd' ? '$' : ($account_balace['balance']['available'][0]['currency'] == 'cad' ? 'CAD' : ''); ?>
                        <?php echo $account_balace['balance']['available'][0]['amount'] / 100; ?>
                        <?php echo $account_balace['balance']['available'][0]['currency'] == 'eur' ? '€' : ''; ?>
                    </span> on your balance.</p>
                    <div class="p-5 border mb-4 w100 dashboard-panel">
                        <h3 class="mb-5 f-16 semibold text-uppercase line-height-small">IN THE NUMBERS</h3>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Classes</span>
                            <span class=""><?php echo count($active_classes) + count($pending_classes); ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Pending classes</span>
                            <span class=""><?php echo count($pending_classes); ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Rejected classes</span>
                            <span class=""><?php echo count($rejected_classes); ?></span>
                        </div>
                        <?php
                            $overall_rate = 0;
                            $overall_divider = 0;
                            $all_sale_sum = 0;
                            $max_sale = 0;
                            $max_sale_row = array();
                            $max_rent = 0;
                            $max_rent_row = array();
                            foreach($active_classes as $single){
                                $overall_rate = $overall_rate + $single['classRate'];
                                $overall_divider = $single['classRate'] != 0 ? $overall_divider + 1 : $overall_divider;
                                if($single['sales'] > $max_sale){
                                    $max_sale++;
                                    $max_sale_row = $single;
                                }
                                if($single['rents'] > $max_rent){
                                    $max_rent++;
                                    $max_rent_row = $single;
                                }
                            }
                        ?>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Overall rating</span>
                            <span class=""><?php echo ($overall_rate > 0 AND $overall_divider > 0) ? number_format(($overall_rate / $overall_divider), 1) : 0; ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Best seller: <?php echo count($max_sale_row) > 0 ? $max_sale_row['title'] : 'No sales yet'; ?></span>
                            <span class=""><?php echo ($max_sale_row > 0 AND isset($max_sale_row['sales'])) ? $max_sale_row['sales'] : 0 ?> sales</span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Most rents: <?php echo count($max_rent_row) > 0 ? $max_rent_row['title'] : 'No rents yet'; ?></span>
                            <span class=""><?php echo ($max_rent_row > 0 AND isset($max_rent_row['rents'])) ? $max_rent_row['rents'] : 0 ?> rents</span>
                        </div>
                    </div>
                    <div class="p-5 border mb-4 w100 dashboard-panel">
                        <h3 class="mb-5 f-16 semibold text-uppercase line-height-small">EARNINGS</h3>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Total earnings</span>
                            <span class="">$<?php echo number_format($earnings_total[0]['total'], 2); ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item mb-5">
                            <span class="light">Last month</span>
                            <span class="">$<?php echo number_format($earnings_last_month[0]['total'], 2); ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">This week (<?php echo date("m/d", strtotime("-1 week")); ?> - <?php echo date("m/d"); ?>)</span>
                            <span class="">$<?php echo number_format($earnings_last_week[0]['total'], 2); ?></span>
                        </div>
                        <div class="flex aic jcsb f-16 dashboard-panel-item">
                            <span class="light">Today</span>
                            <span class="">$<?php echo number_format($earnings_today[0]['total'], 2); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/money-input.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/credit-card-input.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/payments_view.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/cookie.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
    var teacher_balance = <?php echo $account_balace['balance']['available'][0]['amount'] / 100; ?>;
    if(parseFloat(teacher_balance) < 50){
        $('[data-popup="payout-popup"]').addClass('disabled');
    }else{
        $('[data-popup="payout-popup"]').removeClass('disabled');
    }
</script>
</body>
</html>