<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class AskSebastien extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ConversationsModel');
    }

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

        $data['nums'] = create_session_nums();
        $data['current']['conversation'] = $this->model->where(['subscriber_id' => $data['logged_user']['id']])->first();
 		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Lagree On Demand - Ask Sebastien';
		$data['current']['seo_description'] = "Watch Mini, Micro and Mega conversations On Demand, experience the benefits of your practice at home or on the go, on any device.";
		echo view('front/conversations/index_view', $data);
    }

    public function mark_as_useful()
    {
        $SubscribersConversationsModel = model('SubscribersConversationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        if(NULL !== session('user') AND session('user') != ''){
            $save_message_useful = array('id' => $data['id'], 'useful' => 1);

            $response['useful'] = $SubscribersConversationsModel->where(["id" => $data['id'], 'useful' => 1])->first();
            if(empty($response['useful'])){
                $response['success'] = $SubscribersConversationsModel->save($save_message_useful);
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }
		return $this->respond($response);
    }

    public function msg_mark_as_seen()
    {
        $SubscribersConversationsModel = model('SubscribersConversationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        if(NULL !== session('user') AND session('user') != ''){
            $save_message_seen = array('id' => $data['id'], 'seen' => 1);

            $response['seen'] = $SubscribersConversationsModel->where(["id" => $data['id'], 'seen' => 1])->first();
            if(empty($response['seen'])){
                $response['success'] = $SubscribersConversationsModel->save($save_message_seen);
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }
		return $this->respond($response);
    }

    public function new_message()
    {
        $SubscribersConversationsModel = model('SubscribersConversationsModel');
        $ConversationsModel = model('ConversationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;
        if($data['conversation_id'] == 0){
            $new_conversation = $ConversationsModel->save(['subscriber_id' => $data['sender_id']]);
            $data['conversation_id'] = $ConversationsModel->getInsertID();
            $response['conversation_id'] = $data['conversation_id'];
        }else{
            $data_conversation = ['id' => $data['conversation_id'],'hide' => 0];
            $new_conversation = $ConversationsModel->save($data_conversation);
        }
        if(NULL !== session('user') AND session('user') != ''){
            $save_message_useful = array(
                'conversation_id' => $data['conversation_id'],
                'sender_id' => $data['sender_id'],
                'receiver_id' => $data['receiver_id'],
                'message' => $data['message']
            );
            if(!empty($data['message'])){
                $response['success'] = $SubscribersConversationsModel->save($save_message_useful);
                $response['message'] = $data['message'];
                $response['date'] = date('m/d/Y');
                $response['initials'] = user_avatar($data['sender_id']) != NULL ? '<img src="' . user_avatar($data['sender_id']) . '" class="img-fluid" />' : user_initials($data['sender_id']);
            }else{
                $response['msg'] = 'Please enter your message';
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }
		return $this->respond($response);
    }

    public function get_users_conversations($conversation_id = 0){
        if($conversation_id != 0){
            $response['conversation'] = $this->model->query("SELECT subscribers_conversations.*,
                                                                        IF(subscribers_conversations.sender_id = 0, 'seb', CONCAT(subscribers.firstname, ' ', subscribers.lastname)) AS user_name
                                                                        FROM subscribers_conversations
                                                                        LEFT JOIN subscribers ON (subscribers.id = subscribers_conversations.sender_id AND subscribers_conversations.sender_id != 0)
                                                                        WHERE conversation_id = " . $conversation_id . "
                                                                        ORDER BY subscribers_conversations.date asc
                                                                    ")->getResultArray();

            $response['html'] = view('front/conversations/ajax-conversation-single', $response);
        }else{
            $response['conversation'] = [];
        }
        return $this->respond($response);
    }

}