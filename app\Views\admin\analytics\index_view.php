<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.pad-60-25 {
    padding: 60px 25px;
}

@media screen and (max-width: 1200px) {
.analytics-boxes {display:flex; flex-direction:column;}
.analytics-boxes .col-3 {flex: 0 0 100%; max-width: 100%; width: 100%;}
.analytics-boxes .dashboard-widget {padding-top:25px; padding-bottom:25px; margin-bottom:15px;} 
.analytics-boxes .col-3:last-of-type .dashboard-widget {margin-bottom:0;} 

}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container dashb-container mb-150">
            <div class="flex aic jcsb minH45 bottom-border page-title">
                <h1 class="h3">analytics overview</h1>
            </div>
            <div class="py-5 bottom-border">
                <div class="row big-gap analytics-boxes">
                    <div class="col-3">
                        <div class="dashboard-widget text-center pad-60-25">
                            <p class="mb-1 line-height-small f-12 light">TOTAL VIDEO FILES</p>
                            <h4 class="f-18 line-height-small medium text-center"><?php echo number_format($all_video_files, 0 );  ?></h4>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="dashboard-widget text-center pad-60-25">
                            <p class="mb-1 line-height-small f-12 light">CLASSES</p>
                            <h4 class="f-18 line-height-small medium text-center"><?php echo number_format($all_classes, 0 );  ?></h4>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="dashboard-widget text-center pad-60-25">
                            <p class="mb-1 line-height-small f-12 light">EXERCISES</p>
                            <h4 class="f-18 line-height-small medium text-center"><?php echo number_format($all_exercises, 0 );  ?></h4>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="dashboard-widget text-center pad-60-25">
                            <p class="mb-1 line-height-small f-12 light">VIDEOS (COLLECTIONS)</p>
                            <h4 class="f-18 line-height-small medium text-center"><?php echo number_format($all_videos, 0 );  ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">LIFETIME</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium"><?php echo number_format($all_lifetime_views, 0 ); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium"><?php echo number_format($lifetime_classes_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium"><?php echo number_format($lifetime_exercises_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium"><?php echo number_format($lifetime_videos_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium"><?php echo number_format($lifetime_courses_views, 0); ?></span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">THIS WEEK (<?php echo date('m/d', strtotime("-1 week")) ?> - <?php echo date('m/d') ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium"><?php echo number_format($all_this_week_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium"><?php echo number_format($this_week_classes_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium"><?php echo number_format($this_week_exercises_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium"><?php echo number_format($this_week_videos_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium"><?php echo number_format($this_week_courses_views, 0); ?></span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">VS LAST WEEK (<?php echo date('m/d', strtotime("-2 week")) ?> - <?php echo date('m/d', strtotime("-1 week")) ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium <?php echo ($all_last_week_views - $all_this_week_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($all_last_week_views, 0); ?>
                        (<?php 
                        if($all_this_week_views > 0) {
                            echo ($all_last_week_views - $all_this_week_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - (($all_last_week_views * 100) / $all_this_week_views))), 0);
                        }else{
                            echo '0';
                        }
                         ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium <?php echo ($last_week_classes_views - $this_week_classes_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_week_classes_views, 0); ?>
                        (<?php 
                        if($this_week_classes_views > 0){
                            echo ($last_week_classes_views - $this_week_classes_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_week_classes_views * 100) / $this_week_classes_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium <?php echo ($last_week_exercises_views - $this_week_exercises_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_week_exercises_views, 0); ?>
                        (<?php 
                        if($this_week_exercises_views > 0){
                            echo ($last_week_exercises_views - $this_week_exercises_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_week_exercises_views * 100) / $this_week_exercises_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium <?php echo ($last_week_videos_views - $this_week_videos_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_week_videos_views, 0); ?>
                        (<?php 
                        if($this_week_videos_views > 0){
                            echo ($last_week_videos_views - $this_week_videos_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_week_videos_views * 100) / $this_week_videos_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium <?php echo ($last_week_courses_views - $this_week_courses_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_week_courses_views, 0); ?>
                        (<?php 
                        if($this_week_courses_views > 0){
                            echo ($last_week_courses_views - $this_week_courses_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_week_courses_views * 100) / $this_week_courses_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">THIS MONTH (<?php echo strtoupper(date('F Y')) ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium"><?php echo number_format($all_this_month_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium"><?php echo number_format($this_month_classes_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium"><?php echo number_format($this_month_exercises_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium"><?php echo number_format($this_month_videos_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium"><?php echo number_format($this_month_courses_views, 0); ?></span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">VS LAST MONTH (<?php echo strtoupper(date('F Y', strtotime("-1 month"))) ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium <?php echo ($all_last_month_views - $all_this_month_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($all_last_month_views, 0); ?>
                        (<?php 
                        if($all_this_month_views > 0){
                            echo ($all_last_month_views - $all_this_month_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($all_last_month_views * 100) / $all_this_month_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium <?php echo ($last_month_classes_views - $this_month_classes_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_month_classes_views, 0); ?>
                        (<?php 
                        if($this_month_classes_views > 0){
                            echo ($last_month_classes_views - $this_month_classes_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_month_classes_views * 100) / $this_month_classes_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium <?php echo ($last_month_exercises_views - $this_month_exercises_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_month_exercises_views, 0); ?>
                        (<?php 
                        if($this_month_exercises_views > 0){
                            echo ($last_month_exercises_views - $this_month_exercises_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_month_exercises_views * 100) / $this_month_exercises_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium <?php echo ($last_month_videos_views - $this_month_videos_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_month_videos_views, 0); ?>
                        (<?php 
                        if($this_month_videos_views > 0){
                            echo ($last_month_videos_views - $this_month_videos_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_month_videos_views * 100) / $this_month_videos_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium <?php echo ($last_month_courses_views - $this_month_courses_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_month_courses_views, 0); ?>
                        (<?php 
                        if($this_month_courses_views > 0){
                            echo ($last_month_courses_views - $this_month_courses_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($last_month_courses_views * 100) / $this_month_courses_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">THIS YEAR (<?php echo strtoupper(date('Y')) ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium"><?php echo number_format($all_this_year_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium"><?php echo number_format($this_year_classes_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium"><?php echo number_format($this_year_exercises_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium"><?php echo number_format($this_year_videos_views, 0); ?></span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium"><?php echo number_format($this_year_courses_views, 0); ?></span>
                </div>
            </div>
            <div class="analytics-wrap">
                <div class="lh-all pt-5 pb-45 bottom-border lh-small">
                    <span class="f-16 semibold">VS LAST YEAR (<?php echo strtoupper(date('Y', strtotime("-1 year"))) ?>)</span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Total views</span>
                    <span class="f-14 medium <?php echo ($all_last_year_views - $all_this_year_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($all_last_year_views, 0); ?>
                        (<?php 
                        if($all_this_year_views > 0){
                            echo ($all_last_year_views - $all_this_year_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs((100 - ($all_last_year_views * 100) / $all_this_year_views)), 0);
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Classes</span>
                    <span class="f-14 medium <?php echo ($last_year_classes_views - $this_year_classes_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_year_classes_views, 0); ?>
                        (<?php 
                        if($this_year_classes_views > 0){
                            echo ($last_year_classes_views - $this_year_classes_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs(100 - (($last_year_classes_views * 100) / $this_year_classes_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Exercises</span>
                    <span class="f-14 medium <?php echo ($last_year_exercises_views - $this_year_exercises_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_year_exercises_views, 0); ?>
                        (<?php 
                        if($this_year_exercises_views > 0){
                            echo ($last_year_exercises_views - $this_year_exercises_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs(100 - (($last_year_exercises_views * 100) / $this_year_exercises_views)), 0); 
                        }else{
                            echo '0';
                        }    
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Videos (Collections)</span>
                    <span class="f-14 medium <?php echo ($last_year_videos_views - $this_year_videos_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_year_videos_views, 0); ?>
                        (<?php 
                        if($this_year_videos_views > 0){
                            echo ($last_year_videos_views - $this_year_videos_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs(100 - (($last_year_videos_views * 100) / $this_year_videos_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
                <div class="lh-all py-25 bottom-border flex aic jcsb">
                    <span class="f-14">Courses</span>
                    <span class="f-14 medium <?php echo ($last_year_courses_views - $this_year_courses_views) >= 0 ? 'textGreen' : 'normalRed'; ?>">
                        <?php echo number_format($last_year_courses_views, 0); ?>
                        (<?php 
                        if($this_year_courses_views > 0){
                            echo ($last_year_courses_views - $this_year_courses_views) >= 0 ? '+' : '-'; ?><?php echo number_format(abs(100 - (($last_year_courses_views * 100) / $this_year_courses_views)), 0); 
                        }else{
                            echo '0';
                        }
                        ?>%)
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="logdesk-popup">
      <div class="login-desktop">
        <img src="admin_assets_new/images/icon-desktop960.svg">
		<p>For the best user experience, continue with admin management on desktop device.</p>
      </div> 
    </div>

</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>



<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>