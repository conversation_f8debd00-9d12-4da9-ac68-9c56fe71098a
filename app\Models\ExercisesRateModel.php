<?php namespace App\Models;

use CodeIgniter\Model;

class ExercisesRateModel extends Model
{
    protected $table = 'exercises_rate';
	protected $allowedFields = ['exercise_id', 'date', 'rate', 'user_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}