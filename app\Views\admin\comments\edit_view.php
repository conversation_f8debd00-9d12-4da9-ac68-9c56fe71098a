<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.single-selected-exercises .handle,
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
	top: 0;
	margin-top: 40px;
	left: -26px;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 25px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 155px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.add_button.ml-auto {
	flex: 1;
	max-width: 30px;
	margin-left: auto !important;
	align-self: center;
}
.audio_item:hover {
  background: #f0f0f0;
}
.audio_item {
  padding: 0 !important;
  margin: 0 !important;
  cursor: pointer;
}
.audio_item a{
  padding: 8px 20px !important;
  margin: 0 !important;
}
.audio_item.selected {
    font-weight: 700;
}
.dropdown > .dropdown-menu {
  padding: 25px 25px 25px 25px;
}
.upload-zone_audio,
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
    position: absolute;
    border: 10px dashed #F0F0F0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver::before {
    content: "Drop your audio file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.upload-zone_audio.no-border::before,
.upload-zone_audio.no-border {
	background: #fff;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 18px !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
}
#main_form {
	margin: 0 auto 60px;
}
@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
#main_form {margin-bottom: 35px !important;}
}
</style>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5" style="padding-bottom: 120px">
        <div class="container pt-100">
            <div class="flex aic jcsb">
                <?php if($current['status'] == 2){ ?>
                    <h1 class="h3">Class Review</h1>
                    <span class="btn btn-xs yellow-bg white f-1 ml-2" style="min-height: 25px;">Pending</span>
                    <a href="admin/classes" class="btn btn-border white-bg black ml-auto" title="Cancel">Cancel</a>
                <?php }else{ ?>
                    <h1 class="h3"><?php echo isset($current['id']) ? '' : 'Upload' ?> Class <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                    <span class="btn btn-xs red-bg white f-1 ml-2" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                    <a href="admin/classes" class="btn btn-border white-bg black ml-2" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
                <?php } ?>
            </div>
            <hr class="mt-80 mb-4">
            <form action="admin/classes/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 400px;"' : '' ?>>
                <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                    <span class="f-16 bold text-uppercase">Drag and drop video files to upload or <span class="link link-red red text-underline video_choose">select a file.</span></span>
                </div>
                <div class="video_placeholder">
                    <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                </div>
                <span id="video-is-uploading"></span>
                <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                <span id="progress-bar-status-show"></span>
                <span id="toshow" style="display: none;"></span>
            </form>

            <div class="flex aic jcsb mt-1">
                <span id="remove_video" class="link link-red red text-underline f-14" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                <!-- <div class="duration-container ml-auto">
                    <div class="flex aic jcr f-14 no-wrap" hidden>
                        <p class="mr-1">Duration</p>
                        <input type="text" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                        <span class="duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                    </div>
                </div> -->
                <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
            </div>
        </div>
        <div class="container"><hr class="my-4"></div>
        <form action="admin/classes/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom classes_form" id="main_form">
            <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
            <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>
            <div class="row mb-5" style="<?php echo $current['type'] == 0 ? '' : 'display: none;'; ?>">
                <div class="col-4">
                    <h3 class="mb-3">Audio</h3>
                    <div class="input-container mb-3">
                        <div class="dropdown d-inline-block" style="width: 100%;">
                            <span class="dropdown-button" data-dropdown="" style="width: 100%;display: flex;align-items: center;justify-content: space-between;height: 60px;border-bottom: 1px solid #f0f0f0;">
<?php
foreach($all_audio as $single){
    if(isset($current['audio']) AND $current['audio'] != '' AND $current['audio'] == $single['audio']){
        $audio = $single['title'];
        break;
    }
}
?>

                                <span class="dropdown-value"><?php echo (isset($audio) AND NULL != $audio) ? $audio : 'Select audio track'; ?></span>
                                <i class="arrow-down ml-05"></i>
                            </span>
                            <ul class="dropdown-menu drop-right flex-vertical p-0 pb-1" style="width: 100%;">
                                <li class="m-0 mb-1"><input type="text" id="" class="search_audio line-input small" style="padding: 5px;font-size: 13px;" placeholder="Search track"></li>
<?php
foreach($all_audio as $single){
    (isset($current['audio']) AND $current['audio'] != '')
?>

                                <li class="audio_item <?php echo (isset($single['audio']) AND $single['audio'] != '' AND $current['audio'] == $single['audio']) ? 'selected' : ''; ?>">
                                    <a data-val="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" data-audio="<?php echo (isset($single['audio']) AND $single['audio'] != '') ? $single['audio'] : ''; ?>" href="javascript:;" onclick="custom_select_dropdown($(this))" class="darkGray w100" title="Black">
                                        <img src="images/note.svg" alt="" class="img-fluid" style="width: 14px;" />
                                        <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                    </a>
                                </li>
<?php
}
?>
                            </ul>
                            <input type="hidden" name="audio" class="audio" value="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>" />
                        </div>
                        <!-- <span class="input-label" style="top: -15px;">Announcement priority</span> -->
                    </div>
                </div>
                <div class="col-8">
                    <div class="audio_placeholder p-0" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none;'; ?>">
                        <div class="flex flex-column w100">
                            <h3 class="mb-3 pb-1 midGray">Listen</h3>
                            <audio id="class_audio" controls class="after_upload_audio" src="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>"></audio>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="my-5" style="<?php echo $current['type'] == 0 ? '' : 'display: none;'; ?>">
            <h3 class="mb-3">Custom Thumbnail</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your video. A good thumbnail stands out and draws viewers' attention.</p>
            <div class="image_container flex aic">
                <div class="upload-image" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Class Name</h3>
                    <div class="input-container" id="title_container" style="position: relative;">
                        <input type="text" name="title" class="line-input f-3 bold black" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" />
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Page URL</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" style="padding-left: 235px;">
                        <span class="base_url">www.lagreeod.com/classes/</span>
                    </div>
                </div>
            </div> -->
            <!-- <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Description</h3>
                    <div class="input-container" id="content_container">
                        <textarea type="text" name="content" class="line-input" placeholder="Describe Your Class"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                    </div>
                </div>
            </div> -->
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Machine</h3>
<?php
$curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
foreach($machines as $single){
?>
                    <div class="checkbox mb-2" id="machine_container">
                        <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-12">
                    <h3 class="mb-3">Teacher</h3>
                    <div class="row w100">
<?php
$c=0;
$teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
foreach($all_teachers as $single){
$c++;
    if($current['type'] == 1){
?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container" style="<?php echo ($single['id'] != $teacher) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-16"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>
    <?php
    }else{
    ?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container" style="<?php echo ($single['certified'] == 0) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-16"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>

    <?php
    }
    ?>
<?php
}
?>
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Language</h3>
<?php
$current_language = (isset($current['language']) AND $current['language'] != '') ? $current['language'] : 0;
foreach($languages as $single){
?>
                    <div class="checkbox mb-2" id="language_container">
                        <input type="radio" class="" name="language" id="language<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_language ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="language<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Difficulty</h3>
<?php
$current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
foreach($difficulty as $single){
?>
                    <div class="checkbox mb-2" id="difficulty_container">
                        <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-12">
                    <h3 class="mb-3">Body Parts</h3>
                </div>
                <div class="col-12 flex">
                    <div class="mr-150">
<?php
$curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '') ? $current_body_parts : array();
?>
                        <div class="checkbox mb-2" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts16" <?php echo in_array(16, $curr_body_parts) ? 'checked' : '' ?> value="16">
                            <label for="body_parts16" class="f-16">Full body</label>
                        </div>

<?php
$c=0;
foreach($body_parts as $single){
$c++;
    if($single['id'] != 16){
?>
                        <div class="checkbox mb-2" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="body_parts<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
<?php
        if($c == 8){
            echo '</div><div class="mr-150">';
        }
    }
}
?>

                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Accessories Required</h3>
<?php
$curr_accessories = (isset($current_accessories) AND $current_accessories != '') ? $current_accessories : array();
foreach($accessories as $single){
?>
                        <div class="checkbox mb-2" id="accessories_container" data-machine="<?php echo $single['machine']; ?>" data-id="<?php echo $single['id']; ?>">
                            <input type="checkbox" class="" name="accessories[]" id="accessories<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="accessories<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
<?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-3">Spring Load</h3>
<?php
$curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array();
foreach($springs as $single){
?>
                        <div class="checkbox mb-2" id="springs_container" data-machine="<?php echo $single['machine']; ?>" data-id="<?php echo $single['id']; ?>">
                            <input type="checkbox" class="" name="springs[]" id="springs<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="springs<?php echo $single['id']; ?>" class="f-16"><?php echo $single['title']; ?></label>
                        </div>
<?php
}
?>
<?php echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-12">
                    <div class="uploading" style="display: none;">Uploading video. Please wait...</div>
                    <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                    <input type="hidden" name="type" id="type" value="<?php echo isset($current['type']) ? $current['type'] : 0 ?>">
                    <input type="hidden" name="prev_status" id="prev_status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <!-- <input type="hidden" name="created_at" id="created_at" value="<?php // echo isset($current['created_at']) ? $current['created_at'] : 0 ?>"> -->
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="video_encrypted_path" id="video_encrypted_path" value="<?php echo isset($current['video_encrypted_path']) ? $current['video_encrypted_path'] : 0 ?>">
                    <input type="hidden" name="video_preview" id="video_preview" value="<?php echo isset($current['video_preview']) ? $current['video_preview'] : '' ?>">

                    <div class="approve-buttons" <?php echo ($current['status'] == 2) ? '' : 'style="display: none;"';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">APPROVE AND PUBLISH CLASS</button>
                        <button type="button" class="btn btn-wide btn-tall btn-border white-bg black ml-2" data-popup="reject-video" onclick="save_status(3);$('[name=teacher_id]').val($('[name=teacher]:checked').val());">REJECT</button>
                    </div>
                    <div class="default-buttons" <?php echo ($current['status'] == 2) ? 'style="display: none;"' : '';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH CLASS</button>
                        <button type="submit" class="btn btn-wide btn-tall btn-border white-bg black ml-2" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                    </div>
                    <?php if(isset($prev) OR isset($next)){ ?>
                        <div class="bottom-fixed-buttons" <?php echo ($current['status'] == 2) ? 'style="display: none;"' : '';?>>
                            <?php if(isset($prev)){ ?>
                                <a href="admin/classes/edit/<?php echo $prev['id']; ?>" class="link link-black black text-underline f-14 mr-2">Previous Class</a>
                            <?php } ?>
                            <?php if(isset($next)){ ?>
                                <a href="admin/classes/edit/<?php echo $next['id']; ?>" class="link link-black black text-underline f-14">Next Class</a>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/class_exercises.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
const date = "<?php echo date('Y-m-d'); ?>";
var statuss = 0;
<?php if(isset($current['duration']) AND ($current['duration'] == '' OR $current['duration'] == 'NaN' OR is_numeric($current['duration'])) AND isset($current['id']) AND $current['id'] > 0){ ?>
    setTimeout(function(){
        $('#duration_val').val(($('video').get(0).duration).toFixed(0));
    }, 4500);
<?php } ?>

function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
$('#reject_class').on('submit', function (e) {
	console.log('reject_class submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
	var formData = form.serialize();
    button.addClass('btn--loading');
	$.ajax({
		type: "POST",
		url: url,
		data: formData,
		dataType: "json",
		success: function(data) {
			console.log(data);
			if (data.success) {
				console.log('SUCCESS');
                app_msg('Video rejected');
                close_all();
                <?php if(isset($pending) AND is_array($pending) AND count($pending) > 1){ ?>
                    window.location = '/admin/classes/pending';
                <?php }else{ ?>
                    window.location = '/admin/classes';
                <?php } ?>

                button.removeClass('btn--loading');
			} else {
				console.log('NO SUCCESS');
                app_msg('Something went wrong. Please try again', 'danger');
                button.removeClass('btn--loading');
			}
		},
		error: function(result) {
			console.log('ERROR WITH PHP');
			console.log(result);
            app_msg('Server problem', 'danger');
            button.removeClass('btn--loading');
		}
	});
});
function custom_select_dropdown(xx){
    console.log('radi drop');
    var audio = xx.data('audio');
    console.log(audio);

    $('.audio_item').removeClass('selected');
    xx.parent().addClass('selected');
    xx.closest('.dropdown').find('.dropdown-value').html(xx.text())
    xx.closest('.dropdown').find('.audio').val(audio);
    $('#class_audio').attr('src', audio);
    $('.audio_placeholder').show();
    setTimeout(function(){
        $('.search_audio').val('');
        $('.audio_item').show();
    }, 300);
}
$('.search_audio').on('click focus', function(e){
    e.stopPropagation();
});
$('.search_audio').on('keyup', function(){
    var chars = $(this).val().length;
    var val = $(this).val().toLowerCase();

    if(chars > 1){
        $(".audio_item").hide();
        $(".audio_item").each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(val) != -1){
                $(this).show();
            }
        });
    }else{
        $('.audio_item').show();
    }
});
if($('.selected_clases').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".selected_clases").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/classes/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}

</script>

</body>
</html>