<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Comments extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CommentsModel');
    }

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

        $data['nums'] = create_session_nums();
        $data['all_comments'] = $this->model->all_my_comments(0, 9);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Lagree On Demand - Notifications';
		$data['current']['seo_description'] = "Watch Mini, Micro and Mega comments On Demand, experience the benefits of your practice at home or on the go, on any device.";
		echo view('front/comments/index_view', $data);
    }

    public function mark_as_seen()
    {
        $subscribersWatched_model = model('SubscribersNotificationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        if(NULL !== session('user') AND session('user') != ''){
            $save_notification_seen = array('notification_id' => $data['id'], 'subscriber_id' => session('user'), 'date' => date('Y-m-d H:i:s'));

            $response['seen'] = $subscribersWatched_model->where(["notification_id" => $data['id'], "subscriber_id" => session('user')])->first();
            if(empty($response['seen'])){
                $response['success'] = $subscribersWatched_model->save($save_notification_seen);
                $response['new_id'] = $subscribersWatched_model->getInsertID();
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }

		return $this->respond($response);
    }

    public function save()
    {
		$email_model = model('EmailModel');
		$request = service('request');
        $data = $request->getPost();
        $NotificationsModel = model('NotificationsModel');

        $response['success'] = FALSE;
        if(NULL !== session('user') AND session('user') != ''){
            $class_title = $data['class_title'];
            $teacher_email = $data['teacher_email'];
            $teacher_name = $data['teacher_name'];

            unset($data['class_title']);
            unset($data['teacher_email']);
            unset($data['teacher_name']);

            $response['success'] = $this->model->save($data);
            $response['new_id'] = $this->model->getInsertID();

            // if(isset($main_comment) AND $main_comment != NULL AND count($main_comment) == 1 AND $main_comment[0]['notify_replay'] == 1){
            //     $notification_data = array(
            //         'content'   => 'You have a reply to you comment. Check <span class="text-underline">' . $class['title'] . '</span>.',
            //         'link'      => base_url() . '/classes/' . $class['slug'] . '#comment_' . $response['new_id'],
            //         'author'    => 'system',
            //         'subscriber_id'    => isset($user['id']) ? $user['id'] : 0,
            //         'type' => 'comment_reply_notif',
            //         'date'    => date('Y-m-d H:i:s')
            //     );
            //     $response['notification_saved'] = $NotificationsModel->save($notification_data);
            // }
            if((int)session('user') != 292 AND $teacher_email != '' AND $teacher_email != NULL){
                // $to = '<EMAIL>';
                $to = $teacher_email;
                $subject = 'Lagree On Demand - New Comment on ' . $class_title;
                $data = [
                    'class_title' => $class_title,
                    'teacher_name' => ($teacher_name != '' AND $teacher_name != NULL) ? $teacher_name : 'Teacher',
                    'comment' => $data['message'],
                    'date' => date('m/d/Y H:i:s')
                ];
                $template = 'front/email_templates/new-comment-teacher';
                $response = $email_model->send_template($to, FALSE, $subject, $data, $template);
            }

        }else{
            $response['msg'] = 'You must be logged in!';
        }


		return $this->respond($response);
    }
}