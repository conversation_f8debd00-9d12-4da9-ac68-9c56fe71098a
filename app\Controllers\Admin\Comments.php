<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Comments extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CommentsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['all_comments'] = $this->model->nonapproved_comments_admin();
        $data['comment_count'] = count($this->model->nonapproved_comments_admin());

        $data['page'] = 1;
        // $page = 1;
        $data['search_term'] = '';
        $data['sort_by'] = sort_name(session('classes_sort'));

        echo view('admin/comments/index_view', $data);
    }
    public function all($page = 1)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['all_comments'] = $this->model->all_comments(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['comment_count'] = count($this->model->all_comments(0, 1000000));

        $data['page'] = $page;
        // $page = 1;
        $data['search_term'] = '';
        $data['sort_by'] = sort_name(session('classes_sort'));

        echo view('admin/comments/all_view', $data);
    }

    public function sort_by($type = 'collections.title', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }
        $teachers_model = model('TeachersModel');
		// $data = $this->request->getPost();
        $data['all_collections'] = $this->model->query("SELECT collections.*
                                        FROM collections
                                        WHERE collections.deleted_at IS NULL
                                        ORDER BY " . $type. " " . $direction . "")->getResultArray();
        $data['collections_count'] = count($data['all_collections']);
        $types = array(
            "collections.created_atdesc" => "Date Added",
            "collections.titleasc" => "Ascending",
            "collections.titledesc" => "Descending"
        );
        $data['all_teachers'] = $teachers_model->findAll();
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/collections/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['all_comments'] = $this->model->all_comments(0, session('per_page'), $data['search_term']);
        $data['comment_count'] = $this->model->like('message', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/comments/all_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $data['all_comments'] = $this->model->all_comments(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['comment_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/collections/index_view', $data);
    }

    public function approve()
    {
		$request = service('request');
        $data = $request->getPost();
        $NotificationsModel = model('NotificationsModel');

        if(isset($data['parent']) AND $data['parent'] != 0 AND $data['parent'] != "" AND $data['parent'] != NULL){
            $main_comment = $this->model->query("SELECT * FROM comments WHERE id = " . $data['parent'] . " AND comments.status = 0")->getResultArray();

            $class = $this->get_class_info($main_comment[0]['class_id']);
            $user = user_info($main_comment[0]['user_id']);
        }
        if(isset($main_comment) AND $main_comment != NULL AND count($main_comment) == 1 AND $main_comment[0]['notify_replay'] == 1){
            $notification_data = array(
                'content'   => 'Someone replied to your <span class="text-underline">comment</span>.',
                'link'      => base_url() . '/classes/' . $class['slug'] . '#comment_' . $data['id'],
                'author'    => 'system',
                'subscriber_id'    => isset($user['id']) ? $user['id'] : 0,
                'type' => 'comment_reply_notif',
                'date'    => date('Y-m-d H:i:s')
            );
            $response['notification_saved'] = $NotificationsModel->save($notification_data);
        }

        $response['success'] = $this->model->save($data);

        return $this->respond($response);
    }
    public function seen()
    {
		$request = service('request');
        $data = $request->getPost();

        $response['success'] = $this->model->save($data);

        return $this->respond($response);
    }
    public function save()
    {
		$request = service('request');
        $data = $request->getPost();

        // $response['success'] = FALSE;
        $comment_id = $data['comment_id'];
        // $teacher_email = $data['teacher_email'];
        // $teacher_name = $data['teacher_name'];

        unset($data['comment_id']);
        // unset($data['teacher_email']);
        // unset($data['teacher_name']);

        $response['success'] = $this->model->save($data);
        $response['new_id'] = $this->model->getInsertID();

        // $parent_data = [
        //     'id' => $comment_id,
        //     'teacher_replied' => session('admin')
        // ];
        // $response['teacher_replied_success'] = $this->model->save($parent_data);

        /* if((int)session('user') != 292){
            // $to = '<EMAIL>';
            $to = $teacher_email;
            $subject = 'Lagree On Demand - New Comment on ' . $class_title;
            $data = [
                'class_title' => $class_title,
                'teacher_name' => $teacher_name,
                'comment' => $data['message'],
                'date' => date('m/d/Y H:i:s')
            ];
            $template = 'front/email_templates/new-comment-teacher';
            $response = $email_model->send_template($to, FALSE, $subject, $data, $template);
        } */

		return $this->respond($response);
    }

}