<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use App\Models\EmailModel;

require $_SERVER['DOCUMENT_ROOT'] . '/../vendor/autoload.php';

class Admincontroller extends Controller
{
	public $admin;
	public function __construct() {
		$model = model('TeachersModel');
		$classesModel = model('ClassesModel');
		$this->admin = $model->where(['id' => session("admin")])->first();
		$settingsModel = model('SettingsModel');
		$ConversationsModel = model('ConversationsModel');
		$CommentsModel = model('CommentsModel');
		$this->settings = $settingsModel->where(['id' => 1])->first();
		$this->pending_classes = $classesModel->all_pending();
		$this->new_messages = $ConversationsModel->count_conversations_admin();
		$this->new_comments = count($CommentsModel->nonapproved_comments_admin());
		$this->admin_table = model('AdminTableSettingsModel');

        $classes_cur_sort = session('classes_sort');
        $this->classes_sort = explode(' ', $classes_cur_sort);
        $exercises_cur_sort = session('exercises_sort');
        $this->exercises_sort = explode(' ', $exercises_cur_sort);
        $howto_cur_sort = session('howto_sort');
        $this->howto_sort = explode(' ', $howto_cur_sort);
        $courses_cur_sort = session('courses_sort');
        $this->courses_sort = explode(' ', $courses_cur_sort);

        $session = \Config\Services::session();
        if(!isset($_SESSION['per_page']) OR $_SESSION['per_page'] < 10){
            $session->set('per_page', 10);
        }

        if(!$this->admin){
            return redirect()->to('admin/login');
        }
        helper("admin");
	}
    public function index()
    {
        echo 'ADMINCONTROLLER: ';
    }

    public function report_issue()
    {
		$validation =  \Config\Services::validation();
        $request = service('request');
        $data = $request->getPost();
        $email_model = new EmailModel();

        $rules    = [
            'message'     => [
                'label'  => 'Message',
                'rules'  => 'required|min_length[10]',
                'errors' => [
                    'required' => 'Message field is mandatory',
                    'min_length' => 'Message must have at least 10 characters',
                ],
            ]
        ];
        $response['success'] = FALSE;

        // $s1 = session("broj1");
        // $s2 = session("broj2");

		$validation->reset();
		$validation->setRules($rules);
        // if(($s1 + $s2) == $data['sum']){
        if (!$validation->run($data)){
            $response['message'] = 'Your message is not sent: <br /><br />' . implode('<br />', $validation->getErrors());
            $response['json'] = $validation->getErrors();
        }else{
            // if($data['name'] == ''){
            // $to = '<EMAIL>';
            $to = '<EMAIL>';
            // $to = $this->settings['main_email'];
            $reply = NULL;
            $subject = $data['teacher_name'] . " reported an issue";
            $data = [
                'teacher_name' => $data['teacher_name'],
                'message' => $data['message'],
            ];
            $template = 'front/email_templates/report-issue-form';
            $response['success'] = $email_model->send_template($to, $reply, $subject, $data, $template);
            // }else{
                // $response['success'] = TRUE;
            // }
        }
        // }else{
        //     $response['msg'] = 'Wrong result. Please sum these two numbers correctly.';
        // }
        return $this->respond($response);
    }

    public function get_class_info($class_id, $ajax = 0)
    {
        $classModel = model('ClassesModel');
        $response['success'] = TRUE;
        // $response['class'] = $classModel->where(['id' => $class_id])->first();
        $response = $classModel->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image, 'classes' AS type,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title SEPARATOR ',') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM classes
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x ON x.class_id = classes.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y ON y.class_id = classes.id
                                LEFT JOIN classes_accessories ON  classes_accessories.class_id = classes.id
                                LEFT JOIN accessories ON accessories.id = classes_accessories.class_accessories
                                LEFT JOIN classes_springs ON  classes_springs.class_id = classes.id
                                LEFT JOIN springs ON springs.id = classes_springs.class_springs
                                LEFT JOIN classes_body_parts ON classes_body_parts.class_id = classes.id
                                LEFT JOIN body_parts ON body_parts.id = classes_body_parts.class_body_parts
                                LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE classes.deleted_at IS NULL
                                AND classes.status = 0
                                AND classes.id = '" . $class_id . "'
                            ")->getFirstRow('array');
        if($ajax == 1){
            return $this->respond($response);
        }else{
            return $response;
        }
    }
    public function get_howto_info($class_id)
    {
        $howtoModel = model('HowtoModel');
        $response['success'] = TRUE;
        // $response['class'] = $classModel->where(['id' => $class_id])->first();
        $response = $howtoModel->query("SELECT howto.*, difficulty.title as diff, teachers.slug  as teach_slug, CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)) as teach_name, teachers.image  as teach_image, 'videos' AS type,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM howto
                                LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM howto_views GROUP BY class_id) x ON x.class_id = howto.id
                                LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM howto_rate GROUP BY class_id) y ON y.class_id = howto.id
                                LEFT JOIN howto_accessories ON  howto_accessories.class_id = howto.id
                                LEFT JOIN accessories ON accessories.id = howto_accessories.class_accessories
                                LEFT JOIN howto_springs ON  howto_springs.class_id = howto.id
                                LEFT JOIN springs ON springs.id = howto_springs.class_springs
                                LEFT JOIN howto_body_parts ON howto_body_parts.class_id = howto.id
                                LEFT JOIN body_parts ON body_parts.id = howto_body_parts.class_body_parts
                                LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                LEFT JOIN teachers ON teachers.id = howto.teacher
                                WHERE howto.deleted_at IS NULL
                                AND howto.status = 0
                                AND howto.id = '" . $class_id . "'
                            ")->getFirstRow('array');
        return $response;
    }

    public function get_exercises_info($class_id = 0, $ajax = 0)
    {
        $exercisesModel = model('ExercisesModel');
        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();

        $response['success'] = TRUE;
        // $response['class'] = $classModel->where(['id' => $class_id])->first();
        $response = $exercisesModel->query("SELECT exercises.*, difficulty.title as diff, 'exercises' AS type,
                                COALESCE(x.cnt,0) AS countView,
                                COALESCE(y.rate,0) AS classRate,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_class_accessories,
                                GROUP_CONCAT(DISTINCT springs.title SEPARATOR ', ') AS all_class_springs,
                                GROUP_CONCAT(DISTINCT springs.id  ORDER BY springs.sort ASC SEPARATOR ',') AS all_class_springs_ids,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short
                                FROM exercises
                                LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x ON x.exercise_id = exercises.id
                                LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) as rate FROM exercises_rate GROUP BY exercise_id) y ON y.exercise_id = exercises.id
                                LEFT JOIN exercises_accessories ON  exercises_accessories.exercise_id = exercises.id
                                LEFT JOIN accessories ON accessories.id = exercises_accessories.exercise_accessories
                                LEFT JOIN exercises_springs ON  exercises_springs.exercise_id = exercises.id
                                LEFT JOIN springs ON springs.id = exercises_springs.exercise_springs
                                LEFT JOIN exercises_body_parts ON exercises_body_parts.exercise_id = exercises.id
                                LEFT JOIN body_parts ON body_parts.id = exercises_body_parts.exercise_body_parts
                                LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                WHERE exercises.deleted_at IS NULL
                                AND exercises.status = 0
                                AND exercises.id = " . (int)$class_id . "
                            ")->getRowArray();

        if($ajax == 1){
            return $this->respond($response);
        }else{
            return $response;
        }
    }

	public function upload()
	{
        $files = $this->request->getFiles();
        $file = $files['video'];

		if(!empty($file)){
        	$file_name   		=   $file->getName();
            $file_extension     =   $file->guessExtension();
            $allowed_extension  =   array('mp4', 'avi');

            if(in_array($file_extension,$allowed_extension)){
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/videos', $name);
				$data['uploaded_video'] = 'uploads/videos/' . $name;
                echo $data['uploaded_video'];

            }else{
            	echo 'Please upload valid file';
            }
        }
	}

	public function audio_upload()
	{
        $files = $this->request->getFiles();
        $file = $files['audio'];

		if(!empty($file)){
        	$file_name   		=   $file->getName();
            $file_extension     =   $file->guessExtension();
            $allowed_extension  =   array('mp3');

            if(in_array($file_extension,$allowed_extension)){
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/audio', $name);
				$data['uploaded_audio'] = 'uploads/audio/' . $name;
                echo $data['uploaded_audio'];

            }else{
            	echo 'Please upload valid file';
            }
        }
	}

	public function save_class_field()
	{
        $classModel = model('ClassesModel');
        $data = $this->request->getPost();

        $save_field = array('id' => $data['id'], $data['field'] => $data['field_value']);
        $saved = $classModel->save($save_field);

        return $this->respond($saved);
	}

	public function uploadBase64()
	{
        $data = $this->request->getPost();

        $response['success'] = FALSE;
		if(is_base64_encoded($data['image'])){
            $uploaded_file = base64_to_jpeg($data['image'], 'uploads/newImage_123.jpg');

            if($uploaded_file){
                $response['success'] = TRUE;
                $file = new \CodeIgniter\Files\File($uploaded_file);
                $file_name   		=   $file->getBasename();
                $file_extension     =   $file->guessExtension();
                $allowed_extension  =   array('jpg', 'png');

                if(in_array($file_extension,$allowed_extension)){
                    $name = $file->getRandomName();
                    if (!file_exists(ROOTPATH . 'public/uploads/videos/thumbnails')) {
                        mkdir(ROOTPATH . 'public/uploads/videos/thumbnails', 0777, true);
                    }
                    $file->move(ROOTPATH . 'public/uploads/videos/thumbnails', $name);
                    // \Config\Services::image()
                    // ->withFile(ROOTPATH . 'public/uploads/videos/thumbnails/' . $name)
                    // // ->resize(1000, 562, true, 'width')
                    // ->save(ROOTPATH . 'public/uploads/videos/thumbnails/' . $name, 50);

                    $response['uploaded_thumb'] = 'uploads/videos/thumbnails/' . $name;
                }else{
                    $response['msg'] = 'Please upload valid file';
                }
            }
        }

        return $this->respond($response);
	}

	public function video_thumbnail()
	{
        $data = $this->request->getPost();
        $video_path = $data['video'];
        $ffmpeg = "C:\\xampp_server\\ffmpeg\\ffmpeg";
        $ffprobe = "C:\\xampp_server\\ffmpeg\\ffprobe";
        $video['video_path'] = $video_path;

        ////////////////////////// VIDEO THUMBNAIL ///////////////////////////////
        $tmp1 = $_SERVER['DOCUMENT_ROOT'] . '/uploads/videos/test-thumbnail1.jpg';
        if($_ENV['CI_ENVIRONMENT'] == 'production'){
            $video['exec'] = exec("ffmpeg -ss 00:00:20 -i " . str_replace("\\","/" , $video_path ) . " -an -r 1 -vframes 1 -y -filter:v scale=1280:720,crop=1280:720 " . str_replace("\\","/" , $tmp1));
        }else{            
            $video['exec'] = exec("$ffmpeg -ss 00:00:20 -i " . str_replace("\\","/" , $video_path ) . " -an -r 1 -vframes 1 -y -filter:v scale=1280:720,crop=1280:720 " . str_replace("\\","/" , $tmp1));
        }

        $file1 = new \CodeIgniter\Files\File($tmp1);
        $name1 = $file1->getRandomName();
        $file1->move(ROOTPATH . 'public/uploads/videos/thumbnails', $name1);

        $video['success'] = TRUE;
        $video['video'][1] = '/uploads/videos/thumbnails/' . $name1;

        ////////////////////////// VIDEO PREVIEW ///////////////////////////////
        if($_ENV['CI_ENVIRONMENT'] == 'production'){
            $dur = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 " . str_replace("\\","/" , $video_path ) . "");
        }else{
            $dur = shell_exec("$ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 " . str_replace("\\","/" , $video_path ) . "");
        }
        $seconds = round($dur);

        $path_clip = $_SERVER['DOCUMENT_ROOT'] . '/uploads/videos/video_preview';
        if($_ENV['CI_ENVIRONMENT'] == 'production'){
            shell_exec("ffmpeg -ss 00:00:10 -i $video_path -an -t 10 -vf scale=640:360 -y $path_clip/preview_new.mp4");
        }else{
            shell_exec("$ffmpeg -ss 00:00:10 -i $video_path -an -t 10 -vf scale=640:360 -y $path_clip/preview_new.mp4");
        }

        $file_preview = "$path_clip/preview_new.mp4";
        $file_preview1 = new \CodeIgniter\Files\File($file_preview);
        $file_preview_name1 = $file_preview1->getRandomName();

        $file_preview1->move(ROOTPATH . 'public/uploads/videos/video_preview', $file_preview_name1);
        $video['video_preview'] = '/uploads/videos/video_preview/' . $file_preview_name1;
        $video['duration'] = $seconds;
        /////////////////////////////////////////////////////////////////////

        return $this->respond($video);
	}

	public function video_thumbnail_get()
	{
        $video_path = $_GET['video'];
        // $video['video_path'] = $video_path;

        // $tmp1 = $_SERVER['DOCUMENT_ROOT'] . 'uploads/videos/test-thumbnail1.jpg';
        // $video['exec'] = shell_exec("ffmpeg -ss 00:00:20 -i " . str_replace("\\","/" , $video_path ) . " -an -r 1 -vframes 1 -y -vsync vfr -filter:v scale=1280:720,crop=1280:720 " . str_replace("\\","/" , $tmp1));

        // print_r($video);

        // $file1 = new \CodeIgniter\Files\File($tmp1);
        // $name1 = $file1->getRandomName();
        // $file1->move(ROOTPATH . 'public/uploads/videos/thumbnails', $name1);

        // $video['success'] = TRUE;
        // $video['video'][1] = '/uploads/videos/thumbnails/' . $name1;

        /////////////////////////////////////////////////////////////////////
        $dur = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 '$video_path'");
        $seconds = round($dur);
        $video['seconds'] = $seconds;

        $thumb_0 = gmdate('H:i:s', $seconds / 8);
        $thumb_1 = gmdate('H:i:s', $seconds / 4);
        $thumb_2 = gmdate('H:i:s', $seconds / 2 + $seconds / 8);
        $thumb_3 = gmdate('H:i:s', $seconds / 2 + $seconds / 4);

        $path_clip = './uploads/videos/video_preview/';
        // $preview_list = fopen($path_clip . 'list.txt', "w");
        $preview_array = [];

        for ($i=0; $i <= 3; $i++) {
            $thumb = ${'thumb_'.$i};
            shell_exec("ffmpeg -i '$video_path' -an -ss $thumb -t 3 -vf 'scale=320:180:force_original_aspect_ratio=decrease,pad=320:180:(ow-iw)/2:(oh-ih)/2,setsar=1' -y $path_clip/$i.p.mp4");

            $output = $path_clip . $i.'.p.mp4';
        }

        shell_exec("ffmpeg -i $path_clip/0.p.mp4 -i $path_clip/1.p.mp4 -i $path_clip/2.p.mp4 -i $path_clip/3.p.mp4 -filter_complex \"concat=n=4:v=1:a=0\" -vn -y $path_clip/preview_new.mp4");

        // if (!empty($preview_array)) {
        //     foreach ($preview_array as $v) {
        //         // unlink($v);
        //     }
        // }

        // remove preview list
        // unlink($path_clip . 'list.txt');

        $file_preview = "$path_clip/preview_new.mp4";
        $file_preview1 = new \CodeIgniter\Files\File($file_preview);
        $file_preview_name1 = $file_preview1->getRandomName();
        $file_preview1->move(ROOTPATH . 'public/uploads/videos/video_preview', $file_preview_name1);
        $video['video_preview'] = 'public/uploads/videos/video_preview/' . $file_preview_name1;
        /////////////////////////////////////////////////////////////////////

        echo '<pre>';
        print_r($video);
	}

    public function ajax_delete($table = 'classes', $record_id = 0)
    {
        $email_model = model('EmailModel');
        $classes_views_model = model('ClassesViewModel');
        $ClassesModel = model('ClassesModel');
        $classesRate_model = model('ClassesRateModel');
        $subscribersFavs_model = model('SubscribersFavsModel');
        $subscribersWatched_model = model('SubscribersWatchedModel');
    	$SurveysAnswersModel = model('SurveysAnswersModel');
    	$SurveysQuestionsModel = model('SurveysQuestionsModel');
    	$ModelsModel = model('ModelsModel');
    	$TeachersModel = model('TeachersModel');


		$currentModel = model($table . 'Model');
        $current_item = $currentModel->where('id', $record_id)->first();

        $response['current_item'] = $current_item;
        $response['table'] = $table;
        $response['record'] = $record_id;

		if ($record_id > 0){
			$response['success'] = $currentModel->delete($record_id);
            if($table == 'classes'){
                $classes_views_model->where('class_id', $record_id)->delete();
                $classesRate_model->where('class_id', $record_id)->delete();
                $subscribersFavs_model->where('class_id', $record_id)->delete();
                $subscribersWatched_model->where('class_id', $record_id)->delete();
                $db      = \Config\Database::connect();
                $builder = $db->table('collections_selected_classes');
                $builder->delete(['collection_selected_classes' => $record_id]);
            }
	        if($table == 'surveys_questions' OR $table == 'SurveysQuestions'){
                $SurveysAnswersModel->where('question_id', $record_id)->delete();
            }
	        if($table == 'surveys'){
                $SurveysQuestions = $SurveysQuestionsModel->where('survey_id', $record_id)->findAll();
                foreach($SurveysQuestions as $questions){
                    $SurveysQuestionsModel->where('survey_id', $record_id)->delete();
                    $SurveysAnswersModel->where('question_id', $questions['id'])->delete();
                }
            }
	        if($table == 'Calendar'){
                $response['calendar_class_deleted'] = $ClassesModel->where('id', $current_item['class_id'])->delete();
                $teacher = $TeachersModel->where(['id' => $current_item['teacher_id']])->first();
                $model = $ModelsModel->where(['id' => $current_item['model_id']])->first();
        
                // teacher email
                if(!empty($teacher)){
                    $subject = 'Your scheduled LOD class recording is canceled';
                    $data_template = [
                        'title' => 'Scheduled recording is canceled',
                        'name' => $teacher['firstname'],
                        'date' => date('m/d/Y', strtotime($current_item['date'])),
                        'time' => $current_item['time'],
                    ];
                    $template = 'front/email_templates/class-recording-canceled';
                    $to = $teacher['email'];
                    $response['teacher_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                }
                // model email
                if(!empty($model)){
                    $subject = 'Your scheduled LOD class recording is canceled';
                    $data_template = [
                        'title' => 'Scheduled recording is canceled',
                        'name' => $model['firstname'],
                        'date' => date('m/d/Y', strtotime($current_item['date'])),
                        'time' => $current_item['time'],
                    ];
                    $template = 'front/email_templates/class-recording-canceled';
                    $to = $model['email'];
                    $response['model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                }
            }
		}
        return $this->respond($response);
    }

    public function ajax_cancel($table = 'classes', $record_id = 0)
    {
        $email_model = model('EmailModel');
    	$ModelsModel = model('ModelsModel');
    	$TeachersModel = model('TeachersModel');
		$currentModel = model($table . 'Model');

        $response['table'] = $table;
        $response['record'] = $record_id;

		if ($record_id > 0){
            $current_item = $currentModel->where('id', $record_id)->first();
            $response['current_item'] = $current_item;
            $data = $current_item;
            $response['paid_initial'] = $current_item['paid'];

            if($current_item != NULL){
                $now = strtotime(date('Y-m-d'));
                $current_time = strtotime($current_item['date']);
        
                $response['success'] = $currentModel->save(['id' => $record_id, 'paid' => ($response['paid_initial'] != 5 ? 5 : ($now < $current_time ? 2 : 1) )]);
                if($table == 'Calendar'){
                    // $response['calendar_class_deleted'] = $ClassesModel->where('id', $current_item['class_id'])->delete();
                    $teacher = $TeachersModel->where(['id' => $data['teacher_id']])->first();
                    if(isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != ''){
                        $model = $ModelsModel->where(['id' => $data['model_id']])->first();
                    }else{
                        $model = NULL;
                    };
                    if(isset($data['teacher_as_model_id']) AND $data['teacher_as_model_id'] != 0 AND $data['teacher_as_model_id'] != ''){
                        $teacher_as_model = $TeachersModel->where(['id' => $data['teacher_as_model_id']])->first();
                    }else{
                        $teacher_as_model = NULL;
                    };
                    
                    if($response['paid_initial'] != 5){
                        // WAS NOT CANCELED
                        // teacher email
                        if(!empty($teacher)){
                            $subject = 'Your scheduled LOD class recording is canceled';
                            $data_template = [
                                'title' => 'Scheduled recording is canceled',
                                'name' => $teacher['firstname'],
                                'date' => date('m/d/Y', strtotime($current_item['date'])),
                                'time' => $current_item['time'],
                            ];
                            $template = 'front/email_templates/class-recording-canceled';
                            $to = $teacher['email'];
                            $response['teacher_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                        }
                        // model email
                        if(!empty($model)){
                            $subject = 'Your scheduled LOD class recording is canceled';
                            $data_template = [
                                'title' => 'Scheduled recording is canceled',
                                'name' => $model['firstname'],
                                'date' => date('m/d/Y', strtotime($current_item['date'])),
                                'time' => $current_item['time'],
                            ];
                            $template = 'front/email_templates/class-recording-canceled';
                            $to = $model['email'];
                            $response['model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                        }
                    }else{
                        // WAS CANCELED
                        if(!empty($teacher)){
                            $subject = 'Your LOD class recording is scheduled';
                            $data_template = [
                                'click' => base_url() . '/admin/login',
                                'title' => 'Scheduled recording',
                                'teacher_name' => $teacher['firstname'],
                                'model_name' => (isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != '' ? $model['firstname'] : 'Model is missing'),
                                'date' => date('m/d/Y', strtotime($data['date'])),
                                'time' => $data['time'],
                            ];
                            $template = 'front/email_templates/class-recording-teacher';
                            // $to = '<EMAIL>';
                            $to = $teacher['email'];
                            $response['teacher_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                        }
                        // model email
                        if(!empty($model)){
                            $subject = 'Your LOD class recording is scheduled';
                            $data_template = [
                                'title' => 'Scheduled recording',
                                'teacher_name' => $teacher['firstname'],
                                'model_name' => $model['firstname'],
                                'date' => date('m/d/Y', strtotime($data['date'])),
                                'time' => $data['time'],
                            ];
                            $template = 'front/email_templates/class-recording-model';
                            $to = $model['email'];
                            $response['teacher_as_model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                        }
                        // teacher as a model email
                        if(!empty($teacher_as_model)){
                            $subject = 'Your LOD class recording is scheduled';
                            $data_template = [
                                'title' => 'Scheduled recording',
                                'teacher_name' => $teacher['firstname'],
                                'model_name' => $teacher_as_model['firstname'],
                                'date' => date('m/d/Y', strtotime($data['date'])),
                                'time' => $data['time'],
                            ];
                            $template = 'front/email_templates/class-recording-teacher-as-model';
                            $to = $teacher_as_model['email'];
                            $response['model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                        }
                    }
                }
            }
		}
        return $this->respond($response);
    }

    public function ajax_hide($table = 'classes', $record_id = 0)
    {
		$currentModel = model($table . 'Model');
        $SubscribersConversationsModel = model('SubscribersConversationsModel');

        $data_for_hide = ['id' => $record_id, 'hide' => 1];
		if ($record_id > 0){
			$response['success'] = $currentModel->save($data_for_hide);
		}
		if ($table == 'conversations'){
            $conversation_all_messages = $SubscribersConversationsModel->query("SELECT * FROM subscribers_conversations WHERE conversation_id = " . $record_id . "")->getResultArray();
            foreach($conversation_all_messages as $single){
                $data_for_hide_messages = ['id' => $single['id'], 'hide' => 1];
                $response['message_hidden'][] = $SubscribersConversationsModel->save($data_for_hide_messages);
            }
		}
        return $this->respond($response);
    }
    public function ajax_duplicate($model = 'RoutinesExercises', $record_id = 0)
    {
		$currentModel = model($model . 'Model');
        $RoutinesModel = model('RoutinesModel');
        $ClassesModel = model('ClassesModel');

        $response['success'] = FALSE;

		if ($record_id > 0){
            $data = $currentModel->where('id', $record_id)->first();
            unset($data['id']);
            $response['success'] = $currentModel->insert($data);
            $response['new_id'] = $currentModel->insertID();
            $new_data = $currentModel->where('id', $response['new_id'])->first();
            $response['new_data'] = $new_data;

            if($model == 'RoutinesExercises'){
                $single = $RoutinesModel->single_exercise_for_routine($new_data['routine_id'], $response['new_id']);
            }else{
                $single = $ClassesModel->single_exercise_for_single_class($new_data['class_id'], $response['new_id']);
            }
            
            if(!empty($single)){
                $single['single'] = $single[0];                
                $db = \Config\Database::connect();
                $single['springs'] = $db->query('SELECT * FROM springs  ORDER BY sort ASC')->getResultArray();
    
                if($model == 'RoutinesExercises'){
                    $response['html'] = view('admin/routines/cloned_view', $single);
                }else{
                    $response['html'] = view('admin/classes/cloned_view', $single);
                }
                $response['single'] = $single['single'];
            }            
        }

        return $this->respond($response);
    }

    public function ajax_rename()
    {
        $data = $this->request->getPost();
        
		$currentModel = model($data['table'] . 'Model');

        $response['success'] = FALSE;
        
        $data_for_rename = [
            'id' => $data['id'], 
            'title' => $data['title']
        ];
		if (isset($data['id']) AND $data['id'] > 0){
			$response['success'] = $currentModel->save($data_for_rename);
			$response['errors'] = $currentModel->errors();
		}

        return $this->respond($response);
    }

    public function ajax_approve($record_id = 0)
    {
		$currentModel = model('ClassesModel');
		if ($record_id > 0){
            $data_save = [
                "id" => $record_id,
                "status" => 0
            ];
            $response['success'] = $currentModel->save($data_save);
        }
        return $this->respond($response);
    }

    public function ajax_collection_class_delete()
    {
        $data = $this->request->getPost();
        $currentModel = model('Collection' . $data['type'] . 'Model');
        $response['post'] = $data;

		if ($data['id'] > 0){
			$response['success'] = $currentModel->delete($data['id']);
		}
        return $this->respond($response);
    }

    public function ajax_playlist_class_delete()
    {
        $data = $this->request->getPost();
        $currentModel = model('Playlist' . $data['type'] . 'Model');
        $response['post'] = $data;

		if ($data['id'] > 0){
			$response['success'] = $currentModel->delete($data['id']);
		}
        return $this->respond($response);
    }

    public function ajax_exercises_class_delete()
    {
        $data = $this->request->getPost();
        $currentModel = model('Classes' . $data['type'] . 'Model');
        $response['post'] = $data;

		if ($data['id'] > 0){
			$response['success'] = $currentModel->delete($data['id']);
		}
        return $this->respond($response);
    }

    public function ajax_exercises_routine_delete()
    {
        $data = $this->request->getPost();
        $currentModel = model('Routines' . $data['type'] . 'Model');
        $response['post'] = $data;

		if ($data['id'] > 0){
			$response['success'] = $currentModel->delete($data['id'], TRUE);
		}
        return $this->respond($response);
    }

    public function set_class_filter($filter = NULL)
    {
        $session = \Config\Services::session();

        if($filter != NULL){
            $session->set('classes_filter', $filter);
        }

        return $this->respond(session('classes_filter'));
    }

    public function set_per_page($table = NULL, $per_page = 0)
    {
        $session = \Config\Services::session();

        if($table != NULL AND $per_page != NULL){
            $session->set($table . '_per_page', $per_page);

            $admin = $this->admin_table->where(['admin_id' => session('admin')])->first();
            $this->admin_table->save(['id' => $admin['id'], $table . '_per_page' => $per_page]);
        }

        return $this->respond(session($table . '_per_page'));
    }
    public function per_page($per_page = 0)
    {
        $session = \Config\Services::session();
        $session->set('per_page', $per_page);

        return $this->respond(session('per_page'));
    }
    public function set_search($table = NULL, $search = NULL)
    {
        $session = \Config\Services::session();

        if($table != NULL AND $search != NULL){
            $session->set($table . '_search', $search);

            $admin = $this->admin_table->where(['admin_id' => session('admin')])->first();
            $this->admin_table->save(['id' => $admin['id'], $table . '_search' => $search]);
        }

        return $this->respond(session($table . '_search'));
    }
    public function set_sort_by()
    {
        $session = \Config\Services::session();
        $data = $this->request->getPost();

        $session->set($data['table'] . '_sort', $data['sort'] . ' ' . $data['by']);
        session()->set($data['table'] . '_sort', $data['sort'] . ' ' . $data['by']);

        $admin = $this->admin_table->where(['admin_id' => session('admin')])->first();
        $this->admin_table->save(['id' => $admin['id'], $data['table'] . '_sort' => ($data['sort'] . ' ' . $data['by'])]);

        return $this->respond(['sort' => $data['table'] . '_sort']);
    }
}
