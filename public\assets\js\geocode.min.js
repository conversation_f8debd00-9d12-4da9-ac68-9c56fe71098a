function GoogleGeocode(){var a=new google.maps.Geocoder;this.geocode=function(b,c){a.geocode({address:b},function(a,b){if(b===google.maps.GeocoderStatus.OK){var d={};d.latitude=a[0].geometry.location.lat(),d.longitude=a[0].geometry.location.lng(),c(d)}else alert("Geocode was not successful for the following reason: "+b),c(null)})}}$(function(){$("#bh-sl-user-location").on("submit",function(a){a.preventDefault();var b=$("form #bh-sl-address").val();""===b&&alert("The input box was blank.");var c=new GoogleGeocode,d=b;c.geocode(d,function(a){if(null!==a){var b=a.latitude,c=a.longitude;$("#geocode-result").append("Latitude: "+b+"<br />Longitude: "+c+"<br /><br />")}else alert("ERROR! Unable to geocode address")})})});