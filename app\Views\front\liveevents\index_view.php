<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<style>
.min350 {
    min-width: 350px;
}
@media(max-width: 480px){
.min350 {
    min-width: 100%;
}
}
</style>
</head>
<body class="collection-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <!-- <div class="px-100 small-header-image" style="background: url(images/single-collection-header-bg.jpg) no-repeat center center / cover"> -->
    <div class="px-100 small-header-image">
        <div class="img-panel h100">
            <div class="image-overlay h100">
                <img src="<?php echo (isset($next_liveevent[0]['cover_image']) AND $next_liveevent[0]['cover_image'] != '') ? $next_liveevent[0]['cover_image'] : ''; ?>" alt="<?php echo (isset($next_liveevent[0]['title']) AND $next_liveevent[0]['title'] != '') ? $next_liveevent[0]['title'] : ''; ?>" class="img-fluid desktop" />
                <img src="<?php echo (isset($next_liveevent[0]['mob_cover_image']) AND $next_liveevent[0]['mob_cover_image'] != '') ? $next_liveevent[0]['mob_cover_image'] : ''; ?>" alt="<?php echo (isset($next_liveevent[0]['title']) AND $next_liveevent[0]['title'] != '') ? $next_liveevent[0]['title'] : ''; ?>" class="img-fluid mobile" />
            </div>
            <div class="img-panel-content ail">
                <h3 class="f-16 bold mb-4 white line-height-small"><?php echo (isset($next_liveevent[0]['date']) AND $next_liveevent[0]['date'] != '') ? date('m/d/Y', strtotime($next_liveevent[0]['date'])) : ''; ?> @ <?php echo (isset($next_liveevent[0]['time']) AND $next_liveevent[0]['time'] != '') ? $next_liveevent[0]['time'] : ''; ?> PST</h3>
                <h2 class="f-24 bold mb-3 white line-height-small"><?php echo (isset($next_liveevent[0]['title']) AND $next_liveevent[0]['title'] != '') ? $next_liveevent[0]['title'] : ''; ?></h2>
                <h3 class="f-18 mb-5 white line-height-small light"><?php echo (isset($next_liveevent[0]['location']) AND $next_liveevent[0]['location'] != '') ? $next_liveevent[0]['location'] : ''; ?>, <?php echo (isset($next_liveevent[0]['all_class_machines']) AND $next_liveevent[0]['all_class_machines'] != '') ? $next_liveevent[0]['all_class_machines'] : ''; ?>, <?php echo (isset($next_liveevent[0]['duration']) AND $next_liveevent[0]['duration'] != '') ? $next_liveevent[0]['duration'] : ''; ?>, Difficulty: <?php echo (isset($next_liveevent[0]['diff']) AND $next_liveevent[0]['diff'] != '') ? $next_liveevent[0]['diff'] : ''; ?></h3>
                <?php if($is_past){ ?>
                    <?php if(isset($logged_user)){ ?>
                        <div class="buttons-holder flex aic jcc">
                            <a href="/liveevents/<?php echo (isset($next_liveevent[0]['slug']) AND $next_liveevent[0]['slug'] != '') ? $next_liveevent[0]['slug'] : ''; ?>" class="btn white-bg black mr-3" title="Watch">Watch</a>
                        </div>
                    <?php }else{ ?>
                        <div class="buttons-holder flex aic jcc">
                            <a href="/subscribe" class="btn white-bg black mr-3" title="Watch">Subscribe to Watch</a>
                        </div>
                    <?php } ?>
                <?php }else{ ?>
                    <div class="buttons-holder flex aic jcc">
                        <?php if(isset($logged_user)){ ?>
                            <?php if($is_past OR $next_liveevent[0]['date'] == date('Y-m-d')){ ?>
                                <div class="buttons-holder flex aic jcc">
                                    <a href="/liveevents/<?php echo (isset($next_liveevent[0]['slug']) AND $next_liveevent[0]['slug'] != '') ? $next_liveevent[0]['slug'] : ''; ?>" class="btn white-bg black mr-3" title="Watch Now">Watch Now</a>
                                </div>
                            <?php } ?>
                            <?php if(isset($signed_for_event)){ ?>
                                <!-- <a href="javascript:;" class="btn white-bg black mr-3 disabled" title="Already signed for this event">Signed</a> -->
                            <?php }else{ ?>
                                <!-- <a href="javascript:;" class="btn white-bg black mr-3 sign_up_for_live_event" title="Count me in" data-event-id="<?php echo $next_liveevent[0]['id']; ?>" data-user-id="<?php echo $logged_user['id']; ?>">Count me in</a> -->
                            <?php } ?>
                        <?php }else{ ?>
                            <a href="/subscribe" class="btn white-bg black mr-3" title="Subscribe to get in">Subscribe to get in</a>
                        <?php } ?>
                        <a href="javascript:;" class="btn btn-badge lightWhite-bg white" data-popup="share-popup" data-popup-title="Share This Event With Your Friends" title="Share"><img src="images/share-icon.svg" alt="" class="img-fluid" width="20" height="13" /></a>

                        <!-- <span class="white text-underline" title="<?php echo $signed_users; ?> Signed Up"><span class="signed_users"><?php echo $signed_users; ?></span> Signed Up</span> -->
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
    <section class="pt-100 px-100 pb-100">
        <div class="container-fluid">
            <div class="row aic nowrap">
                <div class="col pr-150">
                    <h2 class="mb-3 h3">About <?php echo (!$is_past) ? 'Live' : '' ?> Event</h2>
                    <p class="light f-18 mb-4"><?php echo (isset($next_liveevent[0]['content']) AND $next_liveevent[0]['content'] != '') ? $next_liveevent[0]['content'] : ''; ?></p>
                    <div class="flex aic">
                    <?php if(!$is_past){ ?>
                        <!-- <a href="javascript:;" class="btn darkGray-bg white mr-3" title="Set Reminder">Set Reminder</a> -->
                    <?php } ?>
                    </div>
                </div>
                <hr class="mobile my-100">
                <div class="col flex-vertical ml-auto max450 min350">
                    <div class="single-class-row">
                        <span class="class-row-left">Machine:</span>
                        <span class="class-row-right"><?php echo (isset($next_liveevent[0]['all_class_machines']) AND $next_liveevent[0]['all_class_machines'] != '') ? $next_liveevent[0]['all_class_machines'] : ''; ?></span>
                    </div>
                    <div class="single-class-row">
                        <span class="class-row-left">Duration:</span>
                        <span class="class-row-right"><?php echo (isset($next_liveevent[0]['duration']) AND $next_liveevent[0]['duration'] != '') ? str_replace(' minutes', '', $next_liveevent[0]['duration']) : ''; ?></span>
                    </div>
                    <div class="single-class-row">
                        <span class="class-row-left">Difficulty:</span>
                        <span class="class-row-right"><?php echo (isset($next_liveevent[0]['diff']) AND $next_liveevent[0]['diff'] != '') ? $next_liveevent[0]['diff'] : ''; ?></span>
                    </div>
                </div>

                <!-- <div class="col flex ml-auto border-boxes">
                    <div class="border-box">
                        <span class="border-box-cell bottom-border">
                            <?php echo (isset($next_liveevent[0]['all_class_machines']) AND $next_liveevent[0]['all_class_machines'] != '') ? $next_liveevent[0]['all_class_machines'] : ''; ?>
                            <span class="midGray f-10">MACHINE</span>
                        </span>
                        <span class="border-box-cell f-1">
                            Need Machine?<br>
                            <a href="https://www.shopmaximumfitness.com/" target="_blank" class="link link-red red text-underline">Buy now</a>
                        </span>
                    </div>
                    <div class="border-box">
                        <span class="border-box-cell bottom-border">
                            <img src="images/clock-icon.svg" alt="" class="img-fluid" />
                        </span>
                        <span class="border-box-cell text-center">
                            <?php echo (isset($next_liveevent[0]['duration']) AND $next_liveevent[0]['duration'] != '') ? str_replace(' minutes', '\'', $next_liveevent[0]['duration']) : ''; ?>
                            <span class="midGray f-1">DURATION</span>
                        </span>
                    </div>
                    <div class="border-box">
                        <span class="border-box-cell bottom-border">
                            <img src="images/difficulty-icon.svg" alt="" class="img-fluid" />
                        </span>
                        <span class="border-box-cell text-center">
                            <?php echo (isset($next_liveevent[0]['diff']) AND $next_liveevent[0]['diff'] != '') ? substr($next_liveevent[0]['diff'], 0, 1) : ''; ?>
                            <span class="midGray f-1">DIFFICULTY</span>
                        </span>
                    </div>
                </div> -->
            </div>
            <hr class="my-100" style="display: none !important">
            <div class="row aic mb-100" style="display: none !important">
                <div class="col-12">
                    <h2 class="f-18 line-height-small">Past Live Events</h2>
                </div>
            </div>
        </div>
        <div class="container-fluid" style="display: none !important">
            <div class="row big-big-gap">
<?php
foreach($past_liveevents as $single){
?>
                <div class="col-4">
                    <div class="single-video-item">
                        <a href="liveevents/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container">
                            <span class="duration"><?php echo (isset($single['duration']) AND $single['duration'] != '') ? $single['duration'] : ''; ?></span>
                            <?php if(!empty($logged_user)){ ?>
                                <span class="play-button"><span></span></span>
                            <?php } ?>
                            <div class="image-overlay h100"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ''; ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" /></div>
                            <?php if(empty($logged_user)){ ?>
                            <span class="locked"></span>
                            <?php } ?>
                        </a>
                        <div class="video-text-container">
                            <a href="liveevents/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>">
                                <h4 class="flex jcsb medium mb-2 f-14 ail">
                                    <span class="pr-2"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span>
                                    <p class="flex aic red ml-2 normal" style="min-width: 47px;"><?php echo (isset($single['classRate']) AND $single['classRate'] != '') ? number_format($single['classRate'], 1) : ''; ?> <i class="icon-small-star ml-05"></i></p>
                                </h4>
                            </a>
                            <p class="midGray f-14 mb-3 light">
                                <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] . ', ' : ''; ?>
                                <?php echo (isset($single['duration']) AND $single['duration'] != '') ? only_minutes($single['duration']) . ' minutes' : ''; ?>
                                <?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] . ', ' : ''; ?>
                                 by: <a href="teachers/<?php echo (isset($single['teach_slug']) AND $single['teach_slug'] != '') ? $single['teach_slug'] : ''; ?>" class="link link-black black text-underline"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : ''; ?></a>
                            </p>
                        </div>
                    </div>
                </div>
<?php
}
?>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
</body>
</html>