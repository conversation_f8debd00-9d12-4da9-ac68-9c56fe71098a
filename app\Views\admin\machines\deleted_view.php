<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Slide</h1>
                <a href="admin/slider" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-4">
        </div>
        <form action="admin/slider/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <h3 class="mb-3">Main image</h3>
            <p class="midGray mb-5">Select or upload a photo that shows on teacher’s bio page.</p>
            <div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image cover_image_size">
                    <input type="file" name="cover_image" id="cover_image">
                    <img src="<?php echo empty($current['cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 650px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Mobile image</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 640px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Slide title</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input f-3 bold black red" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Description</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="description" class="line-input" value="<?php echo isset($current['description']) ? $current['description'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row mb-5">
                <div class="col-6">
                    <h3 class="mb-3">Button</h3>
                    <div class="input-container mb-2">
                        <input type="text" name="button_text" class="line-input" placeholder="Button label" value="<?php echo isset($current['button_text']) ? $current['button_text'] : '' ?>" />
                    </div>
                    <div class="input-container">
                        <input type="text" name="link" class="line-input" placeholder="Link" value="<?php echo isset($current['link']) ? $current['link'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="my-5">
            <div class="row">
                <div class="col-6 for_submit">
                    <input type="hidden" name="cover_image_removed" id="cover_image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="status" id="status" value="1">
                    <button type="submit" class="btn btn-wide btn-tall red-bg white">Publish Slide</button>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>