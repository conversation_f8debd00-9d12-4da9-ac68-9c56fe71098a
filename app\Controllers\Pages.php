<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Pages extends Frontcontroller
{
	use ResponseTrait;

    protected $model;
    protected $controller;
    protected $user;
    protected $settings;

	public function __construct() {
		parent::__construct();
		$this->model = model('PagesModel');
        $this->controller = 'pages';
	}

    public function index()
    {
        $classes_model = model('ClassesModel');
        $exercises_model = model('ExercisesModel');
        $PlaylistsModel = model('PlaylistsModel');
        $CoursesModel = model('CoursesModel');
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        $data['nums'] = create_session_nums();

        $data['current'] = $this->model->where(['template' => 'index'])->first();
        
        // $data['buy_rent_classes'] = $classes_model->all_buy_rent_classes_home(0, 6, NULL, 'classes.created_at desc');
        $data['micro_classes'] = $classes_model->all_micro_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['micro_pro_classes'] = $classes_model->all_micro_pro_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['mini_classes'] = $classes_model->mini_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['mini_pro_classes'] = $classes_model->mini_pro_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['mega_classes'] = $classes_model->all_mega_classes_home(0, 3, NULL, 'classes.created_at desc');
        $data['mega_pro_classes'] = $classes_model->all_mega_pro_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['evo_classes'] = $classes_model->evo_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['abwheel_classes'] = $classes_model->abwheel_classes_home(0, 4, NULL, 'classes.created_at desc');
        $data['courses'] = $CoursesModel->courses_home('6,8,18');

        $data['all_sliders'] = $this->model->query("SELECT * FROM slider WHERE deleted_at IS NULL AND status = 0 ORDER BY sort ASC")->getResultArray();
        $data['four_playlists'] = $PlaylistsModel->all_playlists(0, 4);

	    echo view('front/pages/index_view', $data);
    }

    public function slug($slug = '')
    {
        $data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['controller'] = $this->controller;
        // $shopify_model = model('ShopifyModel');

	$data['current'] = $this->model->where(['slug' => $slug])->first();

        // MEGA NEW COLLECT       - 184772526218
        // MEGA RESTORED COLLECT  - 184772755594
        // MICRO                  - 5042742788234
        // FULLY                  - *************
        // MINI                   - 70623********
        // MINI PRO               - *************

        // if($slug == 'shop'){
        //     $data['micro'] = $shopify_model->single_product('5042742788234');
        //     $data['mini'] = $shopify_model->single_product('70623********');
        //     $data['mini_pro'] = $shopify_model->single_product('*************');
        //     $data['fully'] = $shopify_model->single_product('*************');
        // }

        $data['nums'] = create_session_nums();

        if(isset($data['current']['template']) AND $data['current']['template'] != ''){
            if($data['current']['template'] == 'subscribe' AND isset($this->user)){
                if(session('subscription') == 'active'){
                    return redirect()->to('/account/payments');
                }
                return view('front/pages/subscribe_logged_view', $data);
            }else{
                return view('front/pages/' . $data['current']['template']. '_view', $data);
            }
        }else{
            return view('front/pages/404_view', $data);
        }
    }
    // public function get_shopify_collection($id = 0)
    // {
    //     $shopify_model = model('ShopifyModel');
    //     if($id != 0){
    //         $data['shopify'] = $shopify_model->single_collection($id);
    //         if($data['shopify']['success']){
    //             foreach($data['shopify']['products'] as $key => $product){
    //                 $data['collection_products'][] = $shopify_model->single_product($product['id']);
    //             }
    //             $response['success'] = TRUE;
    //         }
    //         $response['html'] = view('front/pages/shop_collection_products_view', $data);
    //     }else{
    //         $response['success'] = FALSE;
    //     }

	// 	return $this->respond($response);
    // }
    public function check_code()
	{
        $codesModel = model('CodesModel');
        $request = service('request');
        $data = $request->getPost();

		$response['success'] = TRUE;
		$response['code_info'] = $codesModel->check_code($data['code']);

        return $this->respond($response);
	}
}
