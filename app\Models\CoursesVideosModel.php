<?php namespace App\Models;

use CodeIgniter\Model;

class CoursesVideosModel extends Model
{
    protected $table = 'courses_videos';
	protected $allowedFields = ['course_id', 'title', 'description', 'video_preview', 'video', 'video_thumb', 'video_encrypted_path', 'duration', 'type', 'date', 'sort', 'teacher', 'slug'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        // 'slug'        => 'required|alpha_dash|is_unique[courses_videos.slug,id,{id}]',
        // 'content'     => 'required',
        // 'video'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
        // 'body_parts'     => 'required',
        // 'accessories'     => 'required',
        // 'springs'     => 'required',
        // 'teacher'     => 'required',
    ];
    protected $validationMessages = [
        // 'slug' => [
        //     'required'  => 'The Page URL field is required.',
        //     'is_unique' => 'The Page URL field must be unique!'
        // ]
    ];

    public function current_video($slug = '')
    {
        $data = $this->query("SELECT courses_videos.*,
                                video_state.video_time as video_state,
                                IF((SELECT count(id) FROM courses_videos_likes WHERE video_id = courses_videos.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                (SELECT count(rate > 3) as rate FROM  courses_videos_likes WHERE video_id =  courses_videos.id GROUP BY video_id) as likeCount,
                                IF((SELECT count(id) FROM subscribers_favs WHERE class_id = courses_videos.id AND type = 'course_video' AND subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as added_to_watch_later,

                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_course_accessories_shopify,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_course_accessories,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_course_springs,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_course_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_course_machines_short,
                                IF(courses_videos.id IN (
                                        SELECT * FROM (
                                                SELECT course_video_id FROM courses_videos_views WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                        ) as classes_watched
                                ), 1, 0) as watched
                                FROM courses_videos
                                LEFT JOIN courses_videos_accessories ON courses_videos_accessories.course_id = courses_videos.id
                                LEFT JOIN accessories ON accessories.id = courses_videos_accessories.course_accessories
                                LEFT JOIN courses_videos_springs ON  courses_videos_springs.course_id = courses_videos.id
                                LEFT JOIN springs ON springs.id = courses_videos_springs.course_springs
                                LEFT JOIN courses_videos_body_parts ON courses_videos_body_parts.course_id = courses_videos.id
                                LEFT JOIN body_parts ON body_parts.id = courses_videos_body_parts.course_body_parts
                                LEFT JOIN courses_videos_machine ON courses_videos_machine.course_id = courses_videos.id
                                LEFT JOIN machines ON machines.id = courses_videos_machine.course_machine
                                LEFT JOIN teachers ON teachers.id = courses_videos.teacher
                                LEFT JOIN video_state on (video_state.video_id = courses_videos.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                                WHERE courses_videos.deleted_at IS NULL
                                AND courses_videos.slug = '" . $slug . "'
                            ")->getRowArray();
        return $data;
    }

    public function course_video_info($id = '')
    {
        $data = $this->query("SELECT courses_videos.*,
                                video_state.video_time as video_state,
                                IF((SELECT count(id) FROM courses_videos_likes WHERE video_id = courses_videos.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                                (SELECT count(rate > 3) as rate FROM  courses_videos_likes WHERE video_id =  courses_videos.id GROUP BY video_id) as likeCount,
                                IF((SELECT count(id) FROM subscribers_favs WHERE class_id = courses_videos.id AND type = 'course_video' AND subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as added_to_watch_later,

                                CONCAT(teachers.firstname, ' ', teachers.lastname) as teach,
                                GROUP_CONCAT(DISTINCT body_parts.title SEPARATOR ', ') AS all_body_parts,
                                GROUP_CONCAT(DISTINCT accessories.shopify_id SEPARATOR ',') AS all_course_accessories_shopify,
                                GROUP_CONCAT(DISTINCT accessories.title SEPARATOR ', ') AS all_course_accessories,
                                GROUP_CONCAT(DISTINCT springs.title ORDER BY springs.sort ASC SEPARATOR ', ') AS all_course_springs,
                                GROUP_CONCAT(DISTINCT machines.shopify_id SEPARATOR ',') AS all_course_machines_shopify,
                                GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_course_machines,
                                GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_course_machines_short,
                                IF(courses_videos.id IN (
                                        SELECT * FROM (
                                                SELECT course_video_id FROM courses_videos_views WHERE user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                        ) as classes_watched
                                ), 1, 0) as watched
                                FROM courses_videos
                                LEFT JOIN courses_videos_accessories ON courses_videos_accessories.course_id = courses_videos.id
                                LEFT JOIN accessories ON accessories.id = courses_videos_accessories.course_accessories
                                LEFT JOIN courses_videos_springs ON  courses_videos_springs.course_id = courses_videos.id
                                LEFT JOIN springs ON springs.id = courses_videos_springs.course_springs
                                LEFT JOIN courses_videos_body_parts ON courses_videos_body_parts.course_id = courses_videos.id
                                LEFT JOIN body_parts ON body_parts.id = courses_videos_body_parts.course_body_parts
                                LEFT JOIN courses_videos_machine ON courses_videos_machine.course_id = courses_videos.id
                                LEFT JOIN machines ON machines.id = courses_videos_machine.course_machine
                                LEFT JOIN teachers ON teachers.id = courses_videos.teacher
                                LEFT JOIN video_state on (video_state.video_id = courses_videos.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'courses')
                                WHERE courses_videos.deleted_at IS NULL
                                AND courses_videos.id = '" . $id . "'
                            ")->getRowArray();
        return $data;
    }

    function prev_next($id = 0, $video_id = 0)
	{
		$result = array('prev' => NULL, 'next' => NULL,);

		if($id != 0 AND $video_id != 0){
            $tmp = $this->query("SELECT * FROM courses_videos WHERE course_id = " . $id . " AND type = 'video' AND deleted_at IS NULL ORDER BY sort asc")->getResultArray();

			if(count($tmp) > 1)
			{
				$total = count($tmp);
				$index_list = array_column($tmp, 'id');
				$index_id = array_search($video_id, array_column($tmp, 'id'));
				if($index_id !== FALSE){
					if($index_id < $total - 1){
						$result['next'] = $this->where(['id' => $index_list[$index_id + 1]])->first();
					}else{
						$result['next'] = $this->where(['id' => $index_list[0]])->first();
					}

					if($index_id > 0){
						$result['prev'] = $this->where(['id' => $index_list[$index_id - 1]])->first();
					}else{
						$result['prev'] = $this->where(['id' => $index_list[$total - 1]])->first();
					}
				}
			}
		}

        // echo "<pre>";
        // var_dump($result);
        // die();

		return $result;
	}

	protected function prepare_data(array $data)
	{
		return $data;
	}

}