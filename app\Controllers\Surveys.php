<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Surveys extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SurveysModel');
        $controller = 'surveys';
	}

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        // if(!isset($data['logged_user'])){
            return redirect()->to('/');
        // }

        $SurveysQuestionsModel = model('SurveysQuestionsModel');
        // $data['all_surveys'] = $this->model->all_surveys();

        $data['survey'] = $SurveysQuestionsModel->single_survey(1);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Surveys of Lagree On Demand | Online Lagree Fitness Trainers';
		$data['current']['seo_description'] = 'Meet our online Lagree surveys who are serving up workouts on the Micro, Mini, and Megaformer. Our Lagree On Demand surveys can help you meet your fitness goals!';

		echo view('front/surveys/step1_view', $data);
    }

    public function step1()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }
        $SurveysQuestionsModel = model('SurveysQuestionsModel');
        // $data['all_surveys'] = $this->model->all_surveys();

        $data['survey'] = $SurveysQuestionsModel->single_survey(1);

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Surveys of Lagree On Demand | Online Lagree Fitness Trainers';
		$data['current']['seo_description'] = 'Meet our online Lagree surveys who are serving up workouts on the Micro, Mini, and Megaformer. Our Lagree On Demand surveys can help you meet your fitness goals!';

		echo view('front/surveys/step1_view', $data);
    }

    public function step2()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

		$data['current']['seo_title'] = 'Lagree On Demand Surveys';
		$data['current']['seo_description'] = 'Lagree On Demand Surveys';
		$data['current']['seo_keywords'] = 'Lagree On Demand Surveys';

        return view('front/surveys/step2_view', $data);
    }

    public function step3()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

		$data['current']['seo_title'] = 'Lagree On Demand Surveys';
		$data['current']['seo_description'] = 'Lagree On Demand Surveys';
		$data['current']['seo_keywords'] = 'Lagree On Demand Surveys';

        return view('front/surveys/step3_view', $data);
    }

    public function save()
    {
		$SurveysResultsModel = model('SurveysResultsModel');
		$SubscribersModel = model('SubscribersModel');
        $logged_user = $this->user;
		$request = service('request');
        $data = $this->request->getPost();
        $response['data'] = $data;
        $response['first_login'] = $logged_user['first_login'];
        $response['first_login'] = $logged_user['first_login'];
        $user_results = $SurveysResultsModel->where(['user_id' => $logged_user['id']])->where(['survey_id' => 1])->findAll();
        $response['success'] = FALSE;
        if(count($user_results) == 0){
            if(isset($data['answer']) AND is_array($data['answer'])){
                foreach($data['answer'] as $item => $value){
                    $qq = explode('-', $item);
                    $q = $qq[1];
                    $a = "";
                    if(strpos($q, '_') !== false){
                        $ee = explode('_', $q);
                        $a = $ee[0];
                        $q = $ee[1];
                    };
                    $save_data = [
                        'question_id' => $q,
                        'answer_id' => $a,
                        'answer' => $value,
                        'survey_id' => $data['survey_id'],
                        'user_id' => $data['user_id']
                    ];
                    $response['success'] = $SurveysResultsModel->save($save_data);
                };

                $response['user_saved'] = $SubscribersModel->save(['id' => $logged_user['id'], 'onboarding' => 1]);
                if($response['user_saved']){
                    $response['onboarding'] = 1;
                }
            };
        }else{
            $response['msg'] = 'Survey already finished';
        }

		return $this->respond($response);
    }

    public function save_survey()
    {
		$SurveysResultsModel = model('SurveysResultsModel');
		$SurveysFinishedModel = model('SurveysFinishedModel');
        $logged_user = $this->user;
		$request = service('request');
        $data = $this->request->getPost();
        $response['data'] = $data;

        // if(session('active_survey') == NULL){
        //     $response['login_required'] = 'Your session expired. Please log in again.';
        //     return $this->respond($response);
        // }
        $response['success'] = FALSE;
        if(isset($data['all_answers']) AND is_array($data['all_answers'])){
            foreach($data['all_answers'] as $question_id => $answer){
                // echo '<pre>';
                // if(is_array(json_decode($answer, TRUE))){
                //     print_r(json_decode($answer));
                // }else{
                //     print_r($answer);
                // };
                // echo '<br>';
                // echo '<br>';
                // die();
                
                if(is_array(json_decode($answer, TRUE))){
                    $a = json_decode($answer, TRUE);
                    foreach($a as $id => $row){
                        if(strpos($row, '//') !== FALSE){
                            $aa = explode('//', $row);
    
                            $save_data = [
                                'question_id' => $question_id,
                                'answer_id' => $aa[1],
                                'answer' => $aa[0],
                                'survey_id' => session('active_survey'),
                                'user_id' => session('user')
                            ];
                            $response['success'] = $SurveysResultsModel->save($save_data);
                        }
                    }
                }else{
                    $save_data = [
                        'question_id' => $question_id,
                        'answer_id' => 0,
                        'answer' => $answer,
                        'survey_id' => session('active_survey'),
                        'user_id' => session('user')
                    ];
                    $response['success'] = $SurveysResultsModel->save($save_data);
                }
            };
            $response['survey_finished'] = $SurveysFinishedModel->save(['user_id' => session('user'), 'survey_id' => session('active_survey')]);
            session()->set('finished_survey', 1);
        };

		return $this->respond($response);
    }

    public function save_user_video()
    {
		$SubscribersModel = model('SubscribersModel');
		$request = service('request');
        $data = $this->request->getPost();

        if($data['video'] == 'first_video'){
            $save_data = [
                'id' => $data['user_id'],
                'first_video' => 1
            ];
        }else{
            $save_data = [
                'id' => $data['user_id'],
                'second_video' => 1
            ];
        }
        $response['success'] = $SubscribersModel->save($save_data);

		return $this->respond($response);
    }

    public function cancel_survey()
    {
		$SurveysFinishedModel = model('SurveysFinishedModel');
		$request = service('request');
        $data = $this->request->getPost();

        $response['data'] = $data;
        if($data['user_id'] != 0 AND $data['survey_id'] != 0){
            $save_data = [
                'user_id' => $data['user_id'],
                'survey_id' => $data['survey_id']
            ];
            $response['success'] = $SurveysFinishedModel->save($save_data);
            if($response['success']){
                session()->set('finished_survey', TRUE);
            }
        }

		return $this->respond($response);
    }

    public function active_survey()
    {
		$SurveysModel = model('SurveysModel');
		$SurveysQuestionsModel = model('SurveysQuestionsModel');
		$survey = $SurveysModel->active();

        $response['success'] = FALSE;
        if($survey != NULL){
            $questions = $SurveysQuestionsModel->single_survey($survey['id']);
            // echo '<pre>';
            // print_r($questions);
            // die();
            $response['success'] = TRUE;
            $response['html'] = view('front/surveys/ajax-survey_view', $questions);
        }

		return $this->respond($response);
    }
}
