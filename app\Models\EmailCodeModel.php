<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class EmailCodeModel extends Model
{
    protected $table = 'email_codes';
	protected $allowedFields = ['code', 'email', 'date', 'code_used'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

	protected function prepare_data(array $data)
	{
		return $data;
	}
}