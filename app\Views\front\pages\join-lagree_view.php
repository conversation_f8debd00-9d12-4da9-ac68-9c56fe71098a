<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<style>
.custom-select.active {
	position: relative;
	z-index: 11111;
}
.custom-select {
    width: 100%;
    display: block;
}
.custom-selectbox ul li:before {
    display: none !important;
}
.custom-selectbox ul li {
    padding: 8px 0px 0px 10px !important;
}
.custom-selectbox ul {
	margin: 0;
    padding-top: 10px;
}
.custom-selectbox ul li {
	padding: 6px 10px 6px 20px !important;
	margin-bottom: 0;
    font-size: 14px;
}
.custom-selectbox ul li:hover {
	background: #fbfbfb;
}
.custom-select.error .custom-selectbox {
	border-color: red !important;
}
.graycheckbox, .checkbox-group .form-box {
	background: #fff;
}
.custom-selectbox ul {
    overflow-y: auto !important;
}
.error input {
	border-color: red !important;
}
.error .custom-selectbox {
	border-color: red !important;
}
</style>
</head>
<body class="contact-page logged">
<?php echo view('front/templates/header.php'); ?>
<main id="onboarding-body" class="joinlagree-body">
    <div class="black-banner joinbanner">
        <h1>JOIN LAGREE ON DEMAND</h1>
        <p>Whether you are a student or a trainer, enroll today and join Lagree On Demand platform.</p>
    </div>
    <section class="pt-0 pb-0">
        <div class="container">
            <div id="tab-outer">
                <ul id="tab-wrapper">
                    <li><a href="#tab1">JOIN AS STUDENT</a></li>
                    <li><a href="#tab2">JOIN AS TEACHER</a></li>
                </ul>
                <div id="tab-body">
                    <div id="tab1">
                        <div class="join-title">
                            <h4>JOIN LAGREE ON DEMAND AS STUDENT</h4>
                            <p class="light">Take the opportunity and seize the benefits of becoming a student or model. Get paid for the workout, and work with the instructor of your choice on a machine that you prefer.</p>
                        </div>
                        <div class="join-videowrap">
                            <img src="images/lod-student.jpg">
                            <a class="playbtn" href="https://youtu.be/W3ppKPIU2O4" data-fancybox><img src="images/playsingle-icon.svg"></a>
                        </div>

                        <div class="join-title">
                            <h4>STUDENT QUALIFICATIONS</h4>
                        </div>
                        <div class="join-qu">
                        <p>Must be local or in town, needs to be able to physically come in to the Lagree HQ to film (in Chatsworth, CA)</p>
                        <p>Must have taken Lagree classes previously</p>
                        <p>Must have good form and great endurance (we do our sets slow and long)</p>
                        </div>

                        <div class="join-title">
                            <h4>APPLICATION FORM - STUDENT</h4>
                        </div>
                        <form action="register/send_student_form" class="joinform" id="student-form">
                            <div class="input-container mb-0">
                                <label>FIRST NAME</label>
                                <input type="text" name="student_firstname" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>LAST NAME</label>
                                <input type="text" name="student_lastname" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>LOCATION (CITY)</label>
                                <input type="text" name="student_location" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>PHONE #</label>
                                <input type="text" name="student_phone" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>EMAIL ADDRESS</label>
                                <input type="email" name="student_email" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>LEVEL OF EXPERIENCE</label>
                                <div class="custom-select mr-0 mb-25">
                                    <div class="custom-selectbox-holder w100">
                                        <div class="custom-selectbox">
                                            <span class="select_val f-14">Select <span class="select_count"></span></span>
                                            <ul style="display: none;">
                                                <li data-val="beginner" class="">Beginner</li>
                                                <li data-val="intermediate" class="">Intermediate</li>
                                                <li data-val="advanced" class="">Advanced</li>
                                            </ul>
                                            <input type="hidden" name="experience" class="select-custom" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="input-container mb-0">
                                <label>DATE OF BIRTH</label>
                                <div class="birth-date">
                                    <div class="custom-select mr-0 w100 mr-2">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Month <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <li data-val="January" class="">January</li>
                                                    <li data-val="February" class="">February</li>
                                                    <li data-val="March" class="">March</li>
                                                    <li data-val="April" class="">April</li>
                                                    <li data-val="May" class="">May</li>
                                                    <li data-val="June" class="">June</li>
                                                    <li data-val="July" class="">July</li>
                                                    <li data-val="August" class="">August</li>
                                                    <li data-val="September" class="">September</li>
                                                    <li data-val="October" class="">October</li>
                                                    <li data-val="November" class="">November</li>
                                                    <li data-val="December" class="">December</li>
                                                </ul>
                                                <input type="hidden" name="student_month" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-select mr-0 w100 mr-2">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Day <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <?php for($i=1;$i<32;$i++){ ?>
                                                        <li data-val="<?php echo $i; ?>" class=""><?php echo $i; ?></li>
                                                    <?php } ?>
                                                </ul>
                                                <input type="hidden" name="student_day" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-select mr-0 w100">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Year <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <?php for($i=1950;$i<(date('Y') - 12);$i++){ ?>
                                                        <li data-val="<?php echo $i; ?>" class=""><?php echo $i; ?></li>
                                                    <?php } ?>
                                                </ul>
                                                <input type="hidden" name="student_year" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr class="mt-5">
                            <div class="row row-mob flex aic mt-3 w100 mx-0 pb-3 bottom-border">
                                <div class="col-6 mob-half line-height-normal f-12 pl-0">
                                    Are you human? &nbsp;&nbsp;&nbsp;
                                    <input type="text" name="s1" id="s1" value="" class="mb-0" style="width: 20px;padding: 0;border: 0;display: inline;pointer-events: none;" readonly>+&nbsp;<span class="s2 f-14"></span>&nbsp;=&nbsp;
                                </div>
                                <div class="col-6 mob-half pr-0">
                                    <input type="text" name="sum" required class="line-input border px-2 f-14 mb-0" placeholder="Enter" />
                                </div>
                            </div>
                            <div class="input-container mb-0">
                                <button type="submit" class="survey-btn">APPLY NOW</button>
                                <p class="completesurvey">Our team will contact you directly by email.</p>
                            </div>
                        </form>
                    </div>
                    <div id="tab2">
                        <div class="join-title">
                            <h4>JOIN LAGREE ON DEMAND AS TEACHER</h4>
                            <p class="light">Take the opportunity to become the Lagree On Demand teacher. Gain experience, get paid for it, and most importantly increase exposure to our Lagree On Demand subscribers.</p>
                        </div>
                        <div class="join-videowrap">
                            <img src="images/lod-teacher.jpg">
                            <a class="playbtn" href="https://youtu.be/sNYoBcAiZU0" data-fancybox><img src="images/playsingle-icon.svg"></a>
                        </div>

                        <div class="join-title">
                            <h4>TEACHER QUALIFICATIONS</h4>
                        </div>
                        <div class="join-qu">
                        <p>Must be local or in town, needs to be able to physically come in to the Lagree HQ to film (in Chatsworth, CA)</p>
                        <p>Must be at least Level 1 Lagree Certified</p>
                        </div>

                        <div class="join-title">
                            <h4>APPLICATION FORM - TEACHER</h4>
                        </div>
                        <form action="register/send_teacher_form" class="joinform" id="teacher-form">
                            <div class="input-container mb-0">
                                <label>FIRST NAME</label>
                                <input type="text" name="teacher_firstname" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>LAST NAME</label>
                                <input type="text" name="teacher_lastname" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>LOCATION (CITY)</label>
                                <input type="text" name="teacher_location" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>PHONE #</label>
                                <input type="text" name="teacher_phone" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>EMAIL ADDRESS</label>
                                <input type="email" name="teacher_email" placeholder="Enter">
                            </div>
                            <div class="input-container mb-0">
                                <label>CERTIFICATION LEVEL</label>
                                <div class="custom-select mr-0 mb-25">
                                    <div class="custom-selectbox-holder w100">
                                        <div class="custom-selectbox">
                                            <span class="select_val f-14">Select <span class="select_count"></span></span>
                                            <ul style="display: none;">
                                                <li data-val="Trainer - Level 1" class="">Trainer - Level 1</li>
                                                <li data-val="Advanced Trainer - Level 2" class="">Advanced Trainer - Level 2</li>
                                                <li data-val="Master Trainer - Level 3" class="">Master Trainer - Level 3</li>
                                            </ul>
                                            <input type="hidden" name="certification" class="select-custom" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="input-container mb-0">
                                <label>DATE OF BIRTH</label>
                                <div class="birth-date">
                                    <div class="custom-select mr-0 w100 mr-2">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Month <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <li data-val="January" class="">January</li>
                                                    <li data-val="February" class="">February</li>
                                                    <li data-val="March" class="">March</li>
                                                    <li data-val="April" class="">April</li>
                                                    <li data-val="May" class="">May</li>
                                                    <li data-val="June" class="">June</li>
                                                    <li data-val="July" class="">July</li>
                                                    <li data-val="August" class="">August</li>
                                                    <li data-val="September" class="">September</li>
                                                    <li data-val="October" class="">October</li>
                                                    <li data-val="November" class="">November</li>
                                                    <li data-val="December" class="">December</li>
                                                </ul>
                                                <input type="hidden" name="teacher_month" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-select mr-0 w100 mr-2">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Day <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <?php for($i=1;$i<32;$i++){ ?>
                                                        <li data-val="<?php echo $i; ?>" class=""><?php echo $i; ?></li>
                                                    <?php } ?>
                                                </ul>
                                                <input type="hidden" name="teacher_day" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-select mr-0 w100">
                                        <div class="custom-selectbox-holder w100">
                                            <div class="custom-selectbox">
                                                <span class="select_val">Year <span class="select_count"></span></span>
                                                <ul style="display: none;">
                                                    <?php for($i=1950;$i<(date('Y') - 12);$i++){ ?>
                                                        <li data-val="<?php echo $i; ?>" class=""><?php echo $i; ?></li>
                                                    <?php } ?>
                                                </ul>
                                                <input type="hidden" name="teacher_year" class="select-custom" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row row-mob flex aic mt-3 w100 mx-0 pb-3 bottom-border">
                                <div class="col-6 mob-half line-height-normal f-12 pl-0">
                                    Are you human? &nbsp;&nbsp;&nbsp;
                                    <input type="text" name="s1" id="s1" value="" class="mb-0" style="width: 20px;padding: 0;border: 0;display: inline;pointer-events: none;" readonly>+&nbsp;<span class="s2 f-14"></span>&nbsp;=&nbsp;
                                </div>
                                <div class="col-6 mob-half pr-0">
                                    <input type="text" name="sum" required class="line-input border px-2 f-14 mb-0" placeholder="Enter" />
                                </div>
                            </div>
                            <div class="input-container mb-0">
                                <button type="submit" class="survey-btn">APPLY NOW</button>
                                <p class="completesurvey">Our team will contact you directly by email.</p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css">
<script>
/* STUDENT FORM */
$('.joinform').on('submit', function(e){
	console.log('joinform-form submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');

    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Your application is sent', 'success', 1500, 1);
                button.removeClass('btn--loading');
                form.trigger('reset');
                setTimeout(function(){
                    window.location = '/join-lod-ty';
                }, 2000);
            }else{
                console.log('NO SUCCESS');
                if(data.message){
                    if(!data.json){
                        app_msg(data.message, 'danger', 2500, 1);
                    }
                }else{
                    app_msg("Please check all required fields", 'danger', 2500, 1);
                }
                if(data.json){
                    $.each(data.json, function(key, val){
                        form.find('[name=' + key + "]").closest('.input-container').addClass('error');
                    });
                    $('html, body').animate({ scrollTop: $('.input-container.error').eq(0).offset().top - 80});
                }
                button.removeClass('btn--loading');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger', 2500, 1);
            button.removeClass('btn--loading');
        }
    });
});
$('form input, form textarea').on('blur', function(){
    $(this).val() != "" ? $(this).closest('.input-container').removeClass('error') : $(this).closest('.input-container').addClass('error');
});
$('form .custom-selectbox ul li').on('click', function(){
    $(this).closest('.input-container').removeClass('error');
});
$(document).ready(function () {
    $('#tab-wrapper li:first').addClass('active');
    $('#tab-body > div').hide();
    $('#tab-body > div:first').show();
    $('#tab-wrapper a').click(function () {
        $('#tab-wrapper li').removeClass('active');
        $(this).parent().addClass('active');
        var activeTab = $(this).attr('href');
        $('#tab-body > div:visible').hide();
        $(activeTab).fadeIn();
        return false;
    });
});

$('.custom-selectbox').on('click', function(){
    var winH = $(window).height();
    var selectTop = $(this).offset().top + $(this).height() - $(window).scrollTop();

    if($(this).hasClass('opened')){
        $('.custom-selectbox').find('ul').slideUp(200);
        $('.custom-selectbox').removeClass('opened');
        setTimeout(function(){
            $('.custom-select').removeClass('active');
            $(this).closest('.custom-select').removeClass('open_top');
        }, 200);
    }else{
        if(selectTop > (winH-220)){
            $(this).closest('.custom-select').addClass('open_top');
        };
        $('.custom-selectbox').find('ul').slideUp(200);
        $('.custom-selectbox').removeClass('opened');
        $('.custom-select').removeClass('active');
        $(this).find('ul').slideDown(200);
        $(this).addClass('opened');
        $(this).closest('.custom-select').addClass('active');
    }
});
$('.custom-selectbox li').on('click', function(e){
    e.stopPropagation();
    var xx = $(this);
    var parent = $(this).closest('.custom-selectbox');
    var val = $(this).data("val");
    var text = $(this).text();

    parent.find('li').removeClass('checked');
    xx.addClass('checked');
    parent.find('input').val(val);
    parent.find('.select_val').html(text + ' <span class="select_count"></span>');

    $(this).closest('.custom-select').removeClass('open_top active');
    $('.custom-selectbox').find('ul').slideUp(200);
    $('.custom-selectbox').removeClass('opened');

    $(this).closest('.single_question').find('h5 span').remove();
    $(this).closest('.custom-select').removeClass('error');
});

</script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>
