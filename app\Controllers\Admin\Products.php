<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Products extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ProductsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_productss'] = $this->model->query("SELECT * FROM products_shopify WHERE deleted_at IS NULL ORDER BY title ASC")->getResultArray();
        $data['draft_productss'] = $this->model->query("SELECT * FROM products_shopify WHERE deleted_at IS NULL AND status = 1")->getResultArray();

        $data['productss_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Joined";
        $data['page'] = 1;

        echo view('admin/products/index_view', $data);
    }

    public function deleted($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $current = $this->model->query("SELECT * FROM products_shopify WHERE id = " . $edit_id . " AND deleted_at IS NULL")->getResultArray();
		$data['current'] = $current[0];

        echo view('admin/products/deleted_view', $data);
    }
    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function edit($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		// $data['current'] = $this->model->where(['id' => $edit_id])->first();
        $data['current'] = $this->model->query("SELECT * FROM products_shopify WHERE id = " . $edit_id . " AND deleted_at IS NULL")->getResultArray();
        // echo '<pre>';
        // print_r($data);
        // die();
        
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/products');
        }

		return view('admin/products/edit_view', $data);
    }
    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Slide successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/products', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/products/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/products/' . $name, 98);
				$data['image'] = 'uploads/products/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/products', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/products/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/products/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/products/' . $name;
			}

            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

            if($data['mob_cover_image_removed'] == 1){
                $data['mob_cover_image'] = "";
            }
            unset($data['mob_cover_image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function get_woo_product($id = 0)
    {
        $WooCommerceModel = model('WooCommerceModel');
        $response = $WooCommerceModel->single_product_api($id);

        return $this->respond($response);
    }
    public function sync_woo_products()
    {
        $WooCommerceModel = model('WooCommerceModel');
        $MachinesModel = model('MachinesModel');
        $AccessoriesModel = model('AccessoriesModel');

        $acc = $AccessoriesModel->findAll();
        $machines = $MachinesModel->findAll();
        foreach($acc as $single){
            if($single['woo_id'] == 0 OR $single['woo_id'] == '' OR $single['woo_id'] == NULL) continue;
            $sync_data = $WooCommerceModel->single_product_api($single['woo_id']);
            if(!isset($sync_data['name']) OR $sync_data['name'] == '') $sync_data['name'] = $single['title'];
            if(!isset($sync_data['permalink']) OR $sync_data['permalink'] == '') $sync_data['permalink'] = $single['url'];
            if(!isset($sync_data['images'][0]['src']) OR $sync_data['images'][0]['src'] == '') $sync_data['images'][0]['src'] = $single['image'];
            $save_data = [
                'id' => $single['id'],
                'woo_image' => $sync_data['images'][0]['src'],
                'woo_title' => $sync_data['name'],
                'woo_url' => $sync_data['permalink']
            ];
            $AccessoriesModel->save($save_data);
        }
        foreach($machines as $single){
            if($single['woo_id'] == 0 OR $single['woo_id'] == '' OR $single['woo_id'] == NULL) continue;
            $sync_data = $WooCommerceModel->single_product_api($single['woo_id']);
            if(!isset($sync_data['name']) OR $sync_data['name'] == '') $sync_data['name'] = $single['title'];
            if(!isset($sync_data['permalink']) OR $sync_data['permalink'] == '') $sync_data['permalink'] = $single['url'];
            if(!isset($sync_data['images'][0]['src']) OR $sync_data['images'][0]['src'] == '') $sync_data['images'][0]['src'] = $single['image'];
            $save_data = [
                'id' => $single['id'],
                'woo_image' => $sync_data['images'][0]['src'],
                'woo_title' => $sync_data['name'],
                'woo_url' => $sync_data['permalink']
            ];
            $MachinesModel->save($save_data);
        }        

        $response['success'] = TRUE;
        return $this->respond($response);
    }
    
}