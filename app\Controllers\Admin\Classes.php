<?php namespace App\Controllers\Admin;

require_once 'dompdf/autoload.inc.php';
use Dompdf\Dompdf;
use Dompdf\Options;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Classes extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ClassesModel');
        $this->streamApiKey = '4882cdc0-c0b4-49ba-a88a028ba266-6fca-4a7c';
        $this->libraryId = '347156';

        $db = \Config\Database::connect();
        $this->all_teachers_routines = $db->query('SELECT *, COALESCE(t.cnt,0) AS countRoutines, CONCAT(firstname, " ", lastname) as teacher_name, firstname
                            FROM teachers
                            LEFT JOIN (SELECT teacher_id, count(*) as cnt FROM routines WHERE deleted_at IS NULL GROUP BY teacher_id) t on t.teacher_id = teachers.id
                            HAVING countRoutines > 0
                            ORDER BY firstname ASC
                            ')->getResultArray();

	}

    public function index()
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['external'] = TRUE;
        }
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('/admin/routines');
        // }
        $data['machines'] = classes_existing_machines();
        $data['languages'] = classes_existing_languages();
        $data['difficulty'] = classes_existing_difficulty();
        $data['duration_less10'] = classes_existing_duration_less10();
        $data['duration_less25'] = classes_existing_duration_less25();
        $data['duration_more25'] = classes_existing_duration_more25();
        $data['body_parts'] = classes_existing_body_parts();
        $data['all_teachers'] = classes_existing_all_teachers();

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                $this->set_search('classes', '0');
                return redirect()->to('/admin/classes');
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== "0"){
            $search_term = session('classes_search');
        }else{
            $search_term = "0";
        };

        if(!empty($post)){
            $session->set('classes_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('classes_filter');
        }

        $data['page'] = 1;
        $page = 1;
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        
        if(isset($data['external'])){
            $data['all_classes'] = $this->model->filter_classes_no_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'));
            $data['classes_count'] = count($this->model->filter_classes_no_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        }else{
            $data['all_classes'] = $this->model->filter_classes_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'));
            $data['classes_count'] = count($this->model->filter_classes_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        }

        echo view('admin/classes/filter_view', $data);
    }

    public function scheduled_recordings($page = 1, $teacher_id = 0, $show_todays = 0)
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;
        $TeachersModel = model('TeachersModel');

        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['external'] = TRUE;
        }
        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('/admin/routines');
        // }
        $data['machines'] = classes_existing_machines();
        $data['languages'] = classes_existing_languages();
        $data['difficulty'] = classes_existing_difficulty();
        $data['duration_less10'] = classes_existing_duration_less10();
        $data['duration_less25'] = classes_existing_duration_less25();
        $data['duration_more25'] = classes_existing_duration_more25();
        $data['body_parts'] = classes_existing_body_parts();
        $data['all_teachers'] = classes_existing_all_teachers();
        $data['scheduled_recordings'] = TRUE;
        $data['show_todays'] = $show_todays;
        $data['current_teacher'] = $teacher_id > 0 ? $TeachersModel->where('id', $teacher_id)->first() : ['id' => 0];
        
        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                $this->set_search('classes', '0');
                return redirect()->to('/admin/classes');
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== "0"){
            $search_term = session('classes_search');
        }else{
            $search_term = "0";
        };

        if(!empty($post)){
            $session->set('classes_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('classes_filter');
        }

        $data['page'] = $page;
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        
        if(isset($data['external'])){
            $data['all_classes'] = $this->model->filter_scheduled_recordings_no_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'), $show_todays, $teacher_id);
            $data['classes_count'] = count($this->model->filter_scheduled_recordings_no_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')), $show_todays, $teacher_id);
        }else{
            $data['all_classes'] = $this->model->filter_scheduled_recordings_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'), $show_todays, $teacher_id);
            $data['classes_count'] = count($this->model->filter_scheduled_recordings_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter'), $show_todays, $teacher_id));
        }

        echo view('admin/classes/filter_view', $data);
    }

    public function clear_filter(){
        $session = \Config\Services::session();
        $session->set('classes_filter', '');
        return redirect()->to('/admin/classes');
    }

    public function pending($page = 1)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;

        $data['machines'] = classes_existing_machines();
        $data['languages'] = classes_existing_languages();
        $data['difficulty'] = classes_existing_difficulty();
        $data['duration_less10'] = classes_existing_duration_less10();
        $data['duration_less25'] = classes_existing_duration_less25();
        $data['duration_more25'] = classes_existing_duration_more25();
        $data['body_parts'] = classes_existing_body_parts();
        $data['all_teachers'] = classes_existing_all_teachers();

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                $this->set_search('classes', '0');
                return redirect()->to('/admin/classes/pending');
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== 0){
            $search_term = session('classes_search');
        }else{
            $search_term = 0;
        };
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        $data['all_classes'] = $this->model->all_pending(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term);
        $data['classes_count'] = count($this->model->all_pending());
        // $data['classes_count'] = $this->model->countAllResults();
        $data['page'] = 1;

        echo view('admin/classes/pending_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;

        $data['machines'] = classes_existing_machines();
        $data['languages'] = classes_existing_languages();
        $data['difficulty'] = classes_existing_difficulty();
        $data['duration_less10'] = classes_existing_duration_less10();
        $data['duration_less25'] = classes_existing_duration_less25();
        $data['duration_more25'] = classes_existing_duration_more25();
        $data['body_parts'] = classes_existing_body_parts();
        $data['all_teachers'] = classes_existing_all_teachers();
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['external'] = TRUE;
        }

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                $this->set_search('classes', '0');
                return redirect()->to('/admin/classes/page/' . $page);
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== 0){
            $search_term = session('classes_search');
        }else{
            $search_term = 0;
        };

        if(!empty($post)){
            // $this->set_filter($post);
            session('classes_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('classes_filter');
            // $session->set('classes_filter', '');
        }

        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));

        if(isset($data['external'])){
            $data['all_classes'] = $this->model->filter_classes_no_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'));
            $data['classes_count'] = count($this->model->filter_classes_no_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        }else{
            $data['all_classes'] = $this->model->filter_classes_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'));
            $data['classes_count'] = count($this->model->filter_classes_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        }

        // $data['all_classes'] = $this->model->filter_classes_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('classes_filter'));
        // $data['classes_count'] = count($this->model->filter_classes_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        $data['page'] = $page;

        echo view('admin/classes/filter_view', $data);
    }

    public function filter($page = 1)
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;

        $data['machines'] = classes_existing_machines();
        $data['languages'] = classes_existing_languages();
        $data['difficulty'] = classes_existing_difficulty();
        $data['duration_less10'] = classes_existing_duration_less10();
        $data['duration_less25'] = classes_existing_duration_less25();
        $data['duration_more25'] = classes_existing_duration_more25();
        $data['body_parts'] = classes_existing_body_parts();
        $data['all_teachers'] = classes_existing_all_teachers();

        // $data['order'] =  isset($post['order']) ? $post['order'] : session('classes_sort');
        // $data['search'] =  isset($post['search']) ? $post['search'] : '';

        // echo "<pre>";
        // print_r($post);
        // die();

        if(isset($post['search_term'])){
            $search_term = $post['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                $this->set_search('classes', '0');
                return redirect()->to('/admin/classes/filter/');
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== 0){
            $search_term = session('classes_search');
        }else{
            $search_term = 0;
        };

        $data['filter'] = $post;

        if(isset($post)){
            $session->set('classes_filter', $post);
        }

        // $search_term = isset($_GET['search_term']) ? $_GET['search_term'] : NULL;
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        $data['order'] =  session('classes_sort');
        $data['all_classes'] = $this->model->filter_classes_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $data['search_term'] == 0 ? NULL : $data['search_term'], $data['order'], $data['filter']);
        $data['classes_count'] = count($this->model->filter_classes_admin(0, 10000, $search_term, session('classes_sort'), session('classes_filter')));
        $data['page'] = $page;

        // echo '<pre>';
        // print_r($data);
        // echo '</pre>';
        echo view('admin/classes/filter_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');
        $audio_model = model('AudioModel');
        $exercises_model = model('ExercisesModel');
        $RoutinesModel = model('RoutinesModel');

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['external'] = TRUE;
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages ')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty ')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts ORDER BY title asc')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
		// $data['custom_accessories'] = $db->query('SELECT * FROM custom_accessories WHERE deleted_at IS NULL ORDER BY title asc')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs  ORDER BY sort ASC')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
		$data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $this->all_teachers_routines;
        $data['all_audio'] = $audio_model->all_audio(0, 0);
        $data['all_routines'] = $RoutinesModel->all_routines_dropdown();
		$current_machines = $db->query("SELECT * FROM classes_machine WHERE class_id = " . $edit_id)->getResultArray();
		$current_machines_ids = $db->query("SELECT GROUP_CONCAT(class_machine) as ids FROM classes_machine WHERE class_id = " . $edit_id)->getRowArray();
        $data['current_machines_ids'] = [];
        if(!empty($current_machines_ids['ids'])){
            $current_machines_ids['ids'] = explode(',', $current_machines_ids['ids']);
            $data['current_machines_ids'] = $current_machines_ids['ids'];
        }

        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['class_machine'] ;
            }
        }else{
            $data['current_machines'] = array();
        }
		$current_body_parts = $db->query("SELECT * FROM classes_body_parts WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['class_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = array();
        }
		// $current_custom_accessories = $db->query("SELECT * FROM classes_custom_accessories WHERE class_id = " . $edit_id)->getResultArray();
        // if(!empty($current_custom_accessories)){
        //     foreach($current_custom_accessories as $k => $single){
        //         $data['current_custom_accessories'][] = $single['class_custom_accessories'] ;
        //     }
        // }else{
        //     $data['current_custom_accessories'] = array();
        // }
		$current_accessories = $db->query("SELECT * FROM classes_accessories WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['class_accessories'] ;
            }
        }else{
            $data['current_accessories'] = array();
        }
		$current_tensions = $db->query("SELECT * FROM classes_tensions WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_tensions)){
            foreach($current_tensions as $k => $single){
                $data['current_tensions'][] = $single['class_tensions'];
            }
        }else{
            $data['current_tensions'] = array();
        }
		$current_springs = $db->query("SELECT * FROM classes_springs WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['class_springs'] ;
            }
        }else{
            $data['current_springs'] = array();
        }
        $current_tempo = $db->query("SELECT * FROM classes_tempo WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_tempo)){
            foreach($current_tempo as $k => $single){
                $data['current_tempo'][] = $single['class_tempo'] ;
            }
        }else{
            $data['current_tempo'] = array();
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/classes');
        };
        // $data['all_classes'] = $this->model->all_classes(0, 0, session('classes_search'), session('classes_sort'), "0, 1");
        $data['all_exercises'] = $exercises_model->load_more_exercises(0, 100, '');
        $data['all_exercises_count'] = $exercises_model->load_more_exercises_count(0, 0, '');
        $data['selected_exercises_for_selection'] = $this->model->exercises_for_single_class($edit_id);

        // $total = count($data['all_classes']);
        // $index_list = array_column($data['all_classes'], 'id');
        // $index_id = array_search($edit_id, array_column($data['all_classes'], 'id'));
        // if($index_id !== FALSE)
        // {
        //     if($index_id < $total - 1){
        //         $data['prev'] = $this->model->where('id', $index_list[$index_id + 1])->first();
        //     }else{
        //         $data['prev'] = $this->model->where('id', $index_list[0])->first();
        //     }
        //     if($index_id > 0){
        //         $data['next'] = $this->model->where('id', $index_list[$index_id - 1])->first();
        //     }else{
        //         $data['next'] = $this->model->where('id', $index_list[$total - 1])->first();
        //     }
        // }
        
        if($edit_id > 0){
            if(isset($data['external'])){
                $tmp = $this->model->query("SELECT id 
                            FROM classes 
                            WHERE deleted_at IS NULL 
                            AND teacher = " . $data['logged_user']['id'] . "
                            AND status = 1
                            AND type = 0
                            AND deleted_at IS NULL
                            ORDER BY id ASC
                        ")->getResultArray();

                if(count($tmp) > 1){                   
                    $total = count($tmp);
                    $index_list = array_column($tmp, 'id');
                    
                    $index_id = array_search($edit_id, $index_list);
                    if($index_id !== FALSE){
                        if($index_id < $total - 1){
                            $data['next'] = $this->model->where(['id' => $index_list[$index_id + 1]])->first();
                        }else{
                            $data['next'] = $this->model->where(['id' => $index_list[0]])->first();
                        }

                        if($index_id > 0){
                            $data['prev'] = $this->model->where(['id' => $index_list[$index_id - 1]])->first();
                        }else{
                            $data['prev'] = $this->model->where(['id' => $index_list[$total - 1]])->first();
                        }
                    }
                }

                // $data['next_prev'] = $this->model->query("SELECT *, 
                //                                                 (SELECT MAX(`id`) FROM `classes` WHERE teacher = " . $data['logged_user']['id'] . " AND id < `tmpclasses`.`id` AND deleted_at IS NULL) AS `prev_id`,
                //                                                 (SELECT MIN(`id`) FROM `classes` WHERE teacher = " . $data['logged_user']['id'] . " AND id > `tmpclasses`.`id` AND deleted_at IS NULL) AS `next_id`
                //                                                 FROM classes AS tmpclasses
                //                                                 WHERE id = $edit_id
                //                                                 AND status = 1
                //                                                 AND deleted_at IS NULL
                //                                             ")->getRowArray();
                // if($data['next_prev'] != NULL){
                //     $data['next'] = $this->model->where('id', $data['next_prev']['next_id'])->first();
                //     $data['prev'] = $this->model->where('id', $data['next_prev']['prev_id'])->first();
                // }
            }else{
                $data['next_prev'] = $this->model->query("SELECT *, 
                                                            (SELECT MAX(`id`) FROM `classes` WHERE id < `tmpclasses`.`id` AND deleted_at IS NULL) AS `prev_id`,
                                                            (SELECT MIN(`id`) FROM `classes` WHERE id > `tmpclasses`.`id` AND deleted_at IS NULL) AS `next_id`
                                                            FROM classes AS tmpclasses
                                                            WHERE id = $edit_id
                                                            AND deleted_at IS NULL
                                                        ")->getRowArray();
                if($data['next_prev'] != NULL){
                    $data['next'] = $this->model->where('id', $data['next_prev']['next_id'])->first();
                    $data['prev'] = $this->model->where('id', $data['next_prev']['prev_id'])->first();
                }
            }
        }

		return view('admin/classes/edit_view', $data);
    }

    public function load_more_exercises()
    {
        $exercises_model = model('ExercisesModel');
        $data_ajax = $this->request->getPost();

        $data['all_exercises'] = $exercises_model->load_more_exercises($data_ajax['start'], 100, $data_ajax['filter']);
		$data['springs'] = $exercises_model->query('SELECT * FROM springs ')->getResultArray();
        $response['success'] = FALSE;
        
        if ($data['all_exercises'] != NULL) {
            $response['all_exercises_count'] = $exercises_model->load_more_exercises_count(0, 0, $data_ajax['filter']);
            $response['html'] = view('admin/classes/ajax_load_exercises_view', $data);
            if ($response['html'] != "") {
                $response['count'] = count($data['all_exercises']);
                $response['success'] = TRUE;
                $response['no_more'] = FALSE;
            }else{
                $response['no_more'] = TRUE;
            }
        }else{
            $response['no_more'] = TRUE;
        }

        return $this->respond($response);
    }
    public function add_routine_to_class()
    {
        $RoutinesModel = model('RoutinesModel');
        $ClassesExercisesModel = model('ClassesExercisesModel');
        $data = $this->request->getPost();
        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();
        $response = [];

        if(isset($data['class_id']) AND $data['class_id'] > 0){
            $response['current_routine'] = $RoutinesModel->query("SELECT * FROM classes_selected_exercises WHERE class_id = " . $data['class_id'] . " ORDER BY sort ASC")->getResultArray();
            $counter = 0;
            if(isset($response['current_routine']) AND is_array($response['current_routine']) AND count($response['current_routine']) > 0){
                foreach($response['current_routine'] as $single_item){
                    $counter++;
                    $data_savee = [
                        'id' => $single_item['id'],
                        'sort' => $counter
                    ];
                    $response['sort_repack'] = $ClassesExercisesModel->save($data_savee);
                }
            }
        }

        if(isset($data['id']) AND $data['id'] > 0){
            $response['exercises'] = $RoutinesModel->exercises_for_routine($data['id']);
        }
        foreach($springs as $single_spring){
            if($single_spring['color'] != ''){
                $color_id[$single_spring['id']] = $single_spring['color'];
            }
        }
        if(count($response['exercises']) > 0){
            $c = $data['sort_count'];
            foreach($response['exercises'] as $single_exercise){
                // echo '<pre>';
                // print_r($single_exercise);
                // die();
                
                $c++;
                $data_save = [
                    'class_id' => $data['class_id'],
                    'class_selected_exercises' => $single_exercise['routine_selected_exercises'],
                    'orientation' => isset($single_exercise['orientation']) ? $single_exercise['orientation'] : '',
                    'duration' => $single_exercise['duration'],
                    'springs' => isset($single_exercise['springs']) ? $single_exercise['springs'] : '',
                    'springs_count' => isset($single_exercise['springs_count']) ? $single_exercise['springs_count'] : '',
                    'date' => date('Y-m-d'),
                    'sort' => $c,
                ];
                $response['success'][] = $ClassesExercisesModel->save($data_save);
                $response['errors'][] = $ClassesExercisesModel->errors();
                $id = $ClassesExercisesModel->getInsertID();
                
                $cc = [];
                if(isset($single_exercise['springs']) AND $single_exercise['springs'] != ''){
                    $colors = json_decode($single_exercise['springs'], true);
                    if(isset($single_exercise['springs_count']) AND $single_exercise['springs_count'] != ''){
                        $springs_count = json_decode($single_exercise['springs_count'], true);
                    }else{

                    }
                    foreach($colors as $key => $single_color){
                        $cc[] = '<span class="' . $color_id[$single_color] . '-bg">' . (($springs_count[$key] == '' OR $springs_count[$key] == 0) ? 1 : $springs_count[$key]) . '</span>';
                    }
                }
                $response['exercises_add'][] = [
                    'id' => $id,
                    'title' => $single_exercise['title'],
                    'class_id' => $data['class_id'],
                    'csc_id' => $single_exercise['id'],
                    'diff' => isset($single_exercise['diff']) ? $single_exercise['diff'] : '',
                    'all_class_machines_short' => isset($single_exercise['all_class_machines_short']) ? $single_exercise['all_class_machines_short'] : '',
                    'orientation' => isset($single_exercise['orientation']) ? $single_exercise['orientation'] : '',
                    'duration' => $single_exercise['duration'],
                    'date' => date('Y-m-d'),
                    'colors' => implode('', $cc),
                    'sort' => $c,
                    'transition' => isset($single_exercise['transition']) ? $single_exercise['transition'] : '',
                ];
            }
        }

        return $this->respond($response);
    }
    public function save_new_routine()
    {
        $RoutinesModel = model('RoutinesModel');
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
        $ClassesExercisesModel = model('ClassesExercisesModel');
        $data = $this->request->getPost();
        $response['success'] = FALSE;
        $response['data'] = $data;

        if(isset($data['title']) AND $data['title'] != ''){
            $routine_data = [
                'title' => $data['title'], 
                'slug' => slugify($data['title']), 
                'teacher_id' => session('admin'), 
                'status' => 0
            ];
            $response['routine_save'] = $RoutinesModel->save($routine_data);
            if($response['routine_save']){
                $response['success'] = TRUE;
            }
            $response['routine_errors'] = $RoutinesModel->errors();
            $response['inserted_id'] = $RoutinesModel->getInsertID();

            if(isset($data['csc_ids']) AND $data['csc_ids'] != ''){
                $csc_ids_new = json_decode($data['csc_ids'], TRUE);
                $csc_ids_new2 = str_replace('[', '', str_replace(']', '', json_encode($csc_ids_new)));
        
                $csc_data = $this->model->query("SELECT * FROM classes_selected_exercises WHERE id IN (" . $csc_ids_new2 . ")")->getResultArray();
                if(count($csc_data) > 0){
                    foreach($csc_data as $k => $single){
                        $save_data = [
                            'routine_id' => $response['inserted_id'],
                            'routine_selected_exercises' => $single['class_selected_exercises'],
                            'date' => $single['date'],
                            'orientation' => $single['orientation'],
                            'duration' => $single['duration'],
                            'springs' => $single['springs'],
                            'springs_count' => $single['springs_count'],
                            'sort' => $single['sort']
                        ];
                        $response['routine_exercises_save'][$k] = $RoutinesExercisesModel->save($save_data);
                        $response['routine_exercises_errors'][$k] = $RoutinesExercisesModel->errors();
                    }
                }
            }
        }


        return $this->respond($response);
    }
    public function edit_bulk()
    {
        $data = $this->request->getPost();
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

        // echo '<pre>';
        // print_r(explode(',', ($data['ids'])));
        // echo '</pre>';
        // die();
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
        $data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
        $data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
        $data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
        $data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL')->getResultArray();
        $data['springs'] = $db->query('SELECT * FROM springs  ORDER BY sort ASC')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
        $data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();

        $ids = explode(',', ($data['ids']));
        foreach($ids as $key => $id){
            $data['current_classes'][] = $this->model->where(['id' => $id])->first();
            $current_machines = $db->query("SELECT * FROM classes_machine WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_machines)){
                foreach($current_machines as $k => $single){
                    $data['current_machines'][$id][] = $single['class_machine'] ;
                }
            }else{
                $data['current_machines'][$id] = array();
            }
            $current_body_parts = $db->query("SELECT * FROM classes_body_parts WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_body_parts)){
                foreach($current_body_parts as $k => $single){
                    $data['current_body_parts'][$id][] = $single['class_body_parts'] ;
                }
            }else{
                $data['current_body_parts'][$id] = array();
            }
            $current_accessories = $db->query("SELECT * FROM classes_accessories WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_accessories)){
                foreach($current_accessories as $k => $single){
                    $data['current_accessories'][$id][] = $single['class_accessories'] ;
                }
            }else{
                $data['current_accessories'][$id] = array();
            }
            $current_tensions = $db->query("SELECT * FROM classes_tensions WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_tensions)){
                foreach($current_tensions as $k => $single){
                    $data['current_tensions'][$id][] = $single['class_tensions'] ;
                }
            }else{
                $data['current_tensions'][$id] = array();
            }
            $current_springs = $db->query("SELECT * FROM classes_springs WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_springs)){
                foreach($current_springs as $k => $single){
                    $data['current_springs'][$id][] = $single['class_springs'] ;
                }
            }else{
                $data['current_springs'][$id] = array();
            }
            $current_tempo = $db->query("SELECT * FROM classes_tempo WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_tempo)){
                foreach($current_tempo as $k => $single){
                    $data['current_tempo'][$id][] = $single['class_tempo'] ;
                }
            }else{
                $data['current_tempo'][$id] = array();
            }
        }

		// echo '<pre>';
		// print_r($data);
		// die();

		return view('admin/classes/multi_edit_view', $data);
    }

    public function multi()
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

		return view('admin/classes/multi_view', $data);
    }

    public function clear_search()
    {
        $this->set_search('classes', "0");
		return redirect('admin/classes');
    }

    public function save_batch()
    {
		$data = $this->request->getPost();
        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('classes');
                foreach($single_field as $k => $v){
                    $fields[$k][$key] = $v;
                }
            }
        }
        if (count($fields) > 0){
            $builder->insertBatch($fields);
        }
		return redirect('admin/classes');
    }

    public function reject_class()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $SubscribersModel = model('SubscribersModel');
        $NotificationsModel = model('NotificationsModel');
        $ClassesModel = model('ClassesModel');
		$request = service('request');
        $data = $request->getPost();

        $save_class = array('id' => $data['class_id'], 'status' => 3, 'reason' => $data['reason']);
        $response['success'] = $ClassesModel->save($save_class);

        $teacher = $TeachersModel->where(["id" => $data['teacher_id']])->first();
        $user = $SubscribersModel->where(["email" => $teacher['email']])->first();

        if(!empty($teacher)){
            $subject = 'Your LOD video is rejected';
            $data_template = [
                'class_title' => $data['class_title'],
                'class_date' => $data['class_date'],
                'description' => $data['reason'],
            ];
            $template = 'front/email_templates/rejected-video';
            $to = $teacher['email'];
            $response['reason_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);

            $notification_data = array(
                'content'   => 'Your class <span class="text-underline">' . $data['class_title'] . '</span> is rejected.<br>Reason: ' . $data['reason'],
                'author'    => 'system',
                'subscriber_id' => $user['id'],
                'type' => 'class_rejected_notif',
                'date'    => date('Y-m-d H:i:s')
            );
            $response['notification_saved'] = $NotificationsModel->save($notification_data);

        }

		return $this->respond($response);
    }

    public function save()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $SubscribersModel = model('SubscribersModel');
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$data = $this->request->getPost();
		$validation->reset();
        if($data['status'] == 1){
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
                // 'machine'       => 'required',
                // 'difficulty'    => 'required',
                // 'body_parts'    => 'required',
            ];
        }else{
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
                'video'         => 'required',
                // 'machine'       => 'required',
                'language'      => 'required',
                // 'difficulty'    => 'required',
                'teacher'       => 'required',
            ];
        }
        $response['rules'] = $rules;
        $validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
                $file->move(ROOTPATH . 'public/uploads/classes', $name);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	->resize(750, 410, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/' . $name, 90);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	->resize(510, 300, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/thumb_' . $name, 100);

                $data['image'] = 'uploads/classes/' . $name;
                // $data['image_small'] = 'uploads/classes/thumb_' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if(isset($data['image_removed']) AND $data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);
            
            if(isset($data['published_at']) AND $data['status'] == 0){

            }else{
                unset($data['published_at']);
            }

            if(isset($data['id']) AND $data['id'] > 0){
                $old_data = $this->model->find($data['id']);
                if(strpos($old_data['title'], 'Scheduled recording - ') !== false){
                    $data['scheduled_title'] = $old_data['title'];
                }
            }
			$response['validation'] = $validation->getErrors();
			$response['success'] = $this->model->save($data);
            $response['errors'] = $this->model->errors();
            $response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            !isset($data['tensions']) ? $data['tensions'] = [] : '';
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['tempo']) ? $data['tempo'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('classes_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $teacher = $TeachersModel->where(["id" => $data['teacher']])->first();
                $user = $SubscribersModel->where(["email" => $teacher['email']])->first();
                if($data['prev_status'] == 2){
                    $subject = 'Your LOD video is approved';
                    $data_template = [
                        'class_title' => $data['title'],
                        // 'class_date' => $data['created_at']
                    ];
                    $template = 'front/email_templates/approved-video';
                    $to = $teacher['email'];
                    $response['teacher_approved_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);

                    $notification_data = array(
                        'content'   => 'Your class <span class="text-underline">' . $data['title'] . '</span> is approved.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'subscriber_id' => $user['id'],
                        'type' => 'class_approved_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_saved'] = $NotificationsModel->save($notification_data);

                    $notification_teacher_data = array(
                        'content'   => '<span class="text-underline">' . $data['title'] . '</span> OD class is uploaded.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'type' => 'new_od_class_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_teacher_saved'] = $NotificationsModel->save($notification_teacher_data);
                }else{
                    $notification_class_data = array(
                        'content'   => '<span class="text-underline">' . $data['title'] . '</span> class is uploaded.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'type' => 'new_class_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_class_saved'] = $NotificationsModel->save($notification_class_data);
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function email_routine($class_id = 0)
    {
        if(isset($class_id) AND $class_id > 0){
            $email_model = model('EmailModel');
            $CalendarModel = model('CalendarModel');
            $TeachersModel = model('TeachersModel');
            $ModelsModel = model('ModelsModel');

            $data = $CalendarModel->where(['class_id' => $class_id])->first();
            
            $teacher_info = $TeachersModel->where(["id" => $data['teacher_id']])->first();
            $model_info = $ModelsModel->where(["id" => $data['model_id']])->first();
            
            $cc['single'] = class_info($data['class_id']);
            $cc['single']['exercises'] = exercises_for_class($data['class_id']);
            
            if (!file_exists('./class_pdf')) {
                mkdir('./class_pdf', 0777, true);
            }
            $tmp_pdf = view('front/email_templates/teacher_edited_class_pdf.php', $cc);
            
            // DomPDF
            $options = new Options();
            $options->setIsRemoteEnabled(true);
            $dompdf = new Dompdf($options);
            // $dompdf->isHtml5ParserEnabled(true);
            $dompdf->loadHtml($tmp_pdf, 'UTF-8');
            $dompdf->setPaper('A4', 'portrait');
            // Render the HTML as PDF
            $dompdf->render();
            // Output the generated PDF to file on server
            $output = $dompdf->output();

            file_put_contents('./class_pdf/class-updated-' . date('m-d-Y') . '-' . $teacher_info['firstname'] . '.pdf', $output);
            $attachment = $output;
            $attachment_filename = 'class-updated-' . date('m-d-Y') . '-' . $teacher_info['firstname'] . '.pdf';

            // EMAIL WITH PDF FILE
            $subject = 'Scheduled LOD Class edited by ' . $teacher_info['firstname'] . ' ' . $teacher_info['lastname'];
            $data_template = [
                'subject' => $subject,
                'teacher_name' => $teacher_info['firstname'] . ' ' . $teacher_info['lastname'],
            ];
            $template = 'front/email_templates/teacher-saved';
            $model_email = $model_info != NULL ? ', ' . $model_info['email'] : '';
            $to = '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, ' . $teacher_info['email'] . $model_email;
            // $to = '<EMAIL>';
            $response['teacher_edited_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template, NULL, $attachment, $attachment_filename);
            $response['success'] = TRUE;
        }else{
            $response['success'] = FALSE;
        }

        return $this->respond($response);
    }
    public function pdf_export($class_id = 0, $to_file = 0)
    {
        if(isset($class_id) AND $class_id > 0){
            $email_model = model('EmailModel');
            $CalendarModel = model('CalendarModel');
            $TeachersModel = model('TeachersModel');
            $ModelsModel = model('ModelsModel');

            $data = $CalendarModel->where(['class_id' => $class_id])->first();
            
            $teacher_info = $TeachersModel->where(["id" => $data['teacher_id']])->first();
            $model_info = $ModelsModel->where(["id" => $data['model_id']])->first();
            
            $cc['single'] = class_info($data['class_id']);
            $cc['single']['exercises'] = exercises_for_class($data['class_id']);
            
            if (!file_exists('./class_pdf')) {
                mkdir('./class_pdf', 0777, true);
            }
            $tmp_pdf = view('front/email_templates/teacher_edited_class_pdf.php', $cc);
            
            // DomPDF
            $options = new Options();
            $options->setIsRemoteEnabled(true);
            $dompdf = new Dompdf($options);
            // $dompdf->isHtml5ParserEnabled(true);
            $dompdf->loadHtml($tmp_pdf, 'UTF-8');
            $dompdf->setPaper('A4', 'portrait');
            // Render the HTML as PDF
            $dompdf->render();
            // Output the generated PDF to file on server
            $output = $dompdf->output();

            file_put_contents('./class_pdf/class-updated-' . date('m-d-Y') . '-' . $teacher_info['firstname'] . '.pdf', $output);
            $attachment = $output;
            $attachment_filename = 'class-updated-' . date('m-d-Y') . '-' . $teacher_info['firstname'] . '.pdf';

            if($to_file == 1){
                // Output the generated PDF to Browser
                $dompdf->stream('LagreeOD routine - ' . $cc['single']['title'] . '.pdf');
                // Output the generated PDF to file on server
                // $output = $dompdf->output();
                // file_put_contents('./routine_pdf/routine_' . $routine['slug'] . '.pdf', $output);
            }else{
                // Output the generated PDF to file on server
                $output = $dompdf->output();
                file_put_contents('./invoices/routine_' . $cc['single']['slug'] . '.pdf', $output);
            }

            // EMAIL WITH PDF FILE
            // $subject = 'Scheduled LOD Class edited by ' . $teacher_info['firstname'] . ' ' . $teacher_info['lastname'];
            // $data_template = [
            //     'subject' => $subject,
            //     'teacher_name' => $teacher_info['firstname'] . ' ' . $teacher_info['lastname'],
            // ];
            // $template = 'front/email_templates/teacher-saved';
            // $model_email = $model_info != NULL ? ', ' . $model_info['email'] : '';
            // $to = $teacher_info['email'];
            // $to = '<EMAIL>, <EMAIL>, <EMAIL>, ' . $teacher_info['email'] . $model_email;
            // $to = '<EMAIL>';
            // $to = '<EMAIL>, <EMAIL>';
            // $response['teacher_edited_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template, NULL, $attachment, $attachment_filename);
            $response['success'] = TRUE;
        }else{
            $response['success'] = FALSE;
        }

        return $this->respond($response);
    }
    public function save_exercises_in_class()
    {
        $ClassesExercisesModel = model('ClassesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        // echo '<pre>';
		// var_dump($data['class_selected_exercises']);
		// die();

        $result['success'] = $ClassesExercisesModel->save($data);
        $result['csc_id'] = $ClassesExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['class_selected_exercises']);
            $duration = $ClassesExercisesModel->where(['id' => $result['csc_id']])->first();
            if((isset($data['duration']) AND $data['duration'] != 0 AND $data['duration'] != NULL AND $data['duration'] != '') OR (isset($duration['duration']) AND $duration['duration'] != 0 AND $duration['duration'] != "")){
                $result['class']['duration'] = $data['duration'];
            }
            if(isset($data['orientation']) AND $data['orientation'] != 0 AND $data['orientation'] != NULL AND $data['orientation'] != ''){
                $result['class']['orientation'] = $data['orientation'];
            }
        }

		return $this->respond($result);
    }
    public function edit_class_exercise($id = 0)
    {
        $RoutinesExercisesModel = model('ClassesExercisesModel');

        if($id != 0){
            $result['success'] = TRUE;
            $result['csc'] = $RoutinesExercisesModel->where(['id' =>  $id])->first();
            $result['exercise'] = $this->get_exercises_info($result['csc']['class_selected_exercises']);
        }else{
            $result['success'] = FALSE;
        }

		return $this->respond($result);
    }

    public function sort_classes_table()
    {
        $ClassesExercisesModel = model('ClassesExercisesModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                // if($single['type'] == 'classes'){
				//     $PlaylistClassesModel->save($single);
                // }
                // if($single['type'] == 'videos'){
				//     $PlaylistHowtoModel->save($single);
                // }
                // if($single['type'] == 'exercises'){
				    $ClassesExercisesModel->save($single);
                // }
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }
    public function pdf($xx)
    {
        $cc['single'] = class_info($xx);
        $cc['single']['exercises'] = exercises_for_class($xx);
        // echo '<pre>';
        // print_r($cc);
        // die();
        
        $tmp_pdf = view('front/email_templates/teacher_edited_class_pdf.php', $cc);

        echo $tmp_pdf;
        
    }
    public function update_exercises_in_routine()
    {
        $ClassesExercisesModel = model('ClassesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();
        foreach($springs as $single_spring){
            if($single_spring['color'] != ''){
                $color_id[$single_spring['id']] = $single_spring['color'];
            }
        }
        $data['duration'] = (int)$data['duration'];

        $result['success'] = $ClassesExercisesModel->save($data);
        $result['csc_id'] = $ClassesExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['class_selected_exercises']);
            $result['class']['colors'] = '';
            if(isset($data['springs']) AND $data['springs'] != ''){
                $colors = json_decode($data['springs'], true);
                $springs_count = json_decode($data['springs_count'], true);
                $cc = [];
                foreach($colors as $key => $single_color){
                    $cc[] = '<span class="' . $color_id[$single_color] . '-bg">' . ((is_array($springs_count)) ? $springs_count[$key] : '') . '</span>';
                }
                if(is_array($springs_count) AND count($springs_count) > 0 AND $springs_count[0] != ''){
                    $result['class']['add_with_number'] = TRUE;
                }else{                    
                    $result['class']['add_with_number'] = FALSE;
                }
                $result['class']['colors'] = implode('', $cc);
            }    
        }

		return $this->respond($result);
    }
    public function update_transition_in_routine()
    {
        $ClassesExercisesModel = model('ClassesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $data['duration'] = (int)$data['duration'];

        $result['success'] = $ClassesExercisesModel->save($data);

		return $this->respond($result);
    }
    public function bunny_videos(){
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

        $data['bunny_videos'] = $this->bunny_videos_list();
        return view('admin/classes/bunny_videos_view', $data);
    }

    function bunny_videos_list(){
        require_once(ROOTPATH . 'vendor/autoload.php');

        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', 'https://video.bunnycdn.com/library/347156/videos?page=1&itemsPerPage=100&orderBy=date', [
            'headers' => [
                'AccessKey' => '4882cdc0-c0b4-49ba-a88a028ba266-6fca-4a7c',
                'accept' => 'application/json',
            ],
        ]);

        return json_decode((string) $response->getBody());
    }


    public function bunny_videos_upload(){
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if a file and title were submitted
            if (isset($_FILES['videoFile']) && isset($_POST['videoTitle'])) {
                $videoTitle = $_POST['videoTitle'];
                $videoFile = $_FILES['videoFile'];
    
                // Ensure the file was uploaded without errors
                if ($videoFile['error'] === UPLOAD_ERR_OK) {
                    $fileTmpPath = $videoFile['tmp_name'];
                    $fileName = $videoFile['name'];
    
                    // Step 1: Create a new video object in Bunny.net
                    $createVideoResponse = $this->createVideo($this->libraryId, $videoTitle, $this->streamApiKey);
    
                    if ($createVideoResponse && isset($createVideoResponse['guid'])) {
                        $videoId = $createVideoResponse['guid'];
    
                        // Step 2: Upload the video file to the created video object
                        $uploadSuccess = $this->uploadVideo($this->libraryId, $videoId, $fileTmpPath, $this->streamApiKey);
    
                        if ($uploadSuccess) {
                            echo "Video '$videoTitle' uploaded successfully!";

                            return redirect()->to(base_url('admin/classes/bunny_videos'));
                        } else {
                            echo "Failed to upload the video file.";
                        }
                    } else {
                        echo "Failed to create a video object in Bunny.net.";
                    }
                } else {
                    echo "Error uploading file: " . $videoFile['error'];
                }
            } else {
                echo "Please provide a video title and select a video file.";
            }
        } else {
            echo "Invalid request method.";
        }    
    }

    /**
     * Creates a new video object in Bunny.net.
     *
     * @param string $libraryId
     * @param string $videoTitle
     * @param string $streamApiKey
     * @return array|null
     */
    function createVideo($libraryId, $videoTitle, $streamApiKey) {
        $url = "https://video.bunnycdn.com/library/$libraryId/videos";
        $headers = [
            "AccessKey: $streamApiKey",
            "Accept: application/json",
            "Content-Type: application/json"
        ];
        $postData = json_encode(['title' => $videoTitle]);

        $client = new \GuzzleHttp\Client();

        $response = $client->request('POST', "https://video.bunnycdn.com/library/$libraryId/videos", [
            'headers' => [
                "AccessKey" => "$streamApiKey",
                "accept" => 'application/json',
                "content-type" => 'application/json',
            ],
            'body' => $postData
        ]);

        return json_decode((string) $response->getBody(), TRUE);
    }

    /**
     * Uploads the video file to the specified video object in Bunny.net.
     *
     * @param string $libraryId
     * @param string $videoId
     * @param string $filePath
     * @param string $streamApiKey
     * @return bool
     */
    function uploadVideo($libraryId, $videoId, $filePath, $streamApiKey) {
        $url = "https://video.bunnycdn.com/library/$libraryId/videos/$videoId";
        $headers = [
            "AccessKey: $streamApiKey",
            "Accept: application/json",
            "Content-Type: application/octet-stream"
        ];
    
        // Open the file in binary read mode
        $fileHandle = fopen($filePath, 'rb');
        if (!$fileHandle) {
            error_log("Failed to open file for reading: $filePath");
            return false;
        }
    
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_PUT, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_INFILE, $fileHandle);
        curl_setopt($ch, CURLOPT_INFILESIZE, filesize($filePath));
    
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        fclose($fileHandle);
        curl_close($ch);
    
        if ($httpCode === 200) {
            return true;
        } else {
            error_log("Failed to upload video. HTTP Status Code: $httpCode. Response: $response");
            return false;
        }
    }
}