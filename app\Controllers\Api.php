<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Api extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
    }

    public function index()
    {
        $data['message'] = 'Please enter a specific api request. Example: /api/classes/';
        return $this->respond($data);
    }

    public function classes()
    {
        $db = \Config\Database::connect();

        if(isset($_GET['id'])){
            $id = $_GET['id'];
        }
        if(isset($_GET['machine'])){
            $machine = $_GET['machine'];
        }
        if(isset($_GET['user'])){
            $user = $_GET['user'];
        }else{
            $user = 0;
        }
        if(isset($_GET['teacher'])){
            $teacher = $_GET['teacher'];
        }else{
            $teacher = 0;
        }
        if(isset($_GET['start'])){
            $start = $_GET['start'];
        }else{
            $start = 0;
        }
        if(isset($_GET['per_page'])){
            $per_page = $_GET['per_page'];
        }else{
            $per_page = 10;
        }

        $machines = (isset($machine)) ? 'AND classes_machine.class_machine = ' . $machine : '';

        if(isset($id) AND $id != 0){
            $response = $db->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, teachers.id  as teach_id, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . $user . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . $teacher . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . $user . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . $user . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            " . $machines . "
                            AND classes.id = '" . $id . "'
                            GROUP BY classes.id
                            ORDER BY title ASC
                    ")->getRowArray();
        }else{
            $response = $db->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, teachers.id  as teach_id, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . $user . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . $teacher . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . $user . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . $user . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            " . $machines . "
                            GROUP BY classes.id
                            ORDER BY created_at DESC
                            LIMIT " . $start . ", " . $per_page . "
                    ")->getResultArray();
        }

        return $this->respond($response);
    }

    public function teachers()
    {
		$TeachersModel = model('TeachersModel');
        $classes_model = model('ClassesModel');

        if(isset($_GET['id'])){
            $id = $_GET['id'];
            $identifier = "AND teachers.id = " . $id;
        }else if(isset($_GET['slug'])){
            $slug = $_GET['slug'];
            $identifier = "AND teachers.slug = '" . $slug . "'";
        }
        if(isset($_GET['user'])){
            $user = $_GET['user'];
        }else{
            $user = 0;
        }
        if(isset($_GET['teacher'])){
            $teacher = $_GET['teacher'];
        }else{
            $teacher = 0;
        }
        if(isset($_GET['start'])){
            $start = $_GET['start'];
        }else{
            $start = 0;
        }
        if(isset($_GET['per_page'])){
            $per_page = $_GET['per_page'];
        }else{
            $per_page = 10;
        }

        $limit = (isset($start) AND isset($per_page)) ? 'LIMIT ' . $start . ', ' . $per_page : '';

        if(isset($identifier)){
            $response = $TeachersModel->query("SELECT teachers.*, COALESCE(x.cnt,0) AS classesCount
                                FROM teachers
                                LEFT OUTER JOIN (SELECT teacher, count(*) as cnt FROM classes WHERE classes.deleted_at IS NULL AND classes.status = 0 GROUP BY teacher) x ON x.teacher = teachers.id
                                WHERE teachers.deleted_at IS NULL
                                " . $identifier . "
                            ")->getRowArray();
            $response['teacher_classes'] = $classes_model->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach, teachers.slug  as teach_slug, teachers.id  as teach_id, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . $user . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . $teacher . ", 1, 0) AS own,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            IF(subscribers_classes.subscriber_id = " . $user . ", 1, 0) AS purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND (subscribers_classes.purchase_type = 'buy' OR subscribers_classes.purchase_type IS NULL)
                                    ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                    ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . $user . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes.class_id = classes.id)
                            WHERE classes.deleted_at IS NULL
                            AND classes.status = 0
                            AND classes.teacher = '" . $response['id'] . "'
                            GROUP BY classes.id
                            ORDER BY title ASC
                            " . $limit . "
                        ")->getResultArray();
        }else{
            $response = $TeachersModel->all_teachers();
        }

        return $this->respond($response);
    }

    public function pages()
    {
        $db = \Config\Database::connect();

        if(isset($_GET['id'])){
            $id = $_GET['id'];
            $identifier = "AND id = " . $id;
        }else if(isset($_GET['slug'])){
            $slug = $_GET['slug'];
            $identifier = "AND slug = '" . $slug . "'";
        }else{
            $identifier = "";
        }

        // if(isset($slug) OR isset($id)){
            $response = $db->query("SELECT * FROM pages WHERE deleted_at IS NULL " . $identifier . "")->getResultArray();
        // }else{
        //     $response = [];
        // }

        return $this->respond($response);
    }

    public function collections()
    {
        $db = \Config\Database::connect();

        if(isset($_GET['id'])){
            $id = $_GET['id'];
            $identifier = " AND id = " . $id;
        }else if(isset($_GET['slug'])){
            $slug = $_GET['slug'];
            $identifier = " AND slug = '" . $slug . "'";
        }else{
            $identifier = "";
        }
        if(isset($_GET['start'])){
            $start = $_GET['start'];
        }
        if(isset($_GET['per_page'])){
            $per_page = $_GET['per_page'];
        }
        $limit = (isset($start) AND isset($per_page)) ? 'LIMIT ' . $start . ', ' . $per_page : '';

        if(isset($slug) OR isset($id)){
            $response = $db->query("SELECT collections.*, difficulty.title as diff,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM collections
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_classes, count(*) as cnt FROM collections_selected_classes WHERE collection_selected_classes != 0 GROUP BY collections_id) x ON x.collections_id = collections.id
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_howto, count(*) as cnty FROM collections_selected_howto WHERE collection_selected_howto != 0 GROUP BY collections_id) y ON y.collections_id = collections.id
                            LEFT JOIN difficulty on difficulty.id = collections.difficulty
                            WHERE collections.deleted_at IS NULL
                            " . $identifier . "
                            GROUP BY collections.id
                            HAVING classesCount > 0
                            ORDER BY title ASC
                        ")->getResultArray();
        }else{
            $response = $db->query("SELECT collections.*, difficulty.title as diff,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM collections
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_classes, count(*) as cnt FROM collections_selected_classes WHERE collection_selected_classes != 0 GROUP BY collections_id) x ON x.collections_id = collections.id
                            LEFT OUTER JOIN (SELECT collections_id, collection_selected_howto, count(*) as cnty FROM collections_selected_howto WHERE collection_selected_howto != 0 GROUP BY collections_id) y ON y.collections_id = collections.id
                            LEFT JOIN difficulty on difficulty.id = collections.difficulty
                            WHERE collections.deleted_at IS NULL
                            GROUP BY collections.id
                            HAVING classesCount > 0
                            ORDER BY title ASC
                            " . $limit . "
                        ")->getResultArray();
        }

        return $this->respond($response);
    }
}