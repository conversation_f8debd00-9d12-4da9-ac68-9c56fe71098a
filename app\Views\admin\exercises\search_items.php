<div class="search-item-count midGray f-12 py-25 bottom-border"><?php echo count($result); ?> exercises</div>
<?php 
foreach($result as $single){ 
?>
                        <div class="search-item py-25 bottom-border flex aic jcsb <?php echo $single['hidden_in_finder'] == 1 ? 'hidden_in_finder' : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                            <span class="video_preview_tooltip">
                                <video loop muted playsinline data-src="<?php echo $single['video_preview']; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') AND $_ENV['CI_ENVIRONMENT'] == 'production' ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '' AND $_ENV['CI_ENVIRONMENT'] == 'production') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                            </span>
                            <div class="f-12 flex flex-column title-area">
                                <a href="admin/exercises/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="f-12 link link-black black medium title_to_rename" onclick="save_referer($(this));return false;" title="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                <span class="link link-midGray midGray text-underline f-10" onclick="$('.exercise_old_title').text($(this).prev().text());$('#rename_id').val('<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>');$('#exercise_title').val('')" data-popup="rename-popup">Rename</span>
                            </div>
                            <?php if($single['hidden_in_finder'] == 0){ ?>
                                <span class="btn btn-xs f-10 px-1 py-05 ml-15 btn-border black hide_exercise" onclick="hide_exercise($(this))">HIDE</span>
                            <?php }else{ ?>
                                <span class="btn btn-xs f-10 px-1 py-05 ml-15 midGray gray-bg unhide_exercise" onclick="unhide_exercise($(this))">UNHIDE</span>
                            <?php } ?>
                        </div>
<?php } ?>