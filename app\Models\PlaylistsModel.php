<?php namespace App\Models;

use CodeIgniter\Model;

class PlaylistsModel extends Model
{
    protected $table = 'playlists';
	protected $allowedFields = ['parent_id', 'title', 'slug', 'image', 'content', 'author', 'user_id', 'private', 'teacher', 'seo_title', 'seo_keywords', 'seo_description', 'status', 'sort', 'notification_sent'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        'slug'        => 'required|alpha_dash|is_unique[playlists.slug,id,{id}]',
        // 'image'     => 'required',
        //'short_desc'     => 'required',
        // 'machine'     => 'required',
        // 'difficulty'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	// public function add_gallery($page_id = 0, $gallery = array())
	// {
	// 	$response['success'] = FALSE;
	// 	$db      = \Config\Database::connect();
	// 	$builder = $db->table('playlists_galleries');
	// 	$builder->delete(['page_id' => $page_id]);
	// 	if (count($gallery) > 0)
	// 	{
	// 		$builder->insertBatch($gallery);
	// 	}

	// 	/*
	// 	if (count($users) == 1)
	// 	{
	// 		$response['user_id'] = $users[0]['id'];
	// 		$response['success'] = TRUE;
	// 	}
	// 	else
	// 	{
	// 		$response['error'] = 'Bad username or password.';
	// 	}*/
	// 	return $response;
	// }

	// public function get_gallery($page_id = 0)
	// {
	// 	$response['success'] = FALSE;
	// 	$db      = \Config\Database::connect();
	// 	$builder = $db->table('playlists_galleries');
	// 	$response =  $builder->getWhere(['page_id' => $page_id])->getResultArray();
	// 	return $response;
	// }

    public function all_playlists($start = 0, $limit = 0, $search_term = NULL, $order = "sort asc, playlists.updated_at desc, status desc", $hasClasses = NULL, $private = NULL){
        $limit_size = ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search = ($search_term != NULL) ? "AND playlists.title LIKE '%$search_term%'" : "";
        $hide_zero_classes = ($hasClasses != NULL) ? "HAVING SUM(classesCount + howtoCount + exercisesCount) > 0" : "";
        $hide_private = ($private != NULL) ? "AND playlists.private != 1" : "";
        $data = $this->query("SELECT playlists.*, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_name, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as users_name, teachers.slug as teach_slug,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(z.cntz,0) AS exercisesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM playlists
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_classes, count(*) as cnt FROM playlists_selected_classes WHERE playlist_selected_classes != 0 GROUP BY playlists_id) x ON x.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_howto, count(*) as cnty FROM playlists_selected_howto WHERE playlist_selected_howto != 0 GROUP BY playlists_id) y ON y.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_exercises, count(*) as cntz FROM playlists_selected_exercises WHERE playlist_selected_exercises != 0 GROUP BY playlists_id) z ON z.playlists_id = playlists.id
                            LEFT JOIN teachers ON teachers.id = playlists.teacher
                            LEFT JOIN subscribers ON subscribers.id = playlists.user_id
                            WHERE playlists.deleted_at IS NULL
                            AND playlists.author = 1
                            " . $hide_private . "
                            " . $search . "
                            GROUP BY playlists.id
                            " . $hide_zero_classes . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();

        foreach($data as $key => $single){
            $all_playlists_classes = $this->classes_for_playlist($single['id']);
            $total_duration = 0;
            foreach($all_playlists_classes as $k => $v){
                $total_duration = $total_duration + (int)$v['duration'];
            }
            $data[$key]['total_duration'] = $total_duration;
        }

        return $data;

    }

    public function single_playlist($field = 'id', $value = NULL)
    {
        $data = [];
        if($value != NULL){
            $res = $this->query("SELECT playlists.*, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_name, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as users_name, teachers.slug as teach_slug,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(z.cntz,0) AS exercisesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM playlists
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_classes, count(*) as cnt FROM playlists_selected_classes WHERE playlist_selected_classes != 0 GROUP BY playlists_id) x ON x.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_howto, count(*) as cnty FROM playlists_selected_howto WHERE playlist_selected_howto != 0 GROUP BY playlists_id) y ON y.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_exercises, count(*) as cntz FROM playlists_selected_exercises WHERE playlist_selected_exercises != 0 GROUP BY playlists_id) z ON z.playlists_id = playlists.id
                            LEFT JOIN teachers ON teachers.id = playlists.teacher
                            LEFT JOIN subscribers ON subscribers.id = playlists.user_id
                            WHERE playlists.deleted_at IS NULL
                            AND playlists." . $field . " = '" . $value . "'
                        ")->getResultArray();
            if(count($res) > 0){
                $data = $res[0];
            }
        }
        // echo '<pre>';
        // print_r($data);
        // die();

        $data['all_playlists_classes'] = $this->classes_for_playlist($data['id']);
        $total_duration = 0;
        foreach($data['all_playlists_classes'] as $k => $v){
            $total_duration = $total_duration + (int)$v['duration'];
        }
        $data['total_duration'] = $total_duration;

        return $data;
    }

    public function get_my_playlists($user = NULL, $class_type_id = "")
    {
        $data = [];
        if($user != NULL){
            $data = $this->query("SELECT playlists.*, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_name, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as users_name, teachers.slug as teach_slug,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(z.cntz,0) AS exercisesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM playlists
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_classes, count(*) as cnt FROM playlists_selected_classes WHERE playlist_selected_classes != 0 GROUP BY playlists_id) x ON x.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_howto, count(*) as cnty FROM playlists_selected_howto WHERE playlist_selected_howto != 0 GROUP BY playlists_id) y ON y.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_exercises, count(*) as cntz FROM playlists_selected_exercises WHERE playlist_selected_exercises != 0 GROUP BY playlists_id) z ON z.playlists_id = playlists.id
                            LEFT JOIN teachers ON teachers.id = playlists.teacher
                            LEFT JOIN subscribers ON subscribers.id = playlists.user_id
                            WHERE playlists.deleted_at IS NULL
                            AND playlists.user_id = '" . $user . "'
                        ")->getResultArray();
        }
        // echo '<pre>';
        // print_r($data);
        // die();

        foreach($data as $key => $value){
            $data[$key]['all_playlists_classes'] = $this->classes_for_playlist($value['id']);
            $total_duration = 0;
            foreach($data[$key]['all_playlists_classes'] as $kk => $vv){
                $total_duration = $total_duration + (int)$vv['duration'];
            }
            $data[$key]['total_duration'] = $total_duration;
            $data[$key]['added'] = $this->single_playlist_added($value['id']);

        }

        // $total_duration = 0;
        if($class_type_id != ""){
            foreach($data as $k => $v){
                $c = explode('_', $class_type_id);
                if($c[0] == 'classes'){
                    $classes_in = $this->query("SELECT count(*) as class_in FROM playlists_selected_classes WHERE playlist_selected_classes = " . $c[1] . " AND playlists_id = " . $v['id'])->getResultArray();
                }else if($c[0] == 'videos'){
                    $classes_in = $this->query("SELECT count(*) as class_in FROM playlists_selected_howto WHERE playlist_selected_howto = " . $c[1] . " AND playlists_id = " . $v['id'])->getResultArray();
                }else if($c[0] == 'exercises'){
                    $classes_in = $this->query("SELECT count(*) as class_in FROM playlists_selected_exercises WHERE playlist_selected_exercises = " . $c[1] . " AND playlists_id = " . $v['id'])->getResultArray();
                }else{
                    $classes_in = array('class_in' => 0);
                }
                // print_r($classes_in['class_in']);
                // die();
                $data[$k]['classes_in'] = $classes_in[0]['class_in'];
            }
        }

        return $data;
    }

    public function get_all_my_playlists($user = NULL, $class_id = 0)
    {
        $data = [];
        if($user != NULL){
            $data = $this->query("
                            SELECT playlists.*, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_name, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as users_name, teachers.slug as teach_slug, 'my' AS ownership,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(z.cntz,0) AS exercisesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM playlists
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_classes, count(*) as cnt FROM playlists_selected_classes WHERE playlist_selected_classes != 0 GROUP BY playlists_id) x ON x.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_howto, count(*) as cnty FROM playlists_selected_howto WHERE playlist_selected_howto != 0 GROUP BY playlists_id) y ON y.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_exercises, count(*) as cntz FROM playlists_selected_exercises WHERE playlist_selected_exercises != 0 GROUP BY playlists_id) z ON z.playlists_id = playlists.id
                            LEFT JOIN teachers ON teachers.id = playlists.teacher
                            LEFT JOIN subscribers ON subscribers.id = playlists.user_id
                            WHERE playlists.deleted_at IS NULL
                            AND playlists.user_id = '" . $user . "'

                            UNION

                            SELECT playlists.*, CONCAT(teachers.firstname, ' ', teachers.lastname) as teach_name, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as users_name, teachers.slug as teach_slug, 'added' AS ownership,
                            COALESCE(x.cnt,0) AS classesCount,
                            COALESCE(z.cntz,0) AS exercisesCount,
                            COALESCE(y.cnty,0) AS howtoCount
                            FROM playlists
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_classes, count(*) as cnt FROM playlists_selected_classes WHERE playlist_selected_classes != 0 GROUP BY playlists_id) x ON x.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_howto, count(*) as cnty FROM playlists_selected_howto WHERE playlist_selected_howto != 0 GROUP BY playlists_id) y ON y.playlists_id = playlists.id
                            LEFT OUTER JOIN (SELECT playlists_id, playlist_selected_exercises, count(*) as cntz FROM playlists_selected_exercises WHERE playlist_selected_exercises != 0 GROUP BY playlists_id) z ON z.playlists_id = playlists.id
                            LEFT JOIN teachers ON teachers.id = playlists.teacher
                            LEFT JOIN subscribers ON subscribers.id = playlists.user_id
                            WHERE playlists.deleted_at IS NULL
                            AND playlists.id IN (
                                SELECT * FROM (
                                        SELECT playlist_id FROM subscribers_playlists WHERE user_id = " . $user . "
                                ) as added_playlists
                            )
                            ORDER BY title ASC
                        ")->getResultArray();
        }
        // echo '<pre>';
        // print_r($data);
        // die();

        foreach($data as $key => $value){
            $data[$key]['all_playlists_classes'] = $this->classes_for_playlist($value['id']);
            $total_duration = 0;
            foreach($data[$key]['all_playlists_classes'] as $kk => $vv){
                $total_duration = $total_duration + (int)$vv['duration'];
            }
            $data[$key]['total_duration'] = $total_duration;
            $data[$key]['added'] = $this->single_playlist_added($value['id']);

        }

        // $total_duration = 0;
        foreach($data as $k => $v){
            $classes_in = $this->query("SELECT count(*) as class_in FROM playlists_selected_classes WHERE playlist_selected_classes = " . $class_id . " AND playlists_id = " . $v['id'])->getResultArray();
            $videos_in = $this->query("SELECT count(*) as video_in FROM playlists_selected_howto WHERE playlist_selected_howto = " . $class_id . " AND playlists_id = " . $v['id'])->getResultArray();
            $exercises_in = $this->query("SELECT count(*) as exercise_in FROM playlists_selected_exercises WHERE playlist_selected_exercises = " . $class_id . " AND playlists_id = " . $v['id'])->getResultArray();
            // print_r($classes_in['class_in']);
            // die();
            $data[$k]['classes_in'] = $classes_in[0]['class_in'] + $videos_in[0]['video_in'] + $exercises_in[0]['exercise_in'];
        }

        // $data['total_duration'] = $total_duration;

        return $data;
    }

    public function single_playlist_added($id = NULL)
    {
        $data = FALSE;
        if($id != NULL){
            $res = $this->query("SELECT * FROM subscribers_playlists WHERE playlist_id = " . $id . " AND user_id = " . (NULL !== session('user') ? session('user') : 0) . "")->getResultArray();
            if(count($res) > 0){
                $data = TRUE;
            }
        }

        return $data;
    }

    public function current($slug = '')
    {
        $data = $this->query("SELECT playlists.*, difficulty.title as diff,
                                        FROM playlists
                                        WHERE playlists.deleted_at IS NULL
                                        AND playlists.slug = '" . $slug . "'
                                    ")->getRowArray();
        return $data;
    }

    public function classes_for_playlist($playlist_id = 0){
        $classes_model = model('ClassesModel');

        if($playlist_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT playlist_selected_classes AS id FROM playlists_selected_classes WHERE playlists_id = " . $playlist_id . "");
            $res = $query->getResultArray();

            $query1 = $db->query("SELECT playlist_selected_howto AS id FROM playlists_selected_howto WHERE playlists_id = " . $playlist_id . "");
            $res1 = $query1->getResultArray();

            $query2 = $db->query("SELECT playlist_selected_exercises AS id FROM playlists_selected_exercises WHERE playlists_id = " . $playlist_id . "");
            $res2 = $query2->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }

            $d = "";
            if(!empty($res1)){
                foreach($res1 as $single){ $d .= $single['id'] . ','; }
                $ids_videos = substr($d, 0, -1);
            }else{
                $ids_videos = 0;
            }

            $e = "";
            if(!empty($res2)){
                foreach($res2 as $single){ $e .= $single['id'] . ','; }
                $ids_exercises = substr($e, 0, -1);
            }else{
                $ids_exercises = 0;
            }

            $data = $classes_model->query("
                                            SELECT * FROM (
                                                (
                                                    SELECT classes.id, classes.title, classes.teacher, 'classes' as type, classes.slug, classes.image, classes.video, classes.video_thumb, classes.duration, classes.status, difficulty.title as diff,
                                                    COALESCE(x.cnt,0) AS countView,
                                                    COALESCE(y.rate,0) AS classRate,
                                                    CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach,
                                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_class_machines,
                                                    GROUP_CONCAT(DISTINCT LOWER(machines.short_name) SEPARATOR ' ') AS machines_classes,
                                                    teachers.slug AS teach_slug,
                                                    playlists_selected_classes.id AS csc_id,
                                                    playlists_selected_classes.sort AS sort, 'class_class' AS class, IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, 'classes' as slug_type,
                                                    IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                                                    IF(classes.id IN (
                                                        SELECT * FROM (
                                                                SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                        ) as subquery
                                                    ), 1, 0) as purchased,
                                                    IF(classes.id IN (
                                                            SELECT * FROM (
                                                                    SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                                            ) as subquery2
                                                    ), 1, 0) as watched
                                                    FROM classes
                                                    LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                                    LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                                    LEFT JOIN difficulty ON difficulty.id = classes.difficulty
                                                    LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                                                    LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                                                    LEFT JOIN machines ON machines.id = classes_machine.class_machine
                                                    LEFT JOIN teachers ON teachers.id = classes.teacher
                                                    LEFT JOIN playlists_selected_classes ON playlists_selected_classes.playlist_selected_classes = classes.id
                                                    WHERE classes.deleted_at IS NULL
                                                    AND classes.status = 0
                                                    AND classes.id IN (" . $ids . ")
                                                    GROUP BY classes.id
                                                )
                                                UNION
                                                (
                                                    SELECT howto.id, howto.title, howto.teacher, 'videos' as type, howto.slug, howto.image, howto.video, howto.video_thumb, howto.duration, howto.status, difficulty.title as diff,
                                                    COALESCE(x.cnt,0) AS countView,
                                                    COALESCE(y.rate,0) AS classRate,
                                                    CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach,
                                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_howto_machines,
                                                    GROUP_CONCAT(DISTINCT LOWER(machines.short_name) SEPARATOR ' ') AS machines_classes,
                                                    teachers.slug AS teach_slug,
                                                    playlists_selected_howto.id AS csc_id,
                                                    playlists_selected_howto.sort AS sort, 'howto_class' AS class, IF(subscribers_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, 'videos' as slug_type,
                                                    IF(howto.id IN (0), 1, 0) as watched,
                                                    0 as purchased,
                                                    0 as own
                                                    FROM howto
                                                    LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                                                    LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                                                    LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                                    LEFT JOIN subscribers_favs ON subscribers_favs.class_id = howto.id
                                                    LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                                    LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                                    LEFT JOIN teachers ON teachers.id = howto.teacher
                                                    LEFT JOIN playlists_selected_howto ON playlists_selected_howto.playlist_selected_howto = howto.id
                                                    WHERE howto.deleted_at IS NULL
                                                    AND howto.status = 0
                                                    AND howto.id IN (" . $ids_videos . ")
                                                    GROUP BY howto.id
                                                )
                                                UNION
                                                (
                                                    SELECT exercises.id, exercises.title, exercises.teacher, 'exercises' as type, exercises.slug, exercises.image, exercises.video, exercises.video_thumb, exercises.duration, exercises.status, difficulty.title as diff,
                                                    COALESCE(x.cnt,0) AS countView,
                                                    COALESCE(y.rate,0) AS classRate,
                                                    CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach,
                                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines,
                                                    GROUP_CONCAT(DISTINCT LOWER(machines.short_name) SEPARATOR ' ') AS machines_classes,
                                                    teachers.slug AS teach_slug,
                                                    playlists_selected_exercises.id AS csc_id,
                                                    playlists_selected_exercises.sort AS sort, 'exercises_class' AS class, IF(subscribers_exercises_favs.subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ", 1, 0) AS inFavs, 'exercises' as slug_type,
                                                    IF(exercises.id IN (0), 1, 0) as watched,
                                                    0 as purchased,
                                                    0 as own
                                                    FROM exercises
                                                    LEFT OUTER JOIN (SELECT exercise_id, count(*) AS cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                                    LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) AS rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                                                    LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                                    LEFT JOIN subscribers_exercises_favs ON subscribers_exercises_favs.exercise_id = exercises.id
                                                    LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                                    LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                                    LEFT JOIN teachers ON teachers.id = exercises.teacher
                                                    LEFT JOIN playlists_selected_exercises ON playlists_selected_exercises.playlist_selected_exercises = exercises.id
                                                    WHERE exercises.deleted_at IS NULL
                                                    AND exercises.status = 0
                                                    AND exercises.id IN (" . $ids_exercises . ")
                                                    GROUP BY exercises.id
                                                )
                                            ) AS i
                                            ORDER BY sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }
    public function howto_for_playlist($playlist_id = 0){
        $howto_model = model('HowtoModel');

        if($playlist_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT playlist_selected_howto AS id FROM playlists_selected_howto WHERE playlists_id = " . $playlist_id . "");
            $res = $query->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }
            $data = $howto_model->query("SELECT howto.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, CONCAT(teachers.firstname, ' ', teachers.lastname)  AS teach, teachers.slug as teach_slug, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_howto_machines, playlists_selected_howto.id as csc_id
                                            FROM howto
                                            LEFT OUTER JOIN (SELECT class_id, count(*) AS cnt FROM howto_views GROUP BY class_id) x on x.class_id = howto.id
                                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) AS rate FROM howto_rate GROUP BY class_id) y on y.class_id = howto.id
                                            LEFT JOIN difficulty ON difficulty.id = howto.difficulty
                                            LEFT JOIN howto_machine ON howto_machine.class_id = howto.id
                                            LEFT JOIN machines ON machines.id = howto_machine.class_machine
                                            LEFT JOIN teachers ON teachers.id = howto.teacher
                                            LEFT JOIN playlists_selected_howto ON playlists_selected_howto.playlist_selected_howto = howto.id
                                            WHERE howto.deleted_at IS NULL
                                            AND howto.status = 0
                                            AND howto.id IN (" . $ids . ")
                                            GROUP BY howto.id
                                            ORDER BY playlists_selected_howto.sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }
    public function exercises_for_playlist($playlist_id = 0){
        $exercises_model = model('ExercisesModel');

        if($playlist_id != 0){
            $db = \Config\Database::connect();
            $query = $db->query("SELECT playlist_selected_exercises AS id FROM playlists_selected_exercises WHERE playlists_id = " . $playlist_id . "");
            $res = $query->getResultArray();

            $c = "";
            if(!empty($res)){
                foreach($res as $single){ $c .= $single['id'] . ','; }
                $ids = substr($c, 0, -1);
            }else{
                $ids = 0;
            }
            $data = $exercises_model->query("SELECT exercises.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate, difficulty.title as diff, GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, playlists_selected_exercises.id as csc_id
                                            FROM exercises
                                            LEFT OUTER JOIN (SELECT exercise_id, count(*) AS cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                                            LEFT OUTER JOIN (SELECT exercise_id, AVG(rate) AS rate FROM exercises_rate GROUP BY exercise_id) y on y.exercise_id = exercises.id
                                            LEFT JOIN difficulty ON difficulty.id = exercises.difficulty
                                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                                            LEFT JOIN playlists_selected_exercises ON playlists_selected_exercises.playlist_selected_exercises = exercises.id
                                            WHERE exercises.deleted_at IS NULL
                                            AND exercises.status = 0
                                            AND exercises.id IN (" . $ids . ")
                                            GROUP BY exercises.id
                                            ORDER BY playlists_selected_exercises.sort asc
                                        ")->getResultArray();
        }else{
            $data = [];
        }
		return $data;
    }
    public function cron(){
        $data = $this->query("SELECT playlist.*, CONCAT(teachers.firstname, ' ', teachers.lastname) AS teach_name
                                FROM playlist
                                LEFT JOIN teachers ON teachers.id = classes.teacher
                                WHERE playlist.deleted_at IS NULL
                                AND playlist.notification_sent = 0
                                LIMIT 1
                            ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}
