<?php namespace App\Models;

use CodeIgniter\Model;

class ExercisesMachinesModel extends Model
{
    protected $table = 'exercises_machine';
	protected $allowedFields = ['exercise_id', 'user_id', 'date'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}