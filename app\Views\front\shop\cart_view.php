<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>

<?php echo view('front/templates/head.php'); ?>
<link rel="stylesheet" href="css/shop.css?v=<?php echo $_ENV['version']; ?>">

<style>
.flex_lr.txtimg {
	border-top: none;
	border-bottom: none;
	padding-top: 80px;
	margin-top: 90px;
	padding-bottom: 0;
	margin-bottom: 150px;
	position: relative;
}
.cart-table-footer *,
.cart-table-header * {
	line-height: 1 !important;
}
@media(max-width: 767px){
.pt-150 {
	padding-top: 50px !important;
}
.light.h2 + .f-18 {
	font-size: 14px !important;
	margin-top: 15px;
	line-height: 25px !important;
}
.mt-mob-05 {
	margin-top: 2px !important;
}
.cart-table-product-name > div {
	padding-right: 15px;
}
}
</style>
</head>
<body class="white-header">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="px-100 pb-0 bb pt-150" style="overflow-x: visible;">
        <div class="container1530">
            <div class="row">
                <div class="col-12 pl-0">
                    <h1 class="light mb-2 h2">CART</h1>
                    <p class="f-18 light">You will be redirected to Shop MaximumFitness to complete the payment.</p>
                </div>
            </div>
        </div>
    </section>
    <section class="px-100 py-0 bb" style="overflow-x: visible;">
        <div class="container1530">
            <div class="row">
                <div class="col-12 pt-150 pl-0 flex aic jcsb">
                    <div class="cart-table">
                        <div class="cart-table-header">
                            <div class="cart-table-product">PRODUCT</div>
                            <div class="cart-table-qty">QUANTITY</div>
                            <div class="cart-table-total">TOTAL</div>
                        </div>
                        <div class="cart-table-body">
                <?php
                $cart = json_decode(session('cart'), TRUE);
                $total = 0;
                foreach($cart['items'] as $key => $single){
                    $total += $single['price'] * $single['qty'];
                ?>
                            <div class="cart-table-single-product" data-variant="<?php echo $single['variant_id']; ?>" data-price="<?php echo $single['price']; ?>">
                                <div class="cart-table-product">
                                    <div class="cart-table-image"><img src="<?php echo $single['image']; ?>" alt="" class="img-fluid" /></div>
                                    <div class="cart-table-product-name">
                                        <div>
                                            <a href="/shop/<?php echo $single['product_id']; ?>" class="f-18 semibold mb-0 line-height-small <?php echo ($single['variant_title'] == 'Default Title') ? 'mb-2' : '' ?> black d-block"><?php echo $single['title']; ?></a>
                                            <?php if($single['variant_title'] != 'Default Title'){ ?>
                                                <p class="f-14 semibold mb-2 midGray d-block mb-mob-0"><?php echo $single['variant_title']; ?></p>
                                            <?php } ?>
                                        </div>
                                        <a href="javascript:;" class="link link-midGray f-14 midGray normal mt-mob-05" title="remove product" onclick="remove_from_cart(<?php echo $single['variant_id']; ?>)">Remove</a>
                                    </div>
                                </div>
                                <div class="cart-table-qty">
                                    <div class="counter">
                                        <span class="down" onclick='decreaseCount(event, this);refresh_cart()'>-</span>
                                        <input type="text" name="qty[<?php echo $key; ?>]" class="product-qty" value="<?php echo $single['qty']; ?>">
                                        <span class="up" onclick='increaseCount(event, this);refresh_cart()'>+</span>
                                    </div>
                                </div>
                                <div class="cart-table-total">$<span class="product_total"><?php echo $single['price'] * $single['qty']; ?></span></div>
                                <input type="hidden" name="variant_id[<?php echo $key; ?>]" value="">
                            </div>
                <?php } ?>
                        </div>
                        <div class="cart-table-footer">
                            <div class="cart-table-footer-left semibold">SUBTOTAL</div>
                            <div class="cart-table-footer-left semibold">$<span class="total_price semibold"><?php echo $total; ?></span></div>
                        </div>
                        <div class="cart-table-shipping light">
                            Taxes and shipping calculated at checkout
                        </div>
                        <div class="cart-table-grand-total semibold">
                            TOTAL: $<span class="total_price semibold"><?php echo $total; ?></span>
                        </div>
                        <div class="row py-100 flex aic">
                            <div class="col-8">
                                <p class="f-18 semibold cart-info">YOU WILL BE REDIRECTED TO SHOPMAXIMUMFITNESS TO COMPLETE THE PAYMENT.</p>
                            </div>
                            <div class="col-4">
                                <a class="btn black-bg white btn-tall w100 single_checkout_button" onclick="empty_cart();" href="https://maximum-fitness-la.myshopify.com/cart/0000000000000001:1" target="_blank">
                                    <img src="images/lock.svg" alt="" class="img-fluid" style="height: 11px; margin-right: 10px" />
                                    PROCEED TO SECURE CHECKOUT
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<!--end main-wrap-->
<?php echo view('front/templates/popups.php'); ?>
<?php echo view('front/templates/footer.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/swiper-bundle.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var grand_total = <?php echo $total; ?>;
function gtag_report_conversion() {
    gtag('event', 'conversion', {
            'send_to': 'AW-323276016/TQDJCNDBz4EYEPCZk5oB',
            'value': grand_total,
            'currency': 'USD'
        }
    );
    return false;
}
$(document).ready(function(){
    generate_checkout_url();
});
function increaseCount(a, b) {
    var input = b.previousElementSibling;
    var value = parseInt(input.value, 10);
    value = isNaN(value) ? 0 : value;
    value++;
    input.value = value;
}
function decreaseCount(a, b) {
    var input = b.nextElementSibling;
    var value = parseInt(input.value, 10);
    if (value > 1) {
        value = isNaN(value) ? 0 : value;
        value--;
        input.value = value;
    }
}
function remove_from_cart(variant_id) {
    console.log('REMOVE FROM CART');
    $.ajax({
        type: 'POST',
        url: 'shop/remove_from_cart',
        data: {
            variant_id
        },
        success: function (data) {
            console.log(data);
            console.log('Success');
            app_msg('Product removed from cart');
            if(!data.items){
                $('.cart-table-body').hide();
                window.location.reload();
            }else{
                $('[data-variant=' + variant_id + ']').remove();
                $('.cart_items_no').text(data.count);
                $('.total_price').text(data.total);
                grand_total = data.total;
            }
            refresh_cart();
            generate_checkout_url();
            // $('.cart-button').hide();
        },
        error: function (request, status, error) {
            console.log('Error');
            app_msg('Error');
        }
    });
}
function generate_checkout_url(){
    var main_url = "https://maximum-fitness-la.myshopify.com/cart/";

    $('[data-variant]').each(function(){
        var variant = $(this).data('variant');
        var qty = $(this).find('.product-qty').val();

        main_url = main_url + variant + ":" + qty + ",";
    });
    $('.single_checkout_button').attr('href', main_url);
}
function empty_cart() {
    console.log('EMPTY CART');
    $.ajax({
        type: 'POST',
        url: 'shop/empty_cart',
        data: "",
        success: function (data) {
            console.log(data);
            console.log('Success');
            gtag_report_conversion();
            window.location = '/shop';
            $('.cart-button').hide();
        },
        error: function (request, status, error) {
            console.log('Error');
            // app_msg('Error');
        }
    });
}
function refresh_cart(){
    console.log('REFRESH CART');
    var total = 0;
    var items = {};

    $('[data-variant]').each(function(index){
        var variant = $(this).data('variant');
        var price = $(this).data('price');
        var qty = $(this).find('.product-qty').val();
        row_total =  (price * qty);
        total = total + (price * qty);
        $('[data-variant="' + variant + '"]').find('.product_total').text(row_total);
        items[index] = {};
        items[index]['variant'] = variant;
        items[index]['qty'] = qty;
    });

    console.log(items);
    $('.total_price').text(total);
    generate_checkout_url();

    console.log('items: ' + JSON.stringify(items));

    $.ajax({
        type: 'POST',
        url: 'shop/update_cart',
        data: {
            items: JSON.stringify(items)
        },
        success: function (data) {
            console.log(data);
            console.log('Success');
            grand_total = total;
        },
        error: function (request, status, error) {
            console.log('Error');
        }
    });
}
</script>
</body>
</html>
