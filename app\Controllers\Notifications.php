<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Notifications extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('NotificationsModel');
    }

    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        if(!isset($data['logged_user'])){
            return redirect()->to('/');
        }

        $data['all_notifications'] = $this->model->all_my_notifications(0, 9);
        $data['nums'] = create_session_nums();

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Lagree On Demand - Notifications';
		$data['current']['seo_description'] = "Watch Mini, Micro and Mega notifications On Demand, experience the benefits of your practice at home or on the go, on any device.";
		echo view('front/notifications/index_view', $data);
    }

    public function mark_as_seen()
    {
        $subscribersWatched_model = model('SubscribersNotificationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        if(NULL !== session('user') AND session('user') != ''){
            $save_notification_seen = array('notification_id' => $data['id'], 'subscriber_id' => session('user'), 'date' => date('Y-m-d H:i:s'));

            $response['seen'] = $subscribersWatched_model->where(["notification_id" => $data['id'], "subscriber_id" => session('user')])->first();
            if(empty($response['seen'])){
                $response['success'] = $subscribersWatched_model->save($save_notification_seen);
                $response['new_id'] = $subscribersWatched_model->getInsertID();
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }


		return $this->respond($response);
    }

    public function mark_as_seen_hidden()
    {
        $subscribersWatched_model = model('SubscribersNotificationsModel');
		$request = service('request');
        $data = $request->getPost();
        $response['success'] = FALSE;

        if(NULL !== session('user') AND session('user') != ''){
            $save_notification_seen = array('notification_id' => $data['id'], 'subscriber_id' => session('user'), 'hide' => 1, 'date' => date('Y-m-d H:i:s'));

            $response['seen'] = $subscribersWatched_model->where(["notification_id" => $data['id'], "subscriber_id" => session('user')])->first();
            if(empty($response['seen'])){
                $response['success'] = $subscribersWatched_model->save($save_notification_seen);
            }else{
                $save_notification_seen['id'] = $response['seen']['id'];
                $response['success'] = $subscribersWatched_model->save($save_notification_seen);
            }
        }else{
            $response['msg'] = 'You must be logged in!';
        }
		return $this->respond($response);
    }

    public function get_users_notifications(){
		$request = service('request');
        $data = $request->getPost();
        $start = (isset($_POST['start']) ? ($_POST['start'] < 0 ? 0 : $_POST['start']) : 0);
        $limit = (isset($_POST['limit']) ? $_POST['limit'] : 5);
        $user = (NULL !== session('user') AND session('user') != '') ? $this->model->query("SELECT * FROM subscribers WHERE id = " . session('user') . "")->getResultArray() : NULL;

        $sql_add = " AND (notifications.type = 'welcome_od'";
        $sql_add .= " OR notifications.type = ''";
        $sql_add .= " OR notifications.type = 'new_conversation_notif'";
        $sql_add .= " OR notifications.type = 'comment_reply_notif'";
        $sql_add .= " OR notifications.type = 'from_admin'";
        if($user != NULL AND count($user) > 0){
            // $sql_add .= $user[0]['new_class_notif'] == 1 ? " OR notifications.type = 'new_class_notif'" : '';
            // $sql_add .= $user[0]['new_od_class_notif'] == 1 ? " OR notifications.type = 'new_od_class_notif'" : '';
            $sql_add .= $user[0]['new_collection_notif'] == 1 ? " OR notifications.type = 'new_collection_notif'" : '';
            $sql_add .= $user[0]['new_liveevents_notif'] == 1 ? " OR notifications.type = 'new_liveevents_notif'" : '';
            $sql_add .= $user[0]['new_teacher_notif'] == 1 ? " OR notifications.type = 'new_teacher_notif'" : '';
            $sql_add .= $user[0]['new_staff_playlist_notif'] == 1 ? " OR notifications.type = 'new_staff_playlist_notif'" : '';
            $sql_add .= $user[0]['class_approved_notif'] == 1 ? " OR notifications.type = 'class_approved_notif'" : '';
            $sql_add .= $user[0]['class_rejected_notif'] == 1 ? " OR notifications.type = 'class_rejected_notif'" : '';
            $sql_add .= $user[0]['class_bought_notif'] == 1 ? " OR notifications.type = 'class_bought_notif'" : '';
            $sql_add .= $user[0]['class_rated_notif'] == 1 ? " OR notifications.type = 'class_rated_notif'" : '';
            $sql_add .= $user[0]['payout_request_notif'] == 1 ? " OR notifications.type = 'payout_request_notif'" : '';
        }
        $sql_add .= ")";

        $response['notifications'] = $this->model->query("SELECT notifications.*,subscribers_notifications.hide AS hidden, DATE_FORMAT(notifications.date, '%Y-%m-%d %H:%i:%s'),
                                                                    IF(notifications.id IN (
                                                                        SELECT * FROM (
                                                                            SELECT subscribers_notifications.notification_id FROM subscribers_notifications WHERE subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . "
                                                                        ) as nesto
                                                                    ), 1, 0) as seen
                                                                    FROM notifications
                                                                    LEFT JOIN subscribers_notifications ON (subscribers_notifications.notification_id = notifications.id AND subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                                                    WHERE (notifications.subscriber_id IS NULL OR notifications.subscriber_id = 0 OR notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                                                    AND notifications.date < NOW()
                                                                    " . $sql_add . "
                                                                    AND (subscribers_notifications.hide = 0 OR subscribers_notifications.hide IS NULL)
                                                                    ORDER BY notifications.date desc
                                                                    LIMIT $start, $limit
                                                                ")->getResultArray();

        $response['html'] = view('front/notifications/ajax-notification-single', $response);
		$response['show_more'] = (count($response['notifications']) < $limit) ? FALSE : TRUE;

        return $this->respond($response);
    }
    public function check_new_notifications(){
        $user = (NULL !== session('user') AND session('user') != '') ? $this->model->query("SELECT * FROM subscribers WHERE id = " . session('user') . "")->getResultArray() : NULL;

        $sql_add = " AND (notifications.type = 'welcome_od'";
        $sql_add .= " OR notifications.type = ''";
        $sql_add .= " OR notifications.type = 'new_conversation_notif'";
        $sql_add .= " OR notifications.type = 'comment_reply_notif'";
        $sql_add .= " OR notifications.type = 'from_admin'";
        if($user != NULL AND count($user) > 0){
            // $sql_add .= $user[0]['new_class_notif'] == 1 ? " OR notifications.type = 'new_class_notif'" : '';
            // $sql_add .= $user[0]['new_od_class_notif'] == 1 ? " OR notifications.type = 'new_od_class_notif'" : '';
            $sql_add .= $user[0]['new_collection_notif'] == 1 ? " OR notifications.type = 'new_collection_notif'" : '';
            $sql_add .= $user[0]['new_liveevents_notif'] == 1 ? " OR notifications.type = 'new_liveevents_notif'" : '';
            $sql_add .= $user[0]['new_teacher_notif'] == 1 ? " OR notifications.type = 'new_teacher_notif'" : '';
            $sql_add .= $user[0]['new_staff_playlist_notif'] == 1 ? " OR notifications.type = 'new_staff_playlist_notif'" : '';
            $sql_add .= $user[0]['class_approved_notif'] == 1 ? " OR notifications.type = 'class_approved_notif'" : '';
            $sql_add .= $user[0]['class_rejected_notif'] == 1 ? " OR notifications.type = 'class_rejected_notif'" : '';
            $sql_add .= $user[0]['class_bought_notif'] == 1 ? " OR notifications.type = 'class_bought_notif'" : '';
            $sql_add .= $user[0]['class_rated_notif'] == 1 ? " OR notifications.type = 'class_rated_notif'" : '';
            $sql_add .= $user[0]['payout_request_notif'] == 1 ? " OR notifications.type = 'payout_request_notif'" : '';
        }
        $sql_add .= ")";

        $response['notifications'] = $this->model->query("SELECT notifications.*, subscribers_notifications.hide AS hidden,
                                                                    IF(notifications.id IN (
                                                                        SELECT * FROM (
                                                                            SELECT subscribers_notifications.notification_id FROM subscribers_notifications WHERE subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . "
                                                                        ) as nesto
                                                                    ), 1, 0) as seen
                                                                    FROM notifications
                                                                    LEFT JOIN subscribers_notifications ON (subscribers_notifications.notification_id = notifications.id AND subscribers_notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . ")
                                                                    WHERE
                                                                    (
                                                                        notifications.subscriber_id IS NULL OR
                                                                        notifications.subscriber_id = 0 OR
                                                                        notifications.subscriber_id = " . ((NULL !== session('user') AND session('user') != '') ? session('user') : 0) . "
                                                                    )
                                                                    AND notifications.date < NOW()
                                                                    " . $sql_add . "
                                                                    AND (subscribers_notifications.hide = 0 OR subscribers_notifications.hide IS NULL)
                                                                    ORDER BY notifications.date desc
                                                                    LIMIT 0, 5
                                                                ")->getResultArray();

        $c = 0;
        foreach($response['notifications'] as $single){
            if($single['seen'] == 0){
                $c++;
            }
        }
        $response['unseen'] = $c;

        return $this->respond($response);
    }

}