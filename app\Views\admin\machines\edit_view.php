<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content mb-100 pb-5">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Machine</h1>
                <a href="admin/machines" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-3">
        </div>
        <form action="admin/machines/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <!-- <h3 class="mb-3">Machine cover image</h3>
            <div class="cover_image_container flex aic">
                <div class="upload-image big-uplad-image image_size">
                    <input type="file" name="image" id="image">
                    <img src="<?php // echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php // echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                  <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 650px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php // echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5">
            <h3 class="mb-3">Machine badge</h3>
            <p class="midGray mb-5">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container" style="height: 205px !important">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php // echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php // echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                  <span>Max. file size is 1mb. Supported formats: PNG/JPG/SVG.<br>Desirable size: 640px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php // echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="my-5"> -->
            <div class="row">
                <div class="col-12">
                <h5 class="mb-4 mt-1 pt-05 f-14 semibold">MACHINE INFO</h5>
                    <h5 class="mb-1 f-11">MACHINE NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input f-14 normal black red" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                <h5 class="mb-1 f-11">SHORT NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="short_name" class="line-input f-14 normal black red" placeholder="Enter" value="<?php echo isset($current['short_name']) ? $current['short_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="mb-3">Long Name</h3>
                    <div class="input-container" id="title_container">
                        <input type="text" name="long_name" class="line-input f-3 normal black red" placeholder="Enter" value="<?php echo isset($current['long_name']) ? $current['long_name'] : '' ?>" />
                    </div>
                </div>
            </div> -->
            <!-- <div class="row mb-5">
                <div class="col-8">
                    <h3 class="flex aic mb-3">Description</h3>
                    <div class="input-container" id="title_container">
                        <textarea name="description" class="line-input" placeholder="Enter description"><?php echo isset($current['description']) ? $current['description'] : '' ?></textarea>
                    </div>
                </div>
            </div> -->
            <!-- <hr class="my-5">
            <div class="row mb-5">
                <div class="col-6">
                    <h3 class="mb-3">Shopify Link</h3>
                    <div class="input-container">
                        <input type="text" name="shopify_link" class="line-input" placeholder="Enter URL" value="<?php echo isset($current['shopify_link']) ? $current['shopify_link'] : '' ?>" />
                    </div>
                </div>
            </div> -->
            <!-- <div class="row mb-25">
                <div class="col-12">
                <h5 class="mb-1 f-11">SHOPIFY ID</h5>
                    <div class="input-container">
                        <input type="text" name="shopify_id" class="line-input" placeholder="Enter ID" value="<?php echo isset($current['shopify_id']) ? $current['shopify_id'] : '' ?>" />
                    </div>
                </div>
            </div> -->
            <div class="row mb-25">
                <div class="col-6">
                    <h5 class="mb-1 f-11">WOOCOMMERCE ID</h5>
                    <div class="input-container">
                        <input type="text" name="woo_id" class="line-input" placeholder="Enter ID" value="<?php echo isset($current['woo_id']) ? $current['woo_id'] : '' ?>" />
                        <span class="btn btn-xs black-bg white f-10 mt-2" onclick="reload_product($(this))">SYNC PRODUCT</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="flex aic gap-2">
                        <div class="woo-image">
                            <img src="<?php echo isset($current['woo_image']) ? $current['woo_image'] : '' ?>" alt="" class="img-fluid" style="width: 120px" />
                        </div>
                        <input type="hidden" name="woo_image" id="woo_image" value="<?php echo isset($current['woo_image']) ? $current['woo_image'] : '' ?>">
                        <div class="woo">
                            <h4 class="f-14 woo-title">
                                <?php echo isset($current['woo_title']) ? $current['woo_title'] : '' ?>
                            </h4>
                            <input type="hidden" name="woo_title" id="woo_title" value="<?php echo isset($current['woo_title']) ? $current['woo_title'] : '' ?>">
                            <p class="f-12 woo-url">
                                <?php echo isset($current['woo_url']) ? $current['woo_url'] : '' ?>
                            </p>
                            <input type="hidden" name="woo_url" id="woo_url" value="<?php echo isset($current['woo_url']) ? $current['woo_url'] : '' ?>">
                        </div>
                    </div>
                </div>
            </div>
            <!-- <hr class="my-5">
            <div class="row">
                <div class="col-6">
                    <h3 class="mb-6">Draft</h3>
                    <div class="checkbox mb-2">
                        <input type="checkbox"  id="status_check" <?php // echo $current['status'] == 1 ? 'checked' : '' ?> onchange="$(this).is(':checked') ? $('#status').val(1) : $('#status').val(0)">
                        <label for="status_check" class="f-16">Yes</label>
                        <input type="hidden" name="status" id="status" value="<?php // echo $current['status']; ?>">
                    </div>
                </div>
            </div> -->
            <hr class="mt-0 mb-5">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                            <a href="/admin/machines" class="cancel-link ml-2" title="Cancel">Cancel</a>
                            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="machines" data-popup="delete-popup" title="Cancel">DELETE MACHINE</a>
                        </div>
                    <?php }else{ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                            <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                            <a href="/admin/machines" class="cancel-link" title="Cancel">Cancel</a>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function reload_product(xx){
    var id = xx.prev().val();
    xx.addClass('btn--loading-small-white');
    $.ajax({
        type: 'POST',
        url: 'admin/products/get_woo_product/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                xx.removeClass('btn--loading-small-white');
                $('.woo-image img').attr('src', data.images[0].src);
                $('.woo-title').html(data.name);
                $('.woo-url').html(data.permalink);
                $('#woo_image').val(data.images[0].src);
                $('#woo_title').val(data.name);
                $('#woo_url').val(data.permalink);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/app.js?v=147"></script>
</body>
</html>