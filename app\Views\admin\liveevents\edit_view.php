<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Live Event</h1>
                <a href="admin/liveevents" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/liveevents/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom" id="main_form">
            <h5 class="mb-1 f-14 semibold">COVER PHOTO</h5>
            <p class="midGray mb-45 f-14">Select or upload a photo that shows what's in your live event.</p>
            <div class="image_container flex aic">
                <div class="upload-image big-uplad-image cover_image_size" id="image_container">
                    <input type="file" name="image" id="image">
                    <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 600px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="mt-5 mb-45">
            <h5 class="mb-1 f-14 semibold">COVER PHOTO (MOBILE)</h5>
            <p class="midGray mb-45 f-14">Select or upload a photo that shows what's in your collection.</p>
            <div class="mob_cover_image_container flex aic">
                <div class="upload-image big-uplad-image mob_cover_image_size" id="image_container">
                    <input type="file" name="mob_cover_image" id="mob_cover_image">
                    <img src="<?php echo empty($current['mob_cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['mob_cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['mob_cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['mob_cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 600px x 640px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['mob_cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <hr class="mt-5 mb-45">
            <div class="row mb-2">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">LIVE EVENT NAME</h5>
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input black make_slug" data-slug_target="#slug" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">PAGE URL</h5>
                    <h5 class="mb-1 f-11">URL *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="slug" id="slug" class="line-input" value="<?php echo isset($current['slug']) ? $current['slug'] : '' ?>" style="padding-left: 246px;">
                        <span class="base_url">www.lagreeod.com/liveevents/</span>
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">HERO TITLE</h5>
                    <h5 class="mb-1 f-11">TITLE</h5>
                    <div class="input-container" id="hero_title_container">
                        <input type="text" name="hero_title" class="line-input black" placeholder="Enter" value="<?php echo isset($current['hero_title']) ? $current['hero_title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">HERO SUBTITLE</h5>
                    <h5 class="mb-1 f-11">SUBTITLE</h5>
                    <div class="input-container" id="hero_subtitle_container">
                        <input type="text" name="hero_subtitle" class="line-input black" placeholder="Enter" value="<?php echo isset($current['hero_subtitle']) ? $current['hero_subtitle'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">DESCRIPTION</h5>
                    <h5 class="mb-1 f-11">DESCRIPTION *</h5>
                    <div class="input-container" id="content_container">
                        <textarea name="content" class="line-input" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">LOCATION</h5>
                    <h5 class="mb-1 f-11">LOCATION *</h5>
                    <div class="input-container" id="location_container">
                        <input type="text" class="line-input" placeholder="Enter" name="location" value="<?php echo isset($current['location']) ? $current['location'] : '' ?>" />
                    </div>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-45">EVENT TIME</h5>
                    <h5 class="mb-1 f-11">TIME *</h5>
                    <div class="input-container" id="location_container">
                        <input type="text" class="line-input" placeholder="Enter" name="duration" value="<?php echo isset($current['duration']) ? $current['duration'] : '' ?>" />
                    </div>
                </div>
            </div>
            <!-- <div class="row mb-5">
                <div class="col-6">
                    <h3 class="mb-3">Date & Time</h3>
                    <div class="row">
                        <div class="col-6">
                            <div class="input-container" id="date_container">
                                <input type="text" class="round-input datepicker" placeholder="Date"  name="date" value="<?php echo (isset($current['date']) AND $current['date'] != '') ? date('m/d/Y', strtotime($current['date'])) : ''; ?>" />
                                <span class="calendar-icon"></span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-container" id="time_container">
                                <select name="time" class="round-select white-bg w100">
                                    <option value="">Stream time</option>
                                    <option value="8:00AM" <?php echo (isset($current['time']) AND $current['time'] == '8:00AM') ? 'SELECTED' : ''; ?>>8:00AM</option>
                                    <option value="9:00AM" <?php echo (isset($current['time']) AND $current['time'] == '9:00AM') ? 'SELECTED' : ''; ?>>9:00AM</option>
                                    <option value="10:00AM" <?php echo (isset($current['time']) AND $current['time'] == '10:00AM') ? 'SELECTED' : ''; ?>>10:00AM</option>
                                    <option value="11:00AM" <?php echo (isset($current['time']) AND $current['time'] == '11:00AM') ? 'SELECTED' : ''; ?>>11:00AM</option>
                                    <option value="12:00PM" <?php echo (isset($current['time']) AND $current['time'] == '12:00PM') ? 'SELECTED' : ''; ?>>12:00PM</option>
                                    <option value="1:00PM" <?php echo (isset($current['time']) AND $current['time'] == '1:00PM') ? 'SELECTED' : ''; ?>>1:00PM</option>
                                    <option value="2:00PM" <?php echo (isset($current['time']) AND $current['time'] == '2:00PM') ? 'SELECTED' : ''; ?>>2:00PM</option>
                                    <option value="3:00PM" <?php echo (isset($current['time']) AND $current['time'] == '3:00PM') ? 'SELECTED' : ''; ?>>3:00PM</option>
                                    <option value="4:00PM" <?php echo (isset($current['time']) AND $current['time'] == '4:00PM') ? 'SELECTED' : ''; ?>>4:00PM</option>
                                    <option value="5:00PM" <?php echo (isset($current['time']) AND $current['time'] == '5:00PM') ? 'SELECTED' : ''; ?>>5:00PM</option>
                                    <option value="6:00PM" <?php echo (isset($current['time']) AND $current['time'] == '6:00PM') ? 'SELECTED' : ''; ?>>6:00PM</option>
                                    <option value="7:00PM" <?php echo (isset($current['time']) AND $current['time'] == '7:00PM') ? 'SELECTED' : ''; ?>>7:00PM</option>
                                    <option value="8:00PM" <?php echo (isset($current['time']) AND $current['time'] == '8:00PM') ? 'SELECTED' : ''; ?>>8:00PM</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="row mb-0">
                <div class="col-12 mt-05">
                    <h5 class="mb-4 f-14 semibold top-border pt-4">LIVE EVENT STREAM CODE</h5>
                    <h5 class="mb-1 f-11">STREAM CODE</h5>
                    <div class="input-container" id="stream_url_container">
                        <textarea class="line-input" placeholder="Enter" id="stream_url" name="stream_url"><?php echo (isset($current['stream_url']) AND $current['stream_url'] != '') ? $current['stream_url'] : ''; ?></textarea>
                    </div>
                </div>
            </div>
            <hr class="mt-25 mb-45">
            <div class="row mb-3 pb-05">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">MACHINE</h5>
                <?php
                $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
                foreach($machines as $single){
                ?>
                    <div class="checkbox mb-15" id="machine_container">
                        <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                    </div>
                <?php } ?>
                </div>
            </div>
            <hr class="mt-0 mb-45">
            <div class="row mb-3 pb-05">
                <div class="col-12">
                    <h5 class="mb-45 f-14 semibold">TEACHER</h5>
                    <div class="row w100">
                <?php
                $c=0;
                $teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0;
                foreach($all_teachers as $single){
                $c++;
                ?>
                        <div class="col-4 checkbox mb-1 pb-05" id="teacher_container">
                            <input type="radio" class="" name="teacher" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-14"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                        </div>
                <?php
                }
                ?>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-45">
            <h5 class="mb-45 f-14 semibold">GUEST HOST PHOTO</h5>
            <div class="cover_image_container flex aic mb-5">
                <div class="upload-image profile-photo" id="cover_image_container">
                    <input type="file" name="cover_image" id="cover_image">
                    <img src="<?php echo empty($current['cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['cover_image']) ? 1 : 1; ?>">
                </div>
                <div class="midGray f-12">
                <span>Max. file size is 1mb. Supported formats: PNG/JPG.<br>Desirable size: 300px x 300px.</span>
                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['cover_image']) ? 'hide' : ''; ?>">
                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image">Replace Current</a>
                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image">Remove</a>
                    </div>

                </div>
            </div>
            <div class="row mb-25">
                <div class="col-5 mt-05">
                    <h5 class="mb-45 f-14 semibold top-border pt-5 lh-small">GUEST NAME</h5>
                    <div class="input-container" id="guest_container">
                    <h5 class="mb-1 f-11">NAME</h5>
                        <input type="text" class="line-input" placeholder="Enter" name="guest_name" value="<?php echo isset($current['guest_name']) ? $current['guest_name'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-45">
            <div class="row mb-3 pb-05">
                <div class="col-6">
                    <h5 class="mb-45 f-14 semibold">DIFFICULTY</h5>
<?php
$current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
foreach($difficulty as $single){
?>
                    <div class="checkbox mb-15" id="difficulty_container">
                        <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                    </div>
<?php } ?>
                </div>
            </div>
            <hr class="mt-0 mb-45">
            <div class="row mb-3 pb-05">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">BODY PARTS</h5>
                </div>
                <div class="col-12 flex">
                    <div class="mr-150">
<?php
$c=0;
$curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '' AND is_array($current_body_parts) AND count($current_body_parts) > 0) ? $current_body_parts : array();
foreach($body_parts as $single){
$c++;
?>
                        <div class="checkbox mb-15" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                            <label for="body_parts<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                        </div>
<?php
if($c == 8){
    echo '</div><div class="mr-150">';
}
}
?>

                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-5">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="cover_image_removed" id="cover_image_removed" value="0">
                    <input type="hidden" name="mob_cover_image_removed" id="mob_cover_image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                            <a href="/admin/liveevents" class="cancel-link ml-2" title="Cancel">Cancel</a>
                            <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="liveevents" data-popup="delete-popup" title="Cancel">DELETE EVENT</a>
                        </div>
                    <?php }else{ ?>
                        <div class="default-buttons flex aic w100">
                            <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                            <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                            <a href="/admin/liveevents" class="cancel-link" title="Cancel">Cancel</a>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>
</main>

<!-- CLASS TEMPLATE -->
<div id="class-template" style="display: none">
    <div class="col-12 single-selected-class" data-id="0">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
                <span class="btn btn-sm red-bg white remove-class remove_class" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()">×</span>
            </div>
            <div class="single-class-rest">
                <div class="single-class-title">Inner thighs & Oblique</div>
                <div class="single-class-desc">Microformer, 30 minutes,<br> Difficulty: 2, <br>by: Sebastien Lagree</div>
            </div>
        </div>
    </div>
</div>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>