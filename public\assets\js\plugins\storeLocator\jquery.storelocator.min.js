/*! jQuery Google Maps Store Locator - v2.0.2 - 2014-12-07
* http://www.bjornblog.com/web/jquery-store-locator-plugin
* Copyright (c) 2014 Bjorn Holine; Licensed MIT */

!function(a,b,c,d){"use strict";function e(b,c){g=a(b),this.element=b,this.settings=a.extend({},D,c),this._defaults=D,this._name=f,this.init()}var f="storeLocator";if("undefined"==typeof a.fn[f]){var g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v=[],w=[],x=[],y=[],z={},A={},B={},C={},D={mapID:"bh-sl-map",locationList:"bh-sl-loc-list",formContainer:"bh-sl-form-container",formID:"bh-sl-user-location",addressID:"bh-sl-address",regionID:"bh-sl-region",mapSettings:{zoom:12,mapTypeId:google.maps.MapTypeId.ROADMAP},markerImg:null,markerDim:null,catMarkers:null,lengthUnit:"m",storeLimit:26,distanceAlert:60,dataType:"xml",dataLocation:"data/locations.xml",xmlElement:"marker",listColor1:"#ffffff",listColor2:"#eeeeee",originMarker:!1,originMarkerImg:null,originMarkerDim:null,bounceMarker:!0,slideMap:!0,modal:!1,overlay:"bh-sl-overlay",modalWindow:"bh-sl-modal-window",modalContent:"bh-sl-modal-content",closeIcon:"bh-sl-close-icon",defaultLoc:!1,defaultLat:null,defaultLng:null,autoGeocode:!1,maxDistance:!1,maxDistanceID:"bh-sl-maxdistance",fullMapStart:!1,noForm:!1,loading:!1,loadingContainer:"bh-sl-loading",featuredLocations:!1,pagination:!1,locationsPerPage:10,inlineDirections:!1,nameSearch:!1,searchID:"bh-sl-search",nameAttribute:"name",infowindowTemplatePath:"assets/js/plugins/storeLocator/templates/infowindow-description.html",listTemplatePath:"assets/js/plugins/storeLocator/templates/location-list-description.html",KMLinfowindowTemplatePath:"assets/js/plugins/storeLocator/templates/kml-infowindow-description.html",KMLlistTemplatePath:"assets/js/plugins/storeLocator/templates/kml-location-list-description.html",listTemplateID:null,infowindowTemplateID:null,taxonomyFilters:null,taxonomyFiltersContainer:"bh-sl-filters-container",querystringParams:!1,callbackNotify:null,callbackBeforeSend:null,callbackSuccess:null,callbackModalOpen:null,callbackModalReady:null,callbackModalClose:null,callbackJsonp:null,callbackPageChange:null,callbackDirectionsRequest:null,callbackCloseDirections:null,callbackNoResults:null,geocodeErrorAlert:"Geocode was not successful for the following reason: ",addressErrorAlert:"Unable to find address",autoGeocodeErrorAlert:"Automatic location detection failed. Please fill in your address or zip code.",distanceErrorAlert:"Unfortunately, our closest location is more than ",mileLang:"mile",milesLang:"miles",kilometerLang:"kilometer",kilometersLang:"kilometers",noResultsTitle:"No results",noResultsDesc:"No locations were found with the given criteria. Please modify your selections or input.",nextPage:"Next &raquo;",prevPage:"&laquo; Prev"};a.extend(e.prototype,{init:function(){B.EarthRadius="km"===this.settings.lengthUnit?6367:3956,j="kml"===this.settings.dataType?"xml":this.settings.dataType,this.settings.inlineDirections===!0&&(t=new google.maps.DirectionsRenderer,u=new google.maps.DirectionsService,a("."+this.settings.locationList).prepend('<div class="bh-sl-directions-panel"></div>')),m=this.settings.mapSettings.zoom,Handlebars.registerHelper("niceURL",function(a){return a?a.replace("https://","").replace("http://",""):void 0}),null!==this.settings.taxonomyFilters&&this.taxonomyFiltering(),this.settings.modal===!0&&(null!==this.settings.taxonomyFilters&&a("."+this.settings.taxonomyFiltersContainer).clone(!0,!0).prependTo(g),g.wrap('<div class="'+this.settings.overlay+'"><div class="'+this.settings.modalWindow+'"><div class="'+this.settings.modalContent+'">'),a("."+this.settings.modalWindow).prepend('<div class="'+this.settings.closeIcon+'"></div>'),a("."+this.settings.overlay).hide()),this._loadTemplates()},destroy:function(){this.reset();var b=a("#"+this.settings.mapID);if(y.length)for(var d=0;d<=y.length;d++)google.maps.event.removeListener(y[d]);a("."+this.settings.locationList+" ul").empty(),b.hasClass("bh-sl-map-open")&&b.empty().removeClass("bh-sl-map-open"),this.settings.modal===!0&&a(". "+this.settings.overlay).remove(),b.attr("style",""),g.hide(),a.removeData(g.get(0)),a(c).off(f),g.unbind()},reset:function(){w=[],v=[],x=[],y=[],a(c).off("click."+f,"."+this.settings.locationList+" li")},notify:function(a){this.settings.callbackNotify?this.settings.callbackNotify.call(this,a):alert(a)},geoCodeCalcToRadian:function(a){return a*(Math.PI/180)},geoCodeCalcDiffRadian:function(a,b){return this.geoCodeCalcToRadian(b)-this.geoCodeCalcToRadian(a)},geoCodeCalcCalcDistance:function(a,b,c,d,e){return 2*e*Math.asin(Math.min(1,Math.sqrt(Math.pow(Math.sin(this.geoCodeCalcDiffRadian(a,c)/2),2)+Math.cos(this.geoCodeCalcToRadian(a))*Math.cos(this.geoCodeCalcToRadian(c))*Math.pow(Math.sin(this.geoCodeCalcDiffRadian(b,d)/2),2))))},getQueryString:function(a){if(a){a=a.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var b=new RegExp("[\\?&]"+a+"=([^&#]*)"),c=b.exec(location.search);return null===c?"":decodeURIComponent(c[1].replace(/\+/g," "))}},_loadTemplates:function(){var b,c=this,d='<div class="bh-sl-error">Error: Could not load plugin templates. Check the paths and ensure they have been uploaded. Paths will be wrong if you do not run this from a web server.</div>';"kml"===this.settings.dataType&&null===this.settings.listTemplateID&&null===this.settings.infowindowTemplateID?a.when(a.get(this.settings.KMLinfowindowTemplatePath,function(a){b=a,i=Handlebars.compile(b)}),a.get(this.settings.KMLlistTemplatePath,function(a){b=a,h=Handlebars.compile(b)})).then(function(){c.locator()},function(){throw a("."+c.settings.formContainer).append(d),new Error("Could not load storeLocator plugin templates")}):null!==this.settings.listTemplateID&&null!==this.settings.infowindowTemplateID?(i=Handlebars.compile(a("#"+this.settings.infowindowTemplateID).html()),h=Handlebars.compile(a("#"+this.settings.listTemplateID).html()),c.locator()):a.when(a.get(this.settings.infowindowTemplatePath,function(a){b=a,i=Handlebars.compile(b)}),a.get(this.settings.listTemplatePath,function(a){b=a,h=Handlebars.compile(b)})).then(function(){c.locator()},function(){throw a("."+c.settings.formContainer).append(d),new Error("Could not load storeLocator plugin templates")})},locator:function(){this.settings.slideMap===!0&&g.hide(),this._start(),this._formEventHandler()},_formEventHandler:function(){var b=this;this.settings.noForm===!0?(a(c).on("click."+f,"."+this.settings.formContainer+" button",function(a){b.processForm(a)}),a(c).on("keyup."+f,function(c){13===c.keyCode&&a("#"+b.settings.addressID).is(":focus")&&b.processForm(c)})):a(c).on("submit."+f,"#"+this.settings.formID,function(a){b.processForm(a)})},_getData:function(b,c,d){var e=this,f=a.Deferred();return this.settings.callbackBeforeSend&&this.settings.callbackBeforeSend.call(this,b,c,d),this.settings.loading===!0&&a("."+this.settings.formContainer).append('<div class="'+this.settings.loadingContainer+'"></div>'),a.ajax({type:"GET",url:this.settings.dataLocation+("jsonp"===this.settings.dataType?(this.settings.dataLocation.match(/\?/)?"&":"?")+"callback=?":""),data:{origLat:b,origLng:c,origAddress:d},dataType:j,jsonpCallback:"jsonp"===this.settings.dataType?this.settings.callbackJsonp:null}).done(function(b){f.resolve(b),e.settings.loading===!0&&a("."+e.settings.formContainer+" ."+e.settings.loadingContainer).remove()}).fail(f.reject),f.promise()},_start:function(){var a=this;if(this.settings.defaultLoc===!0){var b=new this.reverseGoogleGeocode,c=new google.maps.LatLng(this.settings.defaultLat,this.settings.defaultLng);b.geocode({latLng:c},function(b){if(null!==b){var c=b.address;C.lat=a.settings.defaultLat,C.lng=a.settings.defaultLng,C.origin=c,a.mapping(C)}else a.notify(a.settings.addressErrorAlert)})}this.settings.fullMapStart===!0&&(this.settings.querystringParams===!0&&this.getQueryString(this.settings.addressID)||this.getQueryString(this.settings.searchID)?this.processForm(null):this.mapping(null)),this.settings.autoGeocode===!0&&navigator.geolocation&&navigator.geolocation.getCurrentPosition(function(b){a.autoGeocodeQuery(b)},function(b){a._autoGeocodeError(b)})},googleGeocode:function(){var a=this,b=new google.maps.Geocoder;this.geocode=function(c,d){b.geocode(c,function(b,c){if(c===google.maps.GeocoderStatus.OK){var e={};e.latitude=b[0].geometry.location.lat(),e.longitude=b[0].geometry.location.lng(),d(e)}else a.notify(a.settings.geocodeErrorAlert+c),d(null)})}},reverseGoogleGeocode:function(){var a=this,b=new google.maps.Geocoder;this.geocode=function(c,d){b.geocode(c,function(b,c){if(c===google.maps.GeocoderStatus.OK){if(b[0]){var e={};e.address=b[0].formatted_address,d(e)}}else a.notify(a.settings.geocodeErrorAlert+c),d(null)})}},roundNumber:function(a,b){return Math.round(a*Math.pow(10,b))/Math.pow(10,b)},isEmptyObject:function(a){for(var b in a)if(a.hasOwnProperty(b))return!1;return!0},hasEmptyObjectVals:function(a){var b=!0;for(var c in a)a.hasOwnProperty(c)&&""!==a[c]&&0!==a[c].length&&(b=!1);return b},modalClose:function(){this.settings.callbackModalClose&&this.settings.callbackModalClose.call(this),z={},a("."+this.settings.overlay+" select").prop("selectedIndex",0),a("."+this.settings.overlay+" input").prop("checked",!1),a("."+this.settings.overlay).hide()},_createLocationVariables:function(a){var b;for(var c in w[a])w[a].hasOwnProperty(c)&&(b=w[a][c],"distance"===c&&(b=this.roundNumber(b,2)),A[c]=b)},sortNumerically:function(a){a.sort(function(a,b){return a.distance<b.distance?-1:a.distance>b.distance?1:0})},filterData:function(a,b){var c=!0;for(var d in b)b.hasOwnProperty(d)&&(new RegExp(b[d].join(""),"i").test(a[d])||(c=!1));return c?!0:void 0},_paginationOutput:function(a,b){a=parseFloat(a);var c="",d=a+1,e=a-1;a>0&&(c+='<li class="bh-sl-next-prev" data-page="'+e+'">'+this.settings.prevPage+"</li>");for(var f=0;b>f;f++){var g=f+1;c+=f===a?'<li class="bh-sl-current" data-page="'+f+'">'+g+"</li>":'<li data-page="'+f+'">'+g+"</li>"}return b>d&&(c+='<li class="bh-sl-next-prev" data-page="'+d+'">'+this.settings.nextPage+"</li>"),c},paginationSetup:function(b){var c="",d=w.length/this.settings.locationsPerPage,e=a(".bh-sl-pagination-container .bh-sl-pagination");"undefined"==typeof b&&(b=0),0===e.length?c=this._paginationOutput(b,d):(e.empty(),c=this._paginationOutput(b,d)),e.append(c)},markerImage:function(a,b,c){var d;return d="undefined"!=typeof b&&"undefined"!=typeof c?{url:a,size:new google.maps.Size(b,c),scaledSize:new google.maps.Size(b,c)}:{url:a,size:new google.maps.Size(32,32),scaledSize:new google.maps.Size(32,32)}},createMarker:function(a,b,c,d,e,f){var g,h,i,j=[];if("undefined"!=typeof f&&f.length&&(f=f.replace(/\s+/g,"")),null!==this.settings.catMarkers)if(-1!==f.indexOf(",")){j=f.split(",");for(var k=0;k<j.length;k++)j[k]in this.settings.catMarkers&&(h=this.markerImage(this.settings.catMarkers[j[k]][0],this.settings.catMarkers[j[k]][1],this.settings.catMarkers[j[k]][2]))}else f in this.settings.catMarkers&&(h=this.markerImage(this.settings.catMarkers[f][0],this.settings.catMarkers[f][1],this.settings.catMarkers[f][2]));return null!==this.settings.markerImg&&(h=null===this.settings.markerDim?this.markerImage(this.settings.markerImg):this.markerImage(this.settings.markerImg,this.settings.markerDim.width,this.settings.markerDim.height)),-1===this.settings.storeLimit||this.settings.storeLimit>26||null!==this.settings.catMarkers||null!==this.settings.markerImg?g=new google.maps.Marker({position:a,map:e,draggable:!1,icon:h}):(i={url:"https://mt.googleapis.com/vt/icon/name=icons/spotlight/spotlight-waypoint-b.png&text="+d+"&psize=16&font=fonts/Roboto-Regular.ttf&color=ff333333&ax=44&ay=48"},g=new google.maps.Marker({position:a,map:e,icon:i,draggable:!1})),g},_defineLocationData:function(b,c,d){var e="";this._createLocationVariables(b.get("id"));var f;f=A.distance<=1?"km"===this.settings.lengthUnit?this.settings.kilometerLang:this.settings.mileLang:"km"===this.settings.lengthUnit?this.settings.kilometersLang:this.settings.milesLang;var g=b.get("id");return e=-1===this.settings.storeLimit||this.settings.storeLimit>26?g+1:String.fromCharCode(d>0?"A".charCodeAt(0)+(c+g):"A".charCodeAt(0)+g),{location:[a.extend(A,{markerid:g,marker:e,length:f,origin:p})]}},listSetup:function(b,c,d){var e=this._defineLocationData(b,c,d),f=h(e);a("."+this.settings.locationList+" ul").append(f)},createInfowindow:function(b,c,d,e,f){var g=this,h=this._defineLocationData(b,e,f),j=i(h);"left"===c?(d.setContent(j),d.open(b.get("map"),b)):google.maps.event.addListener(b,"click",function(){d.setContent(j),d.open(b.get("map"),b),a("."+g.settings.locationList+" li").removeClass("list-focus");var c=b.get("id"),e=a("."+g.settings.locationList+" li[data-markerid="+c+"]");e.addClass("list-focus");var f=a("."+g.settings.locationList);f.animate({scrollTop:e.offset().top-f.offset().top+f.scrollTop()})})},autoGeocodeQuery:function(a){var b=this,c={},d=new this.reverseGoogleGeocode,e=new google.maps.LatLng(a.coords.latitude,a.coords.longitude);d.geocode({latLng:e},function(d){if(null!==d){var e=d.address;c.lat=a.coords.latitude,c.lng=a.coords.longitude,c.origin=e,b.mapping(c)}else b.notify(b.settings.addressErrorAlert)})},_autoGeocodeError:function(){this.notify(this.settings.autoGeocodeErrorAlert)},paginationChange:function(a){this.settings.callbackPageChange&&this.settings.callbackPageChange.call(this,a),C.page=a,this.mapping(C)},getAddressByMarker:function(a){var b=null;return w[a].address&&(b+=w[a].address+" "),w[a].address2&&(b+=w[a].address2+" "),w[a].city&&(b+=w[a].city+", "),w[a].state&&(b+=w[a].state+" "),w[a].postal&&(b+=w[a].postal+" "),w[a].country&&(b+=w[a].country+" "),b},clearMarkers:function(){for(var a=0;a<w.length;a++)y[a].setMap(null)},directionsRequest:function(b,d,e){this.settings.callbackDirectionsRequest&&this.settings.callbackDirectionsRequest.call(this,b,d,e);var f=this.getAddressByMarker(d);if(f){a("."+this.settings.locationList+" ul").hide(),this.clearMarkers(),t.setMap(e),t.setPanel(a(".bh-sl-directions-panel").get(0));var g={origin:b,destination:f,travelMode:google.maps.TravelMode.DRIVING};u.route(g,function(a,b){b===google.maps.DirectionsStatus.OK&&t.setDirections(a)}),a("."+this.settings.locationList).prepend('<div class="bh-sl-close-directions-container"><div class="'+this.settings.closeIcon+'"></div></div>')}a(c).off("click","."+this.settings.locationList+" li .loc-directions a")},closeDirections:function(){this.settings.callbackCloseDirections&&this.settings.callbackCloseDirections.call(this),a(".bh-sl-close-directions-container").remove(),a("."+this.settings.locationList+" .adp").remove(),a("."+this.settings.locationList+" ul").fadeIn(),this.reset(),q&&r&&(this.settings.mapSettings.zoom=0===this.countFilters()?m:0,this.processForm(null)),a(c).off("click."+f,"."+this.settings.locationList+" .bh-sl-close-icon")},processForm:function(b){var c=this,d=null;"undefined"!=typeof b&&null!==b&&b.preventDefault(),this.settings.maxDistance===!0&&(d=a("#"+this.settings.maxDistanceID).val()),this.settings.querystringParams===!0&&(this.getQueryString(this.settings.addressID)||this.getQueryString(this.settings.searchID))?(p=this.getQueryString(this.settings.addressID),o=this.getQueryString(this.settings.searchID)):(p=a("#"+this.settings.addressID).val(),o=a("#"+this.settings.searchID).val());var e=a("#"+this.settings.regionID).val();if(""===p&&""===o)this._start();else if(""!==p){var f=new this.googleGeocode;f.geocode({address:p,region:e},function(a){null!==a?(q=a.latitude,r=a.longitude,C.lat=q,C.lng=r,C.origin=p,C.name=o,C.distance=d,c.mapping(C)):c.notify(c.settings.addressErrorAlert)})}else""!==o&&(C.name=o,c.mapping(C))},locationsSetup:function(a,b,c,d,e,f,g){if("undefined"!=typeof f&&(a.distance||(a.distance=this.geoCodeCalcCalcDistance(c,d,a.lat,a.lng,B.EarthRadius))),this.settings.maxDistance===!0&&e!==!0&&null!==g){if(!(a.distance<g))return;w[b]=a}else w[b]=a},countFilters:function(){var a=0;if(!this.isEmptyObject(z))for(var b in z)z.hasOwnProperty(b)&&(a+=z[b].length);return a},_existingCheckedFilters:function(b){a("#"+this.settings.taxonomyFilters[b]+" input[type=checkbox]").each(function(){if(a(this).prop("checked")){var c=a(this).val();"undefined"!=typeof c&&""!==c&&-1===z[b].indexOf(c)&&z[b].push(c)}})},_existingSelectedFilters:function(b){a("#"+this.settings.taxonomyFilters[b]+" select").each(function(){var c=a(this).val();"undefined"!=typeof c&&""!==c&&-1===z[b].indexOf(c)&&(z[b]=[c])})},_existingRadioFilters:function(b){a("#"+this.settings.taxonomyFilters[b]+" input[type=radio]").each(function(){if(a(this).prop("checked")){var c=a(this).val();"undefined"!=typeof c&&""!==c&&-1===z[b].indexOf(c)&&(z[b]=[c])}})},checkFilters:function(){for(var a in this.settings.taxonomyFilters)this.settings.taxonomyFilters.hasOwnProperty(a)&&(this._existingCheckedFilters(a),this._existingSelectedFilters(a),this._existingRadioFilters(a))},getFilterKey:function(a){for(var b in this.settings.taxonomyFilters)if(this.settings.taxonomyFilters.hasOwnProperty(b))for(var c=0;c<this.settings.taxonomyFilters[b].length;c++)if(this.settings.taxonomyFilters[b]===a)return b},taxonomyFiltering:function(){var b=this;for(var c in this.settings.taxonomyFilters)this.settings.taxonomyFilters.hasOwnProperty(c)&&(z[c]=[]);a("."+this.settings.taxonomyFiltersContainer).on("change."+f,"input, select",function(c){c.stopPropagation();var d,e,f;if(a(this).is('input[type="checkbox"]')){if(b.checkFilters(),d=a(this).val(),e=a(this).closest(".bh-sl-filters").attr("id"),f=b.getFilterKey(e))if(a(this).prop("checked"))-1===z[f].indexOf(d)&&z[f].push(d),a("#"+b.settings.mapID).hasClass("bh-sl-map-open")===!0&&(q&&r?(b.settings.mapSettings.zoom=0,b.processForm()):b.mapping(C));else{var g=z[f].indexOf(d);g>-1&&(z[f].splice(g,1),a("#"+b.settings.mapID).hasClass("bh-sl-map-open")===!0&&(q&&r?(b.settings.mapSettings.zoom=0===b.countFilters()?m:0,b.processForm()):b.mapping(C)))}}else(a(this).is("select")||a(this).is('input[type="radio"]'))&&(b.checkFilters(),d=a(this).val(),e=a(this).closest(".bh-sl-filters").attr("id"),f=b.getFilterKey(e),d?f&&(z[f]=[d],a("#"+b.settings.mapID).hasClass("bh-sl-map-open")===!0&&(q&&r?(b.settings.mapSettings.zoom=0,b.processForm()):b.mapping(C))):(f&&(z[f]=[]),b.reset(),q&&r?(b.settings.mapSettings.zoom=m,b.processForm()):b.mapping(C)))})},mapping:function(d){var e,h,i,j,m,p,t,u,A,B,C,D,E,F=this,G=0;if(this.isEmptyObject(d)||(e=d.lat,h=d.lng,i=d.origin,j=d.name,m=d.distance,p=d.page),google.maps.visualRefresh=!0,"undefined"==typeof p&&(p=0),"undefined"==typeof i&&this.settings.nameSearch===!0)n=F._getData();else{var H=new google.maps.LatLng(e,h);"undefined"!=typeof k&&i===k&&"undefined"!=typeof l?(i=k,n=l):n=F._getData(q,r,i)}null!==F.settings.taxonomyFilters&&F.hasEmptyObjectVals(z)&&F.checkFilters(),n.done(function(d){var j=a("#"+F.settings.mapID),q="km"===F.settings.lengthUnit?F.settings.kilometersLang:F.settings.milesLang;if(l=n,k=i,F.settings.callbackSuccess&&F.settings.callbackSuccess.call(this),F.settings.fullMapStart===!0&&j.hasClass("bh-sl-map-open")===!1?t=!0:F.reset(),j.addClass("bh-sl-map-open"),"json"===F.settings.dataType||"jsonp"===F.settings.dataType)for(var r=0;G<d.length;r++){var I=d[r],J={};for(var K in I)I.hasOwnProperty(K)&&(J[K]=I[K]);F.locationsSetup(J,G,e,h,t,i,m),G++}else"kml"===F.settings.dataType?a(d).find("Placemark").each(function(){var b={name:a(this).find("name").text(),lat:a(this).find("coordinates").text().split(",")[1],lng:a(this).find("coordinates").text().split(",")[0],description:a(this).find("description").text()};F.locationsSetup(b,G,e,h,t,i,m),G++}):a(d).find(F.settings.xmlElement).each(function(){var a={};for(var b in this.attributes)this.attributes.hasOwnProperty(b)&&(a[this.attributes[b].name]=this.attributes[b].value);F.locationsSetup(a,G,e,h,t,i,m),G++});if(F.settings.nameSearch===!0&&"undefined"!=typeof o&&(z[F.settings.nameAttribute]=[o]),null!==F.settings.taxonomyFilters||F.settings.nameSearch===!0){var L={};for(var M in z)if(z.hasOwnProperty(M)&&z[M].length>0)for(var N=0;N<z[M].length;N++)L[M]||(L[M]=[]),L[M][N]="(?=.*\\b"+z[M][N].replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")+"\\b)";F.isEmptyObject(L)||(w=a.grep(w,function(a){return F.filterData(a,L)}))}if(F.isEmptyObject(w)&&(F.settings.callbackNoResults&&F.settings.callbackNoResults.call(this),j.hasClass("bh-sl-map-open")&&g.hide(),E=a('<li><div class="bh-sl-noresults-title">'+F.settings.noResultsTitle+'</div><br><div class="bh-sl-noresults-desc">'+F.settings.noResultsDesc+"</li>").hide().fadeIn(),w[0]={distance:0,lat:0,lng:0}),"undefined"!=typeof i&&F.sortNumerically(w),F.settings.featuredLocations===!0&&(v=a.grep(w,function(a){return"true"===a.featured}),x=a.grep(w,function(a){return"true"!==a.featured}),w=[],w=v.concat(x)),F.settings.maxDistance===!0&&t!==!0&&m){if("undefined"==typeof w[0]||w[0].distance>m)return void F.notify(F.settings.distanceErrorAlert+m+" "+q)}else-1!==F.settings.distanceAlert&&w[0].distance>F.settings.distanceAlert&&F.notify(F.settings.distanceErrorAlert+F.settings.distanceAlert+" "+q);if(F.settings.pagination===!0&&F.paginationSetup(p),F.settings.slideMap===!0&&g.slideDown(),F.settings.modal===!0&&(F.settings.callbackModalOpen&&F.settings.callbackModalOpen.call(this),a("."+F.settings.overlay).fadeIn(),a(c).on("click."+f,"."+F.settings.closeIcon+", ."+F.settings.overlay,function(){F.modalClose()}),a(c).on("click."+f,"."+F.settings.modalWindow,function(a){a.stopPropagation()}),a(c).on("keyup."+f,function(a){27===a.keyCode&&F.modalClose()})),s=-1===F.settings.storeLimit||w.length<F.settings.storeLimit?w.length:F.settings.storeLimit,F.settings.pagination===!0?(C=F.settings.locationsPerPage,B=p*F.settings.locationsPerPage,w=w.slice(B,B+C),s=w.length):(C=s,B=0),F.settings.fullMapStart===!0&&t===!0||0===F.settings.mapSettings.zoom||"undefined"==typeof i)D=F.settings.mapSettings,A=new google.maps.LatLngBounds;else if(F.settings.pagination===!0){var O=new google.maps.LatLng(w[0].lat,w[0].lng);0===p?(F.settings.mapSettings.center=H,D=F.settings.mapSettings):(F.settings.mapSettings.center=O,D=F.settings.mapSettings)}else F.settings.mapSettings.center=H,D=F.settings.mapSettings;var P=new google.maps.Map(c.getElementById(F.settings.mapID),D);google.maps.event.addDomListener(b,"resize",function(){var a=P.getCenter();google.maps.event.trigger(P,"resize"),P.setCenter(a)}),g.data(F.settings.mapID.replace("#",""),P);var Q=new google.maps.InfoWindow;if(F.settings.originMarker===!0){var R="";if(F.settings.fullMapStart===!1&&t===!0)return;R=null!==F.settings.originMarkerImg?this.markerImage(F.settings.originMarkerImg,F.settings.originMarkerDim.width,F.settings.originMarkerDim.height):{url:"https://mt.googleapis.com/vt/icon/name=icons/spotlight/spotlight-waypoint-a.png"},u=new google.maps.Marker({position:H,map:P,icon:R,draggable:!1})}a(c).on("click."+f,".bh-sl-pagination li",function(){F.paginationChange(a(this).attr("data-page"))}),F.settings.inlineDirections===!0&&(a(c).on("click."+f,"."+F.settings.locationList+" li .loc-directions a",function(b){b.preventDefault();var c=a(this).closest("li").attr("data-markerid");F.directionsRequest(i,c,P)}),a(c).on("click."+f,"."+F.settings.locationList+" .bh-sl-close-icon",function(){F.closeDirections()}));for(var S=0;C-1>=S;S++){var T="";T=String.fromCharCode(p>0?"A".charCodeAt(0)+(B+S):"A".charCodeAt(0)+S);var U=new google.maps.LatLng(w[S].lat,w[S].lng);u=F.createMarker(U,w[S].name,w[S].address,T,P,w[S].category),u.set("id",S),y[S]=u,(F.settings.fullMapStart===!0&&t===!0||0===F.settings.mapSettings.zoom||"undefined"==typeof i)&&A.extend(U),F.createInfowindow(u,null,Q,B,p)}(F.settings.fullMapStart===!0&&t===!0||0===F.settings.mapSettings.zoom||"undefined"==typeof i)&&P.fitBounds(A);var V=a("."+F.settings.locationList+" ul");V.empty(),0===w[0].lat&&0===w[0].lng?V.append(E):a(y).each(function(a){var b=y[a];F.listSetup(b,B,p)}),a(c).on("click."+f,"."+F.settings.locationList+" li",function(){var b=a(this).data("markerid"),c=y[b];a("."+F.settings.locationList+" li").removeClass("list-focus"),a("."+F.settings.locationList+" li[data-markerid="+b+"]").addClass("list-focus"),P.panTo(c.getPosition());var d="left";F.settings.bounceMarker===!0?(c.setAnimation(google.maps.Animation.BOUNCE),setTimeout(function(){c.setAnimation(null),F.createInfowindow(c,d,Q,B,p)},700)):F.createInfowindow(c,d,Q,B,p)}),a(c).on("click."+f,"."+F.settings.locationList+" li a",function(a){a.stopPropagation()}),a("."+F.settings.locationList+" ul li:even").css("background",F.settings.listColor1),a("."+F.settings.locationList+" ul li:odd").css("background",F.settings.listColor2),F.settings.modal===!0&&F.settings.callbackModalReady&&F.settings.callbackModalReady.call(this)})}}),a.fn[f]=function(b){var c=arguments;if(b===d||"object"==typeof b)return this.each(function(){a.data(this,"plugin_"+f)||a.data(this,"plugin_"+f,new e(this,b))});if("string"==typeof b&&"_"!==b[0]&&"init"!==b){var g;return this.each(function(){var d=a.data(this,"plugin_"+f);d instanceof e&&"function"==typeof d[b]&&(g=d[b].apply(d,Array.prototype.slice.call(c,1))),"destroy"===b&&a.data(this,"plugin_"+f,null)}),g!==d?g:this}}}}(jQuery,window,document);