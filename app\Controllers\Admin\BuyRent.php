<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class BuyRent extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ClassesModel');

        $db = \Config\Database::connect();
		$this->machines = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                            HAVING countMachine > 0
                                      ')->getResultArray();
		$this->difficulty = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM difficulty
                                            LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$this->languages = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM languages
                                            LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                            HAVING countClasses > 0
                                        ')->getResultArray();
		$this->duration_less10 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
		$this->duration_less25 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();
		$this->duration_more25 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

		$this->body_parts = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                            FROM body_parts
                                            LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                            HAVING countBodyParts > 0
                                        ')->getResultArray();
        $this->all_teachers = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                          ')->getResultArray();

	}

    public function index()
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;

        $data['machines'] = $this->machines;
        $data['languages'] = $this->languages;
        $data['difficulty'] = $this->difficulty;
        $data['duration_less10'] = $this->duration_less10;
        $data['duration_less25'] = $this->duration_less25;
        $data['duration_more25'] = $this->duration_more25;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                return redirect()->to('/admin/classes');
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== "0"){
            $search_term = session('classes_search');
        }else{
            $search_term = "0";
        };

        if(!empty($post)){
            // $this->set_filter($post);
            $session->set('buy_rent_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('buy_rent_filter');
            // $session->set('buy_rent_filter', '');
        }

        // echo $search_term;
        // die();
        $data['page'] = 1;
        $page = 1;
        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        $data['all_classes'] = $this->model->filter_buy_rent_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('buy_rent_filter'));
        $data['classes_count'] = count($this->model->filter_buy_rent_admin(0, 10000, $search_term, session('classes_sort'), session('buy_rent_filter')));

        echo view('admin/classes/buy_rent_filter_view', $data);
    }

    public function clear_filter(){
        $session = \Config\Services::session();
        $session->set('buy_rent_filter', '');
        return redirect()->to('/admin/buy_rent');
    }

    public function page($page = 1)
    {
        $session = \Config\Services::session();
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = $this->machines;
        $data['languages'] = $this->languages;
        $data['difficulty'] = $this->difficulty;
        $data['duration_less10'] = $this->duration_less10;
        $data['duration_less25'] = $this->duration_less25;
        $data['duration_more25'] = $this->duration_more25;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        $data['machines'] = $this->machines;
        $data['difficulty'] = $this->difficulty;
        $data['duration_less10'] = $this->duration_less10;
        $data['duration_less25'] = $this->duration_less25;
        $data['duration_more25'] = $this->duration_more25;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        if(isset($_GET['search_term'])){
            $search_term = $_GET['search_term'];
            $this->set_search('classes', $search_term);
            if($search_term == ""){
                return redirect()->to('/admin/buy_rent/page/' . $page);
            }
        }else if(session('classes_search') !== "" AND session('classes_search') !== 0){
            $search_term = session('classes_search');
        }else{
            $search_term = 0;
        };

        if(!empty($post)){
            // $this->set_filter($post);
            $session->set('buy_rent_filter', $post);

            $data['filter'] = $post;
        }else{
            $data['filter'] = session('buy_rent_filter');
            // $session->set('buy_rent_filter', '');
        }

        $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        $data['all_classes'] = $this->model->filter_buy_rent_admin(($page * session('classes_per_page')) - session('classes_per_page'), session('classes_per_page'), $search_term, session('classes_sort'), session('buy_rent_filter'));
        $data['classes_count'] = count($this->model->filter_buy_rent_admin(0, 10000, $search_term, session('classes_sort'), session('buy_rent_filter')));
        $data['page'] = $page;

        echo view('admin/classes/buy_rent_filter_view', $data);
    }

    public function filter($page = 1)
    {
        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['machines'] = $this->machines;
        $data['languages'] = $this->languages;
        $data['difficulty'] = $this->difficulty;
        $data['duration_less10'] = $this->duration_less10;
        $data['duration_less25'] = $this->duration_less25;
        $data['duration_more25'] = $this->duration_more25;
        $data['body_parts'] = $this->body_parts;
        $data['all_teachers'] = $this->all_teachers;

        // $data['order'] =  isset($post['order']) ? $post['order'] : session('classes_sort');
        // $data['search'] =  isset($post['search']) ? $post['search'] : '';
        if(isset($_GET['search_term'])){
            $data['search'] = $_GET['search_term'];
            $this->set_search('classes', $data['search']);
        }else if(session('classes_search') !== "" AND session('classes_search') !== 0){
            $data['search'] = session('classes_search');
        }else{
            $data['search'] = NULL;
        };

        $data['filter'] = $post;

        // $search_term = isset($_GET['search_term']) ? $_GET['search_term'] : NULL;
        // $data['search_term'] = $search_term;
        $data['sort_by'] = sort_name(session('classes_sort'));
        $data['order'] =  session('classes_sort');
        $data['all_classes'] = $this->model->filter_buy_rent_admin(($page * session('classes_per_page')) - session('classes_per_page'), 10000, $data['search'], $data['order'], $post);
        $data['classes_count'] = count($data['all_classes']);
        $data['page'] = $page;

        // echo '<pre>';
        // print_r($data);
        // echo '</pre>';
        echo view('admin/classes/buy_rent_filter_view', $data);
    }

    public function datatable()
    {
		$post = $this->request->getPost();
		$draw = $post['draw'];
		$row = $post['start'];

		$rowperpage = $post['length']; // Rows display per page
		$columnIndex = $post['order'][0]['column']; // Column index
		$columnName = $post['columns'][$columnIndex]['data']; // Column name
		$columnSortOrder = $post['order'][0]['dir']; // asc or desc
		$searchValue = $post['search']['value']; // Search value


		$db      = \Config\Database::connect();
		$sql = "SELECT
			classes.*,
			IF(classes.deleted_at IS NULL, 0, 1) as deleted
			FROM classes
			WHERE 1 ";
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecords = count($data);

		if (session()->has('filter_table'))
		{
			$filter = session()->filter_table;
			if(isset($filter['vreme']) AND $filter['vreme'] <> ''){
				$sql .= " AND users.created_at > '" . $filter['vreme'] . "'";
			}
			if(isset($filter['vrsta_potvrde']) AND $filter['vrsta_potvrde'] <> ''){
				$sql .= " AND users.vrsta_potvrde = '" . $filter['vrsta_potvrde'] . "'";
			}
		}
		if($searchValue != ''){
			$sql .= " AND (
				classes.id LIKE '%" . $searchValue . "%'
				OR classes.title LIKE '%" . $searchValue . "%'
				) ";
		}
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecordwithFilter = count($data);
		$sql .= " ORDER BY " . $columnName . " " . $columnSortOrder;
		$sql .= " LIMIT " . $row . "," . $rowperpage;
		$query = $db->query($sql);
		$data_final = $query->getResult();
		$response = array(
		  "mrnj" => $sql,
		  "draw" => intval($draw),
		  "iTotalRecords" => $totalRecords,
		  "iTotalDisplayRecords" => $totalRecordwithFilter,
		  "aaData" => $data_final
		);
		echo json_encode($response, JSON_PRETTY_PRINT);
    }

    public function edit($edit_id = 0)
    {
        $teachers_model = model('TeachersModel');
        $audio_model = model('AudioModel');

        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
		$data['classes_sort'] = $this->classes_sort;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL')->getResultArray();
		$data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
		$data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();

        $data['all_teachers'] = $teachers_model->findAll();
        $data['all_audio'] = $audio_model->all_audio(0, 0);
		$current_machines = $db->query("SELECT * FROM classes_machine WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_machines)){
            foreach($current_machines as $k => $single){
                $data['current_machines'][] = $single['class_machine'] ;
            }
        }else{
            $data['current_machines'] = 0;
        }
		$current_body_parts = $db->query("SELECT * FROM classes_body_parts WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_body_parts)){
            foreach($current_body_parts as $k => $single){
                $data['current_body_parts'][] = $single['class_body_parts'] ;
            }
        }else{
            $data['current_body_parts'] = 0;
        }
		$current_accessories = $db->query("SELECT * FROM classes_accessories WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_accessories)){
            foreach($current_accessories as $k => $single){
                $data['current_accessories'][] = $single['class_accessories'] ;
            }
        }else{
            $data['current_accessories'] = 0;
        }
		$current_tensions = $db->query("SELECT * FROM classes_tensions WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_tensions)){
            foreach($current_tensions as $k => $single){
                $data['current_tensions'][] = $single['class_tensions'];
            }
        }else{
            $data['current_tensions'] = 0;
        }
		$current_springs = $db->query("SELECT * FROM classes_springs WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_springs)){
            foreach($current_springs as $k => $single){
                $data['current_springs'][] = $single['class_springs'] ;
            }
        }else{
            $data['current_springs'] = 0;
        }
        $current_tempo = $db->query("SELECT * FROM classes_tempo WHERE class_id = " . $edit_id)->getResultArray();
        if(!empty($current_tempo)){
            foreach($current_tempo as $k => $single){
                $data['current_tempo'][] = $single['class_tempo'] ;
            }
        }else{
            $data['current_tempo'] = 0;
        }

        $data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/buy_rent');
        };
        $data['all_classes'] = $this->model->all_classes(0, 0, session('classes_search'), session('classes_sort'), "0, 1");
        $data['all_exercises'] = array();
        $data['selected_exercises_for_selection'] = array();

        $total = count($data['all_classes']);
        $index_list = array_column($data['all_classes'], 'id');
        $index_id = array_search($edit_id, array_column($data['all_classes'], 'id'));
        if($index_id !== FALSE)
        {
            if($index_id < $total - 1){
                $data['prev'] = $this->model->where('id', $index_list[$index_id + 1])->first();
            }else{
                $data['prev'] = $this->model->where('id', $index_list[0])->first();
            }
            if($index_id > 0){
                $data['next'] = $this->model->where('id', $index_list[$index_id - 1])->first();
            }else{
                $data['next'] = $this->model->where('id', $index_list[$total - 1])->first();
            }
        }

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/classes/edit_view', $data);
    }

    public function edit_bulk()
    {
        $data = $this->request->getPost();
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // echo '<pre>';
        // print_r(explode(',', ($data['ids'])));
        // echo '</pre>';
        // die();
        $teachers_model = model('TeachersModel');

        $data['logged_user'] = $this->admin;
        $data['settings'] = $this->settings;

        $db = \Config\Database::connect();
        $data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL ORDER BY sort ASC')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
        $data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
        $data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
        $data['accessories'] = $db->query('SELECT * FROM accessories WHERE deleted_at IS NULL')->getResultArray();
        $data['tensions'] = $db->query('SELECT * FROM tensions')->getResultArray();
        $data['springs'] = $db->query('SELECT * FROM springs ')->getResultArray();
        $data['tempo'] = $db->query('SELECT * FROM tempo ORDER BY sort ASC')->getResultArray();
        $data['all_teachers'] = $teachers_model->findAll();

        $ids = explode(',', ($data['ids']));
        foreach($ids as $key => $id){
            $data['current_classes'][] = $this->model->where(['id' => $id])->first();
            $current_machines = $db->query("SELECT * FROM classes_machine WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_machines)){
                foreach($current_machines as $k => $single){
                    $data['current_machines'][$id][] = $single['class_machine'] ;
                }
            }else{
                $data['current_machines'][$id] = 0;
            }
            $current_body_parts = $db->query("SELECT * FROM classes_body_parts WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_body_parts)){
                foreach($current_body_parts as $k => $single){
                    $data['current_body_parts'][$id][] = $single['class_body_parts'] ;
                }
            }else{
                $data['current_body_parts'][$id] = 0;
            }
            $current_accessories = $db->query("SELECT * FROM classes_accessories WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_accessories)){
                foreach($current_accessories as $k => $single){
                    $data['current_accessories'][$id][] = $single['class_accessories'] ;
                }
            }else{
                $data['current_accessories'][$id] = 0;
            }
            $current_tensions = $db->query("SELECT * FROM classes_tensions WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_tensions)){
                foreach($current_tensions as $k => $single){
                    $data['current_tensions'][$id][] = $single['class_tensions'] ;
                }
            }else{
                $data['current_tensions'][$id] = 0;
            }
            $current_springs = $db->query("SELECT * FROM classes_springs WHERE class_id = " . $id)->getResultArray();
            if(!empty($current_springs)){
                foreach($current_springs as $k => $single){
                    $data['current_springs'][$id][] = $single['class_springs'] ;
                }
            }else{
                $data['current_springs'][$id] = 0;
            }
            $current_tempo = $db->query("SELECT * FROM classes_tempo WHERE class_id = " . $edit_id)->getResultArray();
            if(!empty($current_tempo)){
                foreach($current_tempo as $k => $single){
                    $data['current_tempo'][] = $single['class_tempo'] ;
                }
            }else{
                $data['current_tempo'] = 0;
            }
        }

		// echo '<pre>';
		// print_r($data);
		// die();

		return view('admin/classes/multi_edit_view', $data);
    }

    public function multi()
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		return view('admin/classes/multi_view', $data);
    }

    public function clear_search()
    {
        $this->set_search('classes', "0");
		return redirect('admin/buy_rent');
    }

    public function save_batch()
    {
		$data = $this->request->getPost();
        foreach($data as $key => $single_field){
            if(is_array($single_field)){
                $db      = \Config\Database::connect();
                $builder = $db->table('classes');
                foreach($single_field as $k => $v){
                    $fields[$k][$key] = $v;
                }
            }
        }
        if (count($fields) > 0){
            $builder->insertBatch($fields);
        }
		return redirect('admin/classes');
    }
    public function reject_class()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $SubscribersModel = model('SubscribersModel');
        $NotificationsModel = model('NotificationsModel');
        $ClassesModel = model('ClassesModel');
		$request = service('request');
        $data = $request->getPost();

        $save_class = array('id' => $data['class_id'], 'status' => 3, 'reason' => $data['reason']);
        $response['success'] = $ClassesModel->save($save_class);

        $teacher = $TeachersModel->where(["id" => $data['teacher_id']])->first();
        $user = $SubscribersModel->where(["email" => $teacher['email']])->first();

        if(!empty($teacher)){
            $subject = 'Your LOD video is rejected';
            $data_template = [
                'class_title' => $data['class_title'],
                'class_date' => $data['class_date'],
                'description' => $data['reason'],
            ];
            $template = 'front/email_templates/rejected-video';
            $to = $teacher['email'];
            $response['reason_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);

            $notification_data = array(
                'content'   => 'Your class <span class="text-underline">' . $data['class_title'] . '</span> is rejected.<br>Reason: ' . $data['reason'],
                'author'    => 'system',
                'subscriber_id' => $user['id'],
                'type' => 'class_rejected_notif',
                'date'    => date('Y-m-d H:i:s')
            );
            $response['notification_saved'] = $NotificationsModel->save($notification_data);

        }

		return $this->respond($response);
    }

    public function save()
    {
        $email_model = model('EmailModel');
        $TeachersModel = model('TeachersModel');
        $SubscribersModel = model('SubscribersModel');
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$data = $this->request->getPost();
		$validation->reset();
        if($data['status'] == 1){
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
            ];
        }else{
            $rules = [
                'title'         => 'required|min_length[2]',
                'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
                'video'         => 'required',
                'machine'       => 'required',
                'language'      => 'required',
                'difficulty'    => 'required',
                'teacher'       => 'required',
            ];
        }
        $response['rules'] = $rules;
        $validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
                $file->move(ROOTPATH . 'public/uploads/classes', $name);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	->resize(750, 410, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/' . $name, 90);

				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	->resize(510, 300, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/thumb_' . $name, 100);

                $data['image'] = 'uploads/classes/' . $name;
                // $data['image_small'] = 'uploads/classes/thumb_' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if(isset($data['image_removed']) AND $data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['validation'] = $validation->getErrors();
			if($this->model->save($data)){
                $response['success'] = TRUE;
            }else{
                $response['success'] = $this->model->errors();
            }
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('classes_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

            // if($data['id'] == 0 OR $data['prev_status'] != 2){
            //     $notification_data = array(
            //         'content'   => '<span class="text-underline">' . $data['title'] . '</span> class is uploaded.',
            //         'link'      => base_url() . '/classes/' . $data['slug'],
            //         'author'    => 'system',
            //         'date'    => date('Y-m-d H:i:s')
            //     );
            //     $response['notification_saved'] = $NotificationsModel->save($notification_data);
            // }

            if(isset($data['teacher']) AND $data['teacher'] != ''){
                $teacher = $TeachersModel->where(["id" => $data['teacher']])->first();
                $user = $SubscribersModel->where(["email" => $teacher['email']])->first();
                if($data['prev_status'] == 2){
                    $subject = 'Your LOD video is approved';
                    $data_template = [
                        'class_title' => $data['title'],
                        // 'class_date' => $data['created_at']
                    ];
                    $template = 'front/email_templates/approved-video';
                    $to = $teacher['email'];
                    $response['teacher_approved_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);

                    $notification_data = array(
                        'content'   => 'Your class <span class="text-underline">' . $data['title'] . '</span> is approved.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'subscriber_id' => $user['id'],
                        'type' => 'class_approved_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_saved'] = $NotificationsModel->save($notification_data);

                    $notification_teacher_data = array(
                        'content'   => '<span class="text-underline">' . $data['title'] . '</span> OD class is uploaded.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'type' => 'new_od_class_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_teacher_saved'] = $NotificationsModel->save($notification_teacher_data);
                }else{
                    $notification_class_data = array(
                        'content'   => '<span class="text-underline">' . $data['title'] . '</span> class is uploaded.',
                        'link'      => base_url() . '/classes/' . $data['slug'],
                        'author'    => 'system',
                        'type' => 'new_class_notif',
                        'date'    => date('Y-m-d H:i:s')
                    );
                    $response['notification_class_saved'] = $NotificationsModel->save($notification_class_data);
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}