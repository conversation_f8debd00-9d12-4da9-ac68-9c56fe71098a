.video-js .vjs-big-play-button {
    font-size: 3em;
    line-height: 1;
    height: 120px;
    width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    border: 2px solid #fff;
    background-color: rgba(255,255,255,0);
    border-radius: 80px;
    transform: translate(-50%, -50%);
}
.video-js:hover .vjs-big-play-button, .video-js .vjs-big-play-button:focus {
    border-color: #fff;
    background-color: #fff !important;
}
.video-js .vjs-big-play-button .vjs-icon-placeholder:before {
    color: #fff;
    font-size: 60px;
}
.video-js:hover  .vjs-big-play-button .vjs-icon-placeholder:before {
    color: #000;
}
.video-js .vjs-big-play-button .vjs-icon-placeholder:before {
    height: auto !important;
    position: relative;
}
span.vjs-icon-placeholder {
    line-height: 1;
}
.vjs-controls-disabled .vjs-big-play-button,
.vjs-has-started .vjs-big-play-button,
.vjs-using-native-controls .vjs-big-play-button,
.vjs-error .vjs-big-play-button {
    display: none;
}
.vjs-has-started.vjs-paused .vjs-big-play-button {
    display: block;
}
.video-js .vjs-control-bar {
    height: 60px;
    background-color: #fff;
    background-color: rgb(255 255 255 / 90%);
}
.vjs-fullscreen-control:before,
.video-js .vjs-fullscreen-control .vjs-icon-placeholder:before,
.vjs-icon-volume-high:before,
.video-js .vjs-mute-control .vjs-icon-placeholder:before,
.vjs-icon-play:before,
.video-js .vjs-play-control .vjs-icon-placeholder:before,
.vjs-icon-pause:before,
.video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder:before {
    /* display: none !important; */
    color: #333;
    font-size: 30px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.video-js .vjs-progress-holder {
    flex: auto;
    transition: all 0.2s;
    height: 8px;
}
.vjs-chromecast-button:hover .vjs-icon-placeholder{
    background: url("../images/cast.svg") no-repeat center center / cover;
    opacity: 0.6;
}
.video-js .vjs-control {
    width: 60px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 13px;
    font-weight: 500;
}
.video-js .vjs-tech {
    display: block;
    object-fit: cover;
}
.video-js .vjs-play-progress:before {
    display: none !important;
}
.video-js .vjs-mute-control {
    cursor: pointer;
    flex: none;
    height: 100% !important;
}
button.vjs-seek-button.skip-back.skip-10.vjs-control.vjs-button {
    order: -1;
}
.video-js.vjs-v6 .vjs-seek-button.skip-back .vjs-icon-placeholder {
    background: url("../images/rewind.svg") no-repeat center center / cover;
    width: 20px;
    height: 20px;
}
.video-js.vjs-v6 .vjs-seek-button.skip-forward .vjs-icon-placeholder {
    background: url("../images/forward.svg") no-repeat center center / cover;
    width: 20px;
    height: 20px;
}
.vjs-chromecast-button .vjs-icon-placeholder {
    background: url("../images/cast.svg") no-repeat center center / cover;
    width: 31px;
    height: 20px;
}
.video-js .vjs-control {
    height: auto;
}
.video-js .vjs-load-progress div {
    background: rgb(255 255 255 / 50%);
}
.video-js .vjs-load-progress {
    background: rgb(255 255 255 / 50%);
}
.video-js .vjs-play-progress {
    background-color: #871b1e;
}
.vjs-icon-pause, .video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder {
    width: 18px;
    height: 20px;
    background: url("../images/pause.svg") no-repeat center center / cover;
}
.vjs-icon-pause:hover, .video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder:hover {
    opacity: 0.6;
}
.vjs-icon-play, .video-js .vjs-play-control .vjs-icon-placeholder {
    width: 20px;
    height: 22px;
    background: url("../images/play.svg") no-repeat center center / cover;
}
.vjs-icon-pause:hover, .video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder:hover {
    opacity: 0.6;
}
.vjs-progress-control .vjs-progress-holder {
    margin: 0 !important;
    background: rgb(255 255 255 / 0%);
}
.vjs-progress-control.vjs-control {
    position: absolute;
    top: -8px;
    width: 100% !important;
}
.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar {
    visibility: visible;
    opacity: 1;
    pointer-events: none;
    transition: visibility 1s, opacity 1s, transform 1s;
    transform: translateY(60px);
}
.video-js.vjs-16-9 {
    overflow: hidden;
}
.vjs-volume-panel.vjs-control.vjs-volume-panel-horizontal {
    width: 240px !important;
    justify-content: flex-start;
}
.vjs-slider-horizontal .vjs-volume-level:before {
    display: none !important;
}
.video-js .vjs-volume-panel .vjs-volume-control {
    visibility: visible;
    opacity: 1;
    width: 120px !important;
    height: 8px;
    margin-left: -1px;
}
.vjs-volume-bar.vjs-slider-horizontal {
    width: 100%;
    height: 8px;
    background: rgb(235 235 235) !important;
}
.vjs-slider-horizontal .vjs-volume-level {
    height: 8px;
}
.video-js .vjs-volume-level {
    background-color: #871b1e;
}
.vjs-current-time, .vjs-duration, .vjs-remaining-time {
    display: none !important;
}
.vjs-fullscreen-control + .vjs-chromecast-button{
    margin: 0 !important;
}
.vjs-chromecast-button,
.vjs-fullscreen-control {
    margin-left: auto !important;
}
.video-js .vjs-volume-panel .vjs-volume-control.vjs-volume-horizontal {
    width: 0 !important;
    transition-duration: 0.15s;
}
.vjs-volume-panel:hover .vjs-volume-control.vjs-control.vjs-volume-horizontal {
    width: 120px !important;
    transition-duration: 0.15s;
}