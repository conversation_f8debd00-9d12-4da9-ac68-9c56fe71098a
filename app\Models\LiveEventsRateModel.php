<?php namespace App\Models;

use CodeIgniter\Model;

class LiveEventsRateModel extends Model
{
    protected $table = 'liveevents_rate';
	protected $allowedFields = ['class_id', 'date', 'rate', 'user_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}