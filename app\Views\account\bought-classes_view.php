<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="favs-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="py-0 px-100">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 py-6 bottom-border">
                    <h2 class="h3 flex aic jcsb line-height-small">My Bought Classes</h2>
<?php
$all_bought_count=0;
foreach($all_bought as $single){
    $data['single'] = $single;
    if(($single['rented'] == 1 AND $single['expiry_rent_date'] > date('Y-m-d H:i:s')) OR $single['purchased'] == 1){
        $all_bought_count++;
    }
}
?>
                    <h3 class="light f-14"><span class="num_favs"><?php echo $all_bought_count; ?></span> videos</h3>
                </div>
            </div>
    </section>
    <section class="pb-2 px-100">
        <div class="container-fluid">
            <div class="row big-big-gap">
<?php
foreach($all_bought as $single){
    $data['single'] = $single;
    // if(($single['rented'] == 1 AND $single['expiry_rent_date'] > date('Y-m-d H:i:s')) OR $single['purchased'] == 1){
        echo view('front/classes/single_class_grid.php', $data);
    // }
}
?>
            </div>
        </div>
    </section>
</main>

<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
// $(document).ready(function(){
//     setTimeout(function(){
        // to_timezone()
//     }, 1000);
// });
</script>
</body>
</html>