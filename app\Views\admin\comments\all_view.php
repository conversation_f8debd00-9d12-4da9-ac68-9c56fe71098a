<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 20px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}
.comments-form textarea {
	line-height: 1.8;
	padding: 25px 25px 5px;
	border: 1px solid #f0f0f0 !important;
	width: 100%;
}
.comments-list .checkbox.contact-forms label {
	position: absolute;
	top: 41px;
}
.comments-list .checkbox.contact-forms {
	width: 40px;
    min-width: 40px;
}
@media screen and (max-width: 960px) {
    .comments-list .checkbox.contact-forms label {position: absolute; top: 20px; left: 0;}
    .comments-list .class-item {padding: 0 0 0 30px; display: block !important;}
    .single-comment-action {margin-bottom:20px; }
    .single-comment-action p.pr-5.mr-5 {padding-right:0 !important; margin-right:0 !important;}  
    .single-comment-action .flex {flex-direction:column;}
    .single-comment-action form.comments-form .form-options .buttons {margin-top: 15px !important;}
    .single-comment-action form.comments-form .form-options .flex {flex-direction:initial !important;}
    .approvecomment {margin-bottom:25px !important; max-width: 110px; margin-right: auto !important; margin-left: initial !important;}
    .table.comments-list .table-row .class-item + .class-item {padding-left: 30px;}
    .single-comment-action .comments-greytxt a, .single-comment-action .comments-greytxt span {line-height:1.8;}
    .ls-50 {
        letter-spacing: 0.05em;
    }
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb page-title">
                <h1 class="h3">All Comments</h1>
                <?php if(session('super_admin') == 1){ ?>
                <a href="/admin/comments" class="btn black-bg white ml-auto">Unapproved</a>
                <?php } ?>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
            <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $comment_count == 1 ? $comment_count . ' Comment' : $comment_count . ' Comments'; ?></h5>
            <div class="flex aic jcsb">
            <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/comments/sort_by/comments.created_at/desc" class="link midGray" title="">Date Uploaded</a></li>
                        <li><a href="admin/comments/sort_by/user_name/asc" class="link midGray" title="">Ascending</a></li>
                        <li><a href="admin/comments/sort_by/user_name/desc" class="link midGray" title="">Descending</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/comments/search" method="POST" class="search-form <?php echo (isset($search_term) AND $search_term != '') ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input f-14" value="<?php echo (isset($search_term) AND $search_term != '') ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
                </div>
            </div>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple normalRed" data-table="comments" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>
            </div>
            <hr class="mt-2 mb-0">
            <div class="container px-0">
                <div class="col-12 px-0">
                    <div class="table comments-list">
<?php
foreach($all_comments as $single){
?>
                        <div class="table-row single-comment bottom-border">
                            <div class="class-item flex aic" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="<?php echo (isset($single['type']) AND $single['type'] != '') ? $single['type'] : 'classes'; ?>">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <div class="single-comment-action line-height-small">
                                    <p class="f-12 pr-5 mr-5 medium mb-05"><?php echo $single['message'];?></p>
                                    <div class="flex comments-greytxt">
                                        <span class="midGray f-1">
                                            Commented on:&nbsp; 
                                            <?php
                                            switch ($single['type']) {
                                                case 'classes':
                                                    $class = class_info($single['class_id']);
                                                    break;
                                                case 'videos':
                                                    $class = video_info($single['class_id']);
                                                    break;
                                                case 'exercises':
                                                    $class = exercise_info($single['class_id']);
                                                    break;
                                                case 'courses':
                                                    $class = course_info($single['class_id']);
                                                    break;
                                                case 'courses_videos':
                                                    $class = course_video_info($single['class_id']);
                                                    break;
                                                default:
                                                    $class = class_info($single['class_id']);
                                                    break;
                                            }
                                            if(isset($class['id']) AND $class['id'] != '' AND $class['id'] != 0){
                                                if(isset($single['type']) AND $single['type'] == 'courses_videos'){
                                                    $class_url = "/courses/" . ($single['course_slug'] != '' ? $single['course_slug'] . '/' : $single['type']) . "/" . $class['slug'];
                                                    $tb = 'target="_blank"';
                                                }else{
                                                    $class_url = "/admin/" . $single['type'] . "/edit/" . $class['id'];     
                                                    $tb = 'target="_blank"';
                                                }
                                            }else{
                                                $class_url = "javascript:;";
                                                $tb = '';                                           
                                            }
                                            ?>
                                        </span>
                                        <a href="<?php echo $class_url; ?>" class="normal f-1" <?php echo $tb; ?> data-course_slug="<?php echo (isset($single['course_slug']) AND $single['course_slug'] != '') ? $single['course_slug'] : ''; ?>"><?php echo isset($class['title']) ? $class['title'] : 'no title'; ?></a>
                                        </span>
                                        <span class="midGray f-1"><?php echo (isset($single['user_name']) AND $single['user_name'] != '') ? ',&nbsp;by: ' . $single['user_name'] : 'NO NAME'; ?>, </span>
                                        <span class="midGray f-1"><?php echo (isset($single['date']) AND $single['date'] != '') ? '&nbsp;' . date('m/d/Y H:i:s', strtotime($single['date'])) : ''; ?> </span>
                                        <span class="midGray f-1" style="display:none;"><?php echo 'in: ' . $single['type']; ?>, </span>
                                    </div>
                                    <!-- <span class="midGray mr-1 f-1 semibold"> -->
                                    <?php if(isset($single['status']) AND $single['status'] == 0 AND $single['parent'] == 0){ ?>
                                    <a href="javascript:;" onclick="insert_form($(this))" class="link link-black normal f-12 text-underline" data-class_id="<?php echo (isset($single['class_id']) AND $single['class_id'] != '') ? $single['class_id'] : ''; ?>">Reply</a>
                                    <?php } ?>
                                    <?php if($single['user_id'] != session('admin') AND $single['teacher_replied'] != -1 AND session('super_admin') == 0 AND $single['replied_child_comments'] = 0){ ?>
                                    <a href="javascript:;" onclick="seen_comment($(this))" class="link link-midGray midGray normal ml-1 f-12 text-underline" data-class_id="<?php echo (isset($single['class_id']) AND $single['class_id'] != '') ? $single['class_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">Seen</a>
                                    <?php } ?>
                                    <!-- <a href="/<?php // echo $single['type']; ?>/<?php // echo $class['slug']; ?>#comment_<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="normal" target="_blank">Reply</a> -->
                                    <!-- </span> -->
                                </div>
                                <?php if(isset($single['status']) AND $single['status'] == 1){ ?>
                                <div class="flex flex-column ml-auto text-right f-1 pr-2 approvecomment">
                                    <a href="javascript:;" class="btn btn-xs red-bg white f-10"" onclick="approve(<?php echo $single['id']; ?>, <?php echo $single['parent']; ?>)">APPROVE</a>
                                </div>
                                <?php } ?>
                            </div>
                        <?php
                        if(count($single['my_replies']) > 0){
                            foreach($single['my_replies'] as $reply){
                        ?>
                            <div class="class-item flex aic" data-row-id="row_<?php echo (isset($reply['id']) AND $reply['id'] != '') ? $reply['id'] : ''; ?>" data-id="<?php echo (isset($reply['id']) AND $reply['id'] != '') ? $reply['id'] : ''; ?>" data-type="<?php echo (isset($reply['type']) AND $reply['type'] != '') ? $reply['type'] : 'classes'; ?>">
                                <div class="single-comment-action">
                                    <p class="f-12 pr-5 mr-5 medium"><?php echo $reply['teacher_replied'] == session('admin') ? 'My reply: ' : 'Reply: ' ?><?php echo $reply['message']; ?></p>
                                    <div class="flex">
                                        <span class="midGray f-1">
                                            <?php echo (isset($reply['user_name']) AND $reply['user_name'] != '') ? ' by: ' . $reply['user_name'] : 'NO NAME'; ?>
                                        </span>
                                        <span class="midGray f-1 "><?php echo (isset($reply['date']) AND $reply['date'] != '') ? ', ' . date('m/d/Y H:i:s', strtotime($reply['date'])) : ''; ?></span>
                                    </div>
                                    <?php if($reply['teacher_replied'] != session('admin')){ ?>
                                    <a href="javascript:;" onclick="insert_form($(this))" class="link link-black normal f-12 text-underline" data-class_id="<?php echo (isset($reply['class_id']) AND $reply['class_id'] != '') ? $reply['class_id'] : ''; ?>">Reply</a>
                                    <?php } ?>
                                    <?php if($reply['user_id'] != session('admin') AND $reply['teacher_replied'] != -1 AND session('super_admin') == 0){ ?>
                                    <a href="javascript:;" onclick="seen_comment($(this))" class="link link-midGray midGray normal ml-1 f-12 text-underline" data-class_id="<?php echo (isset($reply['class_id']) AND $reply['class_id'] != '') ? $reply['class_id'] : ''; ?>" data-id="<?php echo (isset($reply['id']) AND $reply['id'] != '') ? $reply['id'] : ''; ?>">Seen</a>
                                    <?php } ?>
                                </div>
                                <?php if(isset($reply['status']) AND $reply['status'] == 1 AND $reply['teacher_replied'] != session('admin')){ ?>
                                <div class="flex flex-column ml-auto text-right f-1 pr-2 approvecomment">
                                    <a href="javascript:;" class="btn btn-xs red-bg white f-10"" onclick="approve(<?php echo $reply['id']; ?>, <?php echo $reply['parent']; ?>)">APPROVE</a>
                                </div>
                                <?php } ?>
                            </div>
                            <?php
                                if(isset($reply['my_replies_child']) AND is_array($reply['my_replies_child']) AND count($reply['my_replies_child']) > 0){
                                    foreach($reply['my_replies_child'] as $reply2) {
                            ?>
                            <div class="class-item flex aic" data-row-id="row_<?php echo (isset($reply2['id']) AND $reply2['id'] != '') ? $reply2['id'] : ''; ?>" data-id="<?php echo (isset($reply2['id']) AND $reply2['id'] != '') ? $reply2['id'] : ''; ?>" data-type="<?php echo (isset($reply2['type']) AND $reply2['type'] != '') ? $reply2['type'] : 'classes'; ?>">
                                <div class="single-comment-action">
                                    <p class="f-12 pr-5 mr-5 medium"><?php echo $reply2['teacher_replied'] == session('admin') ? 'My reply: ' : 'Reply: ' ?><?php echo $reply2['message']; ?></p>
                                    <div class="flex">
                                        <span class="midGray f-1">
                                            <?php echo (isset($reply2['user_name']) AND $reply2['user_name'] != '') ? ' by: ' . $reply2['user_name'] : 'NO NAME'; ?>
                                        </span>
                                        <span class="midGray f-1 "><?php echo (isset($reply2['date']) AND $reply2['date'] != '') ? ', ' . date('m/d/Y H:i:s', strtotime($reply2['date'])) : ''; ?></span>
                                    </div>
                                    <?php if($reply2['teacher_replied'] != session('admin') AND $reply2['parent'] == 0){ ?>
                                    <a href="javascript:;" onclick="insert_form($(this))" class="link link-black normal f-12 text-underline" data-class_id="<?php echo (isset($reply2['class_id']) AND $reply2['class_id'] != '') ? $reply2['class_id'] : ''; ?>">Reply</a>
                                    <?php } ?>
                                    <?php if($reply2['user_id'] != session('admin') AND $reply2['teacher_replied'] != -1 AND session('super_admin') == 0){ ?>
                                    <a href="javascript:;" onclick="seen_comment($(this))" class="link link-midGray midGray normal ml-1 f-12 text-underline" data-class_id="<?php echo (isset($reply2['class_id']) AND $reply2['class_id'] != '') ? $reply2['class_id'] : ''; ?>" data-id="<?php echo (isset($reply2['id']) AND $reply2['id'] != '') ? $reply2['id'] : ''; ?>">Seen</a>
                                    <?php } ?>
                                </div>
                                <?php if(isset($reply2['status']) AND $reply2['status'] == 1 AND $reply2['teacher_replied'] != session('admin')){ ?>
                                <div class="flex flex-column ml-auto text-right f-1 pr-2 approvecomment">
                                    <a href="javascript:;" class="btn btn-xs red-bg white f-10"" onclick="approve(<?php echo $reply2['id']; ?>, <?php echo $reply2['parent']; ?>)">APPROVE</a>
                                </div>
                                <?php } ?>
                            </div>
                            <?php
                                    }
                                }
                            }
                        }
                        ?>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($comment_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_comments); ?><span class="midGray mx-1">of <?php echo $comment_count; ?></span>

                    <a href="admin/comments/<?php echo $segment == 'filter' ? 'filter' : 'all'; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>

                    <a href="admin/comments/<?php echo $segment == 'filter' ? 'filter' : 'all'; ?>/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_comments) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_comments)) == $comment_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<template id="reply-form-template">
    <form action="admin/comments/save" method="POST" class="comments-form mt-2" style="max-width: 600px;width: 100%;">
        <div class="textarea">
            <textarea name="comment" id="comment" class="comments-field f-12" placeholder="Your reply..."></textarea>
        </div>
        <div class="form-options">
            <div class="flex aic jcr">
                <div class="buttons mt-3 flex">
                    <input type="hidden" name="type" value="classes">
                    <!-- <input type="hidden" name="class_title" value="Full Body #5 by Sebastien">
                    <input type="hidden" name="teacher_name" value="Carinaa Nesto">
                    <input type="hidden" name="teacher_email" value="<EMAIL>"> -->
                    <button type="button" class="link link-midGray midGray f-12 f-10-mob normal no-underline ls-50" style="background:none !important;" onclick="$(this).closest('form').remove()">Cancel</button>
                    <button type="button" onclick="submit_reply($(this).closest('form'))" class="link link-black black f-12 f-10-mob semibold ml-2 no-underline ls-50" style="background:none !important;">Send</button>
                </div>
            </div>
        </div>
    </form>
</template>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
console.log('<?php echo session('per_page'); ?>');

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/textarea.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('per_page') == "") ? 25 : session('per_page'); ?>;
var sort_by = '<?php echo (session('comment_sort') == "") ? 'comment.created_at/desc' : session('comment_sort'); ?>';
var search = '<?php echo (session('comment_search') == "") ? '' : session('comment_search'); ?>';
var order = '<?php echo (session('comment_sort') == "") ? 'comment.created_at/desc' : session('comment_sort'); ?>';
/* COMMENTS FORM */
function approve(id, parent){
    $.ajax({
        type: "POST",
        url: 'admin/comments/approve',
        data: {
            id,
            parent,
            status: 0
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Comment approved', 'success');
                setTimeout(function(){
                    window.location.reload();
                }, 1000);
            }else{
                console.log('NO SUCCESS');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
        }
    });
};
function seen_comment(xx){
    var id = xx.data('id');

    $.ajax({
        type: "POST",
        url: 'admin/comments/seen',
        data: {
            id,
            teacher_replied: -1
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                xx.hide();
                // setTimeout(function(){
                //     window.location.reload();
                // }, 300);
            }else{
                console.log('NO SUCCESS');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger');
        }
    });
};
/* COMMENTS FORM */
var class_id = 0;
var user_id = <?php echo session('admin'); ?>;
var teacher_replied = <?php echo session('admin'); ?>;
const date = "2024-01-26";

var form_template = $('#reply-form-template').html();
function insert_form(xx){
    class_id = xx.data('class_id');
    console.log(xx.closest('.single-comment-action').find('form').length);
    if(xx.closest('.single-comment-action').find('form').length == 0){
        $('.comments-form').remove();
        xx.parent().append(form_template);
    }
    autosize(document.querySelectorAll('textarea'));
    $('textarea').on('keyup', function(){
        if($(this).height() > 40){
            $(this).css({"padding-bottom":"25px"});
        }else{
            $(this).css({"padding-bottom":"8px"});
        }
    });
}
function submit_reply(form){
	console.log('comments-form-ajax submit');
	// e.preventDefault();
	// var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    var parent = form.closest('.class-item').data('id');
    var message = form.find('.comments-field').val();
    var status = 0;
    // var class_title = form.find('[name="class_title"]').val();
    // var teacher_name = form.find('[name="teacher_name"]').val();
    // var teacher_email = form.find('[name="teacher_email"]').val();
    var type = form.closest('.class-item').data('type');
    var comment_id = parent;
    
    button.addClass('btn--loading');

    console.log("class_id: " + class_id);
    console.log("parent: " + parent);
    console.log("message: " + message);
    console.log("user_id: " + user_id);

    $.ajax({
        type: "POST",
        url: url,
        data: {
            comment_id,
            class_id,
            parent,
            // class_title,
            // teacher_name,
            // teacher_email,
            message,
            status,
            type,
            teacher_replied,
            user_id
        },
        dataType: "json",
        success: function(data) {
            console.log(data);
            if(data.success){
                console.log('SUCCESS');
                app_msg('Your comment is submited', 'success', 2500);
                button.removeClass('btn--loading');
                // form.closest('.single-comment').fadeOut(200);
                form.remove();
                setTimeout(function(){
                    window.location.reload();
                }, 150);
            }else{
                console.log('NO SUCCESS');
                app_msg("Something went wrong", 'danger', 2500);
                // $.each(data.json, function(key, val){
                //     $('.contact-form [name=' + key + "]").addClass('error');
                // });
                button.removeClass('btn--loading');
            }
        },
        error: function(result) {
            console.log('ERROR WITH PHP');
            console.log(result.responseJSON);
            app_msg('Something went wrong! Please try again', 'danger', 2500, 1);
            button.removeClass('btn--loading');
        }
    });
};
/* COMMENTS FORM */

</script>
</body>
</html>