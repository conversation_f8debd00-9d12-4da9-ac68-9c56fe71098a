<?php namespace App\Models;

use CodeIgniter\Model;

class ClassesViewModel extends Model
{
    protected $table = 'classes_views';
	protected $allowedFields = ['class_id', 'user_id', 'date'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}