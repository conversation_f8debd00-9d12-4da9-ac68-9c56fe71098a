<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="favs-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root" class="watchlater-wrap">
    <section class="py-0">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="title-filter">
                        <h2 class="f-24 semibold line-height-small text-uppercase inner-title mt-1 mb-1 mt-mob-0 mb-mob-0">watch later</h2>
                    </div>
                </div>
            </div>
    </section>
<?php if(isset($all_watch_later) AND is_array($all_watch_later) AND count($all_watch_later) > 0){ ?>
    <section class="pt-0 watch-hist-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="top-border bottom-border mb-4 py-2 f-12 midGray line-height-small"><?php echo (int)$all_watch_later['count_all'] . ((int)$all_watch_later['count_all'] > 2 ? ' Videos' : ' Video'); ?></div>
                </div>
            </div>
            <div class="row watch-hist-wrap">
            <?php
                if(isset($all_watch_later['classes']) AND is_array($all_watch_later['classes']) AND count($all_watch_later['classes']) > 0){
                    foreach($all_watch_later['classes'] as $single){
                        $data['single'] = $single;
                        echo view('front/classes/single_class_watch-later.php', $data);
                    }
                }
            ?>
            <?php
                if(isset($all_watch_later['exercises']) AND is_array($all_watch_later['exercises']) AND count($all_watch_later['exercises']) > 0){
                    foreach($all_watch_later['exercises'] as $single){
                        $data['single'] = $single;
                        echo view('front/classes/single_class_watch-later.php', $data);
                    }
                }
            ?>
            <?php
                if(isset($all_watch_later['courses']) AND is_array($all_watch_later['courses']) AND count($all_watch_later['courses']) > 0){
                    foreach($all_watch_later['courses'] as $single){
                        $data['single'] = $single;
                        echo view('front/classes/single_class_watch-later.php', $data);
                    }
                }
            ?>
            </div>
            <?php if(count($pager->links()) > 1){ ?>
            <div class="row">
                <div class="col-12">
                    <?php $pager->setSurroundCount(2) ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                        <?php if ($pager->hasPrevious()){ ?>
                            <li>
                                <a href="<?php echo $pager->getPrevious() ?>" aria-label="<?php echo lang('Pager.previous') ?>">
                                    <span aria-hidden="true">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 24.7 40">
                                            <path id="Path_4917" data-name="Path 4917" d="M35.3,24.7,20,9.433,4.7,24.7,0,20,20,0,40,20Z" transform="translate(0 40) rotate(-90)" fill="#969696" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        <?php } ?>

                        <?php foreach ($pager->links() as $link){ ?>                                
                            <li <?php echo $link['active'] ? 'class="active"' : '' ?>>
                                <a href="<?php echo $link['uri'] ?>">
                                    <?php echo $link['title'] ?>
                                </a>
                            </li>
                        <?php } ?>

                        <?php if ($pager->hasNext()){ ?>
                            <li>
                                <a href="<?php echo $pager->getNext() ?>" aria-label="<?php echo lang('Pager.next') ?>">
                                    <span aria-hidden="true">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 24.7 40">
                                            <path id="Path_4916" data-name="Path 4916" d="M470.3,1414.3,455,1429.562,439.7,1414.3,435,1419l20,20,20-20Z" transform="translate(-1414.295 475) rotate(-90)" fill="#969696" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        <?php } ?>
                        </ul>
                    </nav>
                </div>
            </div>
            <?php } ?>
        </div>
    </section>
<?php }else{ ?>
    <section class="pt-0 watch-hist-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="top-border py-4 f-14 midGray line-height-small">This list has no videos.</div>
                </div>
            </div>
        </div>
    </section>

<?php } ?>

</main>

<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
// $(document).ready(function(){
//     setTimeout(function(){
        // to_timezone()
//     }, 1000);
// });
$('[data-time]').each(function(){
    var time = $(this).data('time');
    if(time != ''){
        $(this).find('.video-container').prepend('<span class="current_video_state" style="width: ' + time + '%"></span>');
    };
});
function watch_later_remove(xx, id){
    if(id != '' && id != 'undefined'){
        $.ajax({
            type: 'POST',
            url: 'account/watch_later_remove',
            data: {
                id
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    // location.reload(); 
                     xx.closest('.watch_later_item').slideUp();
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
</script>
</body>
</html>