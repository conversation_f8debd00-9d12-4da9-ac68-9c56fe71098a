<base href="<?php echo base_url();?>">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<meta name="author" content="Lagree On Demand">
<meta name="keywords" content="<?php echo isset($current['seo_keywords']) ? $current['seo_keywords'] : 'Lagree, ondemand, fitness'; ?>">
<meta name="description" content="<?php echo isset($current['seo_description']) ? $current['seo_description'] : (isset($current['description']) ? $current['description'] : ''); ?>">
<meta name="language" content="English">
<meta name="copyright" content="Lagree On Demand">

<title><?php echo (isset($current['seo_title']) AND $current['seo_title'] != '') ? $current['seo_title'] . ' - ' : ((isset($current['title']) AND $current['title'] != '') ? $current['title'] . ' - ' : ''); ?>Lagree On Demand</title>
<!-- facebook -->
<meta property="og:site_name" content="Lagree On Demand">
<meta property="og:locale" content="en_US">
<meta property="og:type" content="article">
<meta property="og:description" content="<?php echo isset($current['seo_description']) ? $current['seo_description'] : (isset($current['description']) ? $current['description'] : ''); ?>">
<meta property="og:image" content="<?php echo base_url(); ?>/<?php echo isset($current['image']) ? $current['image'] : ''; ?>">
<meta property="og:title" content="<?php echo (isset($current['seo_title']) AND $current['seo_title'] != '') ? $current['seo_title'] : $current['title']; ?>">
<meta property="og:url" content="<?php echo current_url(); ?>">
<!-- twitter -->
<meta property="twitter:card" content="article">
<meta property="twitter:url" content="<?php echo current_url(); ?>">
<meta property="twitter:title" content="<?php echo (isset($current['seo_title']) AND $current['seo_title'] != '') ? $current['seo_title'] : $current['title']; ?>">
<meta property="twitter:description" content="<?php echo isset($current['seo_description']) ? $current['seo_description'] : (isset($current['description']) ? $current['description'] : ''); ?>">
<meta property="twitter:image" content="<?php echo base_url(); ?>/<?php echo isset($current['image']) ? $current['image'] : ''; ?>">
<!-- tab color -->
<meta name="theme-color" content="#000000">
<meta name="msapplication-navbutton-color" content="#000000">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- FAVICON -->
<link rel="apple-touch-icon" sizes="180x180" href="/images/favicons/apple-touch-icon.png?v=<?php echo $_ENV['version']; ?>">
<link rel="icon" type="image/png" sizes="32x32" href="/images/favicons/favicon-32x32.png?v=<?php echo $_ENV['version']; ?>">
<link rel="icon" type="image/png" sizes="16x16" href="/images/favicons/favicon-16x16.png?v=<?php echo $_ENV['version']; ?>">
<link rel="manifest" href="/images/favicons/site.webmanifest?v=<?php echo $_ENV['version']; ?>">
<link rel="mask-icon" href="/images/favicons/safari-pinned-tab.svg?v=<?php echo $_ENV['version']; ?>" color="#000000">
<link rel="shortcut icon" href="/images/favicons/favicon.ico?v=<?php echo $_ENV['version']; ?>">
<meta name="msapplication-TileColor" content="#000000">
<meta name="msapplication-config" content="/images/favicons/browserconfig.xml?v=<?php echo $_ENV['version']; ?>">
<meta name="theme-color" content="#ffffff">

<link hreflang="en-GB" rel="alternate" href="<?php echo base_url(); ?>" />
<link rel="canonical" href="<?php echo current_url(); ?>" />

<link href="css/base.css?v=<?php echo $_ENV['version']; ?>" rel="stylesheet">
<link href="css/style.css?v=<?php echo $_ENV['version']; ?>" rel="stylesheet">
<script>
var base_url = '<?php echo base_url(); ?>';
var current_url = '<?php echo current_url(); ?>';
var subscription = "<?php echo session('subscription'); ?>";
var user_id = <?php echo session('user') != NULL ? session('user') : 0; ?>;
</script>
<!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->

<?php echo isset($settings['header_js']) ? $settings['header_js'] : ''; ?>
<?php echo isset($settings['google_analytics']) ? $settings['google_analytics'] : ''; ?>
<?php // echo (isset($current['page_header_js']) AND $current['page_header_js'] != '') ? $current['page_header_js'] : ''; ?>
<!--<script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v5.0&appId=4419092568206174&autoLogAppEvents=1"></script>-->
<script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v5.0&appId=4156817854418922&autoLogAppEvents=1"></script>
<script src="https://apis.google.com/js/api:client.js"></script>
<script src="js/devices.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var parser = new UAParser();
var device = parser.getResult();
// document.getElementById('log').innerHTML = 'BROWSER: ' + result.browser.name + ' ' + result.browser.major + '<br>';
// document.getElementById('log').append = 'DEVICE: ' + result.device.model + ' ' + result.device.type + '<br>';
// document.getElementById('log').append = 'OS: ' + result.os.name + ' ' + result.os.version + '<br>';
if(typeof(navigator.userAgentData) != 'undefined'){
    navigator.userAgentData.getHighEntropyValues(["platformVersion"]).then(ua => {
        if (navigator.userAgentData.platform === "Windows") {
            const majorPlatformVersion = parseInt(ua.platformVersion.split('.')[0]);
            if (majorPlatformVersion >= 13) {
                console.log("Windows 11 or later");
                device.os.version = '11';
            }
        }
    });
}
setTimeout(function(){
    document.getElementById('os_name').value = device.os.name;
    document.getElementById('os_version').value = device.os.version;
    document.getElementById('browser_name').value = device.browser.name;
    document.getElementById('browser_version').value = device.browser.major;
    document.getElementById('device_model').value = device.device.model;
    document.getElementById('device_type').value = device.device.type;
    document.getElementById('device_vendor').value = device.device.vendor;
    document.getElementById('userAgent').value = navigator.userAgent;
}, 300);
</script>

<!-- Start of ChatBot (www.chatbot.com) code -->
<script type="text/javascript">
    window.__be = window.__be || {};
    window.__be.id = "67b2fee68a03b0000759ffa9";
    (function() {
        var be = document.createElement('script'); be.type = 'text/javascript'; be.async = true;
        be.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'cdn.chatbot.com/widget/plugin.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(be, s);
    })();
</script>
<noscript>You need to <a href="https://www.chatbot.com/help/chat-widget/enable-javascript-in-your-browser/" rel="noopener nofollow">enable JavaScript</a> in order to use the AI chatbot tool powered by <a href="https://www.chatbot.com/" rel="noopener nofollow" target="_blank">ChatBot</a></noscript>
<!-- End of ChatBot code -->
 
<style>
.lodacc-menu {
	position: sticky;
	/*top: 70px;*/
	background: #fff;
	z-index: 11;
    transition: padding 0.25s ease-in-out;
}
.accountmenu {
    transition: padding 0.25s ease-in-out;
}
.video-tags {
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 11;
	display: flex;
	flex-direction: column;
	gap: 10px;
	align-items: flex-start;
}
.video-tags span {
	display: inline-block;
	background: #fff;
	font-size: 10px;
	font-weight: 500;
	line-height: 1;
	padding: 5px 10px;
	border-radius: 20px;
}
</style>