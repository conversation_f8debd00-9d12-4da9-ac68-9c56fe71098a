<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="collection-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <!-- <div class="px-100 small-header-image" style="background: url(images/single-collection-header-bg.jpg) no-repeat center center / cover"> -->
    <section class="pt-5 pb-5 px-100 pt-mob-3">
        <div class="container1030 px-mob-1">
            <div class="row aic position-relative playlist-header">
                <div class="playlist-option dropdown">
                    <span class="option-btn" data-dropdown>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                            <g id="option-icon" transform="translate(-1501 -625)">
                                <g id="Rectangle_2141" data-name="Rectangle 2141" transform="translate(1501 625)" fill="none" stroke="#f0f0f0" stroke-width="1">
                                    <rect width="30" height="30" rx="15" stroke="none"/>
                                    <rect x="0.5" y="0.5" width="29" height="29" rx="14.5" fill="none"/>
                                </g>
                                <circle id="Ellipse_50" data-name="Ellipse 50" cx="1" cy="1" r="1" transform="translate(1511 639)"/>
                                <circle id="Ellipse_51" data-name="Ellipse 51" cx="1" cy="1" r="1" transform="translate(1515 639)"/>
                                <circle id="Ellipse_52" data-name="Ellipse 52" cx="1" cy="1" r="1" transform="translate(1519 639)"/>
                            </g>
                        </svg>
                    </span>
                    <ul class="dropdown-menu drop-right">
                        <li><a href="javascript:;" class="f-12" data-popup="share-popup" data-popup-title="Share This Playlist With Your Friends." title="">Share</a></li>
                    </ul>
                </div>
                <div class="col-12 flex aic flex-column-mob ail-mob">
                    <span class="position-relative playlist-big-image-content mr-0 mr-mob-0">
                        <img src="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : 'images/playlist-bg-x2.jpg'; ?>" alt="" class="img-fluid playlist-big-image" />
                        <?php echo (isset($current['image']) AND $current['image'] != '') ? '' : '<span class="playlist-empty-title">' . $current['title'] . '</span>'; ?>
                    </span>
                    <div class="playlists-big-right w100 position-relative px-5 flex flex-column ail jcc px-mob-0 mt-mob-0 pb-mob-3 pt-mob-2">
                        <h2 class="mb-15 h3 line-height-small"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?> PLAYLIST</h2>
                        <?php if(isset($current['content']) AND $current['content'] != ''){ ?>
                            <p class="light f-14 line-height-normal mb-15"><?php echo (isset($current['content']) AND $current['content'] != '') ? $current['content'] : ''; ?></p>
                        <?php } ?>
                        <p class="light f-14 line-height-small mb-3 midGray mb-mob-3"><?php echo count($current['all_playlists_classes']); ?> Classes (<?php echo only_minutes($current['total_duration']); ?> minutes), <br class="mobile" /><?php echo (isset($single['users_name']) AND $single['users_name'] != '') ? 'By: ' . $single['users_name'] : 'By: LagreeOD staff'; ?></p>
                        <?php if(!empty($logged_user)){ ?>
                            <a href="javascript:;" class="btn black-bg white add_playlist" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" title="<?php echo $added ? 'REMOVE FROM MY LIST' : 'ADD TO MY LIST'; ?>"><?php echo $added ? 'REMOVE FROM MY LIST' : 'ADD TO MY LIST'; ?></a>
                        <?php }else{ ?>
                            <a href="javascript:;" data-popup="login-popup" class="btn black-bg white" title="LOGIN OR SIGNUP">LOGIN OR SIGNUP</a>
                        <?php } ?>
                        </div>
                </div>
            </div>
            <hr class="mt-5 mb-0 mt-mob-1">
            <div class="row big-big-gap">
<?php
foreach($current['all_playlists_classes'] as $single){
?>
                <div class="col-12 <?php echo $single['slug_type']; ?>">
                    <div class="single-playlist-list-item <?php echo $single['slug_type']; ?>">
                        <a href="playlists/<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : ''; ?>/<?php echo substr($single['slug_type'], 0, 1); ?>_<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container">
                            <?php
                                if(isset($single['watched']) AND $single['watched'] == 1){
                            ?>
                                <span class="watched f-14 bold lettet-50 white flex aic"><img src="images/watch-again.svg" alt="" style="width: 20px;object-fit: cover;height: 20px;object-position: left center;" class="img-fluid mr-1" /></span>
                            <?php
                                }else{
                            ?>
                                <span class="play-button"><span></span></span>
                            <?php } ?>
                            <div class="image-overlay h100"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" <?php echo (isset($single['watched']) AND $single['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?> /></div>
                        </a>
                        <div class="video-text-container pr-mob-1">
                            <a href="playlists/<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : ''; ?>/<?php echo substr($single['slug_type'], 0, 1); ?>_<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>">
                                <h4 class="flex jcsb bold mb-1 f-12 ail"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h4>
                            </a>
                            <p class="midGray f-12 mb-0 light line-height-small">
                                <span class="d-inline-block light line-height-small">
                                    by:
                                    <?php if(isset($single['teach_slug']) AND $single['teach_slug'] != ''){ ?>
                                        <a href="teachers/<?php echo (isset($single['teach_slug']) AND $single['teach_slug'] != '') ? $single['teach_slug'] : ''; ?>" class="link link-black black text-underline d-inline-block"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : 'NO TEACHER'; ?></a>,
                                    <?php } ?>
                                    <?php echo (isset($single['duration']) AND $single['duration'] != '') ? '<span class="d-inline-block light line-height-small">' . only_minutes($single['duration']) . ' minutes</span>' : ''; ?>
                                </span>
                            </p>
                        </div>
                        <?php if(!empty($logged_user)){ ?>
                        <a href="javascript:;" class="add_class_to_playlist" onclick="read_playlists_popup($(this))" data-popup="add-to-playlist-popup" data-class_id="<?php echo $single['id']; ?>" data-class_type="<?php echo $single['type']; ?>" data-playlist_id="<?php echo $current['id']; ?>"  title="ADD CLASS TO PLAYLIST"></a>
                        <?php } ?>
                    </div>
                </div>
<?php
}
?>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
</body>
</html>