<?php namespace App\Models;

use CodeIgniter\Model;
use App\Models\SubscribersModel;

class EmailModel extends Model
{
	public function send_email($data = NULL){
        $emailService = \Config\Services::email();
        $emailService->setTo($data['email']);
        $emailService->setFrom('<EMAIL>', 'Lagree On Demand - ' . $data['subject']);
        // $emailService->setBCC('<EMAIL>');
        $emailService->setSubject($data['subject'] . ' - Lagree On Demand');
        $emailService->setMessage($data['message']);

        if($emailService->send()){
            $response['msg'] = 'Email successfully sent';
            $response['success'] = TRUE;
        }else{
            $response['success'] = FALSE;
            $response['msg'] = $emailService->printDebugger(['headers']);
            // print_r($response);
        }
        return $response;
    }
	public function send_verification_link($user_id = NULL)
	{
        $parser = \Config\Services::parser();
        $response['success'] = FALSE;
		$subscribers_model = new SubscribersModel();
		$subscriber = $subscribers_model->where(['id' => $user_id])->first();
		if (!$subscriber)
		{
			$response['message'] = 'No such subscriber.';
			return $response;
		}
        $data = [
            'firstname'   => $subscriber['firstname'],
            'verification_link' => base_url() .'/register/activate/' . md5($subscriber['created_at']) . '/' . md5($subscriber['id'])
        ];

        $msg = $parser->setData($data)->render('front/email_templates/email-confirm');

        $email = new \Config\Email();

        $emailService = \Config\Services::email();
        $emailService->setTo($subscriber['email']);
        $emailService->setFrom('<EMAIL>', 'Lagree On Demand');
        // $emailService->setBCC('<EMAIL>');
        $emailService->setSubject('Lagree On Demand - Confirm your Email');
        $emailService->setMessage($msg);

        if ($emailService->send()){
            $response['success'] = TRUE;
			$subscriber['status'] = 1;
			$subscribers_model->save($subscriber);
        }else{
            //$data = $emailService->printDebugger(['headers']);
            //$data = $emailService->printDebugger(['subject']);
            //$data = $emailService->printDebugger(['body']);
            //print_r($data);
			$response['message'] = 'Message not sent.';
        }
		return $response;
	}
	public function send_template($to, $reply = FALSE, $subject = "", $template_data = NULL, $template = NULL, $cc = NULL, $attachment = NULL, $attachment_filename = NULL)
	{
        $parser = \Config\Services::parser();
        $emailService = \Config\Services::email();

        $response['success'] = FALSE;
        $msg = $parser->setData($template_data)->render($template);

        $emailService->setTo($to);
        $emailService->setFrom('<EMAIL>', 'Lagree On Demand');
        if($reply){
            $emailService->setReplyTo($reply);
        }
        if(isset($cc) AND $cc != NULL AND $cc != ''){
            $emailService->setBCC($cc);
        }
        if($attachment != NULL AND $attachment != ''){
            $emailService->attach($attachment, 'attachment', $attachment_filename, 'application/pdf');
        }

        $emailService->setSubject($subject);
        $emailService->setMessage($msg);

        if($emailService->send()){
            $response['success'] = TRUE;
        }else{
            $response['message'] = "Problem sending email, please try again";
            $response['msg'] = $emailService->printDebugger(['headers']);
            // print_r($response);
        }

		return $response;
	}
}