<?php namespace App\Models;

use CodeIgniter\Model;

class ShopifyProductsModel extends Model
{
    protected $table = 'products_shopify';
	protected $allowedFields = ['product_id', 'variant_id', 'collection_id', 'title', 'description', 'image', 'price', 'url', 'status', 'handle', 'variants', 'options'];
	protected $returnType     = 'array';

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        // 'title'     => 'required|min_length[2]',
        // 'slug'        => 'required|alpha_dash|is_unique[events.slug,id,{id}]',
        // 'content'     => 'required',
        // 'template'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    function __construct()
    {
        parent::__construct();
		$this->shopify_config = array(
			'api_key' => $_ENV['shopify_api_key'],
			'password' => $_ENV['shopify_password'],
			'shopUrl' => $_ENV['shopify_ShopUrl']
		);
    }

    function single_collection($id = NULL)
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $api_url = 'https://' . $config['ApiKey'] . ':' . $config['Password'] . '@' . $config['ShopUrl'];
        $products_obj_url = $api_url . '/admin/api/2021-10/collections/' . $id .'.json';
        // $products_obj_url = $api_url . '/admin/products.json?limit=20&page='.($i+1);
        $products_content = @file_get_contents( $products_obj_url );
        $products_json = json_decode( $products_content, true );

        // echo '<pre>';
        // print_r($products_json);
        // die();
        $result['collection'] = $products_json['products'];

        $result['success'] = FALSE;
		$result['message'] = '';

        return $result;
	}

    function single_collection_products_sync($id = NULL)
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $api_url = 'https://' . $config['ApiKey'] . ':' . $config['Password'] . '@' . $config['ShopUrl'];
        $products_obj_url = $api_url . '/admin/api/2021-10/collections/' . $id .'/products.json?limit=250'; // admin/api/2021-10/products/5042742788234.json
        // $products_obj_url = $api_url . '/admin/products.json?limit=20&page='.($i+1);
        $products_content = @file_get_contents( $products_obj_url );
        $products_json = json_decode( $products_content, true );

        if(isset($products_json['products'])){
            foreach($products_json['products'] as $key => $single){
                $product = $this->single_product_api($single['id']);

                // echo '<pre>';
                // print_r($product);
                // die();

                if(strpos($single['tags'], 'no_shop') === FALSE AND $single['status'] == 'active'){
                    $result['products'][$key]['product_id']     = $single['id'];
                    $result['products'][$key]['variant_id']     = $product['product']['variants'][0]['id'];
                    $result['products'][$key]['collection_id']  = $id;
                    $result['products'][$key]['title']          = $single['title'];
                    $result['products'][$key]['description']    = $single['body_html'];
                    $result['products'][$key]['image']          = $single['image']['src'];
                    $result['products'][$key]['price']          = $product['product']['variants'][0]['price'];
                    $result['products'][$key]['handle']         = $single['handle'];
                    $result['products'][$key]['status']         = $single['status'];
                    $result['products'][$key]['variants']       = json_encode($product['product']['variants']);
                    $result['products'][$key]['options']        = json_encode($product['product']['options']);
                }
            }
        }

        $result['success'] = FALSE;
		$result['message'] = '';

        return $result;
	}

    function all_products_sync()
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $result['success'] = FALSE;
		$result['message'] = '';

        $api_url = 'https://' . $config['ApiKey'] . ':' . $config['Password'] . '@' . $config['ShopUrl'];
        $products_obj_url = $api_url . '/admin/api/2021-10/products.json?limit=250';
        // $products_obj_url = $api_url . '/admin/products.json?limit=20&page='.($i+1);
        $products_content = @file_get_contents( $products_obj_url );
        $products_json = json_decode( $products_content, true );

        if(isset($products_json['products'])){
            foreach($products_json['products'] as $key => $single){
                if(strpos($single['tags'], 'no_shop') === FALSE){
                    $result['products'][$key]['product_id']     = $single['id'];
                    $result['products'][$key]['variant_id']     = $single['variants'][0]['id'];
                    $result['products'][$key]['collection_id']  = "ALL";
                    $result['products'][$key]['title']          = $single['title'];
                    $result['products'][$key]['description']    = $single['body_html'];
                    $result['products'][$key]['image']          = $single['image']['src'];
                    $result['products'][$key]['price']          = $single['variants'][0]['price'];
                    $result['products'][$key]['handle']         = $single['handle'];
                    $result['products'][$key]['status']         = $single['status'];
                    $result['products'][$key]['variants']       = json_encode($single['variants']);
                    $result['products'][$key]['options']        = json_encode($single['options']);
                }
            }
            // echo '<pre>';
            // print_r($products_json);
            // die();
        }

        // echo '<pre>';
        // print_r($products_json);
        // die();

        // $result['products'] = $products_json['products'];
        // $result['pagination_url'] = $products_obj_url;

        return $result;
	}
    function single_product_api($id = NULL)
	{
        $config = array(
            'ShopUrl' => $this->shopify_config['shopUrl'],
            'ApiKey' => $this->shopify_config['api_key'],
            'Password' => $this->shopify_config['password'],
        );

        $result['success'] = FALSE;
		$result['message'] = '';

        $api_url = 'https://' . $config['ApiKey'] . ':' . $config['Password'] . '@' . $config['ShopUrl'];
        $products_obj_url = $api_url . '/admin/api/2021-10/products/' . $id .'.json'; // admin/api/2021-10/products/5042742788234.json
        // $products_obj_url = $api_url . '/admin/products.json?limit=20&page='.($i+1);
        $products_content = @file_get_contents( $products_obj_url );
        $products_json = json_decode( $products_content, true );

        // echo '<pre>';
        // print_r($products_json);
        // die();

        $result = $products_json;

        return $result;
	}

    function single_product($id = NULL)
	{
        $data = $this->where(["product_id" => $id])->first();

        return $data;
	}

    function all_shop_product($collection = 'ALL', $start = 0, $limit = 12, $order = 'price ASC')
	{
        $data = $this->query("SELECT * FROM products_shopify WHERE collection_id = '" . $collection . "' AND status = 'active' AND price > 0 ORDER BY " . $order . " LIMIT " . $start . ", " . $limit)->getResultArray();

        return $data;
	}

    function search($collection = 'ALL', $start = 0, $limit = 12, $order = 'price ASC')
	{
        $data = $this->query("SELECT * FROM products_shopify WHERE collection_id = '" . $collection . "' AND status = 'active' AND price > 0 ORDER BY " . $order . " LIMIT " . $start . ", " . $limit)->getResultArray();

        return $data;
	}
    function single_product_slug($slug = NULL)
	{

        $result = $this->where(["handle" => $slug, "collection_id" => "ALL", "status" => "active"])->first();
        if($result == NULL){
            $result = $this->where(["product_id" => $slug, "collection_id" => "ALL", "status" => "active"])->first();
        }

        return $result;
	}

    protected function prepare_data(array $data)
	{
		return $data;
	}

}