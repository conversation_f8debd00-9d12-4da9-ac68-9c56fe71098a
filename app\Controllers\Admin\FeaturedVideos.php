<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Featuredvideos extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('FeaturedVideosModel');
	}

    public function index()
    {
		$classes_model = model('ClassesModel');
		$videos_model = model('HowtoModel');
		$teachers_model = model('TeachersModel');

		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // echo '<pre>';
        // print_r($first_featured);
        // die();

        $data['all_featured']  = $this->model->findAll();
        $data['all_teachers']  = $teachers_model->all_teachers();
        $first_featured  = $this->model->where("id", 1)->first();
        if(isset($first_featured) AND isset($first_featured['class_id']) AND $first_featured['class_id'] != 0){
            $data['first'] = $first_featured['type'] == 'classes' ? $this->get_class_info($first_featured['class_id']) : $this->get_howto_info($first_featured['class_id']);
        }
        $second_featured  = $this->model->where("id", 2)->first();
        if(isset($second_featured) AND isset($second_featured['class_id']) AND $second_featured['class_id'] != 0){
            $data['second'] = $second_featured['type'] == 'classes' ? $this->get_class_info($second_featured['class_id']) : $this->get_howto_info($second_featured['class_id']);
        }
        $third_featured  = $this->model->where("id", 3)->first();
        if(isset($third_featured) AND isset($third_featured['class_id']) AND $third_featured['class_id'] != 0){
            $data['third'] = $third_featured['type'] == 'classes' ? $this->get_class_info($third_featured['class_id']) : $this->get_howto_info($third_featured['class_id']);
        }

        $data['all_classes'] = $classes_model->all_classes(0, 0, 0, session('classes_sort'), "0");
        $data['all_howto'] = $videos_model->all_howto(0, 0, 0, 'howto.created_at DESC', "0");

        echo view('admin/featured_videos/index_view', $data);
    }

    public function select_video()
    {
		$data = $this->request->getPost();

        $save_data = [
            "id" => $data['id'],
            "class_id" => $data['class_id'],
            "type" => $data['type']
        ];

        // echo '<pre>';
        // print_r($save_data);
        // die();

        $result['success'] = $this->model->save($save_data);

        return $this->respond($result);
    }
    public function load_classes()
    {
		$classes_model = model('ClassesModel');
        $data['all_featured']  = $this->model->findAll();
        $data['all_classes'] = $classes_model->all_classes(0, 0, 0, 'classes.created_at DESC', "0");
        $result['html'] = view('admin/templates/all_classes_ajax', $data);

        return $this->respond($result);
    }

    public function load_videos()
    {
		$videos_model = model('HowtoModel');
        $data['all_howto'] = $videos_model->all_howto(0, 0, 0, 'howto.created_at DESC', "0");
        $data['all_featured']  = $this->model->findAll();
        $result['html'] = view('admin/templates/all_videos_ajax', $data);

        return $this->respond($result);
    }
    public function remove_video($id = 0)
    {
        $result['success'] = FALSE;

        if($id > 0){
            $data = [
                'id' => $id,
                'class_id' => 0,
                'type' => 0
            ];
            $result['id'] = $id;
            $result['success'] = $this->model->save($data);
        }

        return $this->respond($result);
    }
    public function sort_table()
    {
        $data = $this->request->getPost();
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
				$this->model->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }


    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['logo']) AND $files['logo']->isValid()){
				$file = $files['logo'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/settings', $name);
				$data['logo'] = 'uploads/settings/' . $name;
			}
			if (isset($files['popup_image']) AND $files['popup_image']->isValid()){
				$file = $files['popup_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/settings', $name);
				$data['popup_image'] = 'uploads/settings/' . $name;
			}
            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $data[$key] = json_encode($single_field);
                }
            }
            if(isset($data['popup_image_removed']) AND $data['popup_image_removed'] == 1){
                $data['popup_image'] = "";
            }
            unset($data['popup_image_removed']);

            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}

        $response['data'] = $data;
		return $this->respond($response);
    }
}