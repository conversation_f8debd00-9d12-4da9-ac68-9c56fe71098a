<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;

class Dashboard extends Admincontroller
{
	public function __construct() {
		parent::__construct();
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $classes_views_model = model('ClassesViewModel');
        $classes_model = model('ClassesModel');
        $collections_model = model('CollectionsModel');
        $exercises_model = model('ExercisesModel');
        $teachers_model = model('TeachersModel');
        $subscribers_model = model('SubscribersModel');
        // $stripe_model = model('StripeModel');
        if($data['logged_user']['super_admin'] != 1 AND session('certified') != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_classes'] = $classes_model->where(['status' => 0])->countAllResults();
        $data['all_collections'] = $collections_model->findAll();
        $data['all_exercises'] = $exercises_model->where(['status' => 0])->countAllResults();
        $data['all_teachers'] = $teachers_model->findAll();
        $data['all_subscribers'] = $subscribers_model->findAll();

        $data['views'] = $classes_views_model->countAll();
		$data['last_month'] = $classes_views_model->where("date BETWEEN (NOW() - INTERVAL 30 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
		$data['last_week'] = $classes_views_model->where("date BETWEEN (NOW() - INTERVAL 7 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
		$data['today'] = $classes_views_model->where("date = CURDATE()")->countAllResults();

        $data['most_viewed_classes'] = $classes_model->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate
                                        FROM classes
                                        LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                        LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                        WHERE classes.deleted_at IS NULL
                                        ORDER BY countView desc
                                        LIMIT 0, 5
                                    ")->getResultArray();
        $data['best_rated_classes'] = $classes_model->query("SELECT classes.*, COALESCE(x.cnt,0) AS countView, COALESCE(y.rate,0) AS classRate
                                        FROM classes
                                        LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                                        LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                                        WHERE classes.deleted_at IS NULL
                                        ORDER BY classRate desc
                                        LIMIT 0, 5
                                    ")->getResultArray();

        // $data['total_subscriptions'] = $subscribers_model->countAll();
        // $data['last_month_subscriptions'] = $subscribers_model->where("updated_at BETWEEN (NOW() - INTERVAL 30 DAY) AND (NOW() - INTERVAL 1 DAY)")->countAllResults();
        // $data['monthly_subscriptions'] = $subscribers_model->where("subscription_type", "Monthly Subscription")->countAllResults();
        // $data['annual_subscriptions'] = $subscribers_model->where("subscription_type", "Annual Subscription")->countAllResults();
		// $data['not_subscribed'] = $subscribers_model->where("stripe_subscription", NULL)->countAllResults();

		// $data['earnings'] = $stripe_model->earnings();
		// $data['subscribers'] = $stripe_model->subscribers();

        if(session('certified') == 1 AND (session('super_admin') == 0 OR session('super_admin') == NULL OR session('super_admin') == '')){            
            echo view('admin/dashboard-teacher/index_view', $data);
        }else{
            echo view('admin/dashboard/index_view', $data);
        };

    }
}