<?php namespace App\Models;

use CodeIgniter\Model;

class RoutinesModel extends Model
{
    protected $table = 'routines';
	protected $allowedFields = ['title', 'slug', 'teacher_id', 'sort', 'status', 'routine_duration'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    // ALL ROUTINES WITH EMPTY
    public function all_routines($start = 0, $limit = 0, $search_term = NULL, $order = "created_at desc", $teacher_id = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $teacher =  ($teacher_id != 0) ? " AND routines.teacher_id = " . $teacher_id . " " : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(routines.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ",$string) . ')';
        }else{
            $search = "";
        };

        $data = $this->query("SELECT routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                            FROM routines
                            LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                            LEFT JOIN teachers on teachers.id = routines.teacher_id
                            WHERE routines.deleted_at IS NULL
                            $teacher
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    // NO EMPTY ROUTINES
    public function all_routines_dropdown($start = 0, $limit = 0, $search_term = NULL, $order = "created_at desc", $teacher_id = 0){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $teacher =  ($teacher_id != 0) ? " AND routines.teacher_id = " . $teacher_id . " " : "";
        if($search_term != "0" AND $search_term != "" AND $search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function($word){
                return "LOWER(routines.title) LIKE '%" . strtolower($word) . "%'";
            }, $words);

            $search = 'AND (' . implode(" AND ",$string) . ')';
        }else{
            $search = "";
        };

        $data = $this->query("SELECT routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                            FROM routines
                            LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                            LEFT JOIN teachers on teachers.id = routines.teacher_id
                            WHERE routines.deleted_at IS NULL
                            $teacher
                            " . $search . "
                            HAVING exercises_count > 0
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_my_routines($start = 0, $limit = 0, $search_term = NULL, $order = "created_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (routines.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                            FROM routines
                            LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                            LEFT JOIN teachers on teachers.id = routines.teacher_id
                            WHERE routines.deleted_at IS NULL
                            AND routines.teacher_id = " . session('admin') . "
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function all_teacher_routines($id = 0){
        $data = $this->query("SELECT routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                            FROM routines
                            LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                            LEFT JOIN teachers on teachers.id = routines.teacher_id
                            WHERE routines.deleted_at IS NULL
                            AND routines.teacher_id = " . $id . "
                            ORDER BY firstname ASC
                        ")->getResultArray();
        return $data;
    }

    public function current($id = ''){
        $data = $this->query("SELECT routines.*
                                        FROM routines
                                        WHERE routines.deleted_at IS NULL
                                        AND routines.id = '" . $id . "'
                                    ")->getRowArray();
        return $data;
    }

    public function single_routine($id = ''){
        $data = $this->query("SELECT routines.*, COALESCE(x.cnt,0) AS exercises_count
                                    FROM routines
                                    LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                                    WHERE routines.deleted_at IS NULL
                                    AND routines.id = '" . $id . "'
                                ")->getRowArray();
        return $data;
    }

    public function exercises_for_routine($routine_id = 0){
        $exercises_model = model('ExercisesModel');
        $data = [];

        if($routine_id != 0){
            $res = $exercises_model->query("SELECT * FROM routines_selected_exercises WHERE routine_id = " . $routine_id . " ORDER BY sort asc")->getResultArray();

            if(!empty($res)){
                foreach($res as $single){
                    $total_duration = $exercises_model->query("SELECT SUM(duration) as total FROM routines_selected_exercises WHERE routine_id = " . $single['routine_id'] . "")->getRowArray();
                    
                    if($single['routine_selected_exercises'] != 0){
                        $exercise = $exercises_model->query("SELECT exercises.id, exercises.title, exercises.video_thumb, exercises.video_preview, difficulty.title as diff, 'exercises_class' AS class, 
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, 
                                    GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short,
                                    " . $single['sort'] . " as csc_sort, 
                                    " . $single['id'] . " as csc_id, 
                                    '" . $single['springs'] . "' as springs, 
                                    '" . $single['springs_count'] . "' as springs_count, 
                                    '" . $single['routine_selected_exercises'] . "' as routine_selected_exercises, 
                                    '" . $single['orientation'] . "' as orientation,                                     
                                    0 as transition,
                                    " . $single['duration'] . " as custom_duration, 
                                    " . $single['duration'] . " as duration, 
                                    '" . duration_standard($total_duration['total']) . "' as total_duration
                                    FROM exercises
                                    LEFT OUTER JOIN difficulty on difficulty.id = exercises.difficulty
                                    LEFT OUTER JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                    LEFT OUTER JOIN machines ON machines.id = exercises_machine.exercise_machine
                                    WHERE exercises.deleted_at IS NULL
                                    AND exercises.status = 0
                                    AND exercises.id = " . $single['routine_selected_exercises'] . "
                                    GROUP BY exercises.id
                                    ORDER BY csc_sort asc
                                ")->getRowArray();
                        if($exercise != NULL){
                            $data[] = $exercise;
                        }
                    }else{
                        $data[] = [
                            'id' => $single['id'],
                            'title' => "Transition: " . $single['duration'] . " seconds",
                            'class' => "exercises_class",
                            'routine_selected_exercises' => $single['routine_selected_exercises'],
                            'csc_sort' => $single['sort'],
                            'csc_id' => $single['id'],
                            'duration' => $single['duration'],
                            'transition' => 1
                        ];
                    }
                }
                // die();

            }else{
                $data = [];
            }
        }else{
            $data = [];
        }
        
		return $data;
    }

    public function single_exercise_for_routine($routine_id = 0, $id = 0){
        $exercises_model = model('ExercisesModel');
        $data = [];

        if($routine_id != 0 AND $id != 0){
            $res = $exercises_model->query("SELECT * FROM routines_selected_exercises WHERE routine_id = " . $routine_id . " AND id = " . $id . " ORDER BY sort asc")->getResultArray();

            if(!empty($res)){
                foreach($res as $single){
                    $total_duration = $exercises_model->query("SELECT SUM(duration) as total FROM routines_selected_exercises WHERE routine_id = " . $single['routine_id'] . "")->getRowArray();
                    
                    if($single['routine_selected_exercises'] != 0){
                        $exercise = $exercises_model->query("SELECT exercises.id, exercises.title, exercises.video_thumb, exercises.video_preview, difficulty.title as diff, 'exercises_class' AS class, 
                                    GROUP_CONCAT(DISTINCT machines.title SEPARATOR ', ') AS all_exercise_machines, 
                                    GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines_short,
                                    " . $single['sort'] . " as csc_sort, 
                                    " . $single['id'] . " as csc_id, 
                                    '" . $single['springs'] . "' as springs, 
                                    '" . $single['springs_count'] . "' as springs_count, 
                                    '" . $single['routine_selected_exercises'] . "' as routine_selected_exercises, 
                                    '" . $single['orientation'] . "' as orientation,                                     
                                    0 as transition,
                                    " . $single['duration'] . " as custom_duration, 
                                    " . $single['duration'] . " as duration, 
                                    '" . duration_standard($total_duration['total']) . "' as total_duration
                                    FROM exercises
                                    LEFT OUTER JOIN difficulty on difficulty.id = exercises.difficulty
                                    LEFT OUTER JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                                    LEFT OUTER JOIN machines ON machines.id = exercises_machine.exercise_machine
                                    WHERE exercises.deleted_at IS NULL
                                    AND exercises.status = 0
                                    AND exercises.id = " . $single['routine_selected_exercises'] . "
                                    GROUP BY exercises.id
                                    ORDER BY csc_sort asc
                                ")->getRowArray();
                        if($exercise != NULL){
                            $data[] = $exercise;
                        }
                    }else{
                        $data[] = [
                            'id' => $single['id'],
                            'title' => "Transition: " . $single['duration'] . " seconds",
                            'class' => "exercises_class",
                            'routine_selected_exercises' => $single['routine_selected_exercises'],
                            'csc_sort' => $single['sort'],
                            'csc_id' => $single['id'],
                            'duration' => $single['duration'],
                            'transition' => 1
                        ];
                    }
                }
                // die();

            }else{
                $data = [];
            }
        }else{
            $data = [];
        }
        
		return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}
