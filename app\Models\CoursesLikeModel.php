<?php namespace App\Models;

use CodeIgniter\Model;

class CoursesLikeModel extends Model
{
    protected $table = ' courses_likes';
	protected $allowedFields = ['course_id', 'date', 'rate', 'user_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}