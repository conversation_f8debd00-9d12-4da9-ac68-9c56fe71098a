<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="playlists-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="px-100">
        <div class="container-fluid">
            <div class="row big-gap">
                <div class="col-8">
                    <h1 class="light mb-2 h2">ALL PLAYLISTS</h1>
                    <p class="light f-16">Enjoy playlists carefully tailored by LagreeOD staff.</p>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0 px-100 pb-7">
        <div class="container-fluid">
            <div class="row big-gap flex-wrap-mob">
<?php
foreach($all_playlists as $single){
    if(isset($single['classesCount']) AND isset($single['howtoCount']) AND ($single['classesCount'] + $single['howtoCount']) > 0){
        $countt = (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] + $single['howtoCount'] : 0;
?>
                <div class="col-20">
                    <div class="single-playlist-item">
                        <a href="/playlists/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>">
                            <div class="img-panel h100">
                                <div class="image-overlay h100 no-overlay">
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : 'images/playlist-bg-x2.jpg'; ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" />
                                    <?php echo (isset($single['image']) AND $single['image'] != '') ? '' : '<span class="playlist-empty-title">' . $single['title'] . '</span>'; ?>
                                </div>
                            </div>
                        </a>
                        <!-- <div class="img-panel-content aic">
                            <h2 class="white h3 medium mb-3 line-height-small"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h2>
                            <p class="f-1 white line-height-small"><?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] + $single['howtoCount'] : 0; ?> <?php echo (isset($single['slug']) AND $single['slug'] == 'live-events') ? ($countt == 1 ? 'EVENT' : 'EVENTS') : 'CLASSES'; ?></p>
                        </div> -->
                        <div class="single-playlist-desc">
                            <a href="/playlists/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>"><h3 class="f-14 text-uppercase bold line-height-normal"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h3></a>
                            <p class="f-14 midGray light" style="margin-bottom: 5px"><?php echo (isset($single['classesCount']) AND $single['classesCount'] != '') ? $single['classesCount'] + $single['howtoCount'] : 0; ?> Classes</p>
                            <!-- <p class="f-1 midGray m-0 light"><?php // echo (isset($single['users_name']) AND $single['users_name'] != '') ? $single['users_name'] : 'LagreeOD staff'; ?></p> -->
                        </div>
                    </div>
                </div>
<?php
    }
}
?>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
</body>
</html>