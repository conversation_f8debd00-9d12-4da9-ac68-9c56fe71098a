<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<style>
.best-value {color:#52C15A; background:rgba(82,193,90,0.15); padding:5px 7px; border-radius:50px;}
.default-remove {display: flex; flex-direction: column; align-items: end; margin-top: -6px;}
.subscribe-form {line-height:1;} 
.subscribe-form button.btn.btn-xs {
	height: auto !important;
	min-height: 0 !important;
	font-size: 11px !important;
	background: transparent !important;
	color: #000 !important;
	border: none !important;
	padding: 0 !important;
    font-weight: 400 !important;
    text-transform: initial !important;
}
.remove-card {
	position: relative;
	z-index: 1;
	color: #DB1818;
	line-height: 1;
	cursor: pointer;
    font-size:11px;
    margin-top: 5px;
    text-decoration:underline;
}


</style>

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
        <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0 bottom-border">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">PAYMENTS</h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12 px-0 payments-wrap">
                    <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">Subscription: none</h3>
                    <div class="p-4 border px-mob-2 pt-mob-2 radius-10 radius-mob-6">
                        <form id="resubscribe" action="account/resubscribe" method="post" class="subscribe-form" autocomplete="off">
                            <div class="row mb-4">
                                <div class="col-12">
                                    <p class="f-14 line-height-big max500">Please select one of the subscription plans:</p>
                                </div>
                            </div>
                            <div class="row mb-4 max600">
                                <div class="col-12">
                                    <div class="panel mb-15 subscription-option p-0 border">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" name="subscription_type" id="subscribe3" data-plan="Weekly" data-price="3.99" data-unit="" value="Weekly Subscription">
                                            <label for="subscribe3" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small">
                                                    <b class="line-height-small" style="margin-bottom:7px;">WEEKLY</b>
                                                    <span class="f-12 midGray line-height-small">$3.99/week</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="panel mb-15 subscription-option p-0 border selected">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" name="subscription_type" id="subscribe1" checked data-plan="Monthly" data-price="9.99" data-unit="/mo" value="Monthly Subscription">
                                            <label for="subscribe1" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small">
                                                    <b class="line-height-small" style="margin-bottom:7px;">MONTHLY</b>
                                                    <span class="f-12 midGray line-height-small">$9.99/month</span>
                                                </span>
                                                <!--<span class="f-10 semibold ml-auto mr-2 mr-mob-3">MOST POPULAR</span>-->
                                            </label>
                                        </div>
                                    </div>
                                    <div class="panel mb-15 subscription-option p-0 border">
                                        <div class="radio-button f-14 rtl w100">
                                            <input type="radio" name="subscription_type" id="subscribe2" data-plan="Annual" data-price="99.99" data-unit="" value="Annual Subscription">
                                            <label for="subscribe2" class="flex aic f-12">
                                                <span class="flex flex-column line-height-small">
                                                    <b class="line-height-small" style="margin-bottom:7px;">ANNUALLY</b>
                                                    <span class="f-12 midGray line-height-small">$99.99/year</span>
                                                </span>
                                                <span class="f-10 line-height-small ml-auto mr-2 mr-mob-3 medium">
                                                <span class="best-value">Save 20%</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn darkGray-bg white mt-0">Resubscribe</button>
                        </form>
                    </div>
                    <!-- <hr class="my-6"> -->
                    <div class="px-0">
                        <div class="mb-2">
                            <p class="account-subttl f-14 semibold text-uppercase line-height-small">Credit cards</p>
                        </div>
                        <?php if(!empty($sources['sources']) AND isset($sources['sources']['data'])){ ?>
                        <div class="col-12 border radius-10 radius-mob-6 px-4 py-4 pt-mob-2 pb-mob-3 px-mob-2">   
                            <div class="row mx-0 mb-4 mb-mob-3">
                                <p class="f-14 f-12-mob midGray line-height-small mb-4 mb-mob-2">Default card will be used for next payment</p>
                                <?php 
                                foreach ($sources['sources']['data'] as $source) { 
                                ?>                                                   
                                <div class="panel panel-cc w100 flex-row border position-relative">
                                    <span class="card-infoo flex aic jcl f-14">
                                        <?php if($source['brand'] == 'Visa'){ ?>
                                            <img src="images/visa-new.svg" alt="" class="img-fluid credit-card-icon" />
                                        <?php }else if($source['brand'] == 'MasterCard'){ ?>
                                            <img src="images/mastercard.svg" alt="" class="img-fluid credit-card-icon" />
                                        <?php }else if($source['brand'] == 'Maestro'){ ?>
                                            <img src="images/maestro.svg" alt="" class="img-fluid credit-card-icon" />
                                        <?php }else if($source['brand'] == 'AmericanExpress'){ ?>
                                            <img src="images/american.svg" alt="" class="img-fluid credit-card-icon" />
                                        <?php } ?>
                                        <span class="desktop-inline"><?php // echo $source['name']; ?></span>
                                        <?php echo 'xxxx xxxx xxxx ' . $source['last4']; ?>
                                        <span class="midGray mx-2 desktop-inline">(<?php echo $source['exp_month']; ?>/<?php echo substr($source['exp_year'], -2); ?>)</span>
                                        
                                    </span>
                                <?php if($customer['customer']['default_source'] == $source['id']){ ?>
                                    <span class="ml-auto f-11 medium" style="color:#52C15A;">Default card</span>
                                <?php }else{ ?>
                                    <div class="default-remove">
                                    <form action="account/set_new_default_method" method="post" class="subscribe-form set_new_default_method ml-auto">
                                        <input type="hidden" name="default_source" value="<?php echo $source['id']; ?>">
                                        <button type="submit" class="btn btn-xs red-bg white text-underline">Make as default</button>
                                    </form>
                                    <span class="remove-card" data-popup="delete-card-popup" onclick="$('.card_placeholder').html($(this).parent().find('.card-infoo').html());$('.delete_card_item').attr('data-id', '<?php echo $source['id']; ?>');">Remove card</span>
                                    </div>
                                <?php } ?>
                                </div>                            
                                <?php 
                                }
                                ?>
                            </div>
                            <?php 
                            }
                            ?>
                            <a href="javascript:;" class="btn darkGray-bg white f-1 ml-auto w100-mob" data-popup="add-credit-card" title="">Add card</a>
                        </div>
                    </div> 
                    <div class="px-0">
                        <h3 class="account-subttl f-14 semibold text-uppercase line-height-small mb-2 mb-mob-1">PAYMENT HISTORY</h3>
                        <p class="f-14 f-12-mob line-height-normal midGray"><a href="account/payments_history" class="link text-underline midGray">Click here</a> to view or print your payment history</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<!--<script src="js/credit-card-input.js"></script>-->
<script src="js/payments_view.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
function black_friday(code){
    $('.subscribe-code-account').val(code); // change to black friday code
    $('.check-code').trigger('click');
}
function black_friday_prepare(){
    // annual prices change
    $('[data-plan="Annual"]').data('price', 79.99);
    $('.annual-price').html('<s class="line-height-small">$99.99</s> &nbsp;<span class="line-height-small darkRed normal">$79.99</span>');
    $('.annual-price-per-month').text('BLACK FRIDAY OFFER: 11/24 - 11/28');
    $('.best-value').hide();
}
$('.check-code').on('click', function(){
    var code = $('.subscribe-code').val();
    var button = $(this);
    button.addClass('btn--loading');
    if(code == ''){
        app_msg('Please enter coupon code!');
    }else{
        $.ajax({
            type: 'POST',
            url: 'register/check_coupon/' + code,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if(data.success){
                    if(data.valid.valid){
                        $('.coupon-form').addClass('disabled');
                        $('.valid_coupon').show().find('.coupon_message').text(data.valid.name);
                        $('.not_valid_coupon').hide();
                        button.removeClass('btn--loading');
                    }else{
                        setTimeout(function(){
                            $('.subscribe-code').val('');
                            button.removeClass('btn--loading');
                        },2000);
                        $('.coupon-form').removeClass('disabled');
                        $('.valid_coupon').hide();
                        $('.not_valid_coupon').show();
                    }
                    button.removeClass('btn--loading');
                }else{
                    console.log(data.message);
                    $('.not_valid_coupon').show();
                    $('.valid_coupon').hide();
                    setTimeout(function(){
                        $('.subscribe-code').val('');
                        button.removeClass('btn--loading');
                    },2000);
                }
            },
            error: function (request, status, error) {
                alert('Error');
                button.removeClass('btn--loading');
            }
        });
    }
});
$('.remove_coupon').on('click', function(){
    $('.subscribe-code').val('');
    $('.coupon-form').removeClass('disabled');
    $('.valid_coupon').hide();
    $('.not_valid_coupon').hide();
});
$('.subscription-option').on('click', function(){
    $('.subscription-option').removeClass('selected');
    $(this).addClass('selected');
});
$(document).ready(function(){
    // black_friday_prepare();
});
</script>
</body>
</html>