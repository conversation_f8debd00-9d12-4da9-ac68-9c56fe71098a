<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white semibold text-uppercase pb-1 mb-05 mb-mob-0">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
        <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                            ACCOUNT
                        </h2>
                        <div class="dropdown flex aic">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <form action="account/save_profile" method="post" enctype="multipart/form-data" class="default_submit w100 account-form" id="account_form">
                    <div class="col-12 px-0 top-border">
                    <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">PROFILE PICTURE</h3>
                        <div class="image_container py-4 py-mob-2 flex aic border px-4 px-mob-2 radius-10 radius-mob-6">
                            <div class="upload-image big-avatar mr-3 mr-mob-2 <?php echo (isset($logged_user) AND $logged_user['image'] != '') ? 'no-border' : ''; ?>">
                                <input type="file" name="user[image]" id="image">
                                <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                            </div>
                            <div class="image_container flex flex-column midGray f-12">
                                
                                <span class="midgray line-height-small f-14 f-12-mob">If is empty, initials will be automatically generated from the name.</span>
                                <!--<span class="desktop f-12 line-height-small light">Max. file size is 2mb. Supported formats: PNG/JPG. Desired size: 1080px x 1080px.</span>-->
                                <div class="flex flex-column mt-1 image_options mb-mob-0 <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                                    <!--<a href="javascript:;" class="link link-black black text-underline mb-1 replace_image">Replace Current</a>-->
                                    <a href="javascript:;" class="link link-black text-underline remove_image">Remove photo</a>
                                </div>
                            </div>
                        </div>
                        <div class="image_options mobile"></div>
                        <!--<span class="mobile light">Max. file size is 2mb. Supported formats: PNG/JPG. Desired size: 1080px x 1080px.</span>-->
                    </div>
                    <div class="col-12 px-0 pl-0">
                    <h3 class="account-subttl f-14 semibold text-uppercase line-height-small">ACCOUNT INFO</h3>
                        <div class="pt-4 pb-1 border px-4 px-mob-2 pt-mob-2 pb-mob-0 radius-10 radius-mob-6">
                            <div class="row mb-3">
                                <div class="col-12 mb-mob-1">
                                    <div class="input-container full-field">
                                    <h5 class="mb-15 f-11 line-height-small">FIRST NAME</h5>
                                        <input type="text" name="user[firstname]" class="line-input no-bg" id="firstname" placeholder="Enter" value="<?php echo (isset($current['firstname']) AND $current['firstname'] != '') ? $current['firstname'] : ''; ?>">
                                        <span id="Firstname_error" class="input-error"></span>
                                    </div>
                                </div>
                                <div class="col-12 mb-mob-1">
                                    <div class="input-container full-field mb-0">
                                    <h5 class="mb-15 f-11 line-height-small">LAST NAME</h5>
                                        <input type="text" name="user[lastname]" class="line-input no-bg" id="lastname" placeholder="Enter" value="<?php echo (isset($current['lastname']) AND $current['lastname'] != '') ? $current['lastname'] : ''; ?>">
                                        <span id="Lastname_error" class="input-error"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12 mb-mob-1">
                                    <div class="input-container full-field mb-0">
                                    <h5 class="mb-15 f-11 line-height-small">EMAIL ADDRESS</h5>
                                        <input type="text" name="user[email]" class="line-input no-bg" id="email" placeholder="Enter" value="<?php echo (isset($current['email']) AND $current['email'] != '') ? $current['email'] : ''; ?>">
                                        <span id="Email_error" class="input-error"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-0">
                                <div class="col-12">
                                <h5 class="mb-15 f-11 line-height-small">PASSWORD</h5>
                                    <div class="input-container full-field">
                                        <span class="reveal_password" style="right: 15px;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                            <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                        </svg>
                                        </span>
                                        <input type="password" name="user[password]" class="line-input no-bg" id="password" placeholder="New password (change current)" autocomplete="new-password">
                                        <span id="Password_error" class="input-error"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if($current['teacher_profile'] != NULL){ ?>
                    <!-- <div class="col-12">
                        <div class="py-5 bottom-border">
                            <h3 class="mb-3 f-16 bold text-uppercase line-height-small">Teacher Photo</h3>
                            <div class="mob_cover_image_container flex aic">
                                <div class="upload-image big-avatar">
                                    <input type="file" name="teacher[image]" id="mob_cover_image">
                                    <img src="<?php // echo empty($current['teacher_profile']['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['teacher_profile']['image']; ?>" alt="" class="image_preview <?php // echo empty($current['teacher_profile']['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php // echo empty($current['teacher_profile']['image']) ? 1 : 1; ?>">
                                </div>
                                <div class="mob_cover_image_container flex flex-column midGray f-14">
                                    <div class="flex flex-column mb-2 image_options <?php // echo empty($current['teacher_profile']['image']) ? 'hide' : ''; ?>">
                                        <a href="javascript:;" class="link link-black black text-underline replace_mob_cover_image mb-1 line-height-small">Replace Current</a>
                                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_mob_cover_image line-height-small">Remove</a>
                                    </div>
                                    <span class="desktop line-height-small black mb-1">If is empty, initials will be automatically generated from teacher's name.</span>
                                    <span class="desktop f-12 line-height-small light">Max. file size is 2mb. Supported formats: PNG/JPG.Desirable size: 1080px x 1080px.</span>
                                </div>
                            </div>
                            <div class="image_options mobile"></div>
                            <span class="black mobile mb-1">If is empty, initials will be automatically generated from the name.</span>
                            <span class="mobile light">Max. file size is 2mb. Supported formats: PNG/JPG. Desired size: 1080px x 1080px.</span>
                        </div>
                    </div> -->
                    <!--<div class="col-12">
                        <div class="pt-5 pb-6 bottom-border">
                            <h3 class="mb-5 mt-05 f-14 semibold text-uppercase line-height-small">Cover Photo</h3>
                            <p class="midGray mb-5 f-14">Select or upload a photo that shows on teacher’s bio page.</p>-->
                            <!--<div class="cover_image_container flex aic">
                                <div class="upload-image big-uplad-image cover_image_size">
                                    <input type="file" name="teacher[cover_image]" id="cover_image">
                                    <img src="<?php echo empty($current['teacher_profile']['cover_image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['teacher_profile']['cover_image']; ?>" alt="" class="image_preview <?php echo empty($current['teacher_profile']['cover_image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['teacher_profile']['cover_image']) ? 1 : 1; ?>">
                                </div>
                                <div class="midGray f-12 line-height-small">
                                  <span class="desktop f-12">Max. file size is 5mb. Supported formats: PNG/JPG.<br>Desirable size: 1920px x 600px.</span>
                                    <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['teacher_profile']['cover_image']) ? 'hide' : ''; ?>">
                                        <a href="javascript:;" class="link link-black black text-underline replace_cover_image mb-1 line-height-small">Replace Current</a>
                                        <a href="javascript:;" class="link link-midGray midGray text-underline remove_cover_image line-height-small">Remove</a>
                                    </div>
                                    
                                </div>
                            </div>
                            <div class="image_options mobile"></div>
                            <span class="mobile line-height-small midGray">If is empty, initials will be automatically generated from the name.</span>
                            <span class="mobile line-height-small midGray">Max. file size is 2mb. Supported formats: PNG/JPG. Desired size: 1080px x 1080px.</span>
                        </div>
                    </div>-->
                    <!--<div class="col-12 pt-05">
                        <div class="pt-5 pb-6 bottom-border">
                            <div class="row">
                                <div class="col-12">
                                    <h3 class="mb-55 f-14 semibold text-uppercase line-height-small">BIOGRAPHY</h3>
                                    <div class="input-container full-field mb-0" id="content_container">
                                        <textarea type="text" name="teacher[content]" class="line-input" placeholder="Enter"><?php echo isset($current['teacher_profile']['content']) ? $current['teacher_profile']['content'] : '' ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>-->
                    <!--<div class="col-12">
                        <div class="pt-55 pb-55 bottom-border">
                            <div class="row mb-0">
                                <div class="col-8">
                                    <h3 class="mb-5 f-14 semibold text-uppercase line-height-small">Social Networks</h3>
                                    <div class="input-container full-field mb-2">
                                    <h5 class="mb-1 f-12">FACEBOOK URL</h5>
                                        <input type="text" name="teacher[facebook]" class="line-input" placeholder="Enter URL" value="<?php echo isset($current['teacher_profile']['facebook']) ? $current['teacher_profile']['facebook'] : '' ?>" />
                                    </div>
                                    <div class="input-container full-field mb-0">
                                    <h5 class="mb-1 f-12">INSTAGRAM URL</h5>
                                        <input type="text" name="teacher[instagram]" class="line-input" placeholder="Enter  URL" value="<?php echo isset($current['teacher_profile']['instagram']) ? $current['teacher_profile']['instagram'] : '' ?>" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="teacher[cover_image_removed]" id="cover_image_removed" value="0">
                        <input type="hidden" name="teacher[mob_cover_image_removed]" id="mob_cover_image_removed" value="0">
                        <input type="hidden" name="teacher[id]" id="teacher_id" value="<?php echo isset($current['teacher_profile']['id']) ? $current['teacher_profile']['id'] : 0 ?>">
                    </div>-->
                    <?php } ?>
                    <div class="col-12 account-save mt-3 pl-0">
                         
                            <input type="hidden" name="user[image_removed]" id="image_removed" />
                            <input type="hidden" name="user[id]" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                            <button type="submit" class="btn darkGray-bg white w100-mob">SAVE</button>
                         
                    </div>
                </form>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>