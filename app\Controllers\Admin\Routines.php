<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

require_once 'dompdf/autoload.inc.php';
use Dompdf\Dompdf;
use Dompdf\Options;

class Routines extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('RoutinesModel');

        $db = \Config\Database::connect();
        $this->all_teachers = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses, CONCAT(firstname, " ", lastname) as teacher_name, firstname
                                    FROM teachers
                                    LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                    HAVING countClasses > 0
                                    ORDER BY firstname ASC
                                    ')->getResultArray();

        $this->all_teachers_routines = $db->query('SELECT *, COALESCE(t.cnt,0) AS countRoutines, CONCAT(firstname, " ", lastname) as teacher_name, firstname
                                    FROM teachers
                                    LEFT JOIN (SELECT teacher_id, count(*) as cnt FROM routines WHERE deleted_at IS NULL GROUP BY teacher_id) t on t.teacher_id = teachers.id
                                    HAVING countRoutines > 0
                                    ORDER BY firstname ASC
                                    ')->getResultArray();

	}

    public function index($page = 1)
    {

        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $teachersModel = model('TeachersModel');

        // if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
        //     $data['current_teacher'] = $teachersModel->find($data['logged_user']['id']);
        //     $data['all_routines'] = $this->model->all_my_routines(($page * session('per_page')) - session('per_page'), session('per_page'));
        //     $data['routines_count'] = count($data['all_routines']);
        //     $all_routine_teachers = $teachersModel->query("SELECT GROUP_CONCAT(DISTINCT(teacher_id) SEPARATOR ',') as routine_teachers FROM routines WHERE deleted_at IS NULL")->getRowArray();
        //     $data['all_teachers'] = $teachersModel->query("SELECT id, CONCAT(firstname, ' ', lastname) as teacher_name FROM teachers WHERE id IN(" . $all_routine_teachers['routine_teachers'] . ") AND deleted_at IS NULL")->getResultArray();
        // }else if(
        //             ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 1)
        //              OR
        //             ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 0)
        //         ){
            $all_routine_teachers = $teachersModel->query("SELECT GROUP_CONCAT(DISTINCT(teacher_id) SEPARATOR ',') as routine_teachers FROM routines WHERE deleted_at IS NULL")->getRowArray();
            $data['all_teachers'] = $teachersModel->query("SELECT id, CONCAT(firstname, ' ', lastname) as teacher_name FROM teachers WHERE id IN(" . $all_routine_teachers['routine_teachers'] . ") AND deleted_at IS NULL")->getResultArray();
            $data['all_routines'] = $this->model->all_routines(($page * session('per_page')) - session('per_page'), session('per_page'));
            $data['routines_count'] = $this->model->countAllResults();
        // }

        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/routines/index_view', $data);
    }

    public function by_teacher($teacher_id = NULL, $page = 1)
    {

        $post = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $teachersModel = model('TeachersModel');

        // if($data['logged_user']['super_admin'] != 1){
        //     return redirect()->to('admin/routines');
        // }else{
            $all_routine_teachers = $teachersModel->query("SELECT GROUP_CONCAT(DISTINCT(teacher_id) SEPARATOR ',') as routine_teachers FROM routines WHERE deleted_at IS NULL")->getRowArray();
            $data['all_teachers'] = $teachersModel->query("SELECT id, CONCAT(firstname, ' ', lastname) as teacher_name, IF(id = " . $teacher_id . ", 1, 0) as current FROM teachers WHERE id IN(" . $all_routine_teachers['routine_teachers'] . ") AND deleted_at IS NULL")->getResultArray();
            $data['all_routines'] = $this->model->all_routines(($page * session('per_page')) - session('per_page'), session('per_page'), NULL, 'created_at desc', $teacher_id);
            $data['current_teacher'] = $teachersModel->find($teacher_id);
            $data['routines_count'] = count($data['all_routines']);
        // }

        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/routines/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
        $db = \Config\Database::connect();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $teachersModel = model('TeachersModel');

        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['all_routines'] = $this->model->all_my_routines(($page * session('per_page')) - session('per_page'), session('per_page'));
            $data['routines_count'] = count($data['all_routines']);
        }else if(
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 1)
                     OR
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 0)
                ){
            $all_routine_teachers = $teachersModel->query("SELECT GROUP_CONCAT(DISTINCT(teacher_id) SEPARATOR ',') as routine_teachers FROM routines WHERE deleted_at IS NULL")->getRowArray();
            $data['all_teachers'] = $teachersModel->query("SELECT id, CONCAT(firstname, ' ', lastname) as teacher_name FROM teachers WHERE id IN(" . $all_routine_teachers['routine_teachers'] . ") AND deleted_at IS NULL")->getResultArray();
            $data['all_routines'] = $this->model->all_routines(($page * session('per_page')) - session('per_page'), session('per_page'));
            $data['routines_count'] = $this->model->countAllResults();
        }
        $data['sort_by'] = "Ascending";
        $data['page'] = $page;

        echo view('admin/routines/index_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['all_routines'] = $this->model->query("SELECT  routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                                        FROM routines
                                        LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                                        LEFT JOIN teachers on teachers.id = routines.teacher_id
                                        WHERE routines.deleted_at IS NULL
                                        AND routines.teacher_id = " . session('admin') . "
                                        HAVING (routines.title LIKE '%" . $data['search_term'] . "%' OR teacher_name LIKE '%" . $data['search_term'] . "%')
                                        ORDER BY routines.created_at desc")->getResultArray();
            $data['routines_count'] = count($data['all_routines']);
        }else if(
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 1)
                     OR
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 0)
                ){
            $data['all_routines'] = $this->model->query("SELECT  routines.*, COALESCE(x.cnt,0) AS exercises_count, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher_name, teachers.id as teacher_id, teachers.slug as teacher_slug
                                        FROM routines
                                        LEFT JOIN (SELECT routine_id, count(*) as cnt FROM  routines_selected_exercises GROUP BY routine_id) x on x.routine_id = routines.id
                                        LEFT JOIN teachers on teachers.id = routines.teacher_id
                                        WHERE routines.deleted_at IS NULL
                                        HAVING (routines.title LIKE '%" . $data['search_term'] . "%' OR teacher_name LIKE '%" . $data['search_term'] . "%')
                                        ORDER BY routines.created_at desc")->getResultArray();
            $data['routines_count'] = count($data['all_routines']);
        }
        // $data['all_routines'] = $this->model->like('title', $data['search_term'])->findAll();
        $data['routines_count'] = $this->model->like('title', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Ascending";
        $data['page'] = 1;

        echo view('admin/routines/index_view', $data);
    }

    public function sort_by($type = 'routines.created_at', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] == 1){
            $data['all_routines'] = $this->model->all_my_routines(0, session('per_page'), '', $type . " " . $direction);
            $data['routines_count'] = count($data['all_routines']);
        }else if(
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 1)
                     OR
                    ($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] == 0)
                ){
            $data['all_routines'] = $this->model->all_routines(0, session('per_page'), '', $type . " " . $direction);
            $data['routines_count'] = $this->model->countAllResults();
        }

        $types = array(
            "routines.created_atdesc" => "Date Joined",
            "routines.titleasc" => "Ascending",
            "routines.titledesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/routines/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $exercises_model = model('ExercisesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

        // if($data['logged_user']['super_admin'] == 1 OR $data['logged_user']['certified'] != 1){
        //     return redirect()->to('admin/classes');
        // }

        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();

        $data['current'] = $this->model->current($edit_id);

        $data['all_routines'] = $this->model->all_routines_dropdown();
		$data['springs'] = $db->query('SELECT * FROM springs  ORDER BY sort ASC')->getResultArray();

        $data['all_teachers'] = $this->all_teachers_routines;
        $data['all_exercises'] = $exercises_model->load_more_exercises(0, 100, '');
        $data['all_exercises_count'] = $exercises_model->load_more_exercises_count(0, 0, '');
        $data['selected_exercises_for_selection'] = $this->model->exercises_for_routine($edit_id);

        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/routines');
        };

		return view('admin/routines/edit_view', $data);
    }
    public function clear_current_exercises()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
		$request = service('request');
        $data = $request->getPost();

        $response['routines_delete'] = FALSE;
        
        if(isset($data['routine_id']) AND $data['routine_id'] > 0){
            $all_exercises = $RoutinesExercisesModel->where('routine_id', $data['routine_id'])->findAll();
            foreach($all_exercises as $single){
                $response['success'][] = $RoutinesExercisesModel->delete($single['id']);
            }
        }

		return $this->respond($response);
    }

    public function routines_list_ajax($all = '')
    {
        if($all == 'all'){
            $list = $this->model->all_routines_dropdown();
            $owner = 'all';
            $response['owner'] = 'all';
        }else if($all == ''){
            $owner = 'my';
            $response['owner'] = 'my';
            $list = $this->model->all_my_routines();
        }else if($all != 'all' AND $all != '' AND (int)$all > 0){
            $owner = 'teacher';
            $response['owner'] = 'teacher';
            $list = $this->model->all_teacher_routines($all);
        }

        $response['list_count'] = count($list);
        $response['html'] = view('admin/routines/routines_dropdown_ajax_view', ['all_routines' => $list, 'owner' => $owner]);

		return $this->respond($response);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Survey successfully saved';

			$response['success'] = $this->model->save($data);
			$response['routine_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function save_exercises_in_rountines()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        // echo '<pre>';
		// var_dump($data);
		// die();

        $result['success'] = $RoutinesExercisesModel->save($data);
        $result['csc_id'] = $RoutinesExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['routine_selected_exercises']);
        }

		return $this->respond($result);
    }

    public function edit_routine_exercise($id = 0)
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');

        if($id != 0){
            $result['success'] = TRUE;
            $result['csc'] = $RoutinesExercisesModel->where(['id' =>  $id])->first();
            $result['exercise'] = $this->get_exercises_info($result['csc']['routine_selected_exercises']);
        }else{
            $result['success'] = FALSE;
        }

		return $this->respond($result);
    }

    public function send_email_old()
    {
        $email_model = model('EmailModel');
		$request = service('request');
        $data = $request->getPost();

        $routine = $this->model->single_routine($data['id']);
        $routine_exercises = $this->model->exercises_for_routine($data['id']);

        $routine_list = '';
        foreach($routine_exercises as $single){
            $routine_list .= '<p style="width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0; padding: 30px 0;">' . $single['title'] . '</p>';
        }

        $email = session('admin_info')['email'];

        if(!empty($routine)){
            $subject = 'Your "' . $routine['title'] .'" routine is exported.';
            // <p style="width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0; padding: 30px 0;"></p>
            $data_template = [
                'title' => $routine['title'],
                'list' => $routine_list,
                'routine_number' => $routine['exercises_count'],
            ];
            $template = 'front/email_templates/routine-list';
            $to = $email;
            $response['send_email'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
        }
        $response['post'] = $data;

		return $this->respond($response);
    }

    public function send_email()
    {
        $parser = \Config\Services::parser();
        $db = \Config\Database::connect();
		$request = service('request');
        $data = $request->getPost();

        $email_model = model('EmailModel');
        $springs = $db->query('SELECT * FROM springs ')->getResultArray();
        // $email = '<EMAIL>';
        $email = session('admin_info')['email'];

        $routine = $this->model->single_routine($data['id']);
        $routine_exercises = $this->model->exercises_for_routine($data['id']);
        
        $routine_list = '';
        $total_duration = 0;
        foreach($routine_exercises as $single){
            // $total_duration += $single['duration'];

            if(isset($single['springs_count']) AND $single['springs_count'] != ''){
                $single_spring_count = json_decode($single['springs_count'], TRUE);
                if(is_array($single_spring_count) AND count($single_spring_count) > 0 AND $single_spring_count[0] == ''){
                    $single_spring_count = [];
                }else{
                    //
                }
            }else{
                $single_spring_count = [];
            }
            foreach($springs as $single_spring){
                if(isset($single_spring['color']) AND $single_spring['color'] != ''){
                    $color_id[$single_spring['id']] = $single_spring['color'];
                }
            }
    
            $colors_html = '';
            if(isset($single['springs']) AND $single['springs'] != ''){
                $springs = json_decode($single['springs'], true);
                foreach($springs as $k => $single_color){
                    if(array_key_exists($single_color, $color_id)){
                        $aaa = $color_id[$single_color];
                        switch ($aaa) {
                            case 'yellow':
                                $css_color = '#F8C158';
                                break;
                            case 'red':
                                $css_color = '#DB1818';
                                break;
                            case 'gray':
                                $css_color = '#ccc';
                                break;                           
                            default:
                                $css_color = $aaa;
                                break;
                        }
                    }
                    if(array_key_exists($k, $single_spring_count)){
                        $bbb = $single_spring_count[$k];
                    }else{
                        $bbb = '';
                    }
                    $colors_html .= '<span style="font-family: Arial, Helvetica;background: ' . $css_color . ';border-radius: 50%;margin-left: 5px;width: 20px !important;height: 20px !important;display: inline-block;text-align: center;font-size: 10px;font-weight: 600;font-family: Arial, helvetica;color: #fff;line-height: 20px">' . ($bbb == '' ? 1 : $bbb) . '</span>';
                }
            }

            if($single['transition'] == 1){
                $routine_list .= '<table border="0" cellpadding="0" cellmargin="0" style="font-family: Arial, Helvetica;width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0;"><tr><td style="width: 65px; text-align: left;font-size: 12px;font-weight: 700;">' . duration_standard($total_duration) . '</td><td style="font-size: 12px;padding: 15px 0;font-weight: 700;font-family: Arial, Helvetica;"><span style="margin: 0;font-size: 12px;font-weight: 700;font-family: Arial, Helvetica;color: #52c15a;">' . $single['title'] . '</span></td></tr></table>';
            }else{
                $routine_list .= '<table border="0" cellpadding="0" cellmargin="0" style="font-family: Arial, Helvetica;width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0;"><tr><td style="width: 65px; text-align: left;font-size: 12px;font-weight: 700;">' . duration_standard($total_duration) . '</td><td><p style="font-family: Arial, Helvetica;font-size: 12px;font-weight: 400;color: #000;margin: 15px 0 10px; line-height: 1;">' . $single['title'] . '</p><p style="font-size: 12px;font-weight: 400;color: #969696;margin: 10px 0; line-height: 1;">Orientation: ' . ($single['orientation'] == "L" ? 'Left' : ($single['orientation'] == 'R' ? 'Right' : '/')) . '</p><p style="font-size: 12px;font-weight: 400;color: #969696;margin: 10px 0 15px; line-height: 1;">Duration: ' . duration_standard($single['duration']) . '</p></td><td style="text-align: right">' . $colors_html . '</td></tr></table>';
            }
            $total_duration += $single['duration'];
        }

        if(!empty($routine)){
            $subject = 'Your "' . $routine['title'] .'" routine is exported.';
            $data_template = [
                'subject' => $subject,
                'title' => $routine['title'],
                'list' => $routine_list,
                'duration' => duration_standard2($total_duration),
                'exercise_count' => $routine['exercises_count'],
            ];

            $template = 'front/email_templates/routine-list';
            $to = $email;
            $response['send_email'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
        }
        $response['post'] = $data;

		return $this->respond($response);
    }
    public function share_routine()
    {
        $db = \Config\Database::connect();
		$request = service('request');
        $data = $request->getPost();

        $email_model = model('EmailModel');
        $springs = $db->query('SELECT * FROM springs ')->getResultArray();
        // $email = '<EMAIL>';
        // $email = session('admin_info')['email'];

        $routine = $this->model->single_routine($data['id']);
        $routine_exercises = $this->model->exercises_for_routine($data['id']);
        
        $routine_list = '';
        $total_duration = 0;
        foreach($routine_exercises as $single){
            // $total_duration += $single['duration'];

            if(isset($single['springs_count']) AND $single['springs_count'] != ''){
                $single_spring_count = json_decode($single['springs_count'], TRUE);
                if(is_array($single_spring_count) AND count($single_spring_count) > 0 AND $single_spring_count[0] == ''){
                    $single_spring_count = [];
                }else{
                    //
                }
            }else{
                $single_spring_count = [];
            }
            foreach($springs as $single_spring){
                if(isset($single_spring['color']) AND $single_spring['color'] != ''){
                    $color_id[$single_spring['id']] = $single_spring['color'];
                }
            }
    
            $colors_html = '';
            if(isset($single['springs']) AND $single['springs'] != ''){
                $springs = json_decode($single['springs'], true);
                foreach($springs as $k => $single_color){
                    if(array_key_exists($single_color, $color_id)){
                        $aaa = $color_id[$single_color];
                        switch ($aaa) {
                            case 'yellow':
                                $css_color = '#F8C158';
                                break;
                            case 'red':
                                $css_color = '#DB1818';
                                break;
                            case 'gray':
                                $css_color = '#ccc';
                                break;                           
                            default:
                                $css_color = $aaa;
                                break;
                        }
                    }
                    if(array_key_exists($k, $single_spring_count)){
                        $bbb = $single_spring_count[$k];
                    }else{
                        $bbb = '';
                    }
                    $colors_html .= '<span style="font-family: Arial, Helvetica;background: ' . $css_color . ';border-radius: 50%;margin-left: 5px;width: 20px !important;height: 20px !important;display: inline-block;text-align: center;font-size: 10px;font-weight: 600;font-family: Arial, helvetica;color: #fff;line-height: 20px">' . ($bbb == '' ? 1 : $bbb) . '</span>';
                }
            }

            if($single['transition'] == 1){
                $routine_list .= '<table border="0" cellpadding="0" cellmargin="0" style="font-family: Arial, Helvetica;width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0;"><tr><td style="width: 65px; text-align: left;font-size: 12px;font-weight: 700;">' . duration_standard($total_duration) . '</td><td style="font-size: 12px;padding: 15px 0;font-weight: 700;font-family: Arial, Helvetica;"><span style="margin: 0;font-size: 12px;font-weight: 700;font-family: Arial, Helvetica;color: #52c15a;">' . $single['title'] . '</span></td></tr></table>';
            }else{
                $routine_list .= '<table border="0" cellpadding="0" cellmargin="0" style="font-family: Arial, Helvetica;width: 100%; border-bottom: 1px solid #E5E5E5; margin: 0;"><tr><td style="width: 65px; text-align: left;font-size: 12px;font-weight: 700;">' . duration_standard($total_duration) . '</td><td><p style="font-family: Arial, Helvetica;font-size: 12px;font-weight: 400;color: #000;margin: 15px 0 10px; line-height: 1;">' . $single['title'] . '</p><p style="font-size: 12px;font-weight: 400;color: #969696;margin: 10px 0; line-height: 1;">Orientation: ' . ($single['orientation'] == "L" ? 'Left' : ($single['orientation'] == 'R' ? 'Right' : '/')) . '</p><p style="font-size: 12px;font-weight: 400;color: #969696;margin: 10px 0 15px; line-height: 1;">Duration: ' . duration_standard($single['duration']) . '</p></td><td style="text-align: right">' . $colors_html . '</td></tr></table>';
            }
            $total_duration += $single['duration'];
        }

        if(!empty($routine)){
            $subject = session('admin_info')['firstname'] . ' ' . session('admin_info')['lastname'] . ' shared a routine with you.';
            $data_template = [
                'subject' => $subject,
                'title' => $routine['title'],
                'list' => $routine_list,
                'sender' => session('admin_info')['firstname'] . ' ' . session('admin_info')['lastname'],
                'duration' => duration_standard2($total_duration),
                'exercise_count' => $routine['exercises_count'],
            ];

            $template = 'front/email_templates/routine-share';
            $to = $data['email'];
            $response['send_email'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
        }
        $response['post'] = $data;

		return $this->respond($response);
    }
    public function pdf_export($routine_id = 0, $to_file = 0)
    {
        if($routine_id != 0){
            $parser = \Config\Services::parser();
            $db = \Config\Database::connect();
    
            $springs = $db->query('SELECT * FROM springs ')->getResultArray();
    
            $routine = $this->model->single_routine($routine_id);
            $routine_exercises = $this->model->exercises_for_routine($routine_id);
            
            $routine_list = '<div style="font-family: Arial, Helvetica;width: 100%; margin: 0;">';
            $total_duration = 0;
            foreach($routine_exercises as $single){
                // $total_duration += $single['duration'];
    
                if(isset($single['springs_count']) AND $single['springs_count'] != ''){
                    $single_spring_count = json_decode($single['springs_count'], TRUE);
                    if(is_array($single_spring_count) AND count($single_spring_count) > 0 AND $single_spring_count[0] == ''){
                        $single_spring_count = [];
                    }else{
                        //
                    }
                }else{
                    $single_spring_count = [];
                }
                foreach($springs as $single_spring){
                    if(isset($single_spring['color']) AND $single_spring['color'] != ''){
                        $color_id[$single_spring['id']] = $single_spring['color'];
                    }
                }

                $colors_html = '';
                $css_text_color = '#fff';
                if(isset($single['springs']) AND $single['springs'] != ''){
                    $springs = json_decode($single['springs'], true);
                    foreach($springs as $k => $single_color){
                        if(array_key_exists($single_color, $color_id)){
                            $aaa = $color_id[$single_color];
                            switch ($aaa) {
                                case 'yellow':
                                    $css_color = '#F8C158;border:1px solid rgba(0,0,0,0)';
                                    $css_text_color = '#fff';
                                    break;
                                case 'red':
                                    $css_color = '#DB1818;border:1px solid rgba(0,0,0,0)';
                                    $css_text_color = '#fff';
                                    break;
                                case 'gray':
                                    $css_color = '#ccc;border:1px solid rgba(0,0,0,0)';
                                    $css_text_color = '#fff';
                                    break;                           
                                case 'white':
                                    $css_color = '#fff;border:1px solid #ccc';
                                    $css_text_color = '#000';
                                    break;                           
                                default:
                                    $css_color = $aaa . ';border:1px solid rgba(0,0,0,0)';
                                    $css_text_color = '#fff';
                                    break;
                            }
                        }
                        if(array_key_exists($k, $single_spring_count)){
                            $bbb = $single_spring_count[$k];
                        }else{
                            $bbb = '';
                        }
                        $colors_html .= '<span style="font-family: Arial, Helvetica;background: ' . $css_color . ';border-radius: 50%;margin-left: 5px;width: 20px !important;height: 20px !important;display: inline-block;text-align: center;font-size: 10px;font-weight: 700;color: ' . $css_text_color . ';line-height: 18px">' . ($bbb == '' ? 1 : $bbb) . '</span>';
                    }
                }
    
                if($single['transition'] == 1){
                    $routine_list .= '<div class="transition-row">';
                    $routine_list .= '  <div class="time-row" style="line-height: 30px;">' . duration_standard($total_duration) . '</div>';
                    $routine_list .= '  <div class="mid-row" style="line-height: 30px;">';
                    $routine_list .= '      <span style="margin: 0;font-size: 12px;font-weight: 700;font-family: Arial, Helvetica;color: #52c15a;">' . $single['title'] . '</span>';'';
                    $routine_list .= '  </div>';
                    $routine_list .= '  <div class="springs-row" style="line-height: 30px;"></div>';
                    $routine_list .= '</div>';
                }else{
                    $routine_list .= '<div class="default-row">';
                    $routine_list .= '  <div class="time-row">' . duration_standard($total_duration) . '</div>';
                    $routine_list .= '  <div class="mid-row">';
                    $routine_list .= '      <p style="font-family: Arial, Helvetica;font-size: 12px;font-weight: 400;color: #000;margin: 10px 0 5px; line-height: 1;">' . $single['title'] . '</p>';
                    $routine_list .= '      <p style="font-size: 12px;font-weight: 400;color: #969696;margin: 5px 0; line-height: 1;">Orientation: ' . ($single['orientation'] == "L" ? 'Left' : ($single['orientation'] == 'R' ? 'Right' : '/')) . '</p>';
                    $routine_list .= '      <p style="font-size: 12px;font-weight: 400;color: #969696;margin: 5px 0 10px; line-height: 1;">Duration: ' . duration_standard($single['duration']) . '</p>';
                    $routine_list .= '  </div>';
                    $routine_list .= '  <div  class="springs-row">' . $colors_html . '</div>';
                    $routine_list .= '</div>';
                }
                $total_duration += $single['duration'];
            }
            $routine_list .= '</div>';

            if(!empty($routine)){
                $data_template = [
                    'title' => $routine['title'],
                    'list' => $routine_list,
                    'duration' => duration_standard2($total_duration),
                    'exercise_count' => $routine['exercises_count'],
                ];
            }
    
            $tmp_pdf = $parser->setData($data_template)->render('front/email_templates/routine-pdf');
            // echo '<div style="width: 750px;margin: auto">';
            // echo $tmp_pdf;
            // echo '</div>';
            // die();
            
    
            // $tmp_pdf = view('front/email_templates/receipt.php', $data);
    
            $options = new Options();
            $options->setIsRemoteEnabled(true);
            $dompdf = new Dompdf($options);
            $dompdf->loadHtml($tmp_pdf, 'UTF-8');
            $dompdf->setPaper('A4', 'portrait');
            // Render the HTML as PDF
            $dompdf->render();
            if($to_file == 1){
                // Output the generated PDF to Browser
                $dompdf->stream('LagreeOD routine - ' . $routine['title'] . '.pdf');
                // Output the generated PDF to file on server
                // $output = $dompdf->output();
                // file_put_contents('./routine_pdf/routine_' . $routine['slug'] . '.pdf', $output);
            }else{
                // Output the generated PDF to file on server
                $output = $dompdf->output();
                file_put_contents('./invoices/routine_' . $routine['slug'] . '.pdf', $output);
            }
        }else{
            echo 'No such routine in database!';
        }
    }

    public function sort_routines_table()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];
		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                $RoutinesExercisesModel->save($single);
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function save_status()
    {
        $data = $this->request->getPost();

        $save_data = [
            'id' => $data['id'],
            'status' => $data['status'] == 1 ? 0 : 1
        ];
        $response['success'] = $this->model->save($save_data);

		return $this->respond($response);
    }
    public function add_routine_to_routine()
    {
        $RoutinesModel = model('RoutinesModel');
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
        $data = $this->request->getPost();
        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();
        $response = [];
        // current sort re-sort
        if(isset($data['routine_id']) AND $data['routine_id'] > 0){
            $response['current_routine'] = $RoutinesModel->query("SELECT * FROM routines_selected_exercises WHERE routine_id = " . $data['routine_id'] . " ORDER BY sort ASC")->getResultArray();
            $counter = 0;
            if(isset($response['current_routine']) AND is_array($response['current_routine']) AND count($response['current_routine']) > 0){
                foreach($response['current_routine'] as $single_item){
                    $counter++;
                    $data_savee = [
                        'id' => $single_item['id'],
                        'sort' => $counter
                    ];
                    $response['sort_repack'] = $RoutinesExercisesModel->save($data_savee);
                }
            }
        }
        if(isset($data['id']) AND $data['id'] > 0){
            $response['exercises'] = $RoutinesModel->exercises_for_routine($data['id']);
        }
        foreach($springs as $single_spring){
            if($single_spring['color'] != ''){
                $color_id[$single_spring['id']] = $single_spring['color'];
            }
        }

        if(count($response['exercises']) > 0){
            $c = $data['sort_count'];
            foreach($response['exercises'] as $single_exercise){
                $c++;
                $data_save = [
                    'routine_id' => $data['routine_id'],
                    'routine_selected_exercises' => $single_exercise['routine_selected_exercises'],
                    'orientation' => isset($single_exercise['orientation']) ? $single_exercise['orientation'] : '',
                    'duration' => $single_exercise['duration'],
                    'springs' => isset($single_exercise['springs']) ? $single_exercise['springs'] : '',
                    'springs_count' => isset($single_exercise['springs_count']) ? $single_exercise['springs_count'] : '',
                    'date' => date('Y-m-d'),
                    'sort' => $c,
                ];
                $response['success'][] = $RoutinesExercisesModel->save($data_save);
                $response['errors'][] = $RoutinesExercisesModel->errors();
                $id = $RoutinesExercisesModel->getInsertID();

                $cc = [];
                if(isset($single_exercise['springs']) AND $single_exercise['springs'] != ''){
                    $colors = json_decode($single_exercise['springs'], true);
                    if(isset($single_exercise['springs_count']) AND $single_exercise['springs_count'] != ''){
                        $springs_count = json_decode($single_exercise['springs_count'], true);
                    }else{

                    }
                    foreach($colors as $key => $single_color){
                        $cc[] = '<span class="' . $color_id[$single_color] . '-bg">' . (($springs_count[$key] == '' OR $springs_count[$key] == 0) ? 1 : $springs_count[$key]) . '</span>';
                    }
                }
                $response['exercises_add'][] = [
                    'id' => $id,
                    'title' => $single_exercise['title'],
                    'routine_id' => $data['routine_id'],
                    'csc_id' => $single_exercise['id'],
                    'diff' => isset($single_exercise['diff']) ? $single_exercise['diff'] : '',
                    'all_class_machines_short' => isset($single_exercise['all_class_machines_short']) ? $single_exercise['all_class_machines_short'] : '',
                    'orientation' => isset($single_exercise['orientation']) ? $single_exercise['orientation'] : '',
                    'duration' => $single_exercise['duration'],
                    'date' => date('Y-m-d'),
                    'colors' => implode('', $cc),
                    'sort' => $c,
                    'transition' => isset($single_exercise['transition']) ? $single_exercise['transition'] : '',
                ];
            }
        }

        return $this->respond($response);
    }

    public function save_exercises_in_routine()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();
        foreach($springs as $single_spring){
            if($single_spring['color'] != ''){
                $color_id[$single_spring['id']] = $single_spring['color'];
            }
        }
        $data['duration'] = (int)$data['duration'];

        $result['success'] = $RoutinesExercisesModel->save($data);
        $result['csc_id'] = $RoutinesExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['routine_selected_exercises']);
            $duration = $RoutinesExercisesModel->where(['id' => $result['csc_id']])->first();
            if((isset($data['duration']) AND $data['duration'] != 0 AND $data['duration'] != NULL AND $data['duration'] != '') OR (isset($duration['duration']) AND $duration['duration'] != 0 AND $duration['duration'] != "")){
                $result['class']['duration'] = $data['duration'];
            }
            if(isset($data['orientation']) AND $data['orientation'] != 0 AND $data['orientation'] != NULL AND $data['orientation'] != ''){
                $result['class']['orientation'] = $data['orientation'];
            }
            // $cc = [];
            // $result['class']['colors'] = '';

            // if(isset($data['springs']) AND $data['springs'] != ''){
            //     $colors = json_decode($data['springs'], true);
            //     $cc = [];
            //     foreach($colors as $single_color){
            //         $cc[] = '<span class="' . $color_id[$single_color] . '-bg"></span>';
            //     }
            //     $result['class']['colors'] = implode('', $cc);
            // }    
        }

		return $this->respond($result);
    }
    public function get_routine_exercises($edit_id)
    {
        $routine_list = $this->model->exercises_for_routine($edit_id);
        if(isset($routine_list) AND is_array($routine_list) AND count($routine_list) > 0){
            $list['selected_exercises_for_selection'] = $routine_list;
            $db = \Config\Database::connect();
            $list['springs'] = $db->query('SELECT * FROM springs  ORDER BY sort ASC')->getResultArray();
            $html['html'] = view('admin/routines/routine_exercises_ajax_view', $list);            
        }else{
            $html['html'] = FALSE;
        }
        
		return $this->respond($html);
    }

    public function update_exercises_in_routine()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $springs = $this->model->query('SELECT * FROM springs ')->getResultArray();
        foreach($springs as $single_spring){
            if($single_spring['color'] != ''){
                $color_id[$single_spring['id']] = $single_spring['color'];
            }
        }
        $data['duration'] = (int)$data['duration'];

        $result['success'] = $RoutinesExercisesModel->save($data);
        $result['csc_id'] = $RoutinesExercisesModel->getInsertID();
        $result['type'] = 'exercises';

        if($result['success']){
            $result['class'] = $this->get_exercises_info($data['routine_selected_exercises']);
            $result['class']['colors'] = '';
            if(isset($data['springs']) AND $data['springs'] != ''){
                $colors = json_decode($data['springs'], true);
                $springs_count = json_decode($data['springs_count'], true);
                $cc = [];
                foreach($colors as $key => $single_color){
                    $cc[] = '<span class="' . $color_id[$single_color] . '-bg">' . ((is_array($springs_count)) ? $springs_count[$key] : '') . '</span>';
                }
                if(is_array($springs_count) AND count($springs_count) > 0 AND $springs_count[0] != ''){
                    $result['class']['add_with_number'] = TRUE;
                }else{                    
                    $result['class']['add_with_number'] = FALSE;
                }
                $result['class']['colors'] = implode('', $cc);
            }    
        }

		return $this->respond($result);
    }
    public function update_transition_in_routine()
    {
        $RoutinesExercisesModel = model('RoutinesExercisesModel');
		$data = $this->request->getPost();
        $result['post'] = $data;

        $data['duration'] = (int)$data['duration'];

        $result['success'] = $RoutinesExercisesModel->save($data);

		return $this->respond($result);
    }

}
