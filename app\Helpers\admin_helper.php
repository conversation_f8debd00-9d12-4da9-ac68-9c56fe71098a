<?php
// Function: used to create slugs
if (!function_exists("slugify")) {
    function slugify($text = '')
    {
        // replace non letter or digits by -
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);
        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);
        // trim
        $text = trim($text, '-');
        // remove duplicate -
        $text = preg_replace('~-+~', '-', $text);
        // lowercase
        $text = strtolower($text);
        if (empty($text)) {
            return 'n-a';
        }
        return $text;
    }
}
if (!function_exists("duration")) {
    function duration($time = 0)
    {
        if($time < 60){
            $time_formated = gmdate("s\"", $time);
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i\' s\"", $time);
        }else if($time > 3599){
            $time_formated = substr(gmdate("H\h i\'", $time),1);
        }
        return $time_formated;
    }
}
if (!function_exists("reverse_duration")) {
    function reverse_duration($time = 0)
    {
        if(strpos($time, ':') !== false){
            $t = explode(':', $time);
            if(count($t) == 2){
                $time_formated = $t[0] * 60 + $t[1];
            }else if(count($t) == 3){
                $time_formated = $t[0] * 60 * 60 + $t[1] * 60 + $t[2];
            }
        }
        return $time_formated;
    }
}
if (!function_exists('inchToHeight')){
    function inchToHeight($inch){
        $currentHeight = $inch;
        $remainder = ($currentHeight % 12);
        $numberOfFeet = ($currentHeight - $remainder)/12;
        $feetString = $numberOfFeet.'&apos; '.$remainder.'&quot;';
        $resultsArray = $feetString;

        return $resultsArray;
    }
}
if (!function_exists("percentage")) {
    function percentage($num = 0, $total = 0)
    {
        if($total == 0)
            return 0;
        $percentage = ($num*100)/$total;

        return $percentage;
    }
}
if (!function_exists("duration_standard")) {
    function duration_standard($time = 0)
    {
        if($time < 60){
            $time_formated = '00:' . gmdate("s", $time);
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i:s", $time);
        }else if($time > 3599){
            $time_formated = substr(gmdate("H:i", $time),1);
        }
        return $time_formated;
    }
}
if (!function_exists("duration_standard2")) {
    function duration_standard2($time = 0)
    {
        if($time < 60){
            $time_formated = '00:' . gmdate("s", $time);
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i:s", $time);
        }else if($time > 3599){
            $time_formated = substr(gmdate("H:i:s", $time),1);
        }
        return $time_formated;
    }
}
if (!function_exists("sort_name")) {
    function sort_name($sort = '')
    {
        $sort_names = [
            'classes.created_at desc' => 'Date Added',
            'classes.title asc' => 'Ascending',
            'classes.title desc' => 'Descending',
            'countView desc' => 'Popularity',
            'classRate desc' => 'Best Rated',
            'exerciseRate desc' => 'Best Rated',
            'howto.created_at desc' => 'Date Added',
            'howto.title asc' => 'Ascending',
            'howto.title desc' => 'Descending',
            'exercises.created_at desc' => 'Date Added',
            'exercises.title asc' => 'Ascending',
            'exercises.title desc' => 'Descending',
            'courses.created_at desc' => 'Date Added',
            'courses.title asc' => 'Ascending',
            'courses.title desc' => 'Descending',
        ];
        if($sort != ''){
            $sort_by = $sort_names[$sort];
        }
        return $sort_by;
    }
}
if (!function_exists("base64_to_jpeg")) {
    function base64_to_jpeg($base64_string, $output_file) {
        // open the output file for writing
        $ifp = fopen($output_file, 'wb');

        // split the string on commas
        // $data[ 0 ] == "data:image/png;base64"
        // $data[ 1 ] == <actual base64 string>
        $data = explode(',', $base64_string);

        // we could add validation here with ensuring count( $data ) > 1
        fwrite($ifp, base64_decode($data[1]));

        // clean up the file resource
        fclose($ifp);

        return $output_file;
    }
}
if (!function_exists("is_base64_encoded")) {
    function is_base64_encoded($data)
    {
        if (strpos($data, 'data:image/png') !== false OR strpos($a, 'data:image/jpg') !== false) {
            return TRUE;
        } else {
            return FALSE;
        }
    };
}
if (!function_exists("only_minutes")) {
    function only_minutes($time = 0)
    {
        if($time < 60){
            $time_formated = 1;
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i:s", $time);
        }else if($time > 3599){
            $hours = $time / 3600;
            $minutes = (($time - (3600 * round($hours))) / 60);
            $seconds = (($time - (3600 * round($hours)) - (60 * round($minutes))) / 60);
            $time_formated = round($hours) . 'h ' . round($minutes);
        }
        return $time_formated;
    }
}
if (!function_exists('user_initials')){
	function user_initials($id){
        $user = NULL;
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = substr($user['firstname'], 0, 1) . substr($user['lastname'], 0, 1);
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('teacher_initials')){
	function teacher_initials($id){
        $teacher = NULL;
        $TeachersModel = model('TeachersModel');
        if($id != 0){
            $teacher = $TeachersModel->where(["id" =>  $id])->first();
        }
        if($teacher){
            $response = substr($teacher['firstname'], 0, 1) . substr($teacher['lastname'], 0, 1);
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('user_avatar')){
	function user_avatar($id){
        $user = NULL;
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = $user['image'];
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('user_info')){
	function user_info($id){
        $user = NULL;
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = $user;
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('class_info')){
	function class_info($id){
        $ClassesModel = model('ClassesModel');
        if($id != 0){
            $response = $ClassesModel->class_info($id);
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('course_info')){
	function course_info($id){
        $CoursesModel = model('CoursesModel');
        if($id != 0){
            $response = $CoursesModel->course_info($id);
        }else{
            $response = NULL;
        }

        return $response;
	}
}

if (!function_exists('course_video_info')){
	function course_video_info($id){
        $CoursesVideosModel = model('CoursesVideosModel');
        if($id != 0){
            $response = $CoursesVideosModel->course_video_info($id);
        }else{
            $response = NULL;
        }

        return $response;
	}
}

if (!function_exists('exercises_for_class')){
	function exercises_for_class($id){
        $user = NULL;
        $ClassesModel = model('ClassesModel');
        if($id != 0){
            $class_full = $ClassesModel->exercises_for_single_class($id);
        }
        if($class_full){
            $response = $class_full;
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('video_info')){
	function video_info($id){
        $user = NULL;
        $HowtoModel = model('HowtoModel');
        if($id != 0){
            $class = $HowtoModel->where(["id" =>  $id])->first();
        }
        if($class){
            $response = $class;
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('exercise_info')){
	function exercise_info($id){
        $user = NULL;
        $ExercisesModel = model('ExercisesModel');
        if($id != 0){
            $class = $ExercisesModel->where(["id" =>  $id])->first();
        }
        if($class){
            $response = $class;
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('webpImage')){
    function webpImage($source, $quality = 100, $removeOld = false){
        $dir = pathinfo($source, PATHINFO_DIRNAME);
        $name = pathinfo($source, PATHINFO_FILENAME);
        $destination = $dir . DIRECTORY_SEPARATOR . $name . '.webp';
        $info = getimagesize($source);
        $isAlpha = false;
        if ($info['mime'] == 'image/jpeg')
            $image = imagecreatefromjpeg($source);
        elseif ($isAlpha = $info['mime'] == 'image/gif') {
            $image = imagecreatefromgif($source);
        } elseif ($isAlpha = $info['mime'] == 'image/png') {
            $image = imagecreatefrompng($source);
        } else {
            return $source;
        }
        if ($isAlpha) {
            imagepalettetotruecolor($image);
            imagealphablending($image, true);
            imagesavealpha($image, true);
        }
        imagewebp($image, $destination, $quality);

        if ($removeOld)
            unlink($source);

        return $destination;
    }
}

// EXERCISES EXISTING PARTS FOR FILTER
if (!function_exists('exercises_existing_machines')){
	function exercises_existing_machines(){
        $db = \Config\Database::connect();
        $machines = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                    FROM machines
                                    LEFT OUTER JOIN (SELECT exercise_machine, count(*) AS cnt FROM exercises_machine GROUP BY exercise_machine) x ON x.exercise_machine = machines.id
                                    HAVING countMachine > 0
                                    ORDER BY sort ASC

                                ')->getResultArray();

        return $machines;
    }
};
if (!function_exists('exercises_existing_difficulty')){
	function exercises_existing_difficulty(){
        $db = \Config\Database::connect();
        $difficulty = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                    FROM difficulty
                                    LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                    HAVING countExercises > 0
                                ')->getResultArray();

        return $difficulty;
    }
};
if (!function_exists('exercises_existing_languages')){
    function exercises_existing_languages(){
        $db = \Config\Database::connect();
        $languages = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                    FROM languages
                                    LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                    HAVING countExercises > 0
                                ')->getResultArray();
        return $languages;
    }
};

if (!function_exists('exercises_existing_duration_less10')){
    function exercises_existing_duration_less10(){
        $db = \Config\Database::connect();
        $duration_less10 = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration < 600')->getResultArray();

        return $duration_less10;
    }
};
if (!function_exists('exercises_existing_duration_less25')){
    function exercises_existing_duration_less25(){
        $db = \Config\Database::connect();
        $duration_less25 = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();

        return $duration_less25;
    }
};
if (!function_exists('exercises_existing_duration_more25')){
    function exercises_existing_duration_more25(){
        $db = \Config\Database::connect();
        $duration_more25 = $db->query('SELECT count(*) AS cnt FROM exercises WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

        return $duration_more25;
    }
};
if (!function_exists('exercises_existing_body_parts')){
    function exercises_existing_body_parts(){
        $db = \Config\Database::connect();
        $body_parts = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                    FROM body_parts
                                    LEFT OUTER JOIN (SELECT exercise_body_parts, count(*) AS cnt FROM exercises_body_parts GROUP BY exercise_body_parts) x ON x.exercise_body_parts = body_parts.id
                                    HAVING countBodyParts > 0
                                    ORDER BY title asc
                                ')->getResultArray();

        return $body_parts;
    }
};
if (!function_exists('exercises_existing_all_teachers')){
    function exercises_existing_all_teachers(){
        $db = \Config\Database::connect();
        $all_teachers = $db->query('SELECT *, COALESCE(x.cnt,0) AS countExercises
                                    FROM teachers
                                    LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM exercises WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                    HAVING countExercises > 0
                                    ORDER BY firstname ASC
                                  ')->getResultArray();

        return $all_teachers;
    }
};

// CLASSES EXISTING PARTS FOR FILTER
if (!function_exists('classes_existing_machines')){
	function classes_existing_machines(){
        $db = \Config\Database::connect();
        $machines = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                    FROM machines
                                    LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                    HAVING countMachine > 0
                                    ORDER BY sort ASC
                              ')->getResultArray();

        return $machines;
    }
};
if (!function_exists('classes_existing_difficulty')){
	function classes_existing_difficulty(){
        $db = \Config\Database::connect();
        $difficulty = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                    FROM difficulty
                                    LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                    HAVING countClasses > 0
                                ')->getResultArray();

        return $difficulty;
    }
};
if (!function_exists('classes_existing_languages')){
    function classes_existing_languages(){
        $db = \Config\Database::connect();
        $languages = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                    FROM languages
                                    LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                    HAVING countClasses > 0
                                ')->getResultArray();
        return $languages;
    }
};
if (!function_exists('classes_existing_duration_less10')){
    function classes_existing_duration_less10(){
        $db = \Config\Database::connect();
        $duration_less10 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();

        return $duration_less10;
    }
};
if (!function_exists('classes_existing_duration_less25')){
    function classes_existing_duration_less25(){
        $db = \Config\Database::connect();
        $duration_less25 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1500')->getResultArray();

        return $duration_less25;
    }
};
if (!function_exists('classes_existing_duration_more25')){
    function classes_existing_duration_more25(){
        $db = \Config\Database::connect();
        $duration_more25 = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1500')->getResultArray();

        return $duration_more25;
    }
};
if (!function_exists('classes_existing_body_parts')){
    function classes_existing_body_parts(){
        $db = \Config\Database::connect();
        $body_parts = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                    FROM body_parts
                                    LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                    HAVING countBodyParts > 0
                                ')->getResultArray();

        return $body_parts;
    }
};
if (!function_exists('classes_existing_all_teachers')){
    function classes_existing_all_teachers(){
        $db = \Config\Database::connect();
        $all_teachers = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                    FROM teachers
                                    LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                    HAVING countClasses > 0
                                    ORDER BY firstname ASC
                                  ')->getResultArray();

        return $all_teachers;
    }
};