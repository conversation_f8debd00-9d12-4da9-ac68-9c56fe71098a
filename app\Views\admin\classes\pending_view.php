<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 20px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}

</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container py-100 border-bottom">
            <div class="flex aic jcsb minH45">
                <h1 class="h3">CLASSES ON APPROVAL</h1>
                <!-- <div class="ml-auto">
                    <a href="admin/classes/edit" class="btn black-bg white" title="Upload">Upload</a>
                    <a href="admin/classes/multi" class="btn btn-border white-bg black ml-2" title="Bulk">Bulk</a>
                </div> -->
            </div>
            <hr class="mt-80 mb-4">
            <div class="flex aic jcsb">
                <?php
                    $active_machines = isset($filter['machine'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['machine'][0]))) : [];
                    $active_body_parts = isset($filter['body_parts'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['body_parts'][0]))) : [];
                    $active_teacher = isset($filter['teacher'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['teacher'][0]))) : [];
                    $active_difficulty = isset($filter['difficulty'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['difficulty'][0]))) : [];
                    $active_duration = isset($filter['duration'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['duration'][0]))) : [];
                ?>
                <?php if(!isset($filter)){ ?>
                    <a href="javascript:;" class="flex aic jcc f-14" onclick="check_filter_seleboxes()" data-popup="classes-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /> Apply filters</a>
                <?php }else{ ?>
                    <div class="flex aic jcl">
                        <a href="javascript:;" class="flex aic jcc f-14" onclick="check_filter_seleboxes()" data-popup="classes-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /></a>
                        <span class="f-14 mr-05">Active filters: </span>
                        <?php if($active_machines[0] != ""){ ?><span class="f-14">Machine</span><?php } ?>
                        <?php if($active_duration[0] != ""){ ?><span class="f-14">, Duration</span><?php } ?>
                        <?php if($active_difficulty[0] != ""){ ?><span class="f-14">, Difficulty</span><?php } ?>
                        <?php if($active_body_parts[0] != ""){ ?><span class="f-14">, Body Parts</span><?php } ?>
                        <?php if($active_teacher[0] != ""){ ?><span class="f-14">, Teacher</span><?php } ?>
                        <a href="admin/classes" class="link link-midGray midGray clear_filter f-14 ml-2">x Clear</a>
                    </div>
                <?php } ?>
                <div class="flex aic jcsb">
                    <div class="dropdown d-inline-block">
                        <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" data-val="classes.created_at" data-by="desc" class="set_sort_by link midGray" title="">Date Added</a></li>
                            <li><a href="javascript:;" data-val="classes.title" data-by="asc" class="set_sort_by link midGray" title="">Ascending</a></li>
                            <li><a href="javascript:;" data-val="classes.title" data-by="desc" class="set_sort_by link midGray" title="">Descending</a></li>
                            <li><a href="javascript:;" data-val="countView" data-by="desc" class="set_sort_by link midGray" title="">Popularity</a></li>
                            <li><a href="javascript:;" data-val="classRate" data-by="desc" class="set_sort_by link midGray" title="">Best Rated</a></li>
                        </ul>
                    </div>
                    <div class="search-container">
                        <form action="admin/classes/pending" method="GET" class="search-form <?php echo (isset($search_term) AND $search_term != '0') ? 'show' : ''; ?>">
                            <input type="text" name="search_term" class="seach-input" value="<?php echo (isset($search_term) AND $search_term != "0") ? $search_term : ''; ?>">
                            <?php if(isset($search_term) AND $search_term != "0"){ ?>
                            <a href="admin/classes/clear_search" class="clear_search" style="font-size: 18px;right: 40px;">×</a>
                            <?php } ?>
                            <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        </form>
                    </div>
                </div>
            </div>
            <hr class="mt-3 mb-25">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-14 link link-black black">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-14 link link-red red red flex aic delete_multiple" data-table="classes" data-popup="delete-popup"><img src="admin_assets_new/images/trash.svg" alt="" class="img-fluid mr-05" />Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <form class="edit_checked" style="display: none" method="post" action="admin/classes/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-14 link link-red red flex aic white-bg edit_bulk">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>
                    <span class="approve_checked" style="display: none">
                        <span class="ml-3 f-14 link link-red red red flex aic approve_multiple" data-table="classes" data-popup="approve-popup">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="mr-05" style="width: 20px;">
                                <path id="Path_1383" data-name="Path 1383" d="M0,0H24V24H0Z" fill="none"></path>
                                <path id="Path_1384" data-name="Path 1384" d="M7.58,4.08,6.15,2.65A10.425,10.425,0,0,0,2.03,10.5h2A8.446,8.446,0,0,1,7.58,4.08ZM19.97,10.5h2a10.489,10.489,0,0,0-4.12-7.85L16.43,4.08A8.5,8.5,0,0,1,19.97,10.5ZM18,11c0-3.07-1.64-5.64-4.5-6.32V4a1.5,1.5,0,0,0-3,0v.68C7.63,5.36,6,7.92,6,11v5L4,18v1H20V18l-2-2ZM12,22a1.752,1.752,0,0,0,.4-.04,2.029,2.029,0,0,0,1.44-1.18,2.008,2.008,0,0,0,.15-.78h-4A2.014,2.014,0,0,0,12,22Z" fill="#f8c158"></path>
                            </svg>
                            Approve (<span class="checked-amount">2</span>)
                        </span>
                    </span>
                </div>
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $classes_count == 1 ? $classes_count . ' Class' : $classes_count . ' Classes'; ?></h5>
            </div>
            <hr class="my-4">
            <div class="row big-gap">
                <div class="col-12 mb-2">
                    <div class="table">
<?php
foreach($all_classes as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic mb-3">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" style="max-width: 210px;max-height: 120px;height: 120px;width: 210px;object-fit: cover;<?php echo($single['status'] == 1) ? 'opacity: 0.3 !important' : ''; ?>" /></a>
                                <div class="flex flex-column">
                                    <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium mb-05"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray f-1">
                                        by: <a href="admin/teachers/edit/<?php echo (isset($single['teach_id']) AND $single['teach_id'] != '') ? $single['teach_id'] : ''; ?>" class="link link-black black"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : ''; ?></a>
                                        <span class="midGray mb-1 f-1">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    </span>
                                    <div class="row-actions f-1 red mt-1">
                                        <a href="admin/classes/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Review</a>
                                    </div>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-1 pr-0">
                                    <span class="btn btn-xs yellow-bg white f-1" style="text-transform: none !important;min-height: 22px;padding: 0 10px;">Pending Approval</span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($classes_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_classes); ?><span class="midGray mx-1">of <?php echo $classes_count; ?></span>
                    <a href="admin/classes/<?php echo ($segment == 'filter' OR $segment == 'pending') ? $segment == 'filter' : 'page'; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/classes/<?php echo ($segment == 'filter' OR $segment == 'pending') ? $segment == 'filter' : 'page'; ?>/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_classes) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_classes)) == $classes_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<div>
    <pre>
        <?php // print_r($filter); ?>
    </pre>
</div>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('per_page') == "") ? 10 : session('per_page'); ?>;
var order = 'classes.created_at DESC';
</script>
</body>
</html>