<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
</head>
<body class="subscribe-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="pt-0 pb-05 mbsec px-2">
        <div class="container1080 mb-05">
            <div class="row">
                <div class="col-12 subscribe-title">
                    <h1 class="semibold m-0 f-24 line-height-small">SUBSCRIBE</h1>
                </div>
            </div>
            <form id="register_subscribe_1" method="POST" action="register/validate_subscribe_step_1" class="row nowrap flex" autocomplete="off">
                <div class="col pr-4 pr-mob-1 max500 order-mob-2 px-mob-0">
                    <div class="subscribe-image-field">
                        <img src="images/subscribe-x2.jpg" alt="" class="img-fluid" />
                        <div class="lightGray-bg subscribe-whats-box">
                            <h2 class="f-14 semibold text-uppercase line-height-normal mb-3">WHAT'S INSIDE:</h2>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> 1,000+ classes right at your fingertips </p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> 1,000+ exercises right at your fingertips </p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Stream from any device, anywhere, anytime</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Courses</p>
                            <p class="f-12 flex aic line-height-normal"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Opportunity to Ask Sebastien a question</p>
                            <p class="f-12 flex aic line-height-normal mb-2"><img src="images/check.svg" alt="check" class="img-fluid mr-1" /> Custom playlists</p>
                            <p><a href="/what-is-lod" class="link link-black black f-12 line-height-small text-underline" title="See the full list of features">Learn more about LagreeOD</a></p>
                        </div>
                    </div>
                </div>
                <div class="col pl-4 pr-0 w100 px-mob-1 mb-mob-2 order-mob-1 px-mob-0">
                    <div class="panel subsc-right border for--loading">
                        <div class="subscribe_steps_wrapper">
                            <div class="w100 subsc-plans">
                                <h4 class="line-height-small f-14 semibold">CHOOSE YOUR PLAN</h4>
                                <div class="panel mb-15 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'selected' : ''; ?>">
                                    <div class="radio-button f-14 rtl w100">
                                        <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Weekly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe3" data-plan="Weekly" data-price="3.99" data-unit="" value="Weekly Subscription">
                                        <label for="subscribe3" class="flex aic f-12">
                                            <span class="flex flex-column line-height-small">
                                                <p class="medium line-height-small" style="margin-bottom:7px;">WEEKLY</p>
                                                <span class="f-12 midGray line-height-small">$3.99/week</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="panel mb-15 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'selected' : ''; ?>">
                                    <div class="radio-button f-14 rtl w100">
                                        <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Monthly Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe1" data-plan="Monthly" data-price="9.99" data-unit="/mo" value="Monthly Subscription">
                                        <label for="subscribe1" class="flex aic f-12">
                                            <span class="flex flex-column line-height-small">
                                                <p class="medium line-height-small" style="margin-bottom:7px;">MONTHLY</p>
                                                <span class="f-12 midGray line-height-small">$9.99/month</span>
                                            </span>
                                            <!--<span class="f-0 ml-auto mr-2 mr-mob-3">MOST POPULAR</span>-->
                                        </label>
                                    </div>
                                </div>
                                <div class="panel mb-0 subscription-option p-0 <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'selected' : ''; ?>">
                                    <div class="radio-button f-14 rtl w100">
                                        <input type="radio" <?php echo (isset($logged_user) AND $logged_user['subscription_type'] == 'Annual Subscription') ? 'checked' : ''; ?> name="subscription_type" id="subscribe2" data-plan="Annual" data-price="99.99" data-unit="" value="Annual Subscription">
                                        <label for="subscribe2" class="flex aic f-12">
                                            <span class="flex flex-column line-height-small mr-1">
                                            <p class="medium line-height-small" style="margin-bottom:7px;">ANNUALLY</p>
                                                <span class="line-height-small f-12 midGray annual-price" style="white-space: nowrap">$99.99/year</span>
                                            </span>
                                            <span class="f-10 line-height-small ml-auto mr-2 mr-mob-3 medium">
                                                <span class="best-value">Save 20%</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row subscribe-createacc">
                                <div class="col-12">
                                    <div class="w100 flex sic jcsb">
                                        <h4 class="line-height-small f-14 semibold" style="white-space: nowrap;">Create an Account</h4>
                                        <div class="subscribe-haveacc">
                                            <p class="midGray f-1 line-height-small" style="letter-spacing: 0;"><span class="desktop-inline line-height-small mr-05">Have an account?</span> <a href="javascript:;" data-popup="login-popup" class="red text-underline line-height-small">Log in</a></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="input-container">
                                        <label class="f-11 text-uppercase">First Name</label>
                                        <input type="text" name="firstname" class="line-input step_1_input" id="subscribe_firstname" placeholder="Enter" value="<?php echo isset($session['firstname']) ? $session['firstname'] : ''; ?>">
                                        <!--<span class="input-label">First name</span>-->
                                        <span id="Firstname_error" class="input-error"></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="input-container">
                                        <label class="f-11 text-uppercase">Last Name</label>
                                        <input type="text" name="lastname" class="line-input step_1_input" id="subscribe_lastname" placeholder="Enter" value="<?php echo isset($session['lastname']) ? $session['lastname'] : ''; ?>">
                                        <!--<span class="input-label">Last name</span>-->
                                        <span id="Lastname_error" class="input-error"></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="input-container">
                                        <label class="f-11 text-uppercase">Email</label>
                                        <input type="text" name="email" class="line-input step_1_input" id="subscribe_email" placeholder="Email" onblur="check_email()" value="<?php echo isset($session['email']) ? $session['email'] : ''; ?>">
                                        <!--<span class="input-label">Email</span>-->
                                        <span id="Email_error" class="input-error"></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="input-container mb-0">
                                        <label class="f-11 text-uppercase">Password</label>
                                        <span class="reveal_password" style="top: 38px;right: 15px;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                            <path id="Path_7417" data-name="Path 7417" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#ddd"/>
                                        </svg>
                                        </span>
                                        <input type="password" name="password" class="line-input step_1_input" id="subscribe_password" placeholder="Enter">
                                        <!--<span class="input-label">Password</span>-->
                                        <span id="Password_error" class="input-error"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w100 continue_button_wrap">
                            <div class="row">
                                <div class="col-12">
                                    <div class="px-5 pb-4 pb-mob-3 px-mob-2">
                                        <hr class="mb-4 mt-0 hidemob">
                                        <button type="submit" class="btn black-bg white w100 continue_button h50" title="CONTINUE">CONTINUE</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function check_email(){
    var email = $('#subscribe_email').val();

    if(email != ''){
        $.ajax({
            type: 'POST',
            url: 'register/check_email',
            data: {
                email
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $('#Email_error').html('Account with this email already exists. <a href="javascript:;" data-popup="login-popup" class="link link-black black text-underline">Login?</a>');
                    // $('.continue_button').addClass('disabled');
                }else{
                    // $('.continue_button').removeClass('disabled');
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
</script>	
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/subscribe.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script> -->
<!-- <script src="js/google_login.js"></script> -->
<script>
$('.subscription-option').on('click', function(){
    $('.subscription-option').removeClass('selected');
    $(this).addClass('selected');
    $('.remove_coupon').trigger('click');
});
$(document).ready(function(){
    $('#subscribe3').trigger('click').change();
});
$('#register_subscribe_1').on('submit', function(e){
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var button = form.find('button[type=submit]');
    button.addClass('btn--loading disabled');

    $.ajax({
        type: 'POST',
        url: url,
        data: form.serialize(),
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.code_verified == 1 && data.success){
                window.location.href = base_url + '/subscribe-payment';
            }
            // if(data.email_sent){
                // window.location.href = base_url + '/subscribe-confirmation';
            // }
            $.each(data.json, function(key, val){
                var ids = key.replace('.', '[');
                $('#register_subscribe_1').find('[name*="' + ids + '"]').addClass('error').next().html(val);
            });
            button.removeClass('btn--loading disabled');
        },
        error: function (request, status, error) {
            console.log('PHP Error');
            button.removeClass('btn--loading disabled');
        }
    });
    
});
</script>
</body>
</html>