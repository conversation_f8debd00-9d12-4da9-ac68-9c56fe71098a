<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<style>
.tox-tinymce-aux {
	z-index: 1300000;
}
.tox-collection__item-label h1 {
	font-size: 32px !important;
	text-transform: none !important;
}
.tox-collection__item-label h2 {
	font-size: 28px !important;
	text-transform: none !important;
}
.tox-collection__item-label h3 {
	font-size: 24px !important;
	text-transform: none !important;
}
.tox-collection__item-label h4 {
	font-size: 18px !important;
	text-transform: none !important;
}
.tox-tinymce {
    border: 1px solid #eee !important;
    border-radius: 0px !important;
}
</style>
<div class="container tab_content pt-0 px-0">
    <form action="admin/courses/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone" id="video_container" <?php echo isset($course['id']) ? 'style="min-height: 400px;"' : '' ?>>
        <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($course['video']) AND $course['video'] != '') ? 'display: none' : ''; ?>">
        <div class="before_upload" style="<?php echo (isset($course['video']) AND $course['video'] != '') ? 'display: none' : ''; ?>">
        <span class="f-14 semibold mb-1">DRAG AND DROP VIDEO HERE</span> 
        <span class="f-14 midGray video_choose">or <u>select a file</u></span>
        </div>
        <div class="video_placeholder">
            <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($course['image']) AND $course['image'] != '') ? $course['image'] : ((isset($course['video_thumb']) AND $course['video_thumb'] != '') ? $course['video_thumb'] : ''); ?>" src="<?php echo (isset($course['video']) AND $course['video'] != '') ? $course['video'] : ''; ?>" style="<?php echo (isset($course['video']) AND $course['video'] != '') ? '' : 'display: none'; ?>"></video>
        </div>
        <span id="video-is-uploading"></span>
        <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
        <span id="progress-bar-status-show"></span>
        <span id="toshow" style="display: none;"></span>
    </form>
  
    <div class="flex aic jcsb mt-1">
        <span class="link link-red red text-underline f-14 remove_video_popup" onclick="remove_video_popup()" style="<?php echo (isset($course['video']) AND $course['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
        <input type="hidden" id="duration" value="<?php echo (isset($course['duration']) AND $course['duration'] != '') ? $course['duration'] : ''; ?>" />
    </div>
    <form action="admin/courses/save_video_item" class="container border-bottom mb-0 px-0 mt-20" id="add-course-video">
        <h5 class="mb-1 f-11">VIDEO NAME *</h5>
        <div class="input-container" id="title_container" style="position: relative;max-width: 100%">
            <input type="text" name="title" class="line-input" placeholder="Enter" value="<?php echo isset($course['title']) ? $course['title'] : '' ?>"  style="max-width: 100%"/>
            <input type="hidden" name="slug" id="slugg" value="<?php echo (isset($course['slug']) AND $course['slug'] != '') ? $course['slug'] : generate_slug() ?>" />
        </div>
        <h5 class="mb-1 f-11">DESCRIPTION</h5>
        <div class="input-container" id="content_container" style="max-width: 100%">
            <textarea type="text" name="description" class="line-input course_desc f-14" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500" placeholder="Enter"><?php echo isset($course['description']) ? $course['description'] : '' ?></textarea>
            <!--<p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">500</span></p>-->
        </div>
         <!--<div class="row">
            <div class="col-12 courses-bodyparts">
                <h3 class="mb-2 f-14">Body Parts</h3>
                <div class="mb-2 flex aic gap-3">
                    <div class="checkbox mb-15 flex" id="body_parts_container">
                        <input type="checkbox" class="" id="full_body">
                        <label for="full_body" class="f-14">Full Body</label>
                    </div>
                    <div class="checkbox mb-15 flex" id="body_parts_container">
                        <input type="checkbox" class="" id="upper_body">
                        <label for="upper_body" class="f-14">Upper Body</label>
                    </div>
                    <div class="checkbox mb-15 flex" id="body_parts_container">
                        <input type="checkbox" class="" id="lower_body">
                        <label for="lower_body" class="f-14">Lower Body</label>
                    </div>
                </div>
            </div>
            <div class="col-12 flex fd-mob-column">
                <div class="mr-150">-->
<?php
// $curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '') ? $current_body_parts : array();
?>
                        <!-- <div class="checkbox mb-2" id="body_parts_container">
                            <input type="checkbox" class="" name="body_parts[]" id="body_parts16" <?php // echo in_array(16, $curr_body_parts) ? 'checked' : '' ?> value="16">
                            <label for="body_parts16" class="f-14">Full body</label>
                        </div> -->

<?php
// $c=0;
// foreach($body_parts as $single){
   //  if($single['id'] != 16){
     //    $c++;
?>
                    <!--<div class="checkbox mb-2" id="body_parts_container">
                        <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php // echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>" data-group="<?php // echo $single['parts_group']; ?>">
                        <label for="body_parts<?php // echo $single['id']; ?>" class="f-14"><?php // echo $single['title']; ?></label>
                    </div>-->
<?php
  //       if($c == 8){
   //          echo '</div><div class="mr-150">';
   //          $c = 0;
  //       }
  //   }
// }
?>
                <!--</div>
            </div>
        </div> -->
         <!--<div class="row">
            <div class="col-6">
                <h3 class="mb-2 f-14">Accessories Required</h3>
<?php
// $curr_accessories = (isset($current_accessories) AND $current_accessories != '') ? $current_accessories : array();
// foreach($accessories as $single){
?>
                    <div class="checkbox mb-2" id="accessories_container" data-machine="<?php // echo $single['machine']; ?>" data-id="<?php // echo $single['id']; ?>">
                        <input type="checkbox" <?php // if($single['id'] == 41){ ?>onchange="$(this).is(':checked') ? $('.bungee_tension').show() : $('.bungee_tension').hide()"<?php // } ?> name="accessories[]" id="accessories<?php // echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>">
                        <label for="accessories<?php // echo $single['id']; ?>" class="f-14"><?php // echo $single['title']; ?></label>
                    </div>
<?php
// }
?>
<?php // echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
            </div>
        </div>-->
        
        <!--<div class="row">
            <div class="col-6">
                <h3 class="mb-2 f-14 flex aic jcsb">Spring Load <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h3>
<?php
// $curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array();
// foreach($springs as $single){
?>
                <div class="checkbox mb-2" id="springs_container" data-machine="<?php // echo $single['machine']; ?>" data-id="<?php // echo $single['id']; ?>">
                    <input type="checkbox" class="" name="springs[]" id="springs<?php // echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>">
                    <label for="springs<?php // echo $single['id']; ?>" class="f-14"><?php // echo $single['title']; ?></label>
                </div>
<?php
// }
?>
<?php // echo count($curr_machines) == 0 ? '<h5 class="machine_first f-14">Please select machine first</h5>' : ''; ?>
            </div>
        </div>-->
         <div class="default-buttons mt-3" <?php echo (isset($course['status']) AND $course['status'] == 2) ? 'style="display: none;"' : '';?>>
            <input type="hidden" id="video_path" name="video" value="<?php echo (isset($course['video']) AND $course['video'] != '') ? $course['video'] : ''; ?>"/>
            <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($course['video_thumb']) AND $course['video_thumb'] != '') ? $course['video_thumb'] : ''; ?>"/>
            <input type="hidden" id="video_preview" name="video_preview" value="<?php echo (isset($course['video_preview']) AND $course['video_preview'] != '') ? $course['video_thumb'] : ''; ?>"/>
            <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($course['duration']) AND $course['duration'] != '') ? $course['duration'] : ''; ?>">
            <input type="hidden" id="course_item_video_id" name="id" value="<?php echo (isset($course['id']) AND $course['id'] != '') ? $course['id'] : ''; ?>">
            <input type="hidden" name="course_id" value="<?php echo (isset($course['course_id']) AND $course['course_id'] != '') ? $course['course_id'] : ''; ?>">
            <input type="hidden" name="date" value="">
            <input type="hidden" id="course_item_sort" name="sort" value="<?php echo (isset($course['sort']) AND $course['sort'] != '') ? $course['sort'] : 0; ?>">
            <input type="hidden" name="type" value="video">

            <button type="submit" class="btn btn-wide btn-tall red-bg white w100">SUBMIT</button>
        </div>
    </form>
</div>
<script>
<?php if(isset($course['duration']) AND ($course['duration'] == '' OR $course['duration'] == 'NaN' OR is_numeric($course['duration'])) AND isset($course['id']) AND $course['id'] > 0){ ?>
    setTimeout(function(){
        $('#duration_val').val(($('video').get(0).duration).toFixed(0));
    }, 4500);
<?php } ?>
tinymce.remove(".course_desc");
tinymce.init({
    selector: '.course_desc',
    height: 250,
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount'
    ],
    menubar: false,
    toolbar: 'undo redo | blocks | ' +
    'bold italic backcolor | alignleft aligncenter ' +
    'alignright alignjustify | bullist numlist outdent indent | ' +
    'removeformat | help',
    content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }'
});
</script>
