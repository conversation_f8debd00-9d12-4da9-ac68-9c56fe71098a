<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class HelpCenter extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('SubscribersModel');
    }
    public function index()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

		$data['current']['seo_title'] = 'Lagree On Demand Help Center | Lagree On Demand';
		$data['current']['seo_description'] = 'Need help using or building your Lagree Micro, Mini, or Megaformer? We are here to help! Get in touch with the Lagree On Demand help center!';

		echo view('helpcenter/index_view', $data);
    }
    public function account()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

		$data['current']['seo_title'] = 'Lagree On Demand Help Center | Lagree On Demand';
		$data['current']['seo_description'] = 'Need help using or building your Lagree Micro, Mini, or Megaformer? We are here to help! Get in touch with the Lagree On Demand help center!';
		echo view('helpcenter/account_view', $data);
    }
    public function subscription()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

		$data['current']['seo_title'] = 'Lagree On Demand Help Center | Lagree On Demand';
		$data['current']['seo_description'] = 'Need help using or building your Lagree Micro, Mini, or Megaformer? We are here to help! Get in touch with the Lagree On Demand help center!';
		echo view('helpcenter/subscription_view', $data);
    }
    public function watching()
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

		$data['current']['seo_title'] = 'Lagree On Demand Help Center | Lagree On Demand';
		$data['current']['seo_description'] = 'Need help using or building your Lagree Micro, Mini, or Megaformer? We are here to help! Get in touch with the Lagree On Demand help center!';
		echo view('helpcenter/watching_view', $data);
    }

}