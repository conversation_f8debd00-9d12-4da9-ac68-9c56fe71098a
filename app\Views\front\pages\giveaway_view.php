<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<style>
.line-input.border.px-2 + .input-label {
	left: 20px;
	top: -7px;
	font-size: 12px;
}
.line-input.border.px-2:focus, .line-input.border.px-2:active {
	color: #000 !important;
}
.joinform label, .joinform input {
	display: block;
	width: 100%;
}
input.error {
    border-color: red;
}
@media(max-width: 767px) {
    .subsc-human-box {
        margin-top: 30px;
        margin-bottom: 15px;
    }
}
</style>

</head>
<body>

<?php echo view('front/templates/header.php'); ?>

<main>
    <div class="black-banner flex aic jcc flex-column py-0">
        <h1>LAGREE GIVEAWAY</h1>
        <p>Submit the form below and get your spot in the giveaway!</p>
    </div>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
        <div class="container max550">
            <div class="col-12 px-0">
                <div class="pt-3 pb-6 pt-mob-2 pb-mob-3">
                    <div class="row mt-05">
                        <div class="col-12 px-mob-2">
                            <p class="f-14 f-12-mob text-center line-height-big bottom-border mt-15 pb-55 mb-4 mt-mob-0 pb-mob-3 mb-mob-2">Enter for your chance to win a one-year subscription to Lagree OD or 100 Loyalty points on Lagree Shop.</p>                                
                        </div>
                    </div>
                    <form action="register/saveGiveaway" class="joinform mt-05 mb-0" id="giveaway-form">
                        <input type="text" name="name" style="position: absolute; top: 1px;left: 1px;width: 1px;height: 1px;opacity: 0;pointer-events: none">
                        <div class="input-container mb-0">
                            <label>FIRST NAME</label>
                            <input type="text" name="firstname" placeholder="Enter">
                        </div>
                        <div class="input-container mb-0">
                            <label>LAST NAME</label>
                            <input type="text" name="lastname" placeholder="Enter">
                        </div>
                        <div class="input-container">
                            <label>EMAIL ADDRESS</label>
                            <input type="email" name="email" placeholder="Enter">
                        </div>
                        <!-- <div class="input-container">
                            <label>QUESTION FOR SEBASTIEN</label>
                            <textarea class="line-input" name="message" id="message" required placeholder="Enter"></textarea>
                        </div> -->
                        <div class="row row-mob flex aic subsc-human-box">
                            <div class="col-6 mob-half line-height-normal f-12">
                                Are you human 
                                <input type="text" name="s1" id="s1" value="" class="f-12" style="width: 20px;padding: 0;border: 0;display: inline;pointer-events: none;text-align: center;" readonly>+&nbsp;<span class="s2"></span>&nbsp;=&nbsp;
                            </div>
                            <div class="col-6 mob-half">
                                <input type="text" name="sum" class="line-input border px-2 f-14" placeholder="Enter" />
                            </div>
                        </div>
                        <div class="input-container mb-0 mt-1 mt-mob-1">
                            <button type="submit" class="btn black-bg white w100 f-14 f-12-mob h-54 h-mob-46">SUBMIT</button>
                        </div>
                    </form>
                </div>
                <p class="f-10 midGray lh-20 top-border pt-3 mt-mob-05 text-justify">
                    Terms & Conditions
                    <br><br>
                    NO PURCHASE NECESSARY TO ENTER OR WIN. A PURCHASE OR PAYMENT WILL NOT IMPROVE YOUR CHANCES OF WINNING.
                    <br><br>
                    Eligibility: This raffle is open to individuals aged 18 years or older at the time of entry. Employees, contractors, or affiliates of Lagree Fitness are not eligible to participate. Void where prohibited by law. By entering, participants agree to abide by these terms and conditions.
                    How to Enter: To enter, participants must submit their first and last name and a valid email address. Only one entry per person is allowed. Entries must be received by December 1, 2024, 11:59 PM PST.
                    <br><br>
                    Prize: One winner will be randomly selected to win one of the following prizes:
                    - A one-year subscription to Lagree on Demand, valued at $99
                    - 100 loyalty points to Lagreeshop.com, valued at $100
                    <br><br>
                    Selection of Winner: The winner will be chosen in a random drawing from all eligible entries. Odds of winning depend on the number of eligible entries received. The winner will be notified via the email address provided at the time of entry within 3 days following the close of the raffle.
                    Claiming the Prize: The winner must respond to the notification email within 7 days to claim the prize. If the winner does not respond within the stated time, another winner may be chosen. The prize is non-transferable and cannot be redeemed for cash.
                    <br><br>
                    Release of Liability: By entering, participants agree to release and hold harmless Lagree Fitness, and any affiliated parties from any and all liability, injuries, loss, or damage of any kind arising from or in connection with the raffle or any prize won.
                    <br><br>
                    Use of Data: By entering, participants agree to allow Lagree Fitness to contact them via the email address provided. Personal information will not be shared with third parties.
                    Governing Law: This raffle is governed by U.S. law, and any disputes will be subject to the exclusive jurisdiction of the courts of California.
                    <br><br>
                    Disclaimer: This raffle is void where prohibited by law. Lagree Fitness reserves the right to cancel, terminate, modify, or suspend the raffle in the event of unforeseen circumstances.
                    Questions: For questions, please contact <a href="mailto:<EMAIL>" class="midGray text-underline"><EMAIL></a>.
                </p>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
$('input').on('blur', function(e){
    if($(this).val() != '') {
        $(this).removeClass('error');
    }
});
$('#giveaway-form').on('submit', function(e){
	console.log('giveaway-form submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');
    $('#giveaway-form input').removeClass('error');

    $.ajax({
        type: 'POST',
        url: url,
        data: form.serialize(),
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                // button.removeClass('btn--loading');
                // form.trigger('reset');
                setTimeout(function(){
                    window.location = base_url + '/thank-you-giveaway';
                },2000);
            }else{
                console.log('NO SUCCESS');
                app_msg(data.message, 'danger', 2500, 1);
                if(data.json){
                    $.each(data.json, function(key, val){
                        $('#giveaway-form [name=' + key + "]").addClass('error');
                    });
                }else{
                    app_msg(data.message, 'danger', 2500, 1);
                }
                button.removeClass('btn--loading');
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});
</script>
</body>
</html>