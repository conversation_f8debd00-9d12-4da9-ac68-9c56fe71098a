<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;
use \DateTime;
use \DateInterval;
use \DatePeriod;

class Calendar extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
        $this->naviHref = htmlentities($_SERVER['PHP_SELF']);
		$this->model = model('CalendarModel');
	}
    private $dayLabels = array("Mon","Tue","Wed","Thu","Fri","Sat","Sun");
    private $currentYear = 0;
    private $currentMonth = 0;
    private $currentDay = 0;
    private $currentDate = NULL;
    private $daysInMonth = 0;
    private $naviHref = NULL;

    public function index(){
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $TeachersModel = model('TeachersModel');
        $ModelsModel = model('ModelsModel');
        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['teachers'] = $TeachersModel->all_certified_teachers();
        $data['models'] = $ModelsModel->all_models();
        echo view('admin/calendar/index_view', $data);
    }

    public function get_event(){
        $data = $this->request->getPost();

        $response = $this->model->query("SELECT calendar_events.id, calendar_events.is_note, calendar_events.paid, calendar_events.class_id, calendar_events.user_id, calendar_events.status, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, t2.id as teacher_as_model_id, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher, teachers.image as teacher_image, IF(models.firstname != '', CONCAT(models.firstname, ' ', models.lastname), 'Model is missing') as model, IF(t2.firstname != '', CONCAT(t2.firstname, ' ', t2.lastname), '') as teacher_as_model, IF(t2.image != '', t2.image, 0) as teacher_as_model_image, IF(calendar_events.date < CURRENT_DATE(), 'Recorded', 'Upcoming') as video_status
                                                        FROM calendar_events 
                                                        INNER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                        LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                        LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                        WHERE calendar_events.id = " . $data['id'] . "
                                                        AND calendar_events.deleted_at IS NULL
                                                ")->getRowArray();

        // echo '<pre>';
        // print_r($response);
        // die();
        

        if($response != NULL) {
            $response['success'] = TRUE;
            $response['date'] = date('m/d/Y', strtotime($response['date']));
        }

        return $this->respond($response);
    }

    public function get_event_info(){
        $ClassesModel = model('ClassesModel');
        $data = $this->request->getPost();

        $response = $this->model->query("SELECT calendar_events.id, calendar_events.is_note, calendar_events.title, calendar_events.content, calendar_events.paid, calendar_events.class_id, calendar_events.user_id, calendar_events.status, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, CONCAT(teachers.firstname, ' ', teachers.lastname) as teacher, teachers.image as teacher_image, IF(models.firstname != '', CONCAT(models.firstname, ' ', models.lastname), 'Model is missing') as model, IF(t2.firstname != '', CONCAT(t2.firstname, ' ', t2.lastname), '') as teacher_as_model, IF(calendar_events.date < CURRENT_DATE(), 'Recorded', 'Upcoming') as video_status
                                                        FROM calendar_events 
                                                        LEFT JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                        LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                        LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                        WHERE calendar_events.id = " . $data['id'] . "
                                                        AND calendar_events.deleted_at IS NULL
                                                ")->getRowArray();

        if($response != NULL) {
            if($response['class_id'] > 0){
                $response['class_info'] = class_info($response['class_id']);
                $response['teacher_exercises'] = $ClassesModel->exercises_for_single_class($response['class_id']);
            };
            $response['success'] = TRUE;
            $response['date'] = date('m/d/Y', strtotime($response['date']));
        }

        return $this->respond($response);
    }

    public function show() {
        $data = $this->request->getPost();

        $year  = NULL;
        $month = NULL;
        if($year == NULL && isset($data['year'])){
            $year = $data['year'];
        }else if($year == NULL){
            $year = date("Y", time());
        }

        if($month == NULL && isset($data['month'])){
             $month = $data['month'];
        }else if($month == NULL){
            $month = date("m", time());
        }
        $this->currentYear=$year;
        $this->currentMonth=$month;
        $this->daysInMonth=$this->_daysInMonth($month,$year);
        $content='<div id="calendar">'.
                        '<div class="box">'.
                        $this->_createNavi().
                        '</div>'.
                        '<div class="box-content">'.
                                '<ul class="label">'.$this->_createLabels().'</ul>';
                                $content .= '<div class="clear"></div>';
                                $content .= '<ul class="dates">';

                                $weeksInMonth = $this->_weeksInMonth($month,$year);
                                // Create weeks in a month
                                for( $i=0; $i<$weeksInMonth; $i++ ){

                                    //Create days in a week
                                    for($j=1;$j<=7;$j++){
                                        $content .= $this->_showDay($i * 7 + $j);
                                    }
                                }
                                $content.='</ul>';
                                $content.='<div class="clear"></div>';
                        $content .= '</div>';
        $content .= '</div>';

        $response['html'] = $content;
        return $this->respond($response);
    }

    private function _showDay($cellNumber){
        if($this->currentDay == 0){
            $firstDayOfTheWeek = date('N',strtotime($this->currentYear . '-' . $this->currentMonth . '-01'));
            if(intval($cellNumber) == intval($firstDayOfTheWeek)){
                $this->currentDay = 1;
            }
        }
        if(($this->currentDay != 0) AND ($this->currentDay <= $this->daysInMonth)){
            $this->currentDate = date('Y-m-d', strtotime($this->currentYear . '-' . $this->currentMonth . '-' . ($this->currentDay)));
            $cellContent = $this->currentDay;
            $this->currentDay++;
        }else{
            $this->currentDate = NULL;
            $cellContent = NULL;
        }

        $payment = [
            0 => 'paid',
            1 => 'notpaid',
            2 => '',
            3 => 'notpaid',
            4 => 'notpaid',
            5 => 'cancelled',
        ];
        $events = '';
        if($this->currentDate != NULL AND $this->currentDate != ''){
            $day_events = $this->model->query("SELECT calendar_events.id, calendar_events.is_note, calendar_events.title, calendar_events.content, IF(calendar_events.paid != 5, IF(calendar_events.date < CURRENT_DATE(), IF(calendar_events.paid = 2, 1, calendar_events.paid), 2), 5) as paid, calendar_events.class_id, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing'))) as teacher_model, IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing')) as any_model
                                                            FROM calendar_events 
                                                            LEFT JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                            LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                            LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                            LEFT JOIN classes ON classes.id = calendar_events.class_id
                                                            WHERE date = '" . $this->currentDate . "'
                                                            " . (session('super_admin') == 0 ? 'AND calendar_events.teacher_id = ' . session('admin') : '') . "
                                                            AND calendar_events.deleted_at IS NULL
                                                            AND classes.deleted_at IS NULL
                                                    ")->getResultArray();
            $c=0;
            foreach($day_events as $event){
                if($event['is_note'] == 1){
                    $events .= '<div class="single-event note lightBlue-bg blue" data-popup="show-note-popup" onclick="single_note('. $event['id'] . ');"><span class="calendar-events-name">' . $event['title'] . '</span></div>';
                }else{
                    $c++;
                    if($c <= 5){
                        $events .= '<div class="single-event ' . (($event['any_model'] == 'Model is missing' AND $event['paid'] != 5) ? 'model_missing' :  $payment[$event['paid']]) . '" data-popup="new-events-popup" onclick="event.stopPropagation();single_event('. $event['id'] . ');"><span class="calendar-events-name">' . $event['teacher_model'] . '</span><span class="calendar-events-time">' . $event['time'] . '</span></div>';
                    }else{
                        $events .= '<div class="link link-midGray midGray f-10 flex" onclick="get_all_day_events($(this), \'' . $this->currentDate . '\');event.stopPropagation();" style="margin-top: 5px">+' . (count($day_events) - 5) . ' classes</div>';
                        break;
                    }
                }
            }
        }

        $prev_days = date('N', strtotime($this->currentYear . '-' . $this->currentMonth . '-01'));
        $prev_date = date('Y-m-d', strtotime('-' . ($prev_days - $cellNumber) . ' days', strtotime($this->currentYear . '-' . $this->currentMonth . '-01')));
        $prev_day = date('d', strtotime('-' . ($prev_days - $cellNumber) . ' days', strtotime($this->currentYear . '-' . $this->currentMonth . '-01')));

        return '<li data-day="' . ($prev_date) . '" id="li-' . $this->currentDate . '" class="' . ($cellNumber % 7 == 1 ? ' start ' : ($cellNumber % 7 == 0 ? ' end ' : ' ')) . ($cellContent == NULL ? 'mask' : '') . ($this->currentDate == date("Y-m-d") ? ' today ' : '') . '" ' . (session('super_admin') != 0 ? 'data-popup="new-events-popup" onclick="new_event($(this), \'' . ($this->currentDate != NULL ? date("m/d/Y", strtotime($this->currentDate)) : date("m/d/Y", strtotime($prev_date))) . '\')"' : '') . '><span class="date-number">' . ($cellContent == NULL ? $prev_day : $cellContent).'</span>' . $events . '<div class="show_all_events" onclick="event.stopPropagation();"></div></li>';
    }
 
    public function day_events(){
        $data = $this->request->getPost();

        $payment = [
            0 => 'paid',
            1 => 'notpaid',
            2 => '',
            3 => 'notpaid',
            4 => 'notpaid',
            5 => 'cancelled',
        ];
        $response['success'] = FALSE;
        $response['html'] = "";
        if(isset($data['date'])){
            $response['events'] = $this->model->query("SELECT calendar_events.id, calendar_events.is_note, IF(calendar_events.paid != 5, IF(calendar_events.date < CURRENT_DATE(), IF(calendar_events.paid = 2, 1, calendar_events.paid), 2), 5) as paid, calendar_events.class_id, calendar_events.date, calendar_events.time, calendar_events.teacher_id, calendar_events.model_id, CONCAT(teachers.firstname, '/', IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing'))) as teacher_model, IF(models.firstname != '', models.firstname, IF(t2.firstname != '', t2.firstname, 'Model is missing')) as any_model
                                                            FROM calendar_events 
                                                            INNER JOIN teachers ON teachers.id = calendar_events.teacher_id
                                                            LEFT OUTER JOIN models ON models.id = calendar_events.model_id
                                                            LEFT OUTER JOIN teachers t2 ON t2.id = calendar_events.teacher_as_model_id
                                                            INNER JOIN classes ON classes.id = calendar_events.class_id
                                                            WHERE date = '" . $data['date'] . "'
                                                            AND calendar_events.deleted_at IS NULL
                                                            AND classes.deleted_at IS NULL
                                                            ")->getResultArray();
   
            if(count($response['events']) > 0){
                $response['success'] = TRUE;
                foreach($response['events'] as $event){
                    $response['html'] .= '<div class="single-event ' . (($event['any_model'] == 'Model is missing' AND $event['paid'] != 5) ? 'model_missing' :  $payment[$event['paid']]) . '" onclick="event.stopPropagation();single_event('. $event['id'] . ');"><span class="calendar-events-name">' . $event['teacher_model'] . '</span><span class="calendar-events-time">' . $event['time'] . '</span></div>';
                }
                $response['html'] .= '<span class="close_all_events" onclick="close_all_events($(this));event.stopPropagation();">x close</span>';
            }
        }

        return $this->respond($response);
    }
 
    private function _createNavi(){
        $nextMonth = $this->currentMonth == 12 ? 1 : intval($this->currentMonth) + 1;
        $nextYear = $this->currentMonth == 12 ? intval($this->currentYear) + 1 : $this->currentYear;
        $preMonth = $this->currentMonth == 1 ? 12 : intval($this->currentMonth) - 1;
        $preYear = $this->currentMonth == 1 ? intval($this->currentYear) - 1 : $this->currentYear;
        return
            '<div class="header py-5 mt-05 mb-05">'.
                '<span class="title f-14 black semibold line-height-small text-uppercase">'.date('F Y',strtotime($this->currentYear.'-'.$this->currentMonth.'-1')).'</span>'.
                '<div class="months-navigation">'.
                    '<a class="prev" href="javascript:;" onclick="call_month(' . $preMonth .', ' . $preYear . ')">Prev</a>'.
                    '<span class="title f-14 midGray normal line-height-small">'.date('F Y',strtotime($this->currentYear.'-'.$this->currentMonth.'-1')).'</span>'.
                    '<a class="next" href="javascript:;" onclick="call_month(' . $nextMonth . ', ' . $nextYear . ')">Next</a>'.
                '</div>';
            '</div>';
    }

    private function _createLabels(){
        $content='';
        foreach($this->dayLabels as $index=>$label){
            $content.='<li class="'.($label==6?'end title':'start title').' title">'.$label.'</li>';
        }
        return $content;
    }

    private function _weeksInMonth($month=NULL,$year=NULL){
        if($year == NULL) {
            $year =  date("Y",time());
        }

        if($month == NULL) {
            $month = date("m",time());
        }

        // find number of days in this month
        $daysInMonths = $this->_daysInMonth($month,$year);
        $numOfweeks = ($daysInMonths%7==0?0:1) + intval($daysInMonths/7);
        $monthEndingDay= date('N',strtotime($year.'-'.$month.'-'.$daysInMonths));
        $monthStartDay = date('N',strtotime($year.'-'.$month.'-01'));
        if($monthEndingDay<$monthStartDay){
            $numOfweeks++;
        }
        return $numOfweeks;
    }

    private function _daysInMonth($month=NULL,$year=NULL){
        if($year == NULL){
            $year =  date("Y",time());
        }
        if($month == NULL){
            $month = date("m",time());
        }
        return date('t',strtotime($year.'-'.$month.'-01'));
    }

    public function save_class(){
        $email_model = model('EmailModel');
        $ClassesModel = model('ClassesModel');
        $TeachersModel = model('TeachersModel');
        $ModelsModel = model('ModelsModel');

		$request = service('request');
        $data = $request->getPost();
        unset($data['model_type']);

        $response['data'] = $data;

        if(isset($data['teacher_id']) && $data['teacher_id'] != '' AND isset($data['time']) && $data['time'] != '' AND isset($data['date']) && $data['date'] != ''){
            // $send_email = ($data['id'] != '' AND $data['id'] > 0) ? FALSE : TRUE;
            $send_cancel_email = ($data['id'] != '' AND $data['id'] > 0) ? TRUE : FALSE;
            if($send_cancel_email){
                $current_event_info = $this->model->where(['id' => $data['id']])->first();
                $old_teacher = $TeachersModel->where(['id' => $current_event_info['teacher_id']])->first();
                $old_teacher_as_model = $TeachersModel->where(['id' => $current_event_info['teacher_as_model_id']])->first();
                $old_model = $ModelsModel->where(['id' => $current_event_info['model_id']])->first();
                $response['old_teacher'] = $old_teacher['id'];
                if(!empty($old_teacher_as_model)){
                    $response['old_teacher_as_model'] = $old_teacher_as_model['id'];
                }
                if(!empty($old_model)){
                    $response['old_model'] = $old_model['id'];
                }
            }
            
            $teacher = $TeachersModel->where(['id' => $data['teacher_id']])->first();
            if(isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != ''){
                $model = $ModelsModel->where(['id' => $data['model_id']])->first();
            }else{
                $model = NULL;
            };
            if(isset($data['teacher_as_model_id']) AND $data['teacher_as_model_id'] != 0 AND $data['teacher_as_model_id'] != ''){
                $teacher_as_model = $TeachersModel->where(['id' => $data['teacher_as_model_id']])->first();
            }else{
                $teacher_as_model = NULL;
            };
            $save_class = [
                'id' => (isset($data['class_id']) AND $data['class_id'] != '') ? $data['class_id'] : '', 
                'title' => 'Scheduled recording - ' . $teacher['firstname'] . '/' . ((isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != '') ? $model['firstname'] : ((isset($data['teacher_as_model_id']) AND $data['teacher_as_model_id'] != 0 AND $data['teacher_as_model_id'] != '')  ? $teacher_as_model['firstname'] : 'Model is missing')) . ' - ' . $data['date'] . ' @ ' . $data['time'], 
                'scheduled_title' => 'Scheduled recording - ' . $teacher['firstname'] . '/' . ((isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != '') ? $model['firstname'] : ((isset($data['teacher_as_model_id']) AND $data['teacher_as_model_id'] != 0 AND $data['teacher_as_model_id'] != '')  ? $teacher_as_model['firstname'] : 'Model is missing')) . ' - ' . $data['date'] . ' @ ' . $data['time'], 
                'slug' => $this->_generate_slug(),
                'duration' => 0, 
                'teacher' => $data['teacher_id'], 
                'status' => 1, 
                'notification_sent' => 1, 
                'type' => 0, 
                'language' => 1
            ];
            $response['class_save'] = $ClassesModel->save($save_class);
            $response['class_save_errors'] = $this->model->errors();
            $response['class_inserted_id'] = $data['class_id'] > 0 ? $data['class_id'] : $ClassesModel->getInsertID();

            if(isset($data['date']) AND Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles'))
            {
                $tmp = Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles');
                $data['date'] = $tmp->toDateString('Y-m-d');
            }

            $save_calendar = [
                'id' => (isset($data['id']) AND $data['id'] != '') ? $data['id'] : '', 
                'user_id' => (isset($data['user_id']) AND $data['user_id'] != '') ? $data['user_id'] : session('admin'), 
                'class_id' => $response['class_inserted_id'], 
                'date' => $data['date'],
                'time' => $data['time'], 
                'paid' => (isset($data['paid']) AND $data['paid'] != '') ? $data['paid'] : ($data['date'] >= date('Y-m-d') ? 2 : 1), 
                'status' => $data['status'], 
                'teacher_id' => $data['teacher_id'],
                'teacher_as_model_id' => $data['teacher_as_model_id'],
                'model_id' => $data['model_id']
            ];
            $response['success'] = $this->model->save($save_calendar);
            $response['errors'] = $this->model->errors();

            // teacher email
            if(!empty($teacher)){
                if(!empty($old_teacher)){
                    if($old_teacher['id'] != $teacher['id']){
                        $subject = 'Your scheduled LOD class recording is canceled';
                        $data_template = [
                            'title' => 'Scheduled recording is canceled',
                            'name' => $old_teacher['firstname'],
                            'date' => date('m/d/Y', strtotime($data['date'])),
                            'time' => $data['time'],
                        ];
                        $template = 'front/email_templates/class-recording-canceled';
                        $to = $old_teacher['email'];
                        $response['old_teacher_cancel_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                    }
                }
                $subject = 'Your LOD class recording is scheduled';
                $data_template = [
                    'click' => base_url() . '/admin/login',
                    'title' => 'Scheduled recording',
                    'teacher_name' => $teacher['firstname'],
                    'model_name' => (isset($data['model_id']) AND $data['model_id'] != 0 AND $data['model_id'] != '' ? $model['firstname'] : 'Model is missing'),
                    'date' => date('m/d/Y', strtotime($data['date'])),
                    'time' => $data['time'],
                ];
                $template = 'front/email_templates/class-recording-teacher';
                // $to = '<EMAIL>';
                $to = $teacher['email'];
                $response['teacher_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
            }
            // model email
            if(!empty($model)){
                if(!empty($old_model)){
                    if($old_model['id'] != $model['id']){
                        $subject = 'Your scheduled LOD class recording is canceled';
                        $data_template = [
                            'title' => 'Scheduled recording is canceled',
                            'name' => $old_model['firstname'],
                            'date' => date('m/d/Y', strtotime($data['date'])),
                            'time' => $data['time'],
                        ];
                        $template = 'front/email_templates/class-recording-canceled';
                        $to = $old_model['email'];
                        $response['old_model_cancel_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                    }
                }
                $subject = 'Your LOD class recording is scheduled';
                $data_template = [
                    'title' => 'Scheduled recording',
                    'teacher_name' => $teacher['firstname'],
                    'model_name' => $model['firstname'],
                    'date' => date('m/d/Y', strtotime($data['date'])),
                    'time' => $data['time'],
                ];
                $template = 'front/email_templates/class-recording-model';
                $to = $model['email'];
                $response['teacher_as_model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
            }
            // teacher as a model email
            if(!empty($teacher_as_model)){
                if(!empty($old_teacher_as_model)){
                    if($old_teacher_as_model['id'] != $teacher['id']){
                        $subject = 'Your scheduled LOD class recording is canceled';
                        $data_template = [
                            'title' => 'Scheduled recording is canceled',
                            'name' => $old_teacher_as_model['firstname'],
                            'date' => date('m/d/Y', strtotime($data['date'])),
                            'time' => $data['time'],
                        ];
                        $template = 'front/email_templates/class-recording-canceled';
                        $to = $old_teacher_as_model['email'];
                        $response['old_teacher_as_model_cancel_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
                    }
                }
                $subject = 'Your LOD class recording is scheduled';
                $data_template = [
                    'title' => 'Scheduled recording',
                    'teacher_name' => $teacher['firstname'],
                    'model_name' => $teacher_as_model['firstname'],
                    'date' => date('m/d/Y', strtotime($data['date'])),
                    'time' => $data['time'],
                ];
                $template = 'front/email_templates/class-recording-teacher-as-model';
                $to = $teacher_as_model['email'];
                $response['model_email_sent'] = $email_model->send_template($to, FALSE, $subject, $data_template, $template);
            }
        }else{
            $response['success'] = FALSE;
            $response['message'] = 'Please fill all fields';
        }

		return $this->respond($response);
    }

    public function save_new_note(){
		$request = service('request');
        $data = $request->getPost();
        $end_date = FALSE;

        if(isset($data['date']) AND Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles'))
        {
            $tmp = Time::createFromFormat("m/d/Y" , $data['date'], 'America/Los_Angeles');
            $data['date'] = $tmp->toDateString('Y-m-d');
        }
        if(isset($data['end_date']) AND $data['end_date'] != '' AND Time::createFromFormat("m/d/Y" , $data['end_date'], 'America/Los_Angeles'))
        {
            $tmp = Time::createFromFormat("m/d/Y" , $data['end_date'], 'America/Los_Angeles');
            $end_date = $tmp->toDateString('Y-m-d');
            unset($data['end_date']);
        }

        if($end_date) {
            $dates = $this->_dates_array($data['date'], $end_date);
            foreach($dates as $key => $single){
                $data['date'] = $single;
                $response['notes'][$key]['data'] = $data;
                $response['notes'][$key]['note_save'] = $this->model->save($data);
                $response['notes'][$key]['note_save_errors'] = $this->model->errors();
                $response['notes'][$key]['note_save_ID'] = $this->model->getInsertID();
            }
        }else{
            $response['data'] = $data;
            $response['note_save'] = $this->model->save($data);
            $response['note_save_errors'] = $this->model->errors();
            $response['note_save_ID'] = $this->model->getInsertID();
        }

        if(isset($response['note_save']) OR isset($response['notes'][0]['note_save'])){
            $response['success'] = TRUE;
        }else{
            $response['success'] = FALSE;
        }

		return $this->respond($response);
    }

    private function _dates_array($from = 0, $to = 0){
        if($from != 0 && $to != 0){
            $start = new DateTime($from);
            $end = new DateTime($to);
    
            $end->modify('+1 day');
            $interval = new DateInterval('P1D');
            $daterange = new DatePeriod($start, $interval, $end);
            $dateArray = [];
            foreach ($daterange as $date) {
                $dateArray[] = $date->format('Y-m-d');
            }
        }else{
            $dateArray = [];
        }

        return $dateArray;
    }
    public function pay_status(){
		$request = service('request');
        $data = $request->getPost();
        $response['data'] = $data;

        $save_paid_status = [
            'id' => $data['id'], 
            'paid' => $data['paid']
        ];
        $response['success'] = $this->model->save($save_paid_status);
        $response['paid_status_save_errors'] = $this->model->errors();

		return $this->respond($response);
    }

    public function delete_note($id = 0){
        $response['success'] = FALSE;
        if($id != 0){
            $response['success'] = $this->model->delete($id);
        }

		return $this->respond($response);
    }

    private function _generate_slug($length = 8) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

}