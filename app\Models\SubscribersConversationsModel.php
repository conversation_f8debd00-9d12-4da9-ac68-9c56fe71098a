<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class SubscribersConversationsModel extends Model
{
    protected $table = 'subscribers_conversations';
	protected $allowedFields = ['conversation_id', 'sender_id', 'receiver_id', 'message', 'type', 'file', 'file_name', 'date', 'seen', 'admin_seen', 'useful', 'hide'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    protected $validationRules    = [
        // 'conversation_id'     => 'required',
        // 'sender_id'     => 'required',
        // 'receiver_id'     => 'required',
        // 'message'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	protected function prepare_data(array $data)
	{
		return $data;
	}

}