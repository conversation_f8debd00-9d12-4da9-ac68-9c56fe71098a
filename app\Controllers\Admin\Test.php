<?php namespace App\Controllers\Admin;

use App\Models\StripeModel;
use App\Models\ClassesModel;
use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Test extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
	}

    public function index()
    {
		echo "For testing";
    }

    public function stripe()
    {
		$stripe_model = new StripeModel();
		//$balance = $stripe_model->balance_transactions();
		//$balance = $stripe_model->all_balances();
		//$earnings = $stripe_model->earnings();
		//$subscriptions = $stripe_model->list_subscriptions();
		//$subscriptions = $stripe_model->all_subscriptions();
		//$subscribers = $stripe_model->subscribers();
		//$subscription = $stripe_model->retrieve_subscription('sub_1JmvtPA6pGkO4ATFRqvjAz6M');
		//$sources = $stripe_model->list_sources('cus_KURcEl5pJvP9PQ');
		//$sources = $stripe_model->get_customer('cus_KR5VyMDw7EtdLR');
		//$sources = $stripe_model->customer_invoices('cus_KR5VyMDw7EtdLR');
		//$accounts = $stripe_model->list_accounts();
		// $accounts = $stripe_model->create_account();
		//$accounts = $stripe_model->delete_account('acct_1K4kOpPKLQgKonuN');
		$transfer = $stripe_model->create_transfer();
		// $transfer = $stripe_model->list_transfers('acct_1K4kbUPAIJCvPNCa');

		// $data['card'] = array(
		// 	'name' => 'Card for API',
		// 	'number' => '****************',
		// 	'exp_month' => '12',
		// 	'exp_year' => '2022',
		// 	'cvc' => '888',
		// 	'currency' => 'USD',
		// );
		// $token = $stripe_model->create_card_token($data['card']);

		echo '<pre>';
		//var_dump($balance);
		// print_r($accounts);
		print_r($transfer);
		// print_r($token);
		//print_r($balance);
    }

    public function use_helper()
    {
		//File is in app/Helpers/admin_helper.php
		helper("admin");
		echo "<b>Slug Function: </b>" . slugify('Miloš Đonović sa našim slovima, asdh. Č asdasd ć " asd sva đšćčž slova');
    }
    public function test_ffmpeg()
    {
        // $video_path = base_url() . 'uploads/videos/1646866474_770f02a75d2dbce1a551.mp4';
        // $tmp1 = $_SERVER['DOCUMENT_ROOT'] . 'uploads/videos/test-thumbnail1.jpg';
        // $exec = exec("ffmpeg -ss 00:00:20 -i " . str_replace("\\","/" , $video_path ) . " -an -r 1 -vframes 1 -y -vsync vfr -filter:v scale=1280:720,crop=1280:720 " . str_replace("\\","/" , $tmp1));

        // print_r($exec);
        // $ffmpeg = trim(shell_exec('which ffmpeg')); // or better yet:
        echo shell_exec('ffmpeg -version');
    }
    public function check_classes()
    {
        // $classesModel = new ClassesModel();
        $classesModel = model('ClassesModel');

        $cc = $classesModel->query("SELECT id, title, video, video_preview FROM classes WHERE video_preview = '' AND deleted_at IS NULL ORDER BY id desc")->getResultArray();

        // print_r($cc);
        echo '<div style="padding: 20px 40px">';
        $brojac = 0;
        foreach($cc as $key => $single){
            $brojac++;
            if (strpos($single['video'], 'http://') !== false OR strpos($single['video'], 'https://') !== false) {
                $video_path = $single['video'];
            } else {
                $video_path = base_url() . '/' . $single['video'];
            }

            echo "<br><br>" . $brojac . ". VIDEO (" . $single['id'] . "): " . $single['title'] . " <br>&nbsp;&nbsp;&nbsp;&nbsp; " . "video_path: " . $video_path . "<br>&nbsp;&nbsp;&nbsp;------------- CREATING VIDEO PREVIEW --------------<br>";

            $video_path_check = str_replace('https://phpstack-694549-2294802.cloudwaysapps.com/', '', str_replace('https://staging.lagreeod.com/', '', $single['video']));
            if (file_exists($video_path_check)) {

                /////////////////////////////////////////////////////////////////////
                $dur = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 '$video_path'");
                $seconds = round($dur);

                $thumb_0 = gmdate('H:i:s', $seconds / 8);
                $thumb_1 = gmdate('H:i:s', $seconds / 4);
                $thumb_2 = gmdate('H:i:s', $seconds / 2 + $seconds / 8);
                $thumb_3 = gmdate('H:i:s', $seconds / 2 + $seconds / 4);

                $path_clip = './uploads/videos/video_preview';
                $preview_list = fopen($path_clip . '/list.txt', "w");
                $preview_array = [];

                for ($i=0; $i <= 3; $i++) {
                    $thumb = ${'thumb_'.$i};
                    shell_exec("ffmpeg -i '$video_path' -an -ss $thumb -t 3 -vf 'scale=320:180:force_original_aspect_ratio=decrease,pad=320:180:(ow-iw)/2:(oh-ih)/2,setsar=1' -y $path_clip/$i.p.mp4");

                    $output = $path_clip . '/' . $i.'.p.mp4';

                    if (file_exists($output)) {
                        // echo $output . 'File moved';
                        fwrite($preview_list, "file '". $output . "'\n");
                        array_push($preview_array, $output);
                    }else{
                        echo 'file od NOT exist<br>';
                    }
                }
                fclose($preview_list);

                shell_exec("ffmpeg -i $path_clip/0.p.mp4 -i $path_clip/1.p.mp4 -i $path_clip/2.p.mp4 -i $path_clip/3.p.mp4 -filter_complex \"concat=n=4:v=1:a=0\" -vn -y $path_clip/preview_new.mp4");

                if (!empty($preview_array)) {
                    foreach ($preview_array as $v) {
                        unlink($v);
                    }
                }

                // remove preview list
                unlink($path_clip . '/list.txt');

                $file_preview = "$path_clip/preview_new.mp4";
                $file_preview1 = new \CodeIgniter\Files\File($file_preview);
                $file_preview_name1 = $file_preview1->getRandomName();
                $file_preview1->move(ROOTPATH . 'public/uploads/videos/video_preview', $file_preview_name1);

                $save_data = array(
                    'id' => $single['id'],
                    'video_preview' => '/uploads/videos/video_preview/' . $file_preview_name1
                );
                $saved = $classesModel->save($save_data);

                if($saved){
                    echo "<br>SAVED: " . '/uploads/videos/video_preview/' . $file_preview_name1 . "<br>";
                    echo "VIDEO preview: " . '/uploads/videos/video_preview/' . $file_preview_name1 . "<br>------------- DONE --------------<br><br>";
                }

                // echo '&nbsp;&nbsp;&nbsp;Video exists!<br>';
            }else{
                echo '&nbsp;&nbsp;&nbsp;Video file do NOT exist<br>';
            }
            if($brojac > 2){
                exit;
                die();
            }
        }
        echo '</div>';
    }
}