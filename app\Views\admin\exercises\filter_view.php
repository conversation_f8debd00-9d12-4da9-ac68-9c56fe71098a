<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.w-100px {
	width: 120px;
	min-width: 120px;
}
.search-container {
	position: relative;
	height: 40px;
}
.search-form {
	position: relative;
	height: 40px;
	margin-left: 20px;
	top: auto;
	right: auto;
}
.search-form .seach-input {
	height: 40px;
	width: 40px;
}
.search-form .search-button {
	top: 2px;
	right: 2px;
	width: 36px;
	height: 36px;
}
.with-comma {
	display: flex;
}
.with-comma span {
  position: relative;
  margin-left: 5px;
}
.with-comma span:last-child::after {
  display: none;
}
.with-comma span::after {
  content: ",";
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">exercises</h1>
                <?php if($logged_user['super_admin'] == 1){ ?>
                <div class="ml-auto">
                    <a href="admin/exercises/edit" class="btn black-bg white" title="Upload">Upload</a>
                    <a href="admin/exercises/multi" class="btn btn-border white-bg black ml-2 ml-mob-5" title="Bulk">Bulk</a>
                </div>
                <?php } ?>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <?php
                    // $active_class_type = isset($filter['type'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['type'][0]))) : [];
                    $active_machines = isset($filter['machine'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['machine'][0]))) : [];
                    $active_body_parts = isset($filter['body_parts'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['body_parts'][0]))) : [];
                    $active_teacher = isset($filter['teacher'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['teacher'][0]))) : [];
                    $active_difficulty = isset($filter['difficulty'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['difficulty'][0]))) : [];
                    $active_language = isset($filter['language'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['language'][0]))) : [];
                    $active_duration = isset($filter['duration'][0]) ? str_replace("[", "", str_replace("]","", explode(',', $filter['duration'][0]))) : [];
                ?>
                <?php if(isset($filter) AND is_array($filter) AND ((!empty($active_machines) AND $active_machines[0] != "") OR
                                (!empty($active_duration) AND $active_duration[0] != "") OR
                                (!empty($active_difficulty) AND $active_difficulty[0] != "") OR
                                (!empty($active_language) AND $active_language[0] != "") OR
                                (!empty($active_body_parts) AND $active_body_parts[0] != "") OR
                                (!empty($active_teacher) AND $active_teacher[0] != "")
                        )){ ?>
                    <div class="flex aic jcl">
                        <a href="javascript:;" class="flex aic jcc f-12" onclick="check_filter_seleboxes()" data-popup="exercises-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /></a>
                        <span class="f-12 mr-05">Active filters: </span>
                        <span class="with-comma">
                            <!-- <?php // if($active_class_type[0] != ""){ ?><span class="f-12">Class Type</span><?php // } ?> -->
                            <?php if(!empty($active_machines) AND $active_machines[0] != ""){ ?><span class="f-12">Machine</span><?php } ?>
                            <?php if(!empty($active_duration) AND $active_duration[0] != ""){ ?><span class="f-12">Duration</span><?php } ?>
                            <?php if(!empty($active_difficulty) AND $active_difficulty[0] != ""){ ?><span class="f-12">Difficulty</span><?php } ?>
                            <?php if(!empty($active_language) AND $active_language[0] != ""){ ?><span class="f-12">Language</span><?php } ?>
                            <?php if(!empty($active_body_parts) AND $active_body_parts[0] != ""){ ?><span class="f-12">Body Parts</span><?php } ?>
                            <?php if(!empty($active_teacher) AND $active_teacher[0] != ""){ ?><span class="f-12">Teacher</span><?php } ?>
                        </span>
                        <a href="admin/exercises/clear_filter" class="link link-midGray midGray clear_filter f-12 ml-2">x Clear</a>
                    </div>
                <?php }else{ ?>
                    <a href="javascript:;" class="flex aic jcc f-12" onclick="check_filter_seleboxes()" data-popup="exercises-filter" title=""><img src="admin_assets_new/images/filter-icon.svg" alt="" class="img-fluid mr-1" /> Apply filters</a>
                <?php } ?>
                <div class="flex aic jcsb">
                    <div class="dropdown d-inline-block">
                        <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                        <ul class="dropdown-menu drop-right row-vertical">
                            <li><a href="javascript:;" data-val="exercises.created_at" data-by="desc" class="set_sort_by link midGray <?php echo ($exercises_sort[0] == 'exercises.created_at' AND $exercises_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Date Added</a></li>
                            <li><a href="javascript:;" data-val="exercises.title" data-by="asc" class="set_sort_by link midGray <?php echo ($exercises_sort[0] == 'exercises.title' AND $exercises_sort[1] == 'asc') ? 'selected' : ''; ?>" title="">Ascending</a></li>
                            <li><a href="javascript:;" data-val="exercises.title" data-by="desc" class="set_sort_by link midGray <?php echo ($exercises_sort[0] == 'exercises.title' AND $exercises_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Descending</a></li>
                            <li><a href="javascript:;" data-val="countView" data-by="desc" class="set_sort_by link midGray <?php echo ($exercises_sort[0] == 'countView' AND $exercises_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Popularity</a></li>
                            <li><a href="javascript:;" data-val="exerciseRate" data-by="desc" class="set_sort_by link midGray <?php echo ($exercises_sort[0] == 'classRate' AND $exercises_sort[1] == 'desc') ? 'selected' : ''; ?>" title="">Best Rated</a></li>
                        </ul>
                    </div>
                    <div class="search-container">
                        <form action="admin/exercises" method="GET" class="search-form <?php echo (isset($search_term) AND $search_term != '0') ? 'show' : ''; ?>">
                            <input type="text" name="search_term" class="seach-input" value="<?php echo (isset($search_term) AND $search_term != "0") ? $search_term : ''; ?>">
                            <?php if(isset($search_term) AND $search_term != "0"){ ?>
                            <a href="admin/exercises/clear_search" class="clear_search" style="font-size: 18px;right: 40px;">×</a>
                            <?php } ?>
                            <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                        </form>
                    </div>
                </div>
            </div>
            <?php if($logged_user['super_admin'] == 1){ ?>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="exercises" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                    <form class="edit_checked" style="display: none" method="post" action="admin/exercises/edit_bulk">
                        <input type="hidden" name="ids" class="bulk_ids">
                        <button type="submit" class="ml-3 f-12 link flex aic edit_bulk midGray" style="background: #fff !important;">Edit bulk (<span class="checked-amount">2</span>)</button>
                    </form>
                </div>
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $exercises_count == 1 ? $exercises_count . ' Class' : $exercises_count . ' exercises'; ?></h5>
            </div>
            <?php } ?>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders">
<?php
foreach($all_exercises as $single){
?>
                        <div class="table-row exercises-class" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <?php if($logged_user['super_admin'] == 1){ ?>
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <?php } ?>
                                <?php if($logged_user['super_admin'] == 1){ ?>
                                <a href="admin/exercises/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="mr-3" onclick="save_referer_main($(this));return false;">
                                <?php }else{ ?>
                                <a href="javascript:;" data-popup="teacher-exercise-preview" onclick="teacher_exercise_preview(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>);" class="mr-3" title="">
                                <?php } ?>
                                    <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="" class="img-fluid" style="max-width: 210px;max-height: 120px;height: 120px;width: 210px;object-fit: cover;<?php echo($single['status'] == 1) ? 'opacity: 0.3 !important' : ''; ?>" />
                                </a>
                                <div class="flex flex-column">
                                    <?php if($logged_user['super_admin'] == 1){ ?>
                                    <a href="admin/exercises/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium mb-05 flex aic" onclick="save_referer_main($(this));return false;">
                                    <?php }else{ ?>
                                    <a href="javascript:;" data-popup="teacher-exercise-preview" onclick="teacher_exercise_preview(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>);" class="most-title medium mb-05 flex aic">
                                    <?php } ?>
                                        <?php if($single['type'] == 0){ ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 30 30" class="mr-1 hide">
                                            <g id="Group_10334" data-name="Group 10334" transform="translate(-100 -1359)">
                                                <rect id="Rectangle_1481" data-name="Rectangle 1481" width="30" height="30" rx="15" transform="translate(100 1359)"/>
                                                <path id="Path_4894" data-name="Path 4894" d="M4.92-1.416c-1.4,0-2.412-1.08-2.412-2.832v-.1c0-1.728.9-2.82,2.388-2.82s2.388,1.14,2.388,2.808v.1C7.284-2.532,6.372-1.416,4.92-1.416ZM4.884.132A4.217,4.217,0,0,0,9.312-4.284v-.1A4.171,4.171,0,0,0,4.9-8.7,4.242,4.242,0,0,0,.48-4.332v.1A4.179,4.179,0,0,0,4.884.132ZM12.66-1.512V-7.068h.768C15.24-7.068,16-6.12,16-4.356v.1c0,1.776-.816,2.748-2.544,2.748ZM10.716,0h2.8c3.012,0,4.512-1.716,4.512-4.284v-.1c0-2.568-1.488-4.2-4.5-4.2H10.716Z" transform="translate(106 1378)" fill="#fff"/>
                                            </g>
                                        </svg>
                                        <?php }else{ ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 30 30" class="mr-1">
                                            <g id="buy-class-icon" transform="translate(-100 -1359)">
                                                <g id="Rectangle_1482" data-name="Rectangle 1482" transform="translate(100 1359)" fill="none" stroke="#000" stroke-width="1">
                                                <rect width="30" height="30" rx="15" stroke="none"/>
                                                <rect x="0.5" y="0.5" width="29" height="29" rx="14.5" fill="none"/>
                                                </g>
                                                <text id="_" data-name="$" transform="translate(111 1378)" font-size="12" font-family="Graphik-Semibold, Graphik" font-weight="600"><tspan x="0" y="0">$</tspan></text>
                                            </g>
                                        </svg>
                                        <?php } ?>
                                        <span class="title_to_rename"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span><?php echo (isset($single['aka']) AND $single['aka'] != '') ? " (" . $single['aka'] . ")" : ''; ?>
                                    </a>
                                    <span class="midGray mb-05 f-1">
                                        <?php echo $single['all_exercise_machines'] ?>,
                                        <?php echo (isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? duration_standard2($single['duration']) : 0; ?>,
                                        <span class="blockmob"></span><?php echo (isset($single['diff']) AND $single['diff'] != '') ? 'Difficulty: ' . $single['diff'] : ''; ?>
                                    </span>
                                    <span class="midGray f-1">Upload Date: <?php echo (isset($single['created_at']) AND $single['created_at'] != '') ? date('m/d/Y', strtotime($single['created_at'])) : ''; ?></span>
                                    <?php if($logged_user['super_admin'] == 1){ ?>
                                    <a href="javascript:;" class="link link-midGray midGray f-1 mt-15" onclick="prepare_rename(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" data-popup="rename-popup">Rename</a>
                                    <div class="row-actions f-1 red">
                                        <a href="admin/exercises/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1" onclick="save_referer_main($(this));return false;">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="exercises">Delete</a>-->
                                    </div>
                                    <?php } ?>
                                </div>
                                <div class="flex flex-column ml-auto text-right f-1">
                                    <?php if($single['status'] == 1){ ?>
                                        <span class="btn btn-xs status-orange f-12 btnadmin">Draft</span>
                                    <?php }else{ ?>
                                        <?php if($logged_user['super_admin'] == 1){ ?>
                                        <span class="most-title nrviews"><?php echo (isset($single['countView']) AND $single['countView'] != '') ? $single['countView'] : ''; ?> views</span>
                                        <span class="red f-12"><?php echo (isset($single['exerciseRate']) AND $single['exerciseRate'] != '') ? number_format($single['exerciseRate'], 1) : ''; ?> <i class="icon-star" style="position: relative;top: 0px;"></i></span>
                                        <?php } ?>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('exercises_per_page')) - session('exercises_per_page')) + ($exercises_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('exercises_per_page')) - session('exercises_per_page')) + count($all_exercises); ?><span class="midGray mx-1">of <?php echo $exercises_count; ?></span>

                    <a href="admin/exercises/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>

                    <a href="admin/exercises/<?php echo $segment == 'filter' ? 'filter' : 'page'; ?>/<?php echo $page + 1; ?><?php echo isset($search_term) ? '?search_term=' . $search_term : ''; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_exercises) < session('exercises_per_page')) OR (((($page * session('exercises_per_page')) - session('exercises_per_page')) + count($all_exercises)) == $exercises_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('exercises_per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray set_per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="admin_assets_new/js/smooth-scrollbar.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
Scrollbar.initAll({
    continuousScrolling: true,
    renderByPixels: true,
    alwaysShowTracks: true,
    overscrollEffect: true,
    overscrollDamping: 0.2
});
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('per_page') == "") ? 10 : session('per_page'); ?>;
var order = 'exercises.created_at DESC';
function prepare_rename(id){
    var elem = $('.exercises-class[data-id="' + id + '"]');
    var title = elem.find('.title_to_rename').text();
    $('.exercise_old_title').text(title);
    $('#rename_id').val(id);
    $('#exercise_title').val('');
}
function save_referer_main(xx){
    var url = xx.attr('href');
    $.ajax({
        type: 'POST',
        url: 'admin/exercises/save_referer_main',
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('save_referer_main Success');
            if(data.success){
                console.log('exercise type: main');
            }
            window.location.href = url;
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function teacher_exercise_preview(id){
    $.ajax({
        type: 'POST',
        url: 'admin/exercises/exercise_info_teacher_preview/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('exercise_info_teacher_preview Success');
            if(data.success){
                $('.popup-teacher-exercise-preview-html').html(data.html);
                $('.exercise_name_preview').html(data.title);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
var video = document.getElementById("my_video");

document.getElementById('overlay').addEventListener('click', function (e) {
    video.stop;
});
function clear_popup(){
    setTimeout(function(){
        $('.popup-teacher-exercise-preview-html').html('');
    }, 300);
}
</script>
</body>
</html>