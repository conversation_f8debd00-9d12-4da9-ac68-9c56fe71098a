<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<style>
.subscript-invoice {
  margin-left: 10px !important;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #ddd;
  border-radius: 50px;
  padding: 9px 15px;
}
.subscript-invoice:hover {
    background:#000;
    color:#fff;
    border-color:#000;
}
.pay-history-box {
    border-radius: 8px;
  padding: 24px 25px 24px 30px;
  margin-bottom: 15px;
  border:1px solid #f0f0f0;
}
</style>
</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 mt-1 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
        <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                        PAYMENT HISTORY
                        </h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12 pl-0">
                    <div class="payments-wrapper">
<?php
$c=0;
if(!empty($invoices['invoices']) AND isset($invoices['invoices']['data'])){
    foreach ($invoices['invoices']['data'] as $invoice) {
    $c++;
?>
                        <div class="panel flex-row pay-history-box" data-invoice_id="<?php echo $invoice['id']; ?>">
                            <div class="flex flex-column">
                                <?php foreach ($invoice['lines']['data'] as $key => $line) { ?>
                                    <div class="flex <?php echo (count($invoice['lines']['data']) > 1 AND $key == 0) ? 'first-multi-row' : ''; ?>">
                                        <div class="flex flex-column">
                                            <p class="f-12 line-height-small mb-05 text-transf-none medium"><?php echo $line['plan']['nickname']; ?> Subscription $<?php echo $line['amount'] / 100; ?></p>
                                            <div class="">
                                                <p class="midGray f-12 f-12-mob line-height-small"><?php echo date('m/d/Y', $invoice['created']); ?></p>
                                                
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                            <div class="ml-auto f-12-mob pay-status">
                                <!-- <span class="red">Failed</span> -->
                                <span class="<?php echo $invoice['paid'] == 1 ? 'green' : 'red'; ?>"><?php echo $invoice['paid'] == 1 ? 'Paid' : 'Failed'; ?></span>
                                <!-- <a href="<?php echo $invoice['invoice_pdf']; ?>" target="_blank" class="btn btn-sm btn-border white-bg black h-auto f-10-mob" title="pdf" style="margin-left:15px !important;">pdf</a> -->
                                <a href="account/payments_history_download/<?php echo $invoice['id']; ?>/1" target="_blank" class="subscript-invoice" title="pdf" target="_blank">Invoice</a>
                            </div>
                        </div>
<?php
    }
}else{
?>
                        <div class="panel flex-row mb-2 p-3">
                            <h3 class="m-0 f-16 bold text-uppercase line-height-small">No previous payments</h3>
                        </div>

<?php
}
?>


                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>