<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Users extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('UsersModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
		$data['title'] = 'List of users';
		echo view('admin/users/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
		$data['title'] = 'Single user';
		$data['current'] = $this->model->where(['id' => $edit_id])->first();
		$usertypes = model('UsertypesModel');
		$data['usertypes'] = $usertypes->orderBy('title', 'asc')->findAll();
		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('admin/users/edit_view', $data);
    }

    public function save()
    {
		$data = $this->request->getPost();
		$response['message'] = 'Data successfully saved';
		$response['success'] = $this->model->save($data);
		if ($response['success'] === false)
		{
			$response['message'] = implode('</br>', $this->model->errors());
		}
		else
		{
			$response['inserted_id'] = $this->model->getInsertID();
		}

		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function delete_record($record_id = 0, $ajax = FALSE)
    {
		if ($record_id > 0)
		{
			$response['success'] = $this->model->delete($record_id);
		}
		return redirect()->to(site_url('admin/users'));
    }
}