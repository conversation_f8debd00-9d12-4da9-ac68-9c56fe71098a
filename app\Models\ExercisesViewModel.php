<?php namespace App\Models;

use CodeIgniter\Model;

class ExercisesViewModel extends Model
{
    protected $table = 'exercises_views';
	protected $allowedFields = ['exercise_id', 'user_id', 'date'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}