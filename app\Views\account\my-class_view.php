<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<style>
    .upload-zone {
        background: #fff;
        border: none;
        min-height: 500px;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .upload-zone::before {
        content: "";
        position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
    }
    .upload-zone.dragOver::before {
        content: "Drop your video file here";
        font-size: 24px;
        color: rgb(0 0 0);
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        border: 10px solid #f0f0f0;
        top: -5px;
        bottom: -5px;
        left: -5px;
        right: -5px;
    }
    .upload-zone.dragOver {
        background: #f8f8f8;
        border: none;
    }
    #main_form h3.mb-3 {
        font-size: 18px !important;
    }
    .line-input.error {
        border-width: 2px !important;
        border-color: red !important;
    }
    .input-container {
        position: relative;
    }
    .input-container.has-error::before {
        content: "required";
        font-size: 12px;
        position: absolute;
        top: 20px;
        right: 15px;
        color: red;
        text-transform: uppercase;
    }
    .checkbox.has-error label {
        color: red;
    }
</style>

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="container750">
            <div class="row">
                <div class="col-12">
                    <div class="flex aic jcl flex-column-mob">
                        <span class="avatar150 mr-4 mb-mob-3 mr-mob-0">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column aic-mob">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 mt-1 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="py-0">
        <div class="container750">
            <div class="row">
                <div class="col-12">
                    <div class="pt-5 pt-mob-4 pb-4 bottom-border flex aic jcsb">
                        <h2 class="f-18 flex aic jcsb mob-w100 mb-mob-0 line-height-small semibold mb-05">
                            <?php echo isset($current['id']) ? '' : 'Upload' ?> Class <?php echo isset($current['id']) ? 'Details' : '' ?>
                            <span class="btn btn-xs red-bg white f-1 ml-2" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                        </h2>
                        <div class="dropdown flex aic">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                            <?php echo view('front/templates/account-left-menu.php'); ?>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="py-5 bottom-border tab_content">
                        <form action="account/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone front_class_upload" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 400px;"' : '' ?>>
                            <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                            <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                                <span class="f-16 bold text-uppercase">Drag and drop video files to upload or <span class="link text-underline video_choose">select a file.</span></span>
                            </div>
                            <div class="video_placeholder">
                                <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                            </div>
                            <span id="video-is-uploading"></span>
                            <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                            <span id="progress-bar-status-show"></span>
                            <span id="toshow" style="display: none;"></span>
                        </form>

                        <div class="flex aic jcsb mt-1">
                            <span id="remove_video" class="link link-red red text-underline f-14" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                            <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                        </div>
                    </div>
                </div>
                <div class="col-12 py-5 bottom-border">
                    <div class="black-bg p-5 user-upload-info">
                        <div class="flex aic flex-column-mob">
                            <div class="small-box white-bg black mr-4">RULES</div>
                            <div class="white">
                                <p class="f-16 semibold">YOUR CLASS MUST MEET THE FOLLOWING REQUIREMENTS:</p>
                                <ol class="f-14 lh-25">
                                    <li>Recorded in 1080p (1920x1080 Full HD) resolution</li>
                                    <li>Video length: between 20 and 50 mins</li>
                                    <li>Optimal bright lighting conditions (indoor/outdoor)</li>
                                    <li>Any branding apart from your clothes is strictly forbidden</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <form action="classes/save" method="post" enctype="multipart/form-data" class="default_submit classes_form subscriber_class_upload" id="main_form">
                    <div class="col-12 py-5 bottom-border">
                        <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
                        <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>
                        <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-05">Custom Thumbnail</h3>
                        <p class="f-14 normal midGray lh-25">Select or upload a photo that shows what's in your video. <br>A good thumbnail stands out and draws viewers' attention.</p>
                        <div class="image_container flex aic flex-column-mob mt-5">
                            <div class="upload-image mb-mob-2" id="image_container">
                                <input type="file" name="image" id="image">
                                <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                            </div>
                            <div class="midGray f-14">
                                <div class="flex flex-column mb-2 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                                    <a href="javascript:;" class="link link-black black text-underline replace_image mb-1">Replace Current</a>
                                    <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                                </div>
                                <p class="f-1 lh-20">Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pt-5 pb-3 bottom-border">
                        <div class="row">
                            <div class="col-12">
                                <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-05">Class Name</h3>
                                <div class="input-container full-field" id="title_container" style="position: relative;">
                                <h5 class="mb-1 f-12">NAME</h5>
                                    <input type="text" name="title" class="line-input black f-14" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" />
                    </div>
                    <div class="col-12 py-5 bottom-border">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="f-14 text-uppercase mb-mob-1  semibold mb-5">Machine</h3>
    <?php
    $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
    foreach($machines as $single){
    ?>
                                <div class="checkbox mb-2" id="machine_container">
                                    <input type="checkbox" class="" name="machine[]" id="machine_select<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_machines) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                    <label for="machine_select<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                                </div>
    <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 py-5 bottom-border">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-5">Difficulty</h3>
    <?php
    $current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '') ? $current['difficulty'] : 0;
    foreach($difficulty as $single){
    ?>
                                <div class="checkbox mb-2" id="difficulty_container">
                                    <input type="radio" class="" name="difficulty" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                    <label for="difficulty<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                                </div>
    <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 py-5 bottom-border">
                        <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-5">Body Parts</h3>
                        <div class="mr-150 mr-mob-4">
<?php
$c=0;
$curr_body_parts = (isset($current_body_parts) AND $current_body_parts != '') ? $current_body_parts : array();
foreach($body_parts as $single){
$c++;
?>
                                <div class="checkbox mb-2" id="body_parts_container">
                                    <input type="checkbox" class="" name="body_parts[]" id="body_parts<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_body_parts) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                    <label for="body_parts<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                                </div>
<?php
if($c == 8){
echo '</div><div class="mr-150 mr-mob-4">';
}
}
?>

                        </div>
                    </div>
                    <div class="col-12 py-5 bottom-border">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-5">Accessories Required</h3>
    <?php
    $curr_accessories = (isset($current_accessories) AND $current_accessories != '') ? $current_accessories : array();
    foreach($accessories as $single){
    ?>
                                    <div class="checkbox mb-2" id="accessories_container" data-machine="<?php echo $single['machine']; ?>">
                                        <input type="checkbox" class="" name="accessories[]" id="accessories<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                        <label for="accessories<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                                    </div>
    <?php
    }
    ?>
    <?php echo count($curr_machines) == 0 ? '<h5 class="machine_first top--50 f-14">Please select machine first</h5>' : ''; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 py-5 bottom-border">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="f-14 text-uppercase mb-mob-1 semibold mb-5">Spring Load</h3>
    <?php
    $curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array();
    foreach($springs as $single){
    ?>
                                    <div class="checkbox mb-2" id="springs_container" data-machine="<?php echo $single['machine']; ?>">
                                        <input type="checkbox" class="" name="springs[]" id="springs<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                        <label for="springs<?php echo $single['id']; ?>" class="f-14"><?php echo $single['title']; ?></label>
                                    </div>
    <?php
    }
    ?>
    <?php echo count($curr_machines) == 0 ? '<h5 class="machine_first top--50 f-14">Please select machine first</h5>' : ''; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pt-6 pb-6 bottom-border">
                        <div class="row">
                            <div class="col-12">
                                <input type="hidden" name="teacher" value="<?php echo $current['teacher_from_user']; ?>">
                                <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                                <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 2 ?>">
                                <input type="hidden" name="type" id="type" value="1">
                                <input type="hidden" name="image_removed" id="image_removed" value="0">
                                <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">

                                <div class="approve-buttons mb-5">
                                    <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(2);$(this).addClass('btn--loading');event.preventDefault()">SAVE CLASS AND SEND TO APPROVAL</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script> -->
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var statuss = 0;
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
</script>
</body>
</html>