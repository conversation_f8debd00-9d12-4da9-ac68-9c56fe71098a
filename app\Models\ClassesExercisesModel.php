<?php namespace App\Models;

use CodeIgniter\Model;

class ClassesExercisesModel extends Model
{
    protected $table = 'classes_selected_exercises';
	protected $allowedFields = ['class_id', 'class_selected_exercises', 'date', 'orientation', 'duration', 'springs', 'springs_count', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}