<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class DevicesModel extends Model
{
    protected $table = 'devices';
	protected $allowedFields = ['user_id','title','os_name','os_version','browser_name','browser_version','device_model','device_type','device_vendor','userAgent','last_activity', 'city', 'country', 'zip', 'ip'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'userAgent'     => 'required',
        'user_id'     => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function user_devices($user_id){
        $data = $this->query("SELECT *
                                    FROM devices
                                    WHERE deleted_at IS NULL
                                    AND user_id = " . $user_id .  "
                                    LIMIT 0,3
                                ")->getResultArray();
        return $data;
    }
    public function user_current_device($user_id, $user_device_info){
        $device = $this->query("SELECT *
                                    FROM devices
                                    WHERE deleted_at IS NULL
                                    AND user_id = " . $user_id .  "
                                    AND os_name = '" . $user_device_info['os_name'] .  "'
                                    AND browser_name = '" . $user_device_info['browser_name'] .  "'
                                    AND device_model = '" . $user_device_info['device_model'] .  "'
                                ")->getResultArray();
        if(count($device) == 1){
            $data['device'] = $device[0];
            $data['success'] = TRUE;
        }else{
            $data['device'] = [];
            $data['success'] = FALSE;
        }
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}
}