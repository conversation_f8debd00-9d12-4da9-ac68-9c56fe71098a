<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Playlists extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('PlaylistsModel');
    }

    public function index()
    {
        $classes_views_model = model('ClassesViewModel');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        // if(!isset($data['logged_user'])){
        //     return redirect()->to('/');
        // }

        $data['all_playlists'] = $this->model->all_playlists(0, session('per_page'), NULL, "sort, playlists.updated_at desc", 1, 1);
        // if(count($data['all_playlists']) < 1){
        //     return redirect()->to('/');
        // }

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Playlists of On Demand Lagree Classes | Lagree On Demand';
		$data['current']['seo_description'] = 'Check out the playlists on Lagree On Demand and choose the workouts that fit your goals best! Get ready to feel the Lagree Shakes delivered only by The Lagree Method.';
		echo view('front/playlists/index_view', $data);
    }

    public function slug($slug = '', $class = NULL)
    {
        $classes_model = model('ClassesModel');
        $HowtoModel = model('HowtoModel');
        $ExercisesModel = model('ExercisesModel');
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $SubscribersClasses = model('SubscribersClasses');
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        $data['current_playlist'] = $this->model->single_playlist('slug', $slug);
        // if(!isset($data['logged_user'])){
        //     return redirect()->to('/');
        // }

        /*
		echo '<pre>';
		var_dump($data);
		die();
		*/
        if($class != NULL){
            $c = explode('_', $class);
            $data['current_type'] = ($c[0] == 'c' ? 'classes' : ($c[0] == 'e' ? 'exercises' : ''));

            if($c[0] == 'c'){
                $data['current'] = $classes_model->current($c[1]);
                $data['in_favs'] = $SubscribersFavs_model->where(['class_id' => $data['current']['id'], 'subscriber_id' => $data['logged_user']['id']])->find();
                // $data['bought'] = $SubscribersClasses->where(['class_id' => $data['current']['id'], 'subscriber_id' => $data['logged_user']['id'], 'purchase_type' => NULL])->find();
                // $data['rented'] = $SubscribersClasses->query("SELECT *, DATE_ADD(MAX(date), INTERVAL 1 DAY) as expiry_rent_date FROM subscribers_classes  WHERE class_id = " . $data['current']['id'] . " AND subscriber_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND purchase_type = 'rent' AND DATE_ADD(date, INTERVAL 1 DAY) > CURDATE()")->getResultArray();
                // $data['own'] = $classes_model->where(['id' => $data['current']['id'],'teacher' => (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0)])->find();
            }else if($c[0] == 'e'){
                $data['in_favs'] = [];
                // $data['bought'] = [];
                // $data['rented'] = [];
                // $data['own'] = [];
                $data['current'] = $ExercisesModel->current($c[1]);
                $data['current']['type'] = 0;
            }else{
                $data['in_favs'] = [];
                // $data['bought'] = [];
                // $data['rented'] = [];
                // $data['own'] = [];
                $data['current'] = $HowtoModel->current($c[1]);
                $data['current']['type'] = 0;
            }
            $data['list'] = $this->model->single_playlist('slug', $slug);
            return view('front/playlists/single_playlist_class_view', $data);
        }else{
            $data['current'] = $this->model->single_playlist('slug', $slug);
            $data['added'] = $this->model->single_playlist_added($data['current']['id']);
            return view('front/playlists/single_playlist_view', $data);
        }
    }
    public function add_to_favs()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_favs = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($save_favs)->delete();
        }

		return $this->respond($response);
    }

    public function add_playlist()
    {
        $SubscribersPlaylistsModel = model('SubscribersPlaylistsModel');
		$request = service('request');
        $data = $request->getPost();
        $id = $data['id'];
        $save_playlist = array('playlist_id' => $id, 'user_id' => session('user'), 'date' => date('Y-m-d'));
        // echo '<pre>';
        // print_r($save_playlist);
        // die();

        $response['playlist'] = $SubscribersPlaylistsModel->where(["playlist_id" => $id, "user_id" => session('user')])->first();

        $response['success'] = FALSE;
        if(empty($response['playlist'])){
            $response['saved'] = $SubscribersPlaylistsModel->save($save_playlist);
            $response['success'] = TRUE;
        }else{
            $response['removed'] = $SubscribersPlaylistsModel->where($save_playlist)->delete();
        }

		return $this->respond($response);
    }

    public function remove_playlist($id = NULL)
    {
        $SubscribersPlaylistsModel = model('SubscribersPlaylistsModel');
        $response['success'] = FALSE;
        if($id != NULL){
            $save_playlist = array('playlist_id' => $id, 'user_id' => session('user'));
            $response['playlist'] = $SubscribersPlaylistsModel->where(["playlist_id" => $id, "user_id" => session('user')])->first();
            $response['success'] = $SubscribersPlaylistsModel->where($save_playlist)->delete();
        }

		return $this->respond($response);
    }

    public function create_playlist()
    {
        $SubscribersPlaylistsModel = model('SubscribersPlaylistsModel');
        $PlaylistsModel = model('PlaylistsModel');
		$request = service('request');
        $data = $request->getPost();
        $name = $data['name'];
        $slug = slugify($data['name']);
        // $private = $data['private'];
        $check_slug = $this->model->where(["slug" => $slug])->first();
        if(!empty($check_slug)){
            $slug = $slug . '-' . random_slug();
        }
        $create_playlist = array(
            'title' => $name,
            'slug' => $slug,
            // 'private' => $private,
            'user_id' => session('user')
        );
        $response['success'] = $PlaylistsModel->save($create_playlist);
        $response['id'] = $PlaylistsModel->getInsertID();
        $response['error'] = $PlaylistsModel->errors();

        $save_playlist = array('playlist_id' => $response['id'], 'user_id' => session('user'), 'date' => date('Y-m-d'));

		return $this->respond($response);
    }

    public function my_playlists($class_type_id = NULL)
    {
        $PlaylistsModel = model('PlaylistsModel');
        $user_id = NULL !== session('user') ? session('user') : 0;

        if($user_id != 0){
            $response['list'] = $PlaylistsModel->get_my_playlists($user_id, $class_type_id);
        }else{
            $response['msg'] = 'Your session ended. <br>Please log in again';
        }

		return $this->respond($response);
    }

    public function load_playlist($playlist_id = NULL)
    {
        if($playlist_id != NULL){
            $response = $this->playlist_info($playlist_id);
        }else{
            $response = 0;
        }

		return $this->respond($response);
    }

    public function add_class_to_playlist()
    {
        $PlaylistClassesModel = model('PlaylistClassesModel');
        $PlaylistHowtoModel = model('PlaylistHowtoModel');
        $PlaylistExercisesModel = model('PlaylistExercisesModel');
		$request = service('request');
        $data = $request->getPost();
        $class_id = $data['class_id'];
        $class_type = $data['class_type'];
        $playlist_id = $data['playlist_id'];

        if($class_type == 'classes'){
            $save_playlist = array('playlists_id' => $playlist_id, 'playlist_selected_classes' => $class_id);

            $response['playlist'] = $PlaylistClassesModel->where(["playlists_id" => $playlist_id, "playlist_selected_classes" => $class_id])->first();

            if(empty($response['playlist'])){
                $save_playlist['date'] = date('Y-m-d');
                $response['saved'] = $PlaylistClassesModel->save($save_playlist);
            }else{
                $response['removed'] = $PlaylistClassesModel->where($save_playlist)->delete();
            }
        }else if($class_type == 'exercises'){
            $save_playlist = array('playlists_id' => $playlist_id, 'playlist_selected_exercises' => $class_id);

            $response['playlist'] = $PlaylistExercisesModel->where(["playlists_id" => $playlist_id, "playlist_selected_exercises" => $class_id])->first();

            if(empty($response['playlist'])){
                $save_playlist['date'] = date('Y-m-d');
                $response['saved'] = $PlaylistExercisesModel->save($save_playlist);
            }else{
                $response['removed'] = $PlaylistExercisesModel->where($save_playlist)->delete();
            }
        }else{
            $save_playlist = array('playlists_id' => $playlist_id, 'playlist_selected_howto' => $class_id);

            $response['playlist'] = $PlaylistHowtoModel->where(["playlists_id" => $playlist_id, "playlist_selected_howto" => $class_id])->first();

            if(empty($response['playlist'])){
                $save_playlist['date'] = date('Y-m-d');
                $response['saved'] = $PlaylistHowtoModel->save($save_playlist);
            }else{
                $response['removed'] = $PlaylistHowtoModel->where($save_playlist)->delete();
            }
        }

		return $this->respond($response);
    }

    public function sort_classes_table()
    {
        $PlaylistClassesModel = model('PlaylistClassesModel');
        $PlaylistHowtoModel = model('PlaylistHowtoModel');
        $PlaylistExercisesModel = model('PlaylistExercisesModel');
        $data = $this->request->getPost();

		$sorting = $data['sorting'];

		if (count($sorting) > 0) {
			foreach ($sorting as $key => $single) {
                if($single['type'] == 'classes'){
				    $PlaylistClassesModel->save($single);
                }
                if($single['type'] == 'exercises'){
				    $PlaylistExercisesModel->save($single);
                }
                if($single['type'] == 'videos'){
				    $PlaylistHowtoModel->save($single);
                }
			}
		}
		$result['success'] = TRUE;

		return $this->respond($result);
    }

    public function save()
    {
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $slug = slugify($data['title']);
        $check_slug = $this->model->where(["slug" => $slug])->first();
        if(!empty($check_slug)){
            $slug = $slug . '-' . random_slug();
        }
        $data['slug'] = $slug;

		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
            $response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Data successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['image'] = 'uploads/playlists/' . $name;
			}
			if (isset($files['cover_image']) AND $files['cover_image']->isValid()){
				$file = $files['cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['cover_image'] = 'uploads/playlists/' . $name;
			}
			if (isset($files['mob_cover_image']) AND $files['mob_cover_image']->isValid()){
				$file = $files['mob_cover_image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/playlists', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/playlists/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/playlists/' . $name, 98);
				$data['mob_cover_image'] = 'uploads/playlists/' . $name;
			}
            // if($data['image_removed'] == 1){
            //     $data['image'] = "";
            // }
            // unset($data['image_removed']);

            // if($data['cover_image_removed'] == 1){
            //     $data['cover_image'] = "";
            // }
            // unset($data['cover_image_removed']);

            // if($data['mob_cover_image_removed'] == 1){
            //     $data['mob_cover_image'] = "";
            // }
            // unset($data['mob_cover_image_removed']);
            $response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('playlists_' . $key);
                    $builder->delete(['playlists_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'playlists_id' => $response['inserted_id'],
                            'playlist_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }

		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}