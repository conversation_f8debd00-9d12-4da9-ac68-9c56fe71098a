<?php namespace App\Models;

use CodeIgniter\Model;

class SurveysModel extends Model
{
    protected $table = 'surveys';
	protected $allowedFields = ['title', 'sort', 'status'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

    public function all_surveys($start = 0, $limit = 0, $search_term = NULL, $order = "created_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (t1.firstname LIKE '%$search_term%' OR t1.lastname LIKE '%$search_term%')" : "";
        $data = $this->query("SELECT t1.*, (SELECT count(DISTINCT user_id) FROM surveys_results WHERE survey_id = t1.id) as responses
                            FROM surveys t1
                            WHERE t1.deleted_at IS NULL
                            AND id != 1
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }
    public function all_onboarding($start = 0, $limit = 0, $search_term = NULL, $order = "t1.created_at desc"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        $search =  ($search_term != NULL) ? " AND (surveys.firstname LIKE '%$search_term%' OR surveys.lastname LIKE '%$search_term%')" : "";
        // $data['onboarding'] = $this->query("SELECT surveys.*
        //                     FROM surveys
        //                     WHERE surveys.deleted_at IS NULL
        //                     AND id = 1
        //                     " . $search . "
        //                     ORDER BY " . $order . "
        //                     " . $limit_size . "
        //                 ")->getRowArray();
        $data = $this->query("SELECT t1.created_at, CONCAT(subscribers.firstname, ' ', subscribers.lastname) as user, t1.user_id
                                        FROM surveys_results t1
                                        INNER JOIN subscribers ON subscribers.id = t1.user_id
                                        WHERE t1.survey_id = 1
                                        AND t1.deleted_at IS NULL
                                        GROUP BY t1.user_id
                                        ORDER BY " . $order . "
                                        " . $limit_size . "
                                    ")->getResultArray();

        if(isset($data) AND is_array($data) AND count($data) > 0){
            foreach($data as $key => $single){
                $answer1 = $this->query("SELECT GROUP_CONCAT(answer SEPARATOR ', ') as answer1 FROM surveys_results a1 WHERE a1.user_id = " . $single['user_id'] . " AND survey_id = 1 AND question_id = 1 AND a1.deleted_at IS NULL")->getRowArray();
                $data[$key]['answer1'] = $answer1['answer1'];

                $answer2 = $this->query("SELECT GROUP_CONCAT(answer SEPARATOR ', ') as answer2 FROM surveys_results a1 WHERE a1.user_id = " . $single['user_id'] . " AND survey_id = 1 AND question_id = 2")->getRowArray();
                $data[$key]['answer2'] = $answer2['answer2'];

                $answer3 = $this->query("SELECT GROUP_CONCAT(answer SEPARATOR ', ') as answer3 FROM surveys_results a1 WHERE a1.user_id = " . $single['user_id'] . " AND survey_id = 1 AND question_id = 3")->getRowArray();
                $data[$key]['answer3'] = $answer3['answer3'];

                $answer4 = $this->query("SELECT GROUP_CONCAT(answer SEPARATOR ', ') as answer4 FROM surveys_results a1 WHERE a1.user_id = " . $single['user_id'] . " AND survey_id = 1 AND question_id = 4")->getRowArray();
                $data[$key]['answer4'] = $answer4['answer4'];

                $answer5 = $this->query("SELECT GROUP_CONCAT(answer SEPARATOR ', ') as answer5 FROM surveys_results a1 WHERE a1.user_id = " . $single['user_id'] . " AND survey_id = 1 AND question_id = 5")->getRowArray();
                $data[$key]['answer5'] = $answer5['answer5'];
            }
        }

        return $data;
    }

    public function all_onboarding_count(){
        $data['onboarding'] = $this->query("SELECT *
                                        FROM surveys_results
                                        WHERE survey_id = 1
                                        GROUP BY user_id
                                    ")->getResultArray();

        $count = count($data['onboarding']);
        return $count;
    }

    public function current($id = ''){
        $data = $this->query("SELECT surveys.*
                                        FROM surveys
                                        WHERE surveys.deleted_at IS NULL
                                        AND surveys.id = '" . $id . "'
                                    ")->getRowArray();
        return $data;
    }

    public function active(){
        $data = $this->query("SELECT surveys.*
                                        FROM surveys
                                        WHERE surveys.deleted_at IS NULL
                                        AND surveys.status = 0
                                        AND surveys.id != 1
                                    ")->getRowArray();
        return $data;
    }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}
