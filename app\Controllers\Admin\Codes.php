<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Codes extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CodesModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        $StripeModel = model('StripeModel');
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['sort_by'] = "Date Added";
        $data['all_coupons'] = $StripeModel->all_coupons();
        // echo '<pre>';
        // print_r($data['all_coupons']);
        // die();

        $data['codes_count'] = $this->model->countAllResults();
        $data['page'] = 1;

        echo view('admin/codes/index_view', $data);
    }

    public function edit($edit_id = 0)
    {
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['current'] = $this->model->current($edit_id);

		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/

		return view('admin/codes/edit_view', $data);
    }

    public function delete($coupon = '')
    {
        $StripeModel = model('StripeModel');

        if($coupon != ''){
            $response = $StripeModel->delete_coupon($coupon);
        }
        $response['data'] = $coupon;
		//echo json_encode($response);
		return $this->respond($response);

    }
    public function save()
    {
        $StripeModel = model('StripeModel');

		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Coupon successfully saved';

            $coupon = [
                'code' => $data['code'],
                'discount' => $data['discount'],
                'redemption' => $data['redemption'],
                'products' => $data['products']
            ];
            // echo '<pre>';
            // print_r($coupon);
            // die();

            $response = $StripeModel->create_coupon($coupon);
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}