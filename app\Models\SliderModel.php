<?php namespace App\Models;

use CodeIgniter\Model;

class SliderModel extends Model
{
    protected $table = 'slider';
	protected $allowedFields = ['subtitle', 'title', 'description', 'button_text', 'link', 'cover_image', 'mob_cover_image', 'youtube_url', 'status', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'title'     => 'required|min_length[2]',
        // 'cover_image'     => 'required',
        // 'slug'        => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
        // 'content'     => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	// public function add_gallery($page_id = 0, $gallery = array())
	// {
	// 	$response['success'] = FALSE;
	// 	$db      = \Config\Database::connect();
	// 	$builder = $db->table('classes_galleries');
	// 	$builder->delete(['page_id' => $page_id]);
	// 	if (count($gallery) > 0)
	// 	{
	// 		$builder->insertBatch($gallery);
	// 	}

	// 	/*
	// 	if (count($users) == 1)
	// 	{
	// 		$response['user_id'] = $users[0]['id'];
	// 		$response['success'] = TRUE;
	// 	}
	// 	else
	// 	{
	// 		$response['error'] = 'Bad username or password.';
	// 	}*/
	// 	return $response;
	// }

	protected function prepare_data(array $data)
	{
		return $data;
	}

}