<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class TeachersPayoutsModel extends Model
{
    protected $table = 'teachers_payouts';
	protected $allowedFields = ['teacher_id', 'amount', 'date', 'stripe_payout'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	protected function prepare_data(array $data)
	{
		return $data;
	}

}