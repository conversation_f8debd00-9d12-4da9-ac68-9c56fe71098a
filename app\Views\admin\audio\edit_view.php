<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.upload-zone_audio,
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
  position: absolute;
  border: 1px solid #f0f0f0;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 10px;
  background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver::before {
    content: "Drop your audio file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.upload-zone_audio.no-border::before,
.upload-zone_audio.no-border {
	background: #fff;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 18px !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
}

@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="tab_content main-content pb-5">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Audio <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                <a href="admin/audio" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-4">
        </div>
        <div class="container pt-1">
            <form action="admin/audio/audio_upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone_audio <?php echo (isset($current['audio']) AND $current['audio'] != '') ? 'no-border' : ''; ?>" id="audio_container">
                <input type="file" name="audio" id="audio" ondragover="dragOverAudio()" ondragleave="dragLeaveAudio()" ondrop="dragLeaveAudio()" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? 'display: none' : ''; ?>">
                <div class="before_upload_audio" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? 'display: none' : ''; ?>">
                <span class="f-16 semibold mb-1">DRAG AND DROP VIDEO HERE</span> 
                <span class="f-14 midGray video_choose">or <u>select a file</u></span>
                </div>
                <div class="audio_placeholder px-0 py-0" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none;'; ?>">
                    <audio id="my_audio" controls class="after_upload_audio" src="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none'; ?>"></audio>
                </div>
                <span id="audio-is-uploading"></span>
                <!-- <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas> -->
                <span id="progress-bar-status-show_audio"></span>
                <span id="toshow_audio" style="display: none;"></span>
            </form>
            <div class="flex aic jcsb mt-1">
                <span id="remove_audio" class="link link-red red text-underline f-12" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none'; ?>">Remove audio</span>
            </div>
        </div>
        <form action="admin/audio/save" method="post" class="default_submit container border-bottom mt-45" id="main_form">
            <input type="hidden" id="audio_path" name="audio" value="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>"/>
            <div class="row mb-25">
                <div class="col-12">
                    <h5 class="mb-4 f-14 semibold">BASIC INFO</h5>
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="title" class="line-input black" placeholder="Enter" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />
                    </div>
                </div>
            </div>
            <hr class="mt-2 mb-5">
            <div class="row pb-4">
                <div class="col-12 for_submit">
                    <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : 'audio_' . generate_slug(); ?>" />
                    <div class="uploading" style="display: none;">Uploading audio. Please wait...</div>
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
                    <div class="default-buttons flex aic">
                        <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">UPDATE</button>
                        <a href="/admin/audio" class="cancel-link ml-2" title="Cancel">Cancel</a>
                        <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="audio" data-popup="delete-popup" title="Cancel">DELETE AUDIO</a>
                    </div>
                    <?php }else{ ?>
                    <div class="default-buttons flex aic">
                        <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                        <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                        <a href="/admin/audio" class="cancel-link" title="Cancel">Cancel</a>
                    </div>
                    <?php } ?>
                </div>
            </div>
            <!-- <hr class="mt-4 mb-55"> -->
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
</script>
</body>
</html>