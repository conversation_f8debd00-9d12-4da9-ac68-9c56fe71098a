<?php
    $url = $current['video'];
    $path = parse_url($url, PHP_URL_PATH);
    $name_file = basename($path);
    $name = str_replace('.mp4', '', $name_file);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<link rel="stylesheet" type="text/css" href="css/mvp.css" />
<link rel="stylesheet" type="text/css" href="css/flat.css?3" />
<link href="css/videojs.custom.css" rel="stylesheet">
</head>
<body class="collection-page">
<style>
.mobile-flex-vertical.position-relative .playlist-option {
	position: absolute !important;
	bottom: 20px;
	right: 15px;
	z-index: 11;
}
.ui-sortable-placeholder {
	height: 100px;
	width: 100%;
	margin-bottom: 10px;
	background: #f8f8f8;
	border-radius: 8px;
	border: 2px dashed #ddd;
}

@media (max-width: 480px){
    .img-panel-content {
        padding: 0 2vw;
    }
}
</style>
<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="pt-6 pb-5 px-120 pb-mob-2 pt-mob-3">
        <div class="container1530">
            <div class="row video-row jcsb">
            
                <div class="col-12">
                    <?php
                    if(!empty($logged_user)){
                        if(
                            (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
                            OR
                            (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
                            OR
                            (isset($rented) AND count($rented) > 0 AND $rented[0]['id'] != NULL AND $current['type'] == 1)
                            OR
                            (isset($own) AND count($own) > 0 AND $current['type'] == 1)
                        ){
                    ?>
                        <div id="lod-video">
                            <div class="mvp-player-wrap">
                                <div class="mvp-player-holder">
                                    <div class="mvp-media-holder"></div>
                                    <div class="mvp-unmute-toggle">Enable volume</div>
                                    <div class="mvp-player-loader" style="background: url(<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>) no-repeat top center / 100% auto;"></div>

                                    <div class="mvp-live-note">
                                        <div class="mvp-live-note-inner">
                                            <div class="mvp-live-note-icon"></div>
                                            <div class="mvp-live-note-title">LIVE</div>
                                        </div>
                                    </div>
                                    <div class="mvp-big-play">
                                    <img src="images/play-course-icon.svg">
                                    </div>
                                    <div class="mvp-player-controls">
                                        <div class="mvp-player-controls-bottom">
                                        <div class="mvp-player-controls-bottom-left">
                                                <div class="mvp-skip-backward-toggle mvp-contr-btn">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="rewind" d="M14.115,19.886a7.411,7.411,0,1,0-5.671-13.1l2.92,2.518-8.02,1.947.381-8.464L6.433,5.095A9.894,9.894,0,0,1,11.745,2.64,10,10,0,1,1,4.994,18.4l2.053-1.587A7.363,7.363,0,0,0,14.115,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-playback-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-play">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
                                                            <path id="play" d="M10,0,20,18H0Z" transform="translate(18) rotate(90)" fill="#fff"/>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-pause">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 15 20">
                                                            <g id="pause" transform="translate(-240.764 -1653)">
                                                                <path id="Path_1312" data-name="Path 1312" d="M0,0H6V20H0Z" transform="translate(240.764 1653)" fill="#fff"/>
                                                                <path id="Path_1313" data-name="Path 1313" d="M0,0H6V20H0Z" transform="translate(249.764 1653)" fill="#fff"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="mvp-skip-forward-toggle mvp-contr-btn">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19.776" height="20" viewBox="0 0 19.776 20">
                                                        <path id="Path_1030" data-name="Path 1030" d="M12.349,19.886a7.411,7.411,0,1,1,5.671-13.1L15.1,9.306l8.02,1.947-.381-8.464L20.03,5.095A9.894,9.894,0,0,0,14.719,2.64,10,10,0,1,0,21.469,18.4l-2.053-1.587A7.363,7.363,0,0,1,12.349,19.886Z" transform="translate(-3.344 -2.544)" fill="#fff"/>
                                                    </svg>
                                                </div>
                                                <div class="mvp-volume-wrapper mvp-contr-btn">
                                                    <div class="mvp-volume-toggle mvp-contr-btn" data-tooltip="Voice-over On/Off" data-tooltip-position="left">
                                                        <div class="mvp-btn mvp-btn-volume-up">
                                                            <!-- <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                            </svg> -->
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-down">
                                                            <!-- <svg id="volume" xmlns="http://www.w3.org/2000/svg" width="22.693" height="20" viewBox="0 0 22.693 20">
                                                                <g id="Group_4265" data-name="Group 4265" transform="translate(15.696 6.127)">
                                                                    <g id="Group_4264" data-name="Group 4264">
                                                                    <path id="Path_1027" data-name="Path 1027" d="M287.78,136.72c-.14-.14-.281-.309-.449-.449l-1.123,1.264a3.5,3.5,0,0,1,.309,4.914,1.609,1.609,0,0,1-.309.309l1.123,1.264A5.16,5.16,0,0,0,287.78,136.72Z" transform="translate(-286.208 -136.271)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4267" data-name="Group 4267" transform="translate(18.531 3.178)">
                                                                    <g id="Group_4266" data-name="Group 4266" transform="translate(0 0)">
                                                                    <path id="Path_1028" data-name="Path 1028" d="M339.661,83.073c-.2-.2-.393-.393-.59-.562l-1.151,1.235a7.584,7.584,0,0,1,.477,10.7c-.14.168-.309.309-.477.477l1.151,1.235A9.271,9.271,0,0,0,339.661,83.073Z" transform="translate(-337.92 -82.511)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                                <g id="Group_4269" data-name="Group 4269">
                                                                    <g id="Group_4268" data-name="Group 4268" transform="translate(0 0)">
                                                                    <path id="Path_1029" data-name="Path 1029" d="M13.2,24.642a.876.876,0,0,0-.87.056L4.8,29.752H.842A.827.827,0,0,0,0,30.594v7.918a.827.827,0,0,0,.842.842H4.8l7.525,5.054a.856.856,0,0,0,1.179-.225.947.947,0,0,0,.14-.477V25.372A.809.809,0,0,0,13.2,24.642Z" transform="translate(0 -24.552)" fill="#333"/>
                                                                    </g>
                                                                </g>
                                                            </svg> -->
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                        </div>
                                                        <div class="mvp-btn mvp-btn-volume-off">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="64.728" height="60" viewBox="0 0 64.728 60">
                                                                <g id="Group_10754" data-name="Group 10754" transform="translate(-9084 11564.45)">
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48" d="M28.69,65.606v-5.24a25.439,25.439,0,0,0,13.395-9.085,24.273,24.273,0,0,0,5.2-15.254,24.577,24.577,0,0,0-5.155-15.3A24.7,24.7,0,0,0,28.69,11.69V6.45A29.564,29.564,0,0,1,45.761,17.056a29.511,29.511,0,0,1,6.592,18.972A29.511,29.511,0,0,1,45.761,55,29.564,29.564,0,0,1,28.69,65.606ZM27,50.31V21.831a13.85,13.85,0,0,1,7.352,5.409,15.085,15.085,0,0,1,2.789,8.873,14.84,14.84,0,0,1-2.789,8.873A14,14,0,0,1,27,50.31Z" transform="translate(9096.375 -11570.057)"/>
                                                                    <path id="volume_up_FILL0_wght400_GRAD0_opsz48-2" data-name="volume_up_FILL0_wght400_GRAD0_opsz48" d="M6,49.25V26.75H21L39.75,8V68L21,49.25ZM25.312,38Z" transform="translate(9078 -11572.45)"/>
                                                                </g>
                                                            </svg>
                                                            <!-- <svg aria-hidden="true" focusable="false" role="img" viewBox="0 0 640 512"><path d="M633.82 458.1l-69-53.33C592.42 360.8 608 309.68 608 256c0-95.33-47.73-183.58-127.65-236.03-11.17-7.33-26.18-4.24-33.51 6.95-7.34 11.17-4.22 26.18 6.95 33.51 66.27 43.49 105.82 116.6 105.82 195.58 0 42.78-11.96 83.59-33.22 119.06l-38.12-29.46C503.49 318.68 512 288.06 512 256c0-63.09-32.06-122.09-85.77-156.16-11.19-7.09-26.03-3.8-33.12 7.41-7.09 11.2-3.78 26.03 7.41 33.13C440.27 165.59 464 209.44 464 256c0 21.21-5.03 41.57-14.2 59.88l-39.56-30.58c3.38-9.35 5.76-19.07 5.76-29.3 0-31.88-17.53-61.33-45.77-76.88-11.58-6.33-26.19-2.16-32.61 9.45-6.39 11.61-2.16 26.2 9.45 32.61 11.76 6.46 19.12 18.18 20.4 31.06L288 190.82V88.02c0-21.46-25.96-31.98-40.97-16.97l-49.71 49.7L45.47 3.37C38.49-2.05 28.43-.8 23.01 6.18L3.37 31.45C-2.05 38.42-.8 48.47 6.18 53.9l588.36 454.73c6.98 5.43 17.03 4.17 22.46-2.81l19.64-25.27c5.41-6.97 4.16-17.02-2.82-22.45zM32 184v144c0 13.25 10.74 24 24 24h102.06l88.97 88.95c15.03 15.03 40.97 4.47 40.97-16.97V352.6L43.76 163.84C36.86 168.05 32 175.32 32 184z"></path></svg> -->
                                                        </div>
                                                    </div>
                                                    <div class="mvp-volume-seekbar">
                                                        <div class="mvp-volume-bg">
                                                            <div class="mvp-volume-level"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if(isset($current['audio']) AND $current['audio'] != ''){ ?>
                                                <div class="mvp-music-toggle mvp-contr-btn muted" data-tooltip="Music On/Off">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="39.5" height="60" viewBox="0 0 39.5 60">
                                                        <path id="music_note_FILL0_wght400_GRAD0_opsz48" d="M24.65,66a12.379,12.379,0,0,1-12.5-12.5A12.379,12.379,0,0,1,24.65,41a12.5,12.5,0,0,1,4.208.667A10.835,10.835,0,0,1,32.15,43.5V6h19.5V17.25H37.15V53.5A12.379,12.379,0,0,1,24.65,66Z" transform="translate(-12.15 -6)"/>
                                                    </svg>
                                                </div>
                                                <?php } ?>
                                                <!-- <div class="mvp-media-time-current"></div> -->
                                            </div>
                                            <div class="mvp-seekbar">
                                                <div class="mvp-seekbar-wrap">
                                                    <div class="mvp-progress-bg">
                                                        <div class="mvp-load-level"></div>
                                                        <div class="mvp-progress-level"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mvp-player-controls-bottom-right">
                                                <!-- <div class="mvp-media-time-total"></div> -->
                                                <div class="mvp-cast-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-cast-off" data-tooltip="Play on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-cast-on" data-tooltip="Stop playing on TV">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.247" height="17.832" viewBox="0 0 29.247 17.832">
                                                            <g id="cast" transform="translate(-349.801 -1677.321)">
                                                                <g id="full_screen" data-name="full screen" transform="translate(-931 25.476)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M1.3,1.98H0V-.126H15.95v1.3H1.3Z" transform="translate(1282.5 1652)" fill="#fff"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M12.332,6.236h-1.3V1.174H.649v-1.3H12.332Z" transform="translate(1297.716 1652)" fill="#fff"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M12.332,6.886H-.276v-1.3H11.032V-3.4h1.3Z" transform="translate(1297.716 1661.476)" fill="#fff"/>
                                                                </g>
                                                                <g id="Group_5157" data-name="Group 5157" transform="translate(-3.049 28.305)">
                                                                <path id="Path_1027" data-name="Path 1027" d="M2.594.688C2.379.473,2.164.215,1.906,0L0,1.778A5.563,5.563,0,0,1,.66,9.454,4.442,4.442,0,0,1,0,10.067l1.77,1.9A7.784,7.784,0,0,0,2.594.688Z" transform="translate(353.258 1658.666) rotate(-47)" fill="#fff"/>
                                                                <path id="Path_1028" data-name="Path 1028" d="M2.462,0C2.416.024,2.5-.049.574,1.76,5.3,6.1,5,12.963.408,16.577c-.215.258-.15.123-.408.38l1.762,1.89C8.671,13.027,8.075,5.885,2.462,0Z" transform="translate(352.85 1653.994) rotate(-47)" fill="#fff"/>
                                                                <g id="Path_1315-2" data-name="Path 1315" transform="translate(-9663.393 -10704.873)" fill="#fff">
                                                                    <path d="M 10020.5830078125 12369.673828125 L 10018.75 12369.673828125 L 10018.75 12367.2880859375 C 10019.72265625 12367.7041015625 10020.427734375 12368.60546875 10020.5830078125 12369.673828125 Z" stroke="none"/>
                                                                    <path d="M 10021.3564453125 12370.423828125 L 10018 12370.423828125 L 10018 12366.318359375 C 10019.919921875 12366.548828125 10021.365234375 12368.18359375 10021.365234375 12370.12109375 C 10021.365234375 12370.205078125 10021.3603515625 12370.294921875 10021.357421875 12370.37109375 L 10021.3564453125 12370.419921875 L 10021.357421875 12370.421875 L 10021.3564453125 12370.423828125 Z" stroke="none" fill="#fff"/>
                                                                </g>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>

                                                <div class="mvp-airplay-toggle mvp-contr-btn" data-tooltip="AirPlay">
                                                    <svg id="cast" xmlns="http://www.w3.org/2000/svg" width="27.5" height="20" viewBox="0 0 27.5 20">
                                                        <path id="Path_932" data-name="Path 932" d="M6,18V34.25h7.348L14.6,33H7.25V19.25h25V33H24.9l1.25,1.25H33.5V18Z" transform="translate(-6 -18)" fill="#fff"/>
                                                        <g id="Group_4129" data-name="Group 4129" transform="translate(6.25 12.5)">
                                                            <path id="Path_933" data-name="Path 933" d="M26,65.5H41L33.5,58Z" transform="translate(-26 -58)" fill="#fff"/>
                                                        </g>
                                                    </svg>
                                                </div>

                                                <div class="mvp-fullscreen-toggle mvp-contr-btn">
                                                    <div class="mvp-btn mvp-btn-fullscreen" data-tooltip="Fullscreen" data-tooltip-position="left">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                    <div class="mvp-btn mvp-btn-normal" data-tooltip="Exit Fullscreen" data-tooltip-position="left">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="27.548" height="16.488" viewBox="0 0 27.548 16.488">
                                                            <g id="full_screen" data-name="full screen" transform="translate(-1282.499 -1651.874)">
                                                                <path id="Path_1315" data-name="Path 1315" d="M.649,6.236V.524H11.682" transform="translate(1282.5 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1317" data-name="Path 1317" d="M.649.524V6.236H11.682" transform="translate(1282.5 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1316" data-name="Path 1316" d="M11.682,6.236V.524H.649" transform="translate(1297.716 1652)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                                <path id="Path_1318" data-name="Path 1318" d="M11.682.524V6.236H.649" transform="translate(1297.716 1661.476)" fill="none" stroke="#fff" stroke-width="1.3"/>
                                                            </g>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- end mvp-player-holder -->
                            </div><!-- end mvp-player-wrap -->
                            <div id="mvp-playlist-list">
                                <div class="playlist-video">
                                    <div class="mvp-playlist-item"
                                        data-type="video"
                                        data-path='[{"quality": "HD", "mp4": "<?php echo (isset($current['video']) AND $current['video'] != '') ? base_url() . '/video.php?name=' . $current['slug'] . '-' . $name : ''; ?>"}]'
                                        data-poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>"
                                        data-share="<?php echo current_url(); ?>"
                                        data-preview-seek="auto"
                                        data-title="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>">
                                    </div>
                                </div>
                            </div>
                            <!-- base_url() . '/video.php?name=' . $name -->
                            <div class="mvp-preview-seek-wrap">
                                <div class="mvp-preview-seek-inner"></div>
                                <div class="mvp-preview-seek-info"></div>
                            </div>
                        </div>
                        <?php
                            }else{
                        ?>
                            <div class="img-panel">
                                <div class="image-overlay h100"><img src="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" alt="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" class="img-fluid" /></div>
                                <div class="img-panel-content aic">
                                <?php if($current['type'] == 0){ ?>
                                    <a href="/subscribe" class="flex aic white semibold f-14 bold text-uppercase" title="Subscribe to Watch"><img src="images/lock-dark.svg" alt="" class="img-fluid mr-1 unlock-icon"> Subscribe to Watch</a>
                                <?php }else{ ?>
                                    <a href="javascript:;" class="flex aic white semibold f-14 bold text-uppercase" title="Buy now: $<?php echo $_ENV['class_price']; ?> or rent $<?php echo $_ENV['class_rent']; ?>"
                                        data-class_title="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>"
                                        data-class_id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>"
                                        data-seller_id="<?php echo (isset($current['teacher']) AND $current['teacher'] != "") ? $current['teacher'] : 0; ?>"
                                        data-popup="buy-class" onclick="buy_popup($(this))">
                                        <img src="images/lock-dark.svg" alt="" class="img-fluid mr-1 unlock-icon">Buy now: $<?php echo $_ENV['class_price']; ?> or rent $<?php echo $_ENV['class_rent']; ?>
                                    </a>
                                <?php } ?>
                                </div>
                            </div>

                        <?php
                            }
                        ?>
                    <?php
                    }else{
                    ?>
                        <div class="img-panel">
                            <div class="image-overlay h100"><img src="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" alt="<?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?>" class="img-fluid" /></div>
                            <div class="img-panel-content aic">
                                <a href="<?php echo $current['type'] == 0 ? '/subscribe' : 'javascript:;'; ?>" class="flex aic white semibold f-14 bold text-uppercase" title="Subscribe to Watch" data-popup="<?php echo $current['type'] == 0 ? 'login-popup' : 'buy-class" onclick="buy_popup($(this))'; ?>">
                                    <img src="images/lock-dark.svg" alt="" class="img-fluid mr-1 unlock-icon" />
                                    <?php echo $current['type'] == 0 ? 'Subscribe to Watch' : 'Buy now: $' . $_ENV['class_price'] . ' or rent $' . $_ENV['class_rent']; ?>
                                </a>
                            </div>
                        </div>
                    <?php
                    }
                    ?>
                </div>
                <div class="col-12">
                    <!-- <h1 class="h2 mb-3 mobile"><?php echo (isset($current['title']) AND $current['title'] != '') ? $current['title'] : ''; ?></h1> -->
                    <div class="playlist-wrap">
                        <div class="">
                            <div class="mobile-flex-vertical position-relative">
                                <div class="w100">
                                    <h1 class="f-20 f-mob-18 mb-05 line-height-small"><?php echo (isset($list['title']) AND $list['title'] != '') ? $list['title'] : ''; ?></h1>
                                    <div class="flex aic jcsb line-height-small mb-4 pb-05">
                                        <p class="f-12 line-height-normal greytxt"><?php echo count($list['all_playlists_classes']); ?> videos, (<?php echo only_minutes($list['total_duration']); ?> minutes)</p>
                                    </div>
                                 </div>
                            </div>
                            <!-- <hr class="mobile mb-mob-3 mt-mob-3"> -->
                            <div class="playlist-list sortable mb-6">
<?php
foreach($list['all_playlists_classes'] as $single){
?>
                                <div class="col-12 single-playlist-list-item-wrap <?php echo $single['slug_type']; ?>" data-rowid="<?php echo $single['csc_id']; ?>" data-type="<?php echo $single['type']; ?>">
                                    <div class="single-playlist-list-item <?php echo $single['slug_type']; ?>">
                                        <a href="playlists/<?php echo (isset($list['slug']) AND $list['slug'] != '') ? $list['slug'] : ''; ?>/<?php echo substr($single['slug_type'], 0, 1); ?>_<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>" class="video-container">
                                            <?php
                                                if(isset($single['watched']) AND $single['watched'] == 1){
                                            ?>
                                                <span class="watched f-14 bold lettet-50 white flex aic"><img src="images/watch-again.svg" alt="" style="width: 20px;object-fit: cover;height: 20px;object-position: left center;" class="img-fluid" /></span>
                                            <?php
                                                }else{
                                            ?>
                                                <?php
                                                    if($current['slug'] != $single['slug']){
                                                ?>
                                                    <span class="play-button"><span></span></span>
                                                <?php } ?>
                                            <?php } ?>
                                            <div class="image-overlay h100 ">
                                                <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" alt="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" class="img-fluid" <?php echo (isset($single['watched']) AND $single['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?> />
                                                <?php
                                                    if($current['slug'] == $single['slug']){
                                                ?>
                                                <!-- <img src="images/playing.webp" style="width: 32px;height: auto;bottom: 0;right: 0;/*! top: auto; */left: auto;z-index: 111;" alt="" class="img-fluid" /> -->
                                                <?php } ?>
                                            </div>
                                        </a>
                                        <div class="video-text-container">
                                            <div class="info-plst">
                                            <a href="playlists/<?php echo (isset($list['slug']) AND $list['slug'] != '') ? $list['slug'] : ''; ?>/<?php echo substr($single['slug_type'], 0, 1); ?>_<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>">
                                                <h4 class="flex jcsb semibold mb-05 f-12 ail"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h4>
                                            </a>
                                            <p class="midGray f-12 mb-0 line-height-small">
                                                <span class="d-inline-block line-height-small">
                                                    <?php if(isset($single['teach_slug']) AND $single['teach_slug'] != ''){ ?>
                                                        by:
                                                        <a href="teachers/<?php echo (isset($single['teach_slug']) AND $single['teach_slug'] != '') ? $single['teach_slug'] : ''; ?>" class="link link-midGray midGray d-inline-block"><?php echo (isset($single['teach']) AND $single['teach'] != '') ? $single['teach'] : 'NO TEACHER'; ?></a>,
                                                    <?php } ?>
                                                    <?php echo (isset($single['duration']) AND $single['duration'] != '') ? '<span class="d-inline-block line-height-small">' . only_minutes($single['duration']) . ' minutes</span>' : ''; ?>
                                                </span>
                                            </p>
                                            </div>
                                            
                                            <?php if($current['slug'] == $single['slug']){ ?>
                                            <div class="nowplay-tag" style="background:#969696; border-radius:50px; color: #fff;  font-size: 10px; font-weight: 500; padding:0 10px; height: 20px; line-height: 20px; margin-right:17px;">Now playing</div>
                                            <?php } ?>

                                            <div class="options-plst">
                                                <a href="javascript:;" class="btn delete-item-playlist border hover-to-border-black rounded-circle"
                                                    data-class_id="<?php echo $current['id']; ?>"
                                                    data-class_type="<?php echo $current_type; ?>"
                                                    data-playlist_id="<?php echo $current_playlist['id']; ?>"
                                                    onclick="remove_class_from_playlist($(this))"
                                                >
                                                    <img class="" src="images/delete.svg">
                                                </a>

                                                <span class="handle">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 13">
                                                        <g id="Group_16611" data-name="Group 16611" transform="translate(-1711 -18)">
                                                            <rect id="Rectangle_1337" data-name="Rectangle 1337" width="15" height="2" transform="translate(1711 19)"/>
                                                            <rect id="Rectangle_1338" data-name="Rectangle 1338" width="15" height="2" transform="translate(1711 23.735)"/>
                                                            <rect id="Rectangle_1339" data-name="Rectangle 1339" width="15" height="2" transform="translate(1711 29.471)"/>
                                                        </g>
                                                    </svg>
                                                </span>
                                            </div>

                                        </div>
                                    </div>
                                </div>
<?php
}
?>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>
</main>

<?php // echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
<script>
setTimeout(function(){
    to_timezone();
},1000);
if($('.sortable').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".sortable").sortable({
        helper: fixHelper,
        placeholder: "sortable-placeholder",
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "playlists/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}

</script>
<?php
if(!empty($logged_user)){
    if(
        (NULL !== session('subscription') AND session('subscription') == 'active' AND $current['type'] == 0)
        OR
        (isset($bought) AND count($bought) > 0 AND $current['type'] == 1)
        OR
        (isset($rented) AND count($rented) > 0 AND $rented[0]['id'] != NULL AND $current['type'] == 1)
        OR
        (isset($own) AND count($own) > 0 AND $current['type'] == 1)
){
?>
<script type="text/javascript" src="js/new.js"></script>
<script type="text/javascript">
jQuery(document).ready(function($) {
    var player = $("#lod-video").mvp({
        sourcePath: "",
        instanceName: "player1",
        activePlaylist: ".playlist-video",
        activeItem: 0,
        volume: 1,
        usePlayer: false,
        autoPlay: false,
        autoPlayAfterFirst: true,
        randomPlay: false,
        loopingOn: true,
        mediaEndAction: 'poster',
        useMobileNativePlayer: false,
        showPosterOnPause: false,
        hideQualityMenuOnSingleQuality: true,
        aspectRatio: 1, // 1
        facebookAppId: "",
        playlistOpened: false,
        useKeyboardNavigationForPlayback: true,
        truncatePlaylistDescription: true,
        rightClickContextMenu: "custom",
        playlistItemContent: "thumb,title, description,duration",
        elementsVisibilityArr: [
            {
                width: 500, elements: [ "play", "next", "seekbar", "fullscreen", "volume"]
            }
        ],
        skin: "flat-light",
        playlistPosition: "no-playlist",
        playerType: "normal",
        playlistScrollType: "perfect-scrollbar",
        useSearchBar: false
    });
    player.on("mediaEnd", function(e, data){
        $('.mvp-poster-holder').removeClass('hidden_poster');
    });
    var video = document.getElementById("lod-video");
    video.addEventListener('ended', () => {
        console.log('video ENDED');
        video.pause();
        video.currentTime = 0;
        // $('.mvp-poster-holder').css({"opacity":"1"});
    });
});
function iOS() {
  return [
    'iPad Simulator',
    'iPhone Simulator',
    'iPod Simulator',
    'iPad',
    'iPhone',
    'iPod'
  ].includes(navigator.platform)
  // iPad on iOS 13 detection
  || (navigator.userAgent.includes("Mac") && "ontouchend" in document)
}
if(iOS()){
    $('#lod-video').html('<video poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ''; ?>" controls style="width: 100%"><source src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>""></source></video>')
    console.log('IPHONE');
}
// $('.mvp-big-play').on('click', function(){
//     console.log('BIG CLICKED');
//     $('.mvp-poster-holder').toggleClass('hidden_poster');
// });
var video = document.getElementById("lod-video");
video.addEventListener('click', function(){
    console.log('video clicked');
    $('.mvp-poster-holder').addClass('hidden_poster');
    mark_as_watched('<?php echo $current_type; ?>', <?php echo $logged_user['id']; ?>, <?php echo $current['id']; ?>);
});
$('#lod-video').on('mouseleave', function(){
    $('.mvp-player-controls').removeClass('mvp-player-controls-visible');
});
$('#lod-video').on('mouseenter', function(){
    $('.mvp-player-controls').addClass('mvp-player-controls-visible');
});
</script>
<?php
    }
}
?>
</body>
</html>