<?php namespace App\Models;

use CodeIgniter\Model;

class CollectionClassesModel extends Model
{
    protected $table = 'collections_selected_classes';
	protected $allowedFields = ['collections_id', 'collection_selected_classes', 'date', 'sort'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

	protected function prepare_data(array $data)
	{
		return $data;
	}

}