<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class Models extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('ModelsModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_models'] = $this->model->all_models(0, session('per_page'));
        $data['models_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/models/index_view', $data);
    }

    public function page($page = 1)
    {
        $post = $this->request->getPost();
        $db = \Config\Database::connect();
		$data['machines'] = $db->query('SELECT * FROM machines WHERE deleted_at IS NULL')->getResultArray();
		$data['difficulty'] = $db->query('SELECT * FROM difficulty')->getResultArray();
		$data['body_parts'] = $db->query('SELECT * FROM body_parts')->getResultArray();
		$data['languages'] = $db->query('SELECT * FROM languages')->getResultArray();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_models'] = $this->model->all_models(($page * session('per_page')) - session('per_page'), session('per_page'));
        $data['models_count'] = $this->model->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = $page;

        echo view('admin/models/index_view', $data);
    }

    public function history($id = 0, $page = 1, $sort = 1)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
        $data['data'] = $this->request->getPost();
        if(isset($data['data']['search_term'])){
            $data['search_term'] = $data['data']['search_term'];
        }else{
            $data['search_term'] = '';
        }
        $sort_term = 'sort_status ' . ($sort == 0 ? 'ASC' : 'DESC'). ', date DESC';
        $data['models_id'] = $id;
        $data['models_history'] = $this->model->models_history($id, ($page * session('per_page')) - session('per_page'), session('per_page'), $data['search_term'], $sort_term);
        $models_count = $this->model->models_history($id, 0, 10000, $data['search_term']);
        $data['models_count'] = count($models_count);
        $data['sort_by'] = $sort;
        $data['page'] = $page;

        echo view('admin/models/history_view', $data);
    }

    public function search()
    {
        $data = $this->request->getPost();
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        // $data['all_models'] = $this->model->like('title', $data['search_term'])->findAll();
        $data['all_models'] = $this->model->query("SELECT  models.*
                                        FROM  models
                                        WHERE  models.deleted_at IS NULL
                                        AND (models.firstname LIKE '%" . $data['search_term'] . "%' OR models.lastname LIKE '%" . $data['search_term'] . "%')
                                        ORDER BY models.created_at desc")->getResultArray();
        $data['models_count'] = $this->model->like('firstname', $data['search_term'])->countAllResults();
        $data['sort_by'] = "Date Added";
        $data['page'] = 1;

        echo view('admin/models/index_view', $data);
    }

    public function sort_by($type = 'models.firstname', $direction = 'desc')
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

        $data['all_models'] = $this->model->all_models(0, session('per_page'), '', $type . " " . $direction);
        $data['models_count'] = $this->model->countAllResults();
        $types = array(
            "models.created_atdesc" => "Date Added",
            "models.firstnameasc" => "Ascending",
            "models.firstnamedesc" => "Descending",
        );
        $data['sort_by'] = $types[$type.$direction];
        $data['page'] = 1;
		echo view('admin/models/index_view', $data);
    }

    public function mark_paid()
    {
        $CalendarModel = model('CalendarModel');
        $data = $this->request->getPost();

        $current = $CalendarModel->where(['id' => $data['id']])->first();

        switch ($current['paid']) {
            case 1:
                $new_paid_status = 3;
                break;
            case 2:
                $new_paid_status = 3;
                break;
            case 4:
                $new_paid_status = 0;
                break;
            default:
                $new_paid_status = 0;
                break;
        }

        $save_data = [
            'id' => $data['id'],
            'paid' => $new_paid_status
        ];
        $response['success'] = $CalendarModel->save($save_data);

        return $this->respond($response);
    }

    public function edit($edit_id = 0)
    {
		$usertypes = model('UsertypesModel');
        $data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

        if($data['logged_user']['super_admin'] != 1 AND $data['logged_user']['certified'] != 1){
            return redirect()->to('admin/classes');
        }

		$data['current'] = $this->model->where(['id' => $edit_id])->first();
        if($edit_id != 0 AND $data['current'] == NULL){
            return redirect()->to('admin/models');
        };

		return view('admin/models/edit_view', $data);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		$rules = $this->model->validationRules;
		$data = $this->request->getPost();

        $response['rules'] = $rules;
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';

			$response['success'] = $this->model->save($data);
			$response['errors'] = $this->model->errors();
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
}