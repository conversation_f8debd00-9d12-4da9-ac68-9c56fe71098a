<?php namespace App\Controllers\Admin;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Usertypes extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('UsertypesModel');
	}

    public function index()
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['title'] = 'List of user types';
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
		echo view('admin/usertypes/index_view', $data);
    }

    public function datatable()
    {
		$post = $this->request->getPost();
		$draw = $post['draw'];
		$row = $post['start'];
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }

		$rowperpage = $post['length']; // Rows display per page
		$columnIndex = $post['order'][0]['column']; // Column index
		$columnName = $post['columns'][$columnIndex]['data']; // Column name
		$columnSortOrder = $post['order'][0]['dir']; // asc or desc
		$searchValue = $post['search']['value']; // Search value


		$db      = \Config\Database::connect();
		$sql = "SELECT
			usertypes.*
			FROM usertypes
			WHERE 1 ";
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecords = count($data);

		if (session()->has('filter_table'))
		{
			$filter = session()->filter_table;
			if(isset($filter['vreme']) AND $filter['vreme'] <> ''){
				$sql .= " AND users.created_at > '" . $filter['vreme'] . "'";
			}
			if(isset($filter['vrsta_potvrde']) AND $filter['vrsta_potvrde'] <> ''){
				$sql .= " AND users.vrsta_potvrde = '" . $filter['vrsta_potvrde'] . "'";
			}
		}
		if($searchValue != ''){
			$sql .= " AND (
				usertypes.id LIKE '%" . $searchValue . "%'
				OR usertypes.title LIKE '%" . $searchValue . "%'
				) ";
		}
		$query = $db->query($sql);
		$data = $query->getResult();
		$totalRecordwithFilter = count($data);
		$sql .= " ORDER BY " . $columnName . " " . $columnSortOrder;
		$sql .= " LIMIT " . $row . "," . $rowperpage;
		$query = $db->query($sql);
		$data_final = $query->getResult();
		$response = array(
		  "mrnj" => $sql,
		  "draw" => intval($draw),
		  "iTotalRecords" => $totalRecords,
		  "iTotalDisplayRecords" => $totalRecordwithFilter,
		  "aaData" => $data_final
		);
		echo json_encode($response, JSON_PRETTY_PRINT);
    }

    public function edit($edit_id = 0)
    {
		$data['logged_user'] = $this->admin;
		$data['settings'] = $this->settings;
		$data['title'] = 'Single user type';
        if($data['logged_user']['super_admin'] != 1){
            return redirect()->to('admin/classes');
        }
		$data['current'] = $this->model
					->asArray()
					->where(['id' => $edit_id])
					->first();
		/*
		echo '<pre>';
		var_dump($data);
		die();
		*/
		return view('admin/usertypes/edit_view', $data);
    }

    public function save()
    {
		$data = $this->request->getPost();
		$response['message'] = 'Data successfully saved';
		$response['success'] = $this->model->save($data);
		if ($response['success'] === false)
		{
			$response['message'] = implode('</br>', $this->model->errors());
		}
		else
		{
			$response['inserted_id'] = $this->model->getInsertID();
		}

		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }

    public function delete_record($record_id = 0, $ajax = FALSE)
    {
		if ($record_id > 0)
		{
			$response['success'] = $this->model->delete($record_id);
		}
		return redirect()->to(site_url('admin/usertypes'));
    }
}