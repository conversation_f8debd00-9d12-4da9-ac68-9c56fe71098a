<?php
function generate_slug($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.clone_exercise {
	width: 30px !important;
	height: 30px !important;
	position: absolute !important;
	right: 58px;
	top: 50%;
	transform: translateY(-50%);
	line-height: 1;
    display: flex;
    align-items: center;
}
.clone_exercise.btn--loading-small-black {
	color: #fff !important;
    font-size: 10px !important;
}
.clone_exercise.btn--loading-small-black:hover {
    font-size: 10px !important;
	color: #fff !important;
}
.ui-sortable-placeholder {
	height: 58px;
	width: 100%;
	margin-bottom: 10px;
	background: #f8f8f8;
	border-radius: 8px;
	border: 2px dashed #ddd;
}
.w160px {
    width: 160px;
}
.colors span {
    font-size: 0px;
}
.colors.with_number {
    height: 20px;
}
.colors.with_number span {
	width: 20px !important;
	height: 20px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    color: #fff;
}
.num_of_springs {
	width: 40px;
	height: 20px;
	text-align: center;
	font-size: 12px;
	color: #969696;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
}
.num_of_springs:disabled {
	opacity: 0.3;
}
.custom-selectbox:not(.with-checkboxes) .select_val {
	line-height: 39px;
	height: 39px;
    font-size: 12px;
}
.custom-selectbox-holder {
	height: 40px;
}
.mt-15 {
    margin-top: 15px;
}
.single-class-rest {
	position: relative;
}
.colors {
	display: flex;
	position: absolute;
	height: 20px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
    right: 102px;
}
.colors span.red-bg {
	background: #DB1818 !important;
}
.colors span.white-bg {
	box-shadow: 0 0 0 1px rgba(0,0,0,0.2) inset !important;
}
.colors span.gray-bg {
    background-color: #ccc !important;
}
.colors span.white-bg {
	box-shadow: 0 0 0 1px rgba(0,0,0,0.2) inset !important;
	color: #000 !important;
}
.colors span {
	display: block;
	width: 20px;
	height: 20px;
	border-radius: 50%;
    margin-left: 5px;
}
.btn.btn-big {
	font-size: 14px !important;
}
[onclick]{
    cursor: pointer;
}
.beforee {
    position: relative;
    padding-left: 133px !important;
}
.beforee::before {
    content: 'Routine duration';
    position: absolute;
    height: 100%;
    top: 0;
    left: 20px;
}
.not-empty .beforee::before {
    content: 'Routine duration: ';
    position: absolute;
    height: 100%;
    top: 0;
    left: 20px;
}
.custom-select.active {
	position: relative;
	z-index: 11111;
}
.custom-selectbox:not(.with-checkboxes) ul li:before {
    display: none !important;
}
.custom-selectbox:not(.with-checkboxes) ul li {
    padding: 8px 0px 0px 10px !important;
}
.custom-selectbox:not(.with-checkboxes) ul {
	margin: 8px 0;
}
.custom-selectbox:not(.with-checkboxes) ul li {
	padding: 6px 10px 6px 20px !important;
	margin-bottom: 0;
}
.custom-selectbox:not(.with-checkboxes) ul li:hover {
	background: #fbfbfb;
}
.custom-select.error .custom-selectbox:not(.with-checkboxes) {
	border-color: red !important;
}
.graycheckbox, .checkbox-group .form-box {
	background: #fff;
}
.custom-selectbox:not(.with-checkboxes) ul {
    overflow-y: auto !important;
}
.custom-selectbox:not(.with-checkboxes) ul li:last-child {
	margin-bottom: 0;
}
.disabledd {
    pointer-events: none;
    opacity: 0.3;
}
.single-selected-exercises .handle,
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
    margin-top: 37px;
    right: 0;
    z-index: 11;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.ajax-class {
	position:relative;
    /* overflow: hidden; */
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 15px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 45px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.ml-auto {
  flex: initial;
  max-width: 35px;
  margin-left: auto !important;
  align-self: center;
  height: 35px;
  color: #000 !important;
  background: #fff !important;
  border: 1px solid #ddd;
  width: 35px;
  position: absolute;
  right: 20px;
  font-size: 18px !important;
  padding:0 0 0 2px;
  
}
.btn.btn-xs.red-bg.white.f-1.ml-auto:hover {
  color: #fff !important;
  background: #000 !important;
  border: 1px solid black !important;
}
.audio_item:hover {
  background: #f0f0f0;
}
.audio_item {
  padding: 0 !important;
  margin: 0 !important;
  cursor: pointer;
}
.audio_item a{
  padding: 8px 20px !important;
  margin: 0 !important;
}
.audio_item.selected {
    font-weight: 700;
}
.upload-zone_audio,
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
    position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver::before {
    content: "Drop your audio file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.upload-zone_audio.no-border::before,
.upload-zone_audio.no-border {
	background: #fff;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 14px !important;
  font-weight: 600 !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    z-index: 9999;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
}
#main_form {
	margin: 0 auto 25px !important;
}
.ajax-class{
    position: relative;
}
.exercises_add_duration {
	position: absolute;
	top: 0;
	width: 100%;
	display: flex;
	height: 100%;
	background: #fff;
	align-items: flex-start;
	justify-content: center;
	padding: 10px 25px;
	/* box-shadow: 0 0 20px 0 rgba(0,0,0,0.15); */
	right: 0;
	opacity: 0;
	pointer-events: none;
	visibility: hidden;
	transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
    flex-direction: column;
}
.exercises_add_duration.opened {
    opacity: 1;
    pointer-events: auto;
    visibility: visible;
	right: 0px;
    background: rgba(0,0,0,0.3);
}
.exercises_add_duration_input {
	width: 50%;
	border: 1px solid #ddd;
	height: 40px;
	padding: 5px 5px 5px 20px;
	font-size: 14px;
	margin-right: 0;
	text-align: left;
    border-radius:8px;
    background:#fdfdfd;
}
.close_duration_popup {
	position: absolute;
	top: 10px;
	right: 20px;
	z-index: 1;
	cursor: pointer;
	color: #F0F0F0;
	font-size: 40px;
	padding: 0;
	font-weight: 300;
	left: auto;
	width: 30px !important;
}
<?php if($logged_user['super_admin'] != 1){ ?>
.msg-popup .link.link-red.red.ml-auto{
    display: none;
}
<?php } ?>
span.dropdown-button {font-size:14px !important; padding-left: 20px;border: 1px solid #ddd !important;
  border-radius: 8px;
  background: #fdfdfd;}
/*new code*/
.reversecols {justify-content: space-between; max-width: 1260px; margin: 0 auto;}
.reversecols .col-6 {padding:0; max-width:48%;}
.reversecols .classroutinecol {}
.reversecols h5 {margin-top: 5px;margin-bottom: 5px;}
.nomarleftright {margin-left:0; margin-right:0;}
.twodropdwn {margin-left: 0; margin-right: 0; justify-content: space-between;}
.twodropdwn .col-6 {padding: 0 !important; max-width: 48.5%;}
.find-exercises, .single-class-image {display:none !important;}
.remove-textual {width: 10px; height: 10px;	position: absolute;	right: 33px; top: 50%; transform: translateY(-50%);}
.exercises_add_duration {position: fixed; z-index: 99999999999; padding: 0;}
.exercises_add_duration h5 {margin-left:30px; font-weight: 600; padding-bottom: 5px !important; margin-top: 0 !important;}
.pp-wrap {flex-direction:column; background: #fff; padding-top: 25px; padding-bottom: 30px; width: 100%; max-width: 400px; height: auto; top: 50%; left: 50%; transform: translate(-50%, -50%); position: absolute;}
.pp-durat {width: calc(100% - 60px); margin: 0 auto 25px;}
.pp-orient {width: calc(100% - 60px); margin: 0 auto;}
.pp-wrap button {font-size:14px !important; width: calc(100% - 60px); margin: 0 auto;}
.pp-sep {width:100%; height:1px; background:#f0f0f0;margin-top:20px; margin-bottom: 30px;}
.dur-min {position: absolute; margin-left: 123px; font: 12px 'Graphik';	color: #969696;}
.dur-sec {position: absolute; right: 50px; font: 12px 'Graphik'; color: #969696;}
.ajax-class:hover {background: none;}
.ajax-search-classes.search-form .search-button {top: 2px;}

.single-selected-exercises .handle, .single-selected-howto .handle, .single-selected-class .handle {
	margin-top: 0 !important;
	top: 50%;
	transform: translateY(-50%);
}
.single-selected-exercises .single-class {
	border-bottom: none;
	padding-bottom: 0;
	margin-bottom: 0;
}
.single-selected-exercises {
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 25px;
	padding-top: 25px;
}
.total-exercises-count {
	padding: 10px 17px !important;
  background: #f8f8f8;
  border: 1px solid #f0f0f0 !important;
  border-radius: 8px;
  margin-bottom:15px;
}
.selected_exercise:first-child .video_preview_tooltip,
.selected_exercise:nth-child(2) .video_preview_tooltip,
.ajax-class.exercises-class:first-child .video_preview_tooltip,
.ajax-class.exercises-class:nth-child(2) .video_preview_tooltip {
    bottom: auto;
    top: 98%;
}
.selected_exercise:hover .video_preview_tooltip,
.ajax-class.exercises-class:hover .video_preview_tooltip {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;    
}
.video_preview_tooltip {
    transition: all 0.1s ease-in-out 0s;
	position: absolute;
	width: 200px;
	left: 6px;
    bottom: 98%;
	z-index: 99;
	background: #fff;
	box-shadow: 0 0 6px 0 rgba(0,0,0,0.05);
	height: calc(200px * 0.5625);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}
.video_preview_tooltip video {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover;
	position: absolute;
    z-index: 1;
	top: 0;
	left: 0;
}
/*Dropdown checkboxes*/ 
.dropdown-container {position: relative;}
.dropdown-label {font-size:14px; color:#969696;}
.dropdown-label:after {content: ""; width:8px; height:5px; background: url(/admin_assets_new/images/triangle-down.svg) no-repeat center center/cover; position: absolute; right: 15px; top: 50%; margin-top: -3px;}
/*.dropdown-container.is-active .dropdown-label:after {content: "\25B2";}*/
.dropdown-button {cursor: pointer;  border: 1px solid #f0f0f0; background: white; display: flex; flex-flow: row wrap;}
.dropdown-quantity {flex: 1;display: flex; flex-flow: row wrap; flex-basis: 100%; }
.dropdown-list {position: absolute; overflow-y: auto; z-index: 9999999; top: 55px; width: 100%; max-height: 250px; padding: 15px 20px 0 20px; border: 1px solid #ddd !important; border-top: 0; background: white; display: none; max-width: 500px; box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);}
.dropdown-container.is-active .dropdown-list {display: block; border-radius:10px;}
.dropdown-list input[type="search"] {padding: 5px; display: block; width: 100%;}
.dropdown-list ul {padding: 0; padding-top: 10px; list-style: none;}
.dropdown-list li {padding: 0.24em 0;}
input[type="checkbox"] {margin-right: 5px;}
.dropdown-container .is-hidden { display: none; }
.hidebtn {font-size:10px; font-weight:600; border:1px solid #000; padding:7px 20px; float:right; cursor:pointer; margin-top: -5px;} 
.hidebtn:hover {color:#fff; background:#000;} 
.showdiv {display:flex !important;}
.text-part {
    padding-right: 70px !important;
    max-width: 100%;
}
.add-part {
	position: absolute;
	top: 50%;
	right: 0;
	z-index: 1;
	transform: translateY(-50%);
}
.upload-image.small-uplad-image {border-radius:8px;}
/*Disabled input if Please select machine first displayed*/
.machine_first {margin-top: 10px; color:#DB1818 !important;}
.machinefirst-box.disabled-input {display:block !important;}
.machinefirst-box.disabled-input .dropdown-quantity {display:none !important;}
.dropdown-container.machinefirst-box.disabled-input .dropdown-button.noselect {pointer-events: none; cursor: default; background:#f8f8f8;}
/*end*/

@media screen and (max-width: 767px) {
.bottom-fixed-buttons {width:100%; padding-right:20px;}
#main_form {margin-bottom: 20px !important;}
}

@media screen and (max-width: 480px) {
.pp-wrap {max-width: 90%; margin-left: -45%;} 
.close_duration_popup {left: initial; margin-left: 0; right: 0;	margin-right: 35px;}
.dur-min {margin-left: 30%;}
}
.line-input.search-routines {
	height: 30px;
	width: 100%;
    font-size: 12px;
    box-shadow: none !important;
    max-width:100%;
}
.routines-search-container {
	padding: 6px 20px 6px 20px !important;
	position: sticky;
	top: 0;
	background: #fff;
	z-index: 1;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5 mb-100" style="padding-bottom: 120px">
        <div class="container">
            <div class="flex aic jcsb minH45 page-title">
                <?php if(isset($current['status']) AND $current['status'] == 2){ ?>
                    <h1 class="h3">Class Review</h1>
                    <span class="btn btn-xs yellow-bg white f-1 ml-2" style="min-height: 25px;">Pending</span>
                    <a href="admin/classes" class="btn btn-border white-bg black ml-auto" title="Cancel">Cancel</a>
                <?php }else{ ?>
                    <h1 class="f-20 mr-1 pl-05">
                        <?php echo isset($current['id']) ? '' : 'Upload' ?> Class <?php echo isset($current['id']) ? 'Details' : '' ?>
                    </h1>
                    <span class="btn btn-xs status-orange f-12 btnadmin mr-auto" id="draft" <?php echo (isset($current['status']) AND $current['status'] == 1) ? '' : 'style="display: none;"'; ?>>Draft</span>
                    <!-- <a href="admin/classes" class="btn btn-border white-bg black ml-2" title="Cancel"><?php // echo isset($current['id']) ? 'Back' : 'Back' ?></a> -->
                <?php } ?>
            </div>
            <?php if($logged_user['super_admin'] == 1){ ?>
            <form action="admin/classes/upload" method="post" enctype="multipart/form-data" class="flex flex-column aic jcc upload-zone" id="video_container" <?php echo isset($current['id']) ? 'style="min-height: 400px;"' : '' ?>>
                <input type="file" name="video" id="video" ondragover="dragOver()" ondragleave="dragLeave()" ondrop="dragLeave()" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                <div class="before_upload" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? 'display: none' : ''; ?>">
                    <!--<img src="/admin_assets_new/images/upload-icon2.svg">-->
                    <span class="f-16 semibold mb-1">DRAG AND DROP VIDEO HERE</span> 
                    <span class="f-14 midGray video_choose">or <u>select a file</u></span>
                </div>
                <div class="video_placeholder">
                    <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
                </div>
                <span id="video-is-uploading"></span>
                <canvas id="thecanvas" style="display: none;"  width="1000" height="600"></canvas>
                <span id="progress-bar-status-show"></span>
                <span id="toshow" style="display: none;"></span>
            </form>

            <div class="flex aic jcsb mt-1 mb-2">
                <span id="remove_video" class="link link-red red text-underline f-12 mb-1" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>">Remove video</span>
                <!-- <div class="duration-container ml-auto">
                    <div class="flex aic jcr f-14 no-wrap" hidden>
                        <p class="mr-1">Duration</p>
                        <input type="text" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
                        <span class="duration"><img src="images/rewind.svg" style="height: 15px" alt="" title="Get video duration" class="img-fluid ml-1" /></span>
                    </div>
                </div> -->
                <input type="hidden" id="duration" class="line-input small" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>" />
            </div>
            <?php }else{ ?>
            <div class="video_placeholder">
                <video id="my_video" controls muted class="after_upload" poster="<?php echo (isset($current['image']) AND $current['image'] != '') ? $current['image'] : ((isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''); ?>" src="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>" style="<?php echo (isset($current['video']) AND $current['video'] != '') ? '' : 'display: none'; ?>"></video>
            </div>
            <?php } ?>
            <hr class="mt-4 mb-4">
        </div>
        <form action="admin/classes/save" method="post" enctype="multipart/form-data" class="default_submit container border-bottom classes_form" id="main_form">
        <?php if($logged_user['super_admin'] == 1){ ?>
            <input type="hidden" id="video_path" name="video" value="<?php echo (isset($current['video']) AND $current['video'] != '') ? $current['video'] : ''; ?>"/>
            <input type="hidden" id="video_thumb" name="video_thumb" value="<?php echo (isset($current['video_thumb']) AND $current['video_thumb'] != '') ? $current['video_thumb'] : ''; ?>"/>

            <h5 class="flex aic jcsb mb-4 f-14 semibold">CUSTOM THUMBNAIL <a class="btn btn-xs f-10 btn-border hide_bottom">SHOW</a></h5>
            <div class="" style="display: none;">
                <div class="image_container flex aic mb-5">
                    <div class="upload-image small-uplad-image" id="image_container">
                        <input type="file" name="image" id="image">
                        <img src="<?php echo empty($current['image']) ? 'admin_assets_new/images/upload-icon2.svg' : $current['image']; ?>" alt="" class="image_preview <?php echo empty($current['image']) ? '' : 'has_image'; ?> no-img" style="opacity: <?php echo empty($current['image']) ? 1 : 1; ?>">
                    </div>
                    <div class="midGray f-12">
                    <span>Max. file size is 2mb. Supported formats: PNG/JPG.<br>Desirable size: 960px x 540px.</span>
                        <div class="flex flex-column mt-1 pt-05 image_options <?php echo empty($current['image']) ? 'hide' : ''; ?>">
                            <a href="javascript:;" class="link link-black black text-underline replace_image">Replace Current</a>
                            <a href="javascript:;" class="link link-midGray midGray text-underline remove_image" onclick="$('.video_thumbs').slideDown()">Remove</a>
                        </div>

                    </div>
                </div>
            </div>

            <hr class="mt-0 mb-4">

            <h5 class="flex aic jcsb mb-4 f-14 semibold">CLASS AUDIO <a class="btn btn-xs f-10 btn-border hide_bottom">SHOW</a></h5>
            <div class="row mb-2 mb-mob-0" style="display: none;">
                <div class="col-12">
                    <h5 class="mb-1 f-11">CLASS AUDIO TRACK</h5>
                    <div class="input-container mb-0">
                        <div class="dropdown d-inline-block" style="width: 100%;">
                            <span class="dropdown-button" data-dropdown="" style="width: 100%;display: flex;align-items: center;justify-content: space-between;height: 40px;border: 1px solid #f0f0f0;">
                                <?php
                                foreach($all_audio as $single){
                                    if(isset($current['audio']) AND $current['audio'] != '' AND $current['audio'] == $single['audio']){
                                        $audio = $single['title'];
                                        break;
                                    }
                                }
                                ?>
                                <span class="dropdown-value"><?php echo (isset($audio) AND NULL != $audio) ? $audio : 'Select audio track'; ?></span>
                                <i class="arrow-down down-triangle"></i>
                            </span>
                            <ul class="dropdown-menu drop-right flex-vertical p-0 pb-1" style="width: 100%;">
                                <li class="m-0 mb-1"><input type="text" id="" class="search_audio line-input small" style="padding: 5px;font-size: 13px;" placeholder="Search track"></li>
                                <?php
                                foreach($all_audio as $single){
                                    (isset($current['audio']) AND $current['audio'] != '')
                                ?>

                                <li class="audio_item <?php echo (isset($single['audio']) AND $single['audio'] != '' AND isset($current['audio']) AND $current['audio'] == $single['audio']) ? 'selected' : ''; ?>">
                                    <a data-val="<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>" data-audio="<?php echo (isset($single['audio']) AND $single['audio'] != '') ? $single['audio'] : ''; ?>" href="javascript:;" onclick="custom_select_dropdown($(this))" class="darkGray w100" title="Black">
                                        <img src="images/note.svg" alt="" class="img-fluid" style="width: 14px;" />
                                        <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                    </a>
                                </li>
                                <?php
                                }
                                ?>
                            </ul>
                            <input type="hidden" name="audio" class="audio" value="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>" />
                        </div>
                        <!-- <span class="input-label" style="top: -15px;">Announcement priority</span> -->
                    </div>
                </div>
                <div class="col-12 mb-3 mb-mob-1">
                    <div class="audio_placeholder p-0" style="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? '' : 'display: none;'; ?>">
                        <div class="flex flex-column w100 mt-1">
                            <h5 class="mt-05 pb-05 f-14 semibold midGray">LISTEN</h5>
                            <audio id="class_audio" controls class="after_upload_audio" src="<?php echo (isset($current['audio']) AND $current['audio'] != '') ? $current['audio'] : ''; ?>"></audio>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-45">
        <?php } ?>
            <?php if(isset($current['scheduled_title']) AND $current['scheduled_title'] != ''){ ?>
                <h5 class="mb-2 f-14 semibold">CLASS INFO</h5>
                <p class="mb-5 f-14 midGray lh-small"><?php echo $current['scheduled_title']; ?></p>
            <?php }else{ ?>
                <h5 class="mb-4 f-14 semibold">CLASS INFO</h5>
            <?php } ?>

            <div class="row">
                <div class="col-12">
                    <h5 class="mb-1 f-11">NAME *</h5>
                    <div class="input-container" id="title_container" style="position: relative;max-width: 500px;">
                        <input type="text" name="title" class="line-input f-14 black" placeholder="Enter" <?php echo ($logged_user['super_admin'] == 1) ? '' : 'readonly' ?> style="max-width: 600px;<?php echo ($logged_user['super_admin'] == 1) ? '' : 'background: #fbfbfb;' ?>" value="<?php echo isset($current['title']) ? $current['title'] : '' ?>" />

                        <?php if($logged_user['super_admin'] == 0){ ?>
                        <h5 class="f-12 newRed lh-small mt-1" style="">Only the production team can change the class name.</h5>
                         <?php } ?>
                    </div>
                </div>
            </div>


            <h5 class="mb-1 f-11">DIFFICULTY *</h5>
            <?php
                $diff = [];
                foreach($difficulty as $single) {
                    $diff[$single['id']] = $single['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['difficulty']) AND $current['difficulty'] != '' AND $current['difficulty'] != 0) ? 'black' : ''; ?>"><?php echo (isset($current['difficulty']) AND $current['difficulty'] != '' AND $current['difficulty'] != 0) ? $diff[$current['difficulty']] : 'Select'; ?></div>
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    $current_difficulty = (isset($current['difficulty']) AND $current['difficulty'] != '' AND $current['difficulty'] != 0) ? $current['difficulty'] : 0;
                    foreach($difficulty as $single){
                    ?>
                    <div class="checkbox mb-15" id="difficulty_container">
                        <input type="radio" class="" name="difficulty" data-name="<?php echo $single['title']; ?>" id="difficulty<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_difficulty ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="difficulty<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if($current_difficulty > 0){
                        foreach($difficulty as $single){
                            if($single['id'] == $current_difficulty){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="difficulty<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                    ?>                    
                </div>
            </div>

            <h5 class="mt-25 mb-1 f-11">BODY PARTS *</h5>    
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['body_parts']) AND $current['body_parts'] != '' AND $current['body_parts'] != 0) ? 'black' : ''; ?>"><?php echo (isset($current['body_parts']) AND $current['body_parts'] != '' AND $current['body_parts'] != 0) ? $current['body_parts'] : 'Select'; ?></div>
                </div>
                <div class="col-6 dropdown-list">
                    <div class="checkbox mb-15 mb-mob-0" id="body_parts_container" onclick="generate_title()">
                        <input type="radio" data-name="Full body" <?php echo (isset($current['body_parts']) AND $current['body_parts'] == 'Full body') ? 'CHECKED' : ''; ?> value="Full body" name="body_parts" class="" id="full_body_new">
                        <label for="full_body_new" class="f-12">Full Body</label>
                    </div>
                    <div class="checkbox mb-15 mb-mob-0" id="body_parts_container" onclick="generate_title()">
                        <input type="radio" data-name="Upper body" <?php echo (isset($current['body_parts']) AND $current['body_parts'] == 'Upper body') ? 'CHECKED' : ''; ?> value="Upper body" name="body_parts" class="" id="upper_body_new">
                        <label for="upper_body_new" class="f-12">Upper Body</label>
                    </div>
                    <div class="checkbox mb-15 mb-mob-0" id="body_parts_container" onclick="generate_title()">
                        <input type="radio" data-name="Lower body" <?php echo (isset($current['body_parts']) AND $current['body_parts'] == 'Lower body') ? 'CHECKED' : ''; ?> value="Lower body" name="body_parts" class="" id="lower_body_new">
                        <label for="lower_body_new" class="f-12">Lower Body</label>
                    </div>
                </div>
                <div class="dropdown-quantity">
                    <?php if(isset($current['body_parts']) AND $current['body_parts'] != '' AND $current['body_parts'] != 0){ ?>                        
                        <span class="dropdown-sel"><?php echo $current['body_parts']; ?><span class="remove_tag" data-uncheck="body_parts1">×</span></span>
                    <?php } ?>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="checkbox mb-25" id="based_on_lagree_container">
                    <input type="checkbox" onchange="$(this).is(':checked') ? $('#based_on_lagree').val(1) : $('#based_on_lagree').val(0)" id="based_on_lagree_check" <?php echo (isset($current['based_on_lagree']) AND $current['based_on_lagree'] == '1') ? 'checked' : '' ?>>
                        <label for="based_on_lagree_check" class="f-12">Based on Lagree 2.0</label>
                        <input type="hidden" name="based_on_lagree" id="based_on_lagree" value="<?php echo isset($current['based_on_lagree']) AND $current['based_on_lagree'] == '1' ? $current['based_on_lagree'] : 0 ?>">
                    </div> 
                </div> 
            </div> 

            <?php if($logged_user['super_admin'] == 1){ ?>
                <h5 class="mb-1 f-11">TEACHER *</h5>
            <?php 
                $teach = [];
                foreach($all_teachers as $single) {
                    $teach[$single['id']] = ((isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : '');
                }
                $curr_teacher = (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : 0; 
            ?>
            <div class="row dropdown-container mx-0 mb-25 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['teacher']) AND $current['teacher'] != '') ? (isset($teach[$current['teacher']]) ? 'black' : '') : ''; ?>"><?php echo (isset($current['teacher']) AND $current['teacher'] != '' AND $current['teacher'] != 0) ? (isset($teach[$current['teacher']]) ? $teach[$current['teacher']] : 'Select') : 'Select'; ?></div>
                </div>
                <div class="col-12 dropdown-list">
                    <div class="w100">
                        <?php
                        $c=0;
                        foreach($all_teachers as $single){
                        $c++;
                            if(isset($current['type']) AND $current['type'] == 1){
                        ?>
                        <div class="checkbox mb-1 pb-05 w100" id="teacher_container" style="<?php echo ($single['id'] != $curr_teacher) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" data-name="<?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?>" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $curr_teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-12"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>
                         </div>
                        
                        <?php
                            }else{
                        ?>
                        <div class="checkbox mb-1 pb-05 w100" id="teacher_container" style="<?php echo ($single['certified'] == 0) ? 'display: none' : '' ?>">
                            <input type="radio" class="" name="teacher" data-name="<?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?>" id="teacher<?php echo $c; ?>" <?php echo $single['id'] == $curr_teacher ? 'checked' : '' ?> value="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <label for="teacher<?php echo $c; ?>" class="f-12"><?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''; ?></label>

                        </div>
                            <?php
                            }
                            ?>
                        <?php
                        }
                        ?>
                    </div>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if($curr_teacher > 0){
                        foreach($all_teachers as $single){
                            if($single['id'] == $curr_teacher){
                    ?>
                    <span class="dropdown-sel"><?php echo ((isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] . ' ' . $single['lastname'] : ''); ?><span class="remove_tag" data-uncheck="teacher<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                ?>                    
                </div>

            </div>
          
            <?php }else{ ?>
                <input type="hidden" name="teacher" value="<?php echo (isset($current['teacher']) AND $current['teacher'] != '') ? $current['teacher'] : ''; ?>" />
            <?php } ?>

            <h5 class="mb-1 f-11">MACHINE *</h5>
            <div class="row dropdown-container mx-0 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'black' : ''; ?>"><?php echo (isset($current_machines) AND $current_machines != '' AND count($current_machines) > 0) ? 'Selected (' . count($current_machines) . ')' : 'Select'; ?></div>   
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    $curr_machines = (isset($current_machines) AND $current_machines != '') ? $current_machines : array();
                    foreach($machines as $single){
                    ?>
                    <div class="checkbox mb-15" id="machine_container">
                        <input type="checkbox" class="available_machines machinecheck" name="machine[]" data-name="<?php echo $single['title']; ?>" id="machine_select<?php echo isset($single['id']) ? $single['id'] : 0; ?>" <?php echo (isset($single['id']) AND in_array($single['id'], $curr_machines)) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="machine_select<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>                        
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if(count($curr_machines)){
                        foreach($machines as $single){
                            foreach($curr_machines as $single2){
                                if($single['id'] == $single2){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="machine_select<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                                }
                            }
                        }
                    }
                ?>                    
                </div>

            </div>

            <div class="row mt-25" id="micro_friendy_wrap" style="<?php echo (isset($current_machines_ids) AND is_array($current_machines_ids) AND count($current_machines_ids) > 0 AND in_array(2, $current_machines_ids) OR in_array(3, $current_machines_ids) OR in_array(4, $current_machines_ids) OR in_array(10, $current_machines_ids) OR in_array(11, $current_machines_ids)) ? '' : 'display: none'; ?>">
                <div class="col-12">
                    <div class="checkbox" id="micro_friendly_container">
                        <input type="checkbox" onchange="$(this).is(':checked') ? $('#micro_friendly').val(1) : $('#micro_friendly').val(0)" id="micro_friendly_check" <?php echo (isset($current['micro_friendly']) AND $current['micro_friendly'] == '1') ? 'checked' : '' ?>>
                        <label for="micro_friendly_check" class="f-12">Micro Friendly</label>
                        <input type="hidden" name="micro_friendly" id="micro_friendly" value="<?php echo isset($current['micro_friendly']) AND $current['micro_friendly'] == '1' ? $current['micro_friendly'] : 0 ?>">
                    </div> 
                </div> 
            </div> 

            <div class="row">
            <?php 
                $curr_accessories = (isset($current_accessories) AND is_array($current_accessories) AND $current_accessories != '') ? $current_accessories : array();
                ?>
                <div class="col-12">               
                <h5 class="mt-25 mb-1 f-11">ACCESSORIES REQUIRED</h5>     
                    <div class="dropdown-container machinefirst-box dropdown-wrap">
                        <div class="dropdown-button noselect">
                            <div class="dropdown-label <?php echo (isset($curr_accessories) AND $curr_accessories != '' AND count($curr_accessories) > 0) ? 'black' : ''; ?>"><?php echo (isset($curr_accessories) AND $curr_accessories != '' AND count($curr_accessories) > 0) ? 'Selected (' . count($curr_accessories) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                            <?php
                            foreach($accessories as $single){
                                if($single['title'] != 'No Accessories / Base Model'){
                            ?>
                                <div class="checkbox mb-15" id="accessories_container" data-machine="<?php echo $single['machine']; ?>" data-id="<?php echo $single['id']; ?>">
                                    <input type="checkbox" <?php if($single['id'] == 41){ ?>onchange="$(this).is(':checked') ? $('.bungee_tension').show() : $('.bungee_tension').hide()"<?php } ?> name="accessories[]" data-name="<?php echo $single['title']; ?>" id="accessories<?php echo $single['id']; ?>" <?php echo in_array($single['id'], $curr_accessories) ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                                    <label for="accessories<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                                </div>
                            <?php
                                }
                            }
                            ?>
                        </div>
                        <div class="dropdown-quantity">
                        <?php 
                        if(count($curr_accessories)){
                            foreach($accessories as $single){
                                foreach($curr_accessories as $single2){
                                    if($single['id'] == $single2){
                        ?>
                        <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="accessories<?php echo $single['id']; ?>">×</span></span>
                        <?php 
                                    }
                                }
                            }
                        }
                        ?>                    
                        </div>
                    </div>
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>
                <!-- <div class="col-12 bungee_tension" style="<?php // echo (in_array(41, $curr_accessories)) ? '' : 'display: none'; ?>">
                    <h5 class="f-14 semibold flex aic jcsb top-border pt-55 mt-4 mb-5">BUNGEE TENSION</h5>
                    <?php 
                    // $curr_tensions = (isset($current_tensions) AND is_array($current_tensions) AND $current_tensions != '') ? $current_tensions : array(); 
                    ?>
                    <div class="dropdown-container machinefirst-box">
                        <div class="dropdown-button noselect">
                            <div class="dropdown-label <?php // echo (isset($curr_tensions) AND $curr_tensions != '' AND count($curr_tensions) > 0) ? 'black' : ''; ?>"><?php // echo (isset($curr_tensions) AND $curr_tensions != '' AND count($curr_tensions) > 0) ? 'Selected (' . count($curr_tensions) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                            <?php                            
                            // foreach($tensions as $single){
                            ?>
                                <div class="checkbox mb-15" id="tensions_container" data-class-id="<?php // echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>" data-id="<?php // echo $single['id']; ?>">
                                    <input type="checkbox" class="" name="tensions[]" data-name="<?php // echo $single['title']; ?>" id="tensions<?php echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_tensions) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>">
                                    <label for="tensions<?php // echo $single['id']; ?>" class="f-12"><?php // echo $single['title']; ?></label>
                                </div>
                            <?php
                            // }
                            ?>
                            </div>
                        <div class="dropdown-quantity">
                        <?php 
                            // if(count($curr_tensions)){
                            //     foreach($tensions as $single){
                            //         foreach($curr_tensions as $single2){
                            //             if($single['id'] == $single2){
                            ?>
                            <span class="dropdown-sel"><?php // echo $single['title']; ?><span class="remove_tag" data-uncheck="tensions<?php // echo $single['id']; ?>">×</span></span>
                            <?php 
                            //             }
                            //         }
                            //     }
                            // }
                            ?>                    
                        </div>
                    </div>
                </div> -->
            </div>

            <h5 class="mt-25 mb-1 f-11">LANGUAGE *</h5>
            <?php
                $langs = [];
                foreach($languages as $lang) {
                    $langs[$lang['id']] = $lang['title'];
                }
            ?>
            <div class="row dropdown-container mx-0 dropdown-wrap">
                <div class="dropdown-button noselect">
                    <div class="dropdown-label <?php echo (isset($current['language']) AND $current['language'] != '') ? 'black' : ''; ?>"><?php echo (isset($current['language']) AND $current['language'] != '') ? $langs[$current['language']] : 'Select'; ?></div>
                </div>
                <div class="col-6 dropdown-list">
                    <?php
                    $current_language = (isset($current['language']) AND $current['language'] != '') ? $current['language'] : 0;
                    foreach($languages as $single){
                    ?>
                    <div class="checkbox mb-15" id="language_container">
                        <input type="radio" class="" name="language" data-name="<?php echo $single['title']; ?>" id="language<?php echo $single['id']; ?>" <?php echo $single['id'] ==  $current_language ? 'checked' : '' ?> value="<?php echo $single['id']; ?>">
                        <label for="language<?php echo $single['id']; ?>" class="f-12"><?php echo $single['title']; ?></label>
                    </div>
                    <?php } ?>
                </div>
                <div class="dropdown-quantity">
                <?php 
                    if($current_language > 0){
                        foreach($languages as $single){
                            if($single['id'] == $current_language){
                    ?>
                    <span class="dropdown-sel"><?php echo $single['title']; ?><span class="remove_tag" data-uncheck="language<?php echo $single['id']; ?>">×</span></span>
                    <?php 
                            }
                        }
                    }
                ?>                    
                </div>
            </div>


                    
        

            
            <input type="hidden" name="slug" id="slug" value="<?php echo (isset($current['slug']) AND $current['slug'] != '') ? $current['slug'] : generate_slug() ?>" />
            <?php if($logged_user['super_admin'] == 1){ ?>
            <div class="row mb-2">
                <div class="col-12">
                <h5 class="mb-45 mt-45 pt-45 top-border f-14 semibold">CLASS DESCRIPTION</h5>
                <div class="input-container mb-0" id="content_container">
                        <textarea type="text" name="content" class="line-input class_description f-14" onkeyup="$('.words_count').text($(this).attr('maxlength') - $(this).val().length)" maxlength="500" placeholder="Enter"><?php echo isset($current['content']) ? $current['content'] : '' ?></textarea>
                        <p class="f-12 midGray mt-1 pt-05">Characters left: <span class="words_count">500</span></p>
                    </div>
                </div>
            </div>
        <?php } ?>

            <!--<h5 class="mt-25 mb-1 f-11">SPRING LOAD <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h5>-->
            <?php // $curr_springs = (isset($current_springs) AND $current_springs != '') ? $current_springs : array(); ?>
            <!--<div class="row">
                <div class="col-12">                  
                    <div class="dropdown-container machinefirst-box">
                        <div class="dropdown-button noselect">
                            <div class="dropdown-label <?php // echo (isset($curr_springs) AND $curr_springs != '' AND count($curr_springs) > 0) ? 'black' : ''; ?>"><?php // echo (isset($curr_springs) AND $curr_springs != '' AND count($curr_springs) > 0) ? 'Selected (' . count($curr_springs) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                        <?php
                        // foreach($springs as $single){
                        ?>
                            <div class="checkbox mb-15" id="springs_container" data-machine="<?php // echo $single['machine']; ?>" data-id="<?php // echo $single['id']; ?>">
                                <input type="checkbox" class="" name="springs[]" data-name="<?php // echo $single['title']; ?>" id="springs<?php // echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_springs) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>">
                                <label for="springs<?php // echo $single['id']; ?>" class="f-12"><?php // echo $single['title']; ?></label>
                            </div>
                        <?php
                        // }
                        ?>
                        </div>
                        <div class="dropdown-quantity">
                            <?php 
                            // if(count($curr_springs)){
                             //    foreach($springs as $single){
                             //        foreach($curr_springs as $single2){
                             //            if($single['id'] == $single2){
                            ?>
                            <span class="dropdown-sel"><?php // echo $single['title']; ?><span class="remove_tag" data-uncheck="springs<?php // echo $single['id']; ?>">×</span></span>
                            <?php 
                                    //     }
                                  //   }
                               //  }
                            // }
                            ?>                    
                        </div>
                    </div>
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>
            </div>-->

             <!--<h5 class="mt-25 mb-1 f-11">TEMPO COUNT <span class="link midGray f-12 normal select_all text-capitalize">Select All</span></h5>-->
            <!--<div class="row">
                <div class="col-12">
                    <div class="dropdown-container machinefirst-box">
                        <div class="dropdown-button noselect">
                            <?php // $curr_tempo = (isset($current_tempo) AND is_array($current_tempo) AND $current_tempo != '') ? $current_tempo : array(); ?>
                            <div class="dropdown-label <?php // echo (isset($curr_tempo) AND $curr_tempo != '' AND count($curr_tempo) > 0) ? 'black' : ''; ?>"><?php // echo (isset($curr_tempo) AND $curr_tempo != '' AND count($curr_tempo) > 0) ? 'Selected (' . count($curr_tempo) . ')' : 'Select'; ?></div>   
                        </div>
                        <div class="dropdown-list">
                        <?php
                        // foreach($tempo as $single){
                        ?>
                            <div class="checkbox mb-15" id="tempo_container" data-machine="<?php // echo $single['machine']; ?>" data-id="<?php // echo $single['id']; ?>">
                                <input type="checkbox" class="" name="tempo[]" data-name="<?php // echo $single['title']; ?>" id="tempo<?php // echo $single['id']; ?>" <?php // echo in_array($single['id'], $curr_tempo) ? 'checked' : '' ?> value="<?php // echo $single['id']; ?>">
                                <label for="tempo<?php // echo $single['id']; ?>" class="f-12"><?php // echo $single['title']; ?></label>
                            </div>
                        <?php
                        // }
                        ?>
                        </div>
                        <div class="dropdown-quantity">
                            <?php 
                            // if(count($curr_tempo)){
                             //    foreach($tempo as $single){
                             //        foreach($curr_tempo as $single2){
                              //           if($single['id'] == $single2){
                            ?>
                            <span class="dropdown-sel"><?php // echo $single['title']; ?><span class="remove_tag" data-uncheck="tempo<?php // echo $single['id']; ?>">×</span></span>
                            <?php 
                             //            }
                             //        }
                             //    }
                           //  }
                            ?>                    
                        </div>
                    </div>
                    <h5 class="machine_first f-12 newRed lh-small" style="display: none;">Please select machine first</h5>
                </div>
            </div>-->

            <div class="row">
                <div class="col-12">
                    <div class="uploading" style="display: none;">Uploading video. Please wait...</div>
                    <input type="hidden" name="duration" id="duration_val" value="<?php echo (isset($current['duration']) AND $current['duration'] != '') ? $current['duration'] : ''; ?>">
                    <input type="hidden" name="teacher_saved" id="teacher_saved" value="<?php echo isset($current['teacher_saved']) ? $current['teacher_saved'] : 0 ?>">
                    <input type="hidden" name="type" id="type" value="<?php echo isset($current['type']) ? $current['type'] : 0 ?>">
                    <input type="hidden" name="prev_status" id="prev_status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <!-- <input type="hidden" name="created_at" id="created_at" value="<?php // echo isset($current['created_at']) ? $current['created_at'] : 0 ?>"> -->
                    <input type="hidden" name="status" id="status" value="<?php echo isset($current['status']) ? $current['status'] : 0 ?>">
                    <input type="hidden" name="image_removed" id="image_removed" value="0">
                    <input type="hidden" name="id" id="id" value="<?php echo isset($current['id']) ? $current['id'] : 0 ?>">
                    <input type="hidden" name="video_encrypted_path" id="video_encrypted_path" value="<?php echo isset($current['video_encrypted_path']) ? $current['video_encrypted_path'] : 0 ?>">
                    <input type="hidden" name="video_preview" id="video_preview" value="<?php echo isset($current['video_preview']) ? $current['video_preview'] : '' ?>">
                    <?php if(!isset($current['published_at']) OR $current['published_at'] == NULL){ ?>
                        <input type="hidden" name="published_at" id="published_at" value="<?php echo date('Y-m-d H:i:s'); ?>">                        
                    <?php } ?>

                    <div class="approve-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? '' : 'style="display: none;"';?>>
                        <button type="submit" class="btn btn-wide btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">APPROVE AND PUBLISH CLASS</button>
                        <button type="button" class="btn btn-wide btn-tall btn-border white-bg black ml-2" data-popup="reject-video" onclick="save_status(3);$('[name=teacher_id]').val($('[name=teacher]:checked').val());">REJECT</button>
                    </div>

                    <?php if(isset($prev) OR isset($next)){ ?>
                        <div class="bottom-fixed-buttons" <?php echo (isset($current['status']) AND $current['status'] == 2) ? 'style="display: none;"' : '';?>>
                            <?php if(isset($prev)){ ?>
                                <a href="admin/classes/edit/<?php echo isset($prev['id']) ? $prev['id'] : 0; ?>" class="link link-black black text-underline f-14 mr-2">Previous Class</a>
                            <?php } ?>
                            <?php if(isset($next)){ ?>
                                <a href="admin/classes/edit/<?php echo isset($next['id']) ? $next['id'] : 0; ?>" class="link link-black black text-underline f-14">Next Class</a>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <input type="hidden" name="routine_duration" id="class_routine_duration" value="<?php echo (isset($current['routine_duration']) AND $current['routine_duration'] != '') ? $current['routine_duration'] : ''; ?>">
        </form>

<?php if(count($all_exercises) > 0 AND isset($current['id']) AND $current['id'] > 0){ ?>
        <div class="container">
            
            <div class="flex jcsb aic pb-4 pt-4 top-border">
            <h5 class="f-14 semibold flex aic jcsb">ROUTINE BUILDER</h5>
            <?php if($logged_user['super_admin'] == 0){ ?>
            <a href="javascript:;" class="btn black-bg white ml-0 f-10" data-popup="view-instructions-popup">NEED HELP? VIEW INSTRUCTIONS</a>
            <?php } ?>
            </div>

            <div class="border-div pt-3 mb-2">            
            <div class="row big-gap reversecols">
                <div class="col-6 classroutinecol">
                    <h5 class="f-14 semibold flex aic jcsb position-relative rb-title">
                        CLASS ROUTINE
                    </h5>
                    <div class="flex flex-column" style="position: relative;z-index: 1111;">
                        <div id="content_container" style="position: relative;z-index: 111; margin-bottom:8px;">
                            <div class="custom-select">
                                <div class="custom-selectbox-holder w100 mb-0">
                                    <div class="custom-selectbox advanced-dropdown">
                                        <span class="select_val">Choose saved routines <span class="select_count"></span></span>
                                        <ul class="pt-2" style="display: none; margin: 0 !important;">
                                            <?php if(session('admin') > 0 AND session('super_admin') == NULL){ ?>
                                            <span class="dropdown_checkbox select_routine_list_type <?php echo (session('admin') > 0 AND session('super_admin') == NULL) ? 'checked' : ''; ?>" data-type="my">Show only my routines</span>
                                            <?php } ?>
                                            <span class="dropdown_checkbox select_routine_list_type <?php echo (session('super_admin') != NULL AND session('super_admin') == 1) ? 'checked' : ''; ?>" data-type="all">Show all</span>
                                            <span class="dropdown_checkbox select_routine_list_type mb-2" data-type="teacher">Select teacher</span>
                                            <div class="px-2 mb-2 select_routine_teachers_list" style="display: none;">
                                                <div class="teachers-list-wrap">
                                                    <span class="selected_teacher">Select</span>
                                                    <div class="teachers-list">
                                                        <div class="routines-search-container teachers-search-container px-0 mb-1" style="padding-right: 15px !important;padding-left: 15px !important;padding-top: 15px !important;background: #fff;margin: -15px -15px 15px -15px;top: -15px;z-index: 111;"><input type="text" class="line-input search-routines selected_teacher_search" onkeyup="search_teachers($(this))" id="" placeholder="Search teachers" /></div>
                                                        <?php 
                                                        foreach($all_teachers as $single){ 
                                                            if($single['countRoutines'] > 0){
                                                        ?>
                                                            <span class="select_teacher" data-name="<?php echo (isset($single['firstname']) AND $single['firstname'] != '') ? $single['firstname'] : ''; ?>" data-fullname="<?php echo (isset($single['teacher_name']) AND $single['teacher_name'] != '') ? $single['teacher_name'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" title="">
                                                                <?php echo $single['teacher_name']; ?>
                                                                <span class="midGray f-10"><?php echo (isset($single['countRoutines']) AND $single['countRoutines'] != '') ? $single['countRoutines'] : ''; ?><?php echo $single['countRoutines'] == 1 ? ' routine' : ' routines'; ?></span>
                                                            </span>
                                                        <?php
                                                            }
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <hr class="my-1" style="background: #DDDDDD;" data-type="teacher">
                                            <div class="routines-list-wrap">
                                                <h4 class="routines-dropdown-title f-14 medium px-2 mt-25 mb-15">All routines</h4>
                                                <div class="routines-search-container"><input type="text" class="line-input search-routines selected_routines_search" onkeyup="search_rountines($(this))" id="" placeholder="Search routines" /></div>
                                                <div class="routines-list">
                                                    <!-- ROUTINES LIST AJAX -->
                                                </div>
                                            </div>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="content_container" style="position: relative;z-index: 110;">
                            <div class="custom-select">
                                <div class="custom-selectbox-holder w100 mb-0">
                                    <div class="custom-selectbox <?php echo (isset($current['routine_duration']) AND $current['routine_duration'] != '') ? 'not-empty' : ''; ?>">
                                        <span class="select_val beforee" style=""><?php echo (isset($current['routine_duration']) AND $current['routine_duration'] != '') ? str_replace(':00', ' min', $current['routine_duration']) : ''; ?><span class="select_count"></span></span>
                                        <ul style="display: none;">
                                            <li data-val="20:00" data-duration="20:00" onclick="$('[name=routine_duration]').val('20:00')" class="routine_duration">20 min</li>
                                            <li data-val="30:00" data-duration="30:00" onclick="$('[name=routine_duration]').val('30:00')" class="routine_duration">30 min</li>
                                            <li data-val="40:00" data-duration="40:00" onclick="$('[name=routine_duration]').val('40:00')" class="routine_duration">40 min</li>
                                            <li data-val="50:00" data-duration="50:00" onclick="$('[name=routine_duration]').val('50:00')" class="routine_duration">50 min</li>
                                            <li data-val="60:00" data-duration="60:00" onclick="$('[name=routine_duration]').val('60:00')" class="routine_duration">60 min</li>
                                        </ul>
                                        <input type="hidden" name="routine_duration_hidden" class="select-custom" value="<?php echo (isset($current['routine_duration']) AND $current['routine_duration'] != '') ? $current['routine_duration'] : ''; ?>" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex aic jcsb addtransit" style="position: sticky;top: 5px;z-index: 111;background: #fff;">
                        <div class="text-left">
                            <span class="f-12 normal textGreen d-block"><span class="exercises_total_duration">Current duration: 00:00</span></span>
                            <span class="f-12 normal normalRed d-block mt-05"><span class="selected_duration">Routine duration: <?php echo (isset($current['routine_duration']) AND $current['routine_duration'] != '') ? $current['routine_duration'] : '00:00'; ?></span></span>                     
                        </div>
                        <a href="javascript:;" class="btn black-bg white ml-0 f-10" data-popup="add-add-transition-popup">ADD TRANSITION</a>
                    </div>

                    <div class="empty-routine">
                        <div class="flex aic jcsb addtransit" style="margin-top:13px;">
                            <div class="text-left">
                                <span class="f-12 normal d-block">Routine is empty</span>
                            </div>
                        </div>
                    </div>
                    <div class="not-empty-routine">
                        <div class="flex aic jcsb addtransit" style="margin-top:13px;">
                            <div class="text-left">
                                <span class="f-12 normal d-block">Clear the list to start a new routine</span>
                            </div>
                            <span class="btn black-bg white ml-0 f-10" data-popup="clear-exercises-popup">CLEAR ROUTINE</span>
                        </div>
                    </div>

                    <div class="">
                        <div class="row selected_clases sortable ml-mob-0 nomarleftright rout-exerc">
                        <?php
                        foreach($selected_exercises_for_selection as $single){
                        ?>
                            <div class="col-12 pl-0 pr-0 selected_exercise sortable-placeholder single-selected-exercises white-bg selected_routine_exercises" data-rowid="<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-type="exercises" data-duration="<?php echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? $single['custom_duration'] : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? $single['duration'] : 0); ?>" data-sort="<?php echo (isset($single['csc_sort']) AND $single['csc_sort'] != '') ? $single['csc_sort'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                                <span class="video_preview_tooltip" <?php echo(isset($single['transition']) AND $single['transition'] != 1) ? '' : 'style="display: none;'; ?>>
                                    <video loop muted playsinline data-src="<?php echo isset($single['video_preview']) ? $single['video_preview'] : ''; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                                </span>
                                <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid mr-2 handle" style="margin-top: 13px;">
                                <div class="single-class aic">
                                    <div class="single-class-rest">
                                        <div class="single-class-title link link-black black f-12 medium <?php echo (isset($single['transition']) AND $single['transition'] == 1) ? 'textGreen' : ''; ?>" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: calc(100% - 140px);display: block;padding-right: 0px;" data-popup="<?php echo(isset($single['transition']) AND $single['transition'] != 1) ? 'edit-routine-exercise-popup' : 'edit-transition-popup'; ?>" onclick="<?php echo(isset($single['transition']) AND $single['transition'] != 1) ? 'edit_routine_exercise' : 'edit_transition'; ?>(<?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : 0; ?>, $(this))">
                                            <?php echo (isset($single['orientation']) AND $single['orientation'] != '' AND $single['orientation'] != 'N') ? '<span class="mr-05" ' . ($single['orientation'] == 'R' ? 'style="color: #db1818"' : 'style="color: #aa2feb"') . '">(' . $single['orientation'] . ')</span>' : ''; ?><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>
                                        </div>
                                        <?php if(isset($single['transition']) AND $single['transition'] != 1){ ?>
                                        <div class="single-class-desc normal" style="padding-right: 140px;">
                            
                                        <span class="custom_duration"><?php echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? ' ' . duration_standard($single['custom_duration']) : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''); ?></span>
                                        <?php // echo (isset($single['teach']) AND $single['teach'] != '') ? '<br>by: ' . $single['teach'] : ''; ?>
                                        </div>
                                        <?php } ?>
                                        <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-<?php echo $single['class'] == 'class_class' ? 'class' : ($single['class'] == 'exercises_class' ? 'exercises' : 'howto'); ?>').remove();remove_class_from_selected(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>, <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : ''; ?>, '<?php echo $single['class'] == 'class_class' ? 'Classes' : ($single['class'] == 'exercises_class' ? 'Exercises' : 'Howto'); ?>')"><img src="images/removeicon.jpg"></span>
                                        <a href="javascript:;" class="link link-midGray midGray f-10 clone_exercise" onclick="clone_exercise('ClassesExercises', <?php echo (isset($single['csc_id']) AND $single['csc_id'] != '') ? $single['csc_id'] : 0; ?>)" title="">Clone</a>
                                        <?php
                                            // $max_1 = TRUE;
                                            if(isset($single['springs_count']) AND $single['springs_count'] != ''){
                                                $single_spring_count = json_decode($single['springs_count'], TRUE);
                                                if(is_array($single_spring_count) AND count($single_spring_count) > 0 AND $single_spring_count[0] == ''){
                                                    $single_spring_count = [];
                                                }else{
                                                    // foreach($single_spring_count as $s){
                                                    //     if($s > 1){
                                                    //         $max_1 = FALSE;
                                                    //     }
                                                    // }
                                                }
                                            }else{
                                                $single_spring_count = [];
                                            }
                                        ?>
                                        <div class="colors <?php echo (count($single_spring_count) > 0) ? 'with_number' : ''; ?>">
                                            <?php
                                            foreach($springs as $single_spring){
                                                if($single_spring['color'] != ''){
                                                    $color_id[$single_spring['id']] = $single_spring['color'];
                                                }
                                            }

                                            if(isset($single['springs']) AND $single['springs'] != ''){
                                                $colors = json_decode($single['springs'], true);
                                                
                                                foreach($colors as $k => $single_color){
                                            ?>
                                                <span class="<?php echo $color_id[$single_color]; ?>-bg"><?php echo (isset($single_spring_count[$k])) ? $single_spring_count[$k] : ''; ?></span>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        </div>
                    </div>
                    <div class="routine-info-box"><p>You don't have to click the "save routine" button if you add a new exercise or transition. Changes to the routine are automatically saved.</p></div>
                    <?php if($logged_user['super_admin'] != 1 AND $logged_user['certified'] == 1){ ?>
                    <!-- <div class="row normal-gap email_routine_wrap" style="display: none;">
                        <div class="col-12">
                            <div class="flex aic jcsb py-3" style="column-gap: 30px">
                                <a href="javascript:;" class="btn black-bg white email_routine" onclick="email_routine($(this))">EMAIL ROUTINE</a>
                                <p class="m-0 f-12 lh-20"><small>NOTE: Make sure that the routine is completed before sending.</small></p>
                            </div>
                        </div>
                    </div> -->
                    <?php } ?>
                </div>
                <div class="col-6 allexercisescol">
                    <h5 class="f-14 semibold flex aic jcsb rb-title">ALL EXERCISES
                        <span class="link midGray f-12 normal" onclick="clear_exercise_filters()">Clear</span>
                    </h5>
                    <div class="search-container mb-1">
                        <div class="ajax-search-classes search-form show ml-0 px-0">
                            <input type="text" class="seach-input search-wide search_exercises_filter" placeholder="Search (enter at least 3 characters)..." onclick="$('.custom-selectbox').removeClass('opened');$('.custom-selectbox ul').slideUp(200)">
                            <button type="button" class="search-button" style="right: 7px;border-radius: 0 !important;height: 36px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" /></button>
                        </div>
                    </div>
                    <div class="row mb-15 twodropdwn">
                        <div class="col-6">
                            <div class="custom-select small">
                                <div class="custom-selectbox-holder mb-0">
                                    <div class="custom-selectbox with-checkboxes">
                                        <span class="select_val">Machines <span class="select_count select_count_machines"></span></span>
                                        <ul>
                                        <?php foreach($machines as $single){ ?>
                                            <li class="exercises_machines" data-name="<?php echo $single['title']; ?>" data-val="<?php echo strtolower(str_replace(' ', '_', $single['id'])); ?>"><?php echo $single['title']; ?></li>
                                        <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-select small">
                                <div class="custom-selectbox-holder mb-0">
                                    <div class="custom-selectbox with-checkboxes">
                                        <span class="select_val">Body Parts <span class="select_count select_count_body_parts"></span></span>
                                        <ul>
                                        <?php foreach($body_parts as $single){ ?>
                                            <li class="exercises_body_parts" data-val="<?php echo strtolower($single['id']); ?>"><?php echo $single['title']; ?></li>
                                        <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="find-exercises">
                        <p class="f-14 medium my-6">Didn’t find an exercise? Please submit a request <a href="mailto:<EMAIL>" class="link link-black black text-underline">here</a>.</p>
                    </div>
                    <div class="total-exercises-count">
                        <p class="f-12 normal"><span class="total-exercises"><?php echo $all_exercises_count; ?></span> Exercises</p>
                    </div>
                    <div class="search-ajax-classes" id="search-ajax-classes" data-scrollbar style="max-height: 70vh;padding-right: 12px">
                        <h3 class="f-14 px-2 text-center no_result" style="display: none;">There are no exercises matching your filters. <br>Try removing some.</h3>
                        <div class="search-ajax-classes-container">
                            <?php
                            foreach($all_exercises as $single){
                                $added = FALSE;
                                if($single['status'] == 0){
                                    foreach($selected_exercises_for_selection as $single2){
                                        if($single['id'] == $single2['id']){ $added = TRUE; }
                                    }
                            ?>
                            <div class="ajax-class exercises-class" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                                <span class="video_preview_tooltip">
                                    <video loop muted playsinline data-src="<?php echo $single['video_preview']; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                                </span>
                                <span class="pr-2 flex ail jcc flex-column text-part">
                                    <span class="f-12" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 100%;display: block;font-weight: 500;margin-bottom: 1px;"><span class="title_to_rename"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></span><?php echo (isset($single['aka']) AND $single['aka'] != '') ? " (" . $single['aka'] . ")" : ''; ?></span>
                                    <!--<span style="color: #969696;font-size: 12px;display: inline-block;line-height: 20px;font-weight: 400;">
                                        <?php // echo (isset($single['all_exercise_machines']) AND $single['all_exercise_machines'] != '') ? $single['all_exercise_machines'] : ''; ?><?php // echo (isset($single['diff']) AND $single['diff'] != '') ? ', ' . $single['diff'] : ''; ?>
                                        <?php // echo (isset($single['custom_duration']) AND $single['custom_duration'] != 0) ? ', ' . duration_standard($single['custom_duration']) : ((isset($single['duration']) AND is_numeric($single['duration']) AND $single['duration'] != '' AND $single['duration'] != 'NaN') ? ', ' . duration_standard($single['duration']) : ''); ?>                                        
                                    </span>-->
                                    <?php if($logged_user['super_admin'] == 1){ ?>
                                    <span class="link link-midGray midGray normal f-12 mr-2" onclick="$('.exercise_old_title').text('<?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?>');$('#rename_id').val('<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>');$('#exercise_title').val('')" data-popup="rename-popup">Rename</span>
                                    <?php } ?>
                                </span>
                                <div class="flex aic jcsb ml-auto pr-2 add-part" style="max-width: 120px">
                                    
                                    <span class="btn btn-xs red-bg white f-1 ml-auto" data-popup="add-routine-exercise-popup" onclick="add_new_duration_popup($(this), <?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" style="<?php if($logged_user['super_admin'] == 1){ ?>margin-left: 0 !important;<?php } ?>right: auto !important;position: relative !important;">+</span>
                                </div>
                            </div>
                            <?php
                                }
                            }
                            ?>
                            <!-- <div class="f-14 bold text-center w100 loadMore py-5"></div> -->
                            <!-- <div class="f-14 bold text-center w100 loadMore py-5">LOADING...</div> -->
                            <div class="flex aic jcc p-2 load_more_click_container">
                                <a href="javascript:;" class="btn black-bg white ml-0 f-10 load_more_click">LOAD MORE</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
<?php } ?>

        <div class="container">
        <?php if(isset($current['id']) AND $current['id'] != ''){ ?>
            <?php if($logged_user['super_admin'] != 1){ ?>
            <div class="default-buttons centerbtns flex aic flex-column">
            <div class="border-div mb-2 px-3 py-2 flex aic">
                    <button type="submit" class="btn btn-tall red-bg white w160px" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SUBMIT CLASS</button>
                    <span class="f-12 black ml-3">The class will be submitted to the production team.</span>
                </div>
                <div class="border-div mb-2 px-3 py-2 flex aic">
                    <!-- <a href="admin/classes/pdf_export/<?php echo isset($current['id']) ? $current['id'] : 0; ?>/1" class="btn btn-border btn-tall white-bg black w160px" title="PDF">PDF ROUTINE</a>
                    <span class="f-12 black ml-3">Download routine as PDF.</span> -->
                    <a href="javascript:;" data-popup="save-routine-popup" class="btn btn-border btn-tall white-bg black w160px">SAVE ROUTINE</a>
                    <div class="btn-txt-twolines">
                    <span class="f-12 black ml-3">The routine will be stored in "routine builder" section.</span>
                    <span class="f-12 normalRed ml-3 mt-05">If you make any updates to an existing routine, the system will save that routine as a copy.</span>
                    </div>
                </div>
                <div class="border-div mb-2 px-3 py-2 email_routine_wrap">
                    <a href="javascript:;" class="btn btn-border btn-tall white-bg black w160px email_routine" onclick="email_routine($(this))" title="EMAIL">EMAIL ROUTINE</a>
                    <span class="f-12 black ml-3">The routine will be emailed to the production team and model.</span>
                </div>
                
            </div>
            <?php }else{ ?>
            <hr class="mt-0 mb-5">
            <div class="default-buttons centerbtns flex aic">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(<?php echo $logged_user['super_admin'] == 1 ? 0 : 1; ?>);$(this).addClass('btn--loading');event.preventDefault()">
                <?php echo $logged_user['super_admin'] == 1 ? ($current['status'] == 1 ? 'PUBLISH' : 'UPDATE') : 'SAVE'; ?></button>
                <?php if($current['status'] == 1){ ?>
                    <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                <?php }else{ ?>
                    <a href="/admin/classes" class="cancel-link ml-2" title="Cancel">Cancel</a>
                <?php } ?>
                <?php if($logged_user['super_admin'] == 1){ ?>
                <a href="javascript:;" class="link link-darkRed darkRed f-12 ml-auto delete_record semibold refresh_page" data-id="<?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : ''; ?>" data-table="classes" data-popup="delete-popup" title="Cancel">DELETE</a>
                <?php } ?>
            </div>
            <?php } ?>
        <?php }else{ ?>
            <div class="default-buttons centerbtns flex aic">
                <button type="submit" class="btn btn-tall red-bg white" onclick="save_status(0);$(this).addClass('btn--loading');event.preventDefault()">PUBLISH</button>
                <button type="submit" class="btn btn-tall black savedraft" onclick="save_status(1);$(this).addClass('btn--loading');event.preventDefault()">SAVE AS DRAFT</button>
                <a href="/admin/classes" class="cancel-link" title="Cancel">Cancel</a>
            </div>
        <?php } ?>
        </div>
    </div>
</main>

<!-- CLASS TEMPLATE -->
<div id="class-template" style="display: none">
    <div class="col-12 single-selected-class" data-id="0" data-rowid="0" data-type="0" data-sort="3">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
                <!-- just testing a git commit and merge -->
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()"><img src="images/removeicon.jpg"></span>
            </div>
        </div>
    </div>
</div>
<!-- HOW TO CLASS TEMPLATE -->
<div id="howto-template" style="display: none">
    <div class="col-12 single-selected-howto" data-id="0" data-rowid="0" data-type="0" data-sort="3" style="overflow: hidden">
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle">
        <div class="single-class">
            <div class="single-class-image">
                <img src="admin_assets_new/images/class1.jpg" alt="" class="img-fluid" />
            </div>
            <div class="single-class-rest">
                <div class="single-class-title" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <div class="single-class-desc">NO DESCRIPTION</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()"><img src="images/removeicon.jpg"></span>
            </div>
        </div>
    </div>
</div>
<!-- EXERCISES CLASS TEMPLATE -->
<div id="exercises-template" style="display: none">
    <div class="col-12 pl-0 pr-0 single-selected-exercises white-bg selected_routine_exercises" data-rowid="0" data-id="0" data-type="exercises" data-sort="0" data-springs="0" data-duration="0" style="overflow: hidden" >
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid mr-2 handle" style="margin-top: 13px;">
        <div class="single-class aic">
            <div class="single-class-rest">
                <div class="single-class-title link link-black black" data-popup="edit-routine-exercise-popup" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 100%;display: block;padding-right: 140px;">NO TITLE</div>
                <div class="single-class-desc show-time" style="padding-right: 140px;font-size: 12px !important;">NO DESCRIPTION</div>
                <a href="javascript:;" class="link link-midGray midGray f-10 clone_exercise" onclick="clone_exercise('ClassesExercises', 0)" title="">Clone</a>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()"><img src="images/removeicon.jpg"></span>
                <div class="colors with_number"></div>
            </div>
        </div>
    </div>
</div>
<!-- EXERCISES TRANSITION TEMPLATE -->
<div id="exercises-transition-template" style="display: none">
    <div class="col-12 pl-0 pr-0 single-selected-exercises white-bg" data-rowid="0" data-id="0" data-type="exercises" data-sort="0" data-duration="0" style="overflow: hidden" >
        <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid mr-2 handle" style="margin-top: 13px;">
        <div class="single-class aic">
            <div class="single-class-rest">
                <div class="single-class-title link link-black black textGreen" data-popup="edit-transition-popup" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;">NO TITLE</div>
                <span class="remove_class remove-textual" onclick="$(this).closest('.single-selected-class').remove();remove_class_from_selected()"><img src="images/removeicon.jpg"></span>
            </div>
        </div>
    </div>
</div>

<?php echo view('admin/templates/popups.php'); ?>

<script>
const date = "<?php echo date('Y-m-d'); ?>";
var class_id = <?php echo (isset($current['id']) AND $current['id'] != '') ? $current['id'] : 0; ?>;
var statuss = <?php echo (isset($current['status']) AND $current['status'] != '') ? $current['status'] : 0; ?>;
</script>
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="admin_assets_new/js/smooth-scrollbar.js"></script>
<script>
Scrollbar.initAll({
    continuousScrolling: false,
    renderByPixels: true,
    alwaysShowTracks: true
});
$('.advanced-dropdown ul').on('click', function(e){
    e.stopPropagation();
});
$('.select_teacher').on('click', function(e){
    e.stopPropagation();
    var fullname = $(this).data('fullname');
    var name = $(this).data('name');
    var id = $(this).data('id');
    console.log(id);
    $('.routines-list').html('').addClass('bg--loading-small');
    
    $('.select_teacher').removeClass('checked');
    $(this).addClass('checked');
    $('.selected_teacher').text(fullname).addClass('black');
    $('.teachers-list-wrap').removeClass('opened');
    $('.routines-dropdown-title').text(name + '\'s ROUTINES');

    $.ajax({
        type: 'POST',
        url: 'admin/routines/routines_list_ajax/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.list_count > 0){                    
                $('.routines-list').html(data.html);
            }else{                    
                $('.routines-list').html('<p class="normalRed mt-1 f-12">This teacher has no routines saved.</p>');
            }
            setTimeout(function(){
                $('.routines-list').removeClass('bg--loading-small');
                $('.selected_teacher_search').val('');
                $('.select_teacher').show();
            }, 300);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});
$('.select_routine_list_type').on('click', function(e){
    e.stopPropagation();
    var type = $(this).data('type');
    $('.select_routine_list_type').removeClass('checked');
    $(this).addClass('checked');
    $('.routines-list').html('').addClass('bg--loading-small');

    if(type == 'my'){
        $('.select_routine_teachers_list').hide();
        $('.routines-dropdown-title').text('MY ROUTINES');
        $.ajax({
            type: 'POST',
            url: 'admin/routines/routines_list_ajax',
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('.routines-list').html(data.html);
                setTimeout(function(){
                    $('.routines-list').removeClass('bg--loading-small');
                }, 300);
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }else if(type == 'all'){
        $('.routines-dropdown-title').text('ALL ROUTINES');
        $('.select_routine_teachers_list').hide();
        $.ajax({
            type: 'POST',
            url: 'admin/routines/routines_list_ajax/all',
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                $('.routines-list').html(data.html);
                setTimeout(function(){
                    $('.routines-list').removeClass('bg--loading-small');
                }, 300);
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }else if(type == 'teacher'){
        $('.select_routine_teachers_list').show();
        $('.routines-dropdown-title').text('ALL ROUTINES');
        $('.routines-list').html('<p class="normalRed mt-1 f-12">Please select a teacher from the list</p>');
        setTimeout(function(){
            $('.routines-list').removeClass('bg--loading-small');
        }, 300);
    }
});
$('.teachers-list-wrap').on('click', function(){
    $(this).toggleClass('opened');
});
$(document).ready(function(){
    $.ajax({
        type: 'POST',
        url: 'admin/routines/routines_list_ajax/<?php echo session('super_admin') != NULL ? 'all' : ''; ?>',
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            $('.routines-list').html(data.html);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});

function clone_exercise(model, id){
    if(model != '' && id > 0){
        var button = $(this.event.target);
        var parent = $('.single-selected-exercises[data-rowid="' + id + '"]');
       
        button.addClass('btn--loading-small-black');
        $.ajax({
            type: 'POST',
            url: '/admin/classes/ajax_duplicate/' + model + '/' + id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $(data.html).insertAfter(parent);
                    setTimeout(function(){
                        button.removeClass('btn--loading-small-black');                    
                    }, 200);
                }else{
                    app_msg('ERROR, Exercise NOT cloned');
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
function search_rountines(input){
    var chars = input.val().length;
    var val = input.val().toLowerCase();
    var parent = input.closest('ul');
    var children = parent.find('li');
    
    if(chars > 1){
        children.hide();
        children.each(function(index, single_routine){
            var text = $(single_routine).text().toLowerCase();
            if(text.indexOf(val) != -1){                
                $(single_routine).show();
            }
        });
    }else{
        children.show();
    }
};
$('.search-routines').on('click', function(e){
    e.stopPropagation();
});
//email_routine_wrap 
// function check_exercises_count(){
//     if($('.selected_routine_exercises').length > 1) {
        // $('.email_routine_wrap').show();
    // }else{
        // $('.email_routine_wrap').hide();
    // }
// }
$('.remove_tag').on('click', function(){
    var id = $(this).data('uncheck');
    var container = $(this).closest('.dropdown-container');

    container.find('input#' + id).attr('checked', false).prop('checked', false);
    $(this).closest('.dropdown-sel').remove();
    
    setTimeout(function(){
        var num = container.find('input:checked').length;
        console.log(num);
        if(num > 0){
            container.find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
        }else{
            container.find('.dropdown-label').removeClass('black').text('Select');
        }
    }, 50);
    if($('[data-uncheck*="machine_select"]').length == 0){
        reset_machine_dependencies();
    };
    show_hide_micro_friendly();
    generate_title();
});
$(document).on('click', '.remove_tag', function(){
    var id = $(this).data('uncheck');
    var container = $(this).closest('.dropdown-container');

    container.find('input#' + id).attr('checked', false).prop('checked', false);
    $(this).closest('.dropdown-sel').remove();
    
    setTimeout(function(){
        var num = container.find('input:checked').length;
        console.log(num);
        if(num > 0){
            container.find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
        }else{
            container.find('.dropdown-label').removeClass('black').text('Select');
        }
    }, 50);
    if($('[data-uncheck*="machine_select"]').length == 0){
        reset_machine_dependencies();
    }
    show_hide_micro_friendly();
    generate_title();
});
$('.routine_duration').on('click', function(){
    var dur = $(this).data('duration');
    $('.selected_duration').text('Routine duration: ' + dur);
});
function sum_duration(xx){
    var parent = xx.closest('div');
    var min = parent.find('.exercise_minutes').val();
    var sec = parent.find('.exercise_seconds').val();
    var total = parseInt(min * 60) + parseInt(sec);

    console.log('total: ' + total);
    parent.find('.new_duration_total').val(total);
};
function sum_edit_duration(xx){
    var parent = xx.closest('div');
    var min = parent.find('.edit_exercise_minutes').val();
    var sec = parent.find('.edit_exercise_seconds').val();
    var total = parseInt(min * 60) + parseInt(sec);

    console.log('total: ' + total);
    parent.find('.edit_duration_total').val(total);
};
</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/video-to-frames.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/file_upload.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/classes.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script src="admin_assets_new/js/class_exercises.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
function custom_dropdown(xx){
    // console.log('radi drop');
    // console.log(xx);
    // console.log(xx.data('val'));

    xx.closest('.dropdown').find('.audio_item').removeClass('selected');
    xx.closest('.dropdown').find('.orientation_val').val(xx.data('val'));
    xx.closest('.dropdown').find('.edit_orientation_val').val(xx.data('val'));
    xx.parent().addClass('selected');
    xx.closest('.dropdown').find('.dropdown-value').html(xx.html());
    xx.closest('.dropdown').removeClass('opened');
    setTimeout(function(){
        $('.search_audio').val('');
        $('.audio_item').show();
    }, 300);
}
// $('#bundle_1').on('change', function(e){
//     $('#full_body').prop('checked', false);
//     $('#upper_body').prop('checked', false);
//     $('#lower_body').prop('checked', false);
//     $('#bundle_2').prop('checked', false);
//     $('[data-group]').prop('checked', false);
//     if($(this).is(':checked')){
//         $('.body_parts_items[value="27"]').prop('checked', true);
//         $('.body_parts_items[value="7"]').prop('checked', true);
//         $('.body_parts_items[value="8"]').prop('checked', true);
//         $('.body_parts_items[value="9"]').prop('checked', true);
//         $('.body_parts_items[value="10"]').prop('checked', true);
//         $('.body_parts_items[value="11"]').prop('checked', true);
//         $('.body_parts_items[value="12"]').prop('checked', true);
//         $('.body_parts_items[value="13"]').prop('checked', true);
//         $('.body_parts_items[value="26"]').prop('checked', true);
//     }else{
//         $('[data-group]').prop('checked', false);
//     }
// });
// $('#bundle_2').on('change', function(e){
//     $('#full_body').prop('checked', false);
//     $('#upper_body').prop('checked', false);
//     $('#lower_body').prop('checked', false);
//     $('#bundle_1').prop('checked', false);
//     $('[data-group]').prop('checked', false);
//     if($(this).is(':checked')){        
//         $('.body_parts_items[value="1"]').prop('checked', true);
//         $('.body_parts_items[value="2"]').prop('checked', true);
//         $('.body_parts_items[value="3"]').prop('checked', true);
//         $('.body_parts_items[value="4"]').prop('checked', true);
//         $('.body_parts_items[value="5"]').prop('checked', true);
//         $('.body_parts_items[value="7"]').prop('checked', true);
//         $('.body_parts_items[value="11"]').prop('checked', true);
//         $('.body_parts_items[value="12"]').prop('checked', true);
//         $('.body_parts_items[value="14"]').prop('checked', true);
//         $('.body_parts_items[value="15"]').prop('checked', true);
//     }else{
//         $('[data-group]').prop('checked', false);
//     }
// });
$('#lower_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#upper_body').prop('checked', false);
    $('#bundle_1').prop('checked', false);
    $('#bundle_2').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="2"]').prop('checked', true);
    }else{
        $('[data-group="2"]').prop('checked', false);
    }
});
$('#upper_body').on('change', function(e){
    $('#full_body').prop('checked', false);
    $('#lower_body').prop('checked', false);
    $('[data-group]').prop('checked', false);
    if($(this).is(':checked')){
        $('[data-group="1"]').prop('checked', true);
    }else{
        $('[data-group="1"]').prop('checked', false);
    }
});
$('#full_body').on('change', function(e){
    if($(this).is(':checked')){
        $('#lower_body').prop('checked', false);
        $('#upper_body').prop('checked', false);
        $('[data-group]').prop('checked', true);
    }else{
        $('[data-group]').prop('checked', false);
    }
});

<?php if(isset($current['duration']) AND ($current['duration'] == '' OR $current['duration'] == 'NaN' OR is_numeric($current['duration'])) AND isset($current['id']) AND $current['id'] > 0){ ?>
    <?php if($logged_user['super_admin'] == 1){ ?>
    setTimeout(function(){
        $('#duration_val').val(($('video').get(0).duration).toFixed(0));
        if($('#title_container input').val() == ''){
            generate_title();
        }

    }, 4500);
    <?php } ?>
<?php } ?>
function save_status(xx){
    statuss = xx;
    console.log(xx);
    xx == 1 ? $('#draft').show() : $('#draft').hide();
    $('#status').val(xx);
    if(xx != 3){
        setTimeout(function(){
            $('#main_form').submit();
        }, 200);
    }else{
        $('[name="teacher_id"]').val($('[name="teacher"]:checked').val());
    }
}
function email_routine(button){
	console.log('email routine submit');
    button.addClass('btn--loading');

    if($('.selected_routine_exercises').length < 2){
        app_msg('Please add at least one exercise to the list', 'danger');
        setTimeout(function(){
            button.removeClass('btn--loading');
        }, 600);
    }else{
        $.ajax({
            type: "POST",
            url: 'admin/classes/email_routine/' + class_id,
            dataType: "json",
            success: function(data) {
                console.log(data);
                if (data.success) {
                    console.log('SUCCESS');
                    app_msg('Email sent');

                    button.removeClass('btn--loading');
                } else {
                    console.log('NO SUCCESS');
                    app_msg('Something went wrong. Please try again', 'danger');
                    button.removeClass('btn--loading');
                }
            },
            error: function(result) {
                console.log('ERROR WITH PHP');
                console.log(result);
                app_msg('Server problem', 'danger');
                button.removeClass('btn--loading');
            }
        });
    }
};
$('#reject_class').on('submit', function (e) {
	console.log('reject_class submit');
	e.preventDefault();
	var form = $(this);
	var url = form.attr('action');
	var button = form.find('button[type=submit]');
	var formData = form.serialize();
    button.addClass('btn--loading');
	$.ajax({
		type: "POST",
		url: url,
		data: formData,
		dataType: "json",
		success: function(data) {
			console.log(data);
			if (data.success) {
				console.log('SUCCESS');
                app_msg('Video rejected');
                close_all();
                <?php if(isset($pending) AND is_array($pending) AND count($pending) > 1){ ?>
                    window.location = '/admin/classes/pending';
                <?php }else{ ?>
                    window.location = '/admin/classes';
                <?php } ?>

                button.removeClass('btn--loading');
			} else {
				console.log('NO SUCCESS');
                app_msg('Something went wrong. Please try again', 'danger');
                button.removeClass('btn--loading');
			}
		},
		error: function(result) {
			console.log('ERROR WITH PHP');
			console.log(result);
            app_msg('Server problem', 'danger');
            button.removeClass('btn--loading');
		}
	});
});
function custom_select_dropdown(xx){
    console.log('radi drop');
    var audio = xx.data('audio');
    console.log(audio);

    $('.audio_item').removeClass('selected');
    xx.parent().addClass('selected');
    xx.closest('.dropdown').find('.dropdown-value').html(xx.text())
    xx.closest('.dropdown').find('.audio').val(audio);
    $('#class_audio').attr('src', audio);
    $('.audio_placeholder').show();
    setTimeout(function(){
        $('.search_audio').val('');
        $('.audio_item').show();
    }, 300);
}
$('.search_audio').on('click focus', function(e){
    e.stopPropagation();
});
$('.search_audio').on('keyup', function(){
    var chars = $(this).val().length;
    var val = $(this).val().toLowerCase();

    if(chars > 1){
        $(".audio_item").hide();
        $(".audio_item").each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(val) != -1){
                $(this).show();
            }
        });
    }else{
        $('.audio_item').show();
    }
});
if($('.selected_clases').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".selected_clases").sortable({
        helper: fixHelper,
        placeholder: "sortable-placeholder",
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var type = $(this).data("type");
                var pom = {
                    id: section_id,
                    type: type,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/classes/sort_classes_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}
function duration_standard(time){
    return new Date(time * 1000).toISOString().slice(11, 19);
}
function calculate_duration(){
    var total = 0;
    var duration_val = $('.selected_duration').text().match(/(\d+)/);
    var duration_set = parseInt(duration_val[0]*60);

    $('.selected_clases > div').each(function(i, el){
        var tt = $(el).data('duration');
        total = total + parseInt(tt);
    });
    $('.exercises_total_duration').text('Current Duration: ' + (total > 3600 ? duration_standard(total) : duration_standard(total).slice(3)));
    if(duration_set < total && duration_set > 0){
        app_msg('You exceeded total routine duration');
    }

}
$(document).ready(function(){
    calculate_duration();
    var intervalId = window.setInterval(function(){
        // check_exercises_count();
    }, 5000);
});
$('html, body').on('click', function(e){
    $('.custom-selectbox').closest('.custom-select').removeClass('active');
});
$('.custom-selectbox:not(.with-checkboxes) li').on('click', function(e){
    e.stopPropagation();
    var xx = $(this);
    var parent = $(this).closest('.custom-selectbox');
    var val = $(this).data("val");
    var text = $(this).text();

    parent.find('li').removeClass('checked');
    xx.addClass('checked');
    parent.find('input').val(val);
    parent.find('.select_val').html(text + ' <span class="select_count"></span>');

    $(this).closest('.custom-select').removeClass('open_top active');
    $('.custom-selectbox').find('ul').slideUp(200);
    $('.custom-selectbox').removeClass('opened');

    $(this).closest('.single_question').find('h5 span').remove();
    $(this).closest('.custom-select').removeClass('error');
});
$(document).on('click', '.custom-selectbox:not(.with-checkboxes) li', function(e){
    e.stopPropagation();
    var xx = $(this);
    var parent = $(this).closest('.custom-selectbox');
    var val = $(this).data("val");
    var text = $(this).text();

    parent.find('li').removeClass('checked');
    xx.addClass('checked');
    parent.find('input').val(val);
    parent.find('.select_val').html(text + ' <span class="select_count"></span>');

    $(this).closest('.custom-select').removeClass('open_top active');
    $('.custom-selectbox').find('ul').slideUp(200);
    $('.custom-selectbox').removeClass('opened');

    $(this).closest('.single_question').find('h5 span').remove();
    $(this).closest('.custom-select').removeClass('error');
});
function get_selected_exercises(){
    var sel_exercises_ids = [];
    $('.selected_clases .single-selected-exercises').each(function(i, el){
        sel_exercises_ids.push($(this).data('rowid'));
    });

    return JSON.stringify(sel_exercises_ids);
}
$('#save_new_routine').on('submit', function(e){
    e.preventDefault();    
    var form = $(this);
    var url = form.attr("action");
    var idss = get_selected_exercises();
    var title = $('#new_routine_title').val();

    if(title != ''){
        $.ajax({
            type: 'POST',
            url: url,
            data: {
                title: title,
                csc_ids: idss
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    close_all();
                    app_msg("New routine saved");
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }else{
        app_msg("Title field is required");
    }
});
// SEARCH
var exercises_start = 0;
function get_checked_body_parts(){
    var selected_exercise_body_parts = [];
    $('.exercises_body_parts.checked').each(function(i, el){
        selected_exercise_body_parts.push($(el).data('val'));
    });

    return selected_exercise_body_parts;
}
function get_checked_machines(){
    var selected_exercise_machines = [];
    $('.exercises_machines.checked').each(function(i, el){
        selected_exercise_machines.push($(el).data('val'));
    });

    return selected_exercise_machines;
}
const element = document.getElementById('search-ajax-classes');
let lastScrollTop = 0;
// if(element != null){
//     element.onscroll = (e)=>{
//         if (element.scrollTop < lastScrollTop){ return; }   // upscroll
//         lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
//         if (element.scrollTop + element.offsetHeight>= element.scrollHeight ){
//             console.log("End");
//             $('.loadMore').show();
//             load_more_exercises(1);
//         }
//     }
// }
$('.load_more_click').on('click', function(){
    $('.load_more_click').addClass('btn--loading');
    load_more_exercises(1);
});
$('.exercises_machines, .exercises_body_parts').on('click', function(){
    load_more_exercises(0);
});
var typingTimer;
var doneTypingInterval = 500;
var search_input = $('.search_exercises_filter');

search_input.on('keyup', function () {
  clearTimeout(typingTimer);
  typingTimer = setTimeout(doneTyping, doneTypingInterval);
});
search_input.on('keydown', function () {
  clearTimeout(typingTimer);
});
function doneTyping () {
    if(search_input.val().length > 2){
        load_more_exercises(0);
    }else if(search_input.val().length == 1 || search_input.val().length == 2){
        // do nothing
    }else{
        // clear_exercise_filters();
        load_more_exercises();
    }
}
function load_more_exercises(append = 1){
    var filter = {};
    filter['search_term'] = $('.search_exercises_filter').val();
    filter['machines'] = get_checked_machines();
    filter['body_parts'] = get_checked_body_parts();
    var filter_json = JSON.stringify(filter);
    if(append == 0){
        $('.search-ajax-classes-container .ajax-class').remove();
        exercises_start = 0;
    }
    $.ajax({
        type: 'POST',
        url: 'admin/classes/load_more_exercises',
        data: {
            start: append == 0 ? 0 : exercises_start,
            filter: filter_json
        },
        dataType: 'json',
        success: function (data) {
            console.log('Success');
            console.log(data);
            if(data.success && data.html != ''){
                // $(data.html).insertBefore('.loadMore');
                $(data.html).insertBefore('.load_more_click_container');
                $('.total-exercises').text(data.all_exercises_count);
                exercises_start = exercises_start + 100;
            }
            if(data.count < 100 || data.no_more){
                // $('.loadMore').hide();
                $('.load_more_click').hide();
            }else{
                // $('.loadMore').show();
                $('.load_more_click').show();
            }
            $('.load_more_click').removeClass('btn--loading');
        },
        error: function (request, status, error) {
            console.log('Error');
        }
    });
}
function clear_exercise_filters(){
    $('.search_exercises_filter').val('');
    $('.exercises_machines').removeClass('checked');
    $('.select_count_machines').text('');
    $('.exercises_body_parts').removeClass('checked');
    $('.select_count_body_parts').text('');
    load_more_exercises(0);
}
function edit_routine_exercise(id = 0, xx){
    if(id != 0){
        $.ajax({
            type: 'POST',
            url: 'admin/classes/edit_class_exercise/' + id,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success){
                    $('.edit_spring_container input').prop('checked', false).attr('checked', false);
                    $('.edit_spring_container').next().val('').attr('disabled', true);
                    // TIME
                    var exercise_minutes = (Math.floor(data.csc.duration/60) < 10 ? '0' + Math.floor(data.csc.duration/60) : Math.floor(data.csc.duration/60));
                    var exercise_seconds = (data.csc.duration%60 < 10 ? '0' + data.csc.duration%60 : data.csc.duration%60);
                    $('.edit_exercise_minutes').val(exercise_minutes);
                    $('.edit_exercise_seconds').val(exercise_seconds);
                    $('.edit_duration_total').val(data.csc.duration);
                    // ORIENTATION
                    $('.edit_orientation_val').val(data.csc.orientation);
                    var orientation = (data.csc.orientation == '' ? 'N' : data.csc.orientation);
                    $('.edit_orientation_dropdown').find('a[data-val="' + orientation + '"]').trigger('click');
                    // SPRING LOAD
                    var springs = JSON.parse(data.csc.springs);
                    var springs_count = (data.csc.springs_count != '') ? JSON.parse(data.csc.springs_count) : [];

                    if($.isArray(springs)){
                        springs.forEach(function(spring, index) {
                            var input = $('.edit_spring_container[data-id=' + spring + ']').find('input').val();
                            var input_val = $('.edit_spring_container[data-id=' + spring + ']').find('input').val();
                            console.log(input_val, spring);
                            if(input_val == spring){
                                $('.edit_spring_container[data-id=' + spring + ']').find('input').prop('checked', true).attr('checked', true);
                                if($.isArray(springs_count)){
                                    $('.edit_spring_container[data-id=' + spring + ']').next().attr('disabled', false).val(springs_count[index]);
                                }
                            }
                        });
                    };
                    // ID
                    $('#edit_csc_id').val(data.csc.id);
                    // ROUTINE ID
                    $('#edit_exercise_id').val(data.csc.class_selected_exercises);
                    // TITLE
                    $('#edit_csc_title').val(data.exercise.title);
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    }
}
function edit_transition(id = 0, xx){
    if(id != 0){
        var duration = $('.single-selected-exercises[data-rowid="' + id + '"]').data('duration');
        $('#edit_transition_id').val(id);
        $('#edit_routine_duration_val').val(duration);
        $('.edit-transition-popup .select_val').html(duration + ' seconds<span class="select_count"></span>');
    }
}
$('#difficulty_container input').on('click', function(){
    if($('#difficulty_container input:checked').length > 0){ generate_title(); }
});
// $('#full_body').on('click', function(){
//     $('#full_body').is(':checked') ? generate_title() : '';
// });
// $('#upper_body').on('click', function(){
//     $('#upper_body').is(':checked') ? generate_title() : '';
// });
// $('#lower_body').on('click', function(){
//     $('#lower_body').is(':checked') ? generate_title() : '';
// });
function generate_title(){
    if($('[name="difficulty"]:checked').length > 0){
        var diff = $('[name="difficulty"]:checked').data('name') + '';
    }else{
        var diff = "";
    };
    var body_part_selected = "";
    var durr = "";
    ($('#duration_val').val() != '' && $('#duration_val').val() != 'NaN' && $.isNumeric($('#duration_val').val())) ? durr = (Math.floor($('#duration_val').val() / 60)) + ' min, ' : '';
    $('#full_body_new').is(':checked') ? body_part_selected = ' Full Body' : '';
    $('#upper_body_new').is(':checked') ? body_part_selected = ' Upper Body' : '';
    $('#lower_body_new').is(':checked') ? body_part_selected = ' Lower Body' : '';

    if(class_id == 0 || statuss == 1){
        $('#title_container input').val(durr + diff + body_part_selected);
    }
}
</script>

<script>//Checkboxes inside dropdwon
const $dropdown = $('.dropdown-container'); // Cache all;

function UI_dropdown() {
    const $this = $(this);
    const $btn = $('.dropdown-button', this);
    const $list = $('.dropdown-list', this);
    const $li = $('li', this);
    const $search = $('.dropdown-search', this);
    const $ckb = $(':checkbox, :radio', this);
    const $qty = $('.dropdown-quantity', this);

    $btn.on('click', function() {
        $dropdown.not($this).removeClass('is-active'); // Close other
        $this.toggleClass('is-active'); // Toggle this
    });

    $search.on('input', function() {
        const val = $(this).val().trim();
        const rgx = new RegExp(val, 'i');
        $li.each(function() {
        const name = $(this).text().trim();
        $(this).toggleClass('is-hidden', !rgx.test(name));
        });
    });

    $ckb.on('change', function(elem) {
        const names = $ckb.get().filter(el => el.checked).map(el => {
            return `<span class="dropdown-sel">${el.dataset.name.trim()}<span class="remove_tag" data-uncheck="${el.id.trim()}">×</span></span>`;
        });
        var num = $(elem.target).closest('.dropdown-container').find(':checked').length;
        setTimeout(function(){
            if(num > 0){
                $(elem.target).closest('.dropdown-container').find('.input-container').removeClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').addClass('black').text('Selected (' + num + ')');
            }else{
                $(elem.target).closest('.dropdown-container').find('.input-container').addClass('has-error');
                $(elem.target).closest('.dropdown-container').find('.dropdown-label').removeClass('black').text('Select');
            }
        }, 50);
        $qty.html(names.join(''));
        select_machine();
    });
}

$dropdown.each(UI_dropdown); // Apply logic to all dropdowns

// Dropdown - Close opened 
$(document).on('click', function(ev) {
    const $targ = $(ev.target).closest('.dropdown-container');
    if (!$targ.length) $dropdown.filter('.is-active').removeClass('is-active');
});
</script> 
  
<script>//**************SHOW-HIDE***************
$('.hide_bottom').click(function() { //CUSTOM THUMBNAIL SECTION
    $(this).parent().next().slideToggle();
    $(this).text() == 'SHOW' ? $(this).text('HIDE') : $(this).text('SHOW');
});
</script>
</body>
</html>