<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\StripeModel;
use App\Models\SubscribersModel;
use App\Models\BlacklistModel;

class StripeWebhook extends Controller
{
    protected $stripe_model;
    protected $subscribers_model;
    protected $blacklist_model;
    protected $endpoint_secret;

    public function __construct()
    {
        $this->stripe_model = new StripeModel();
        $this->subscribers_model = new SubscribersModel();
        $this->blacklist_model = new BlacklistModel();
        $this->endpoint_secret = $_ENV['stripe_webhook_secret'] ?? 'whsec_fKyqHVR7vro0cEchXParPvmgC1pNZBU6';
    }

    public function handle()
    {
        \Stripe\Stripe::setApiKey($_ENV['api_key_live']);
        
        // Retrieve the request's body and parse it as JSON
        $input = @file_get_contents('php://input');
        $event = null;

        // Log the full webhook payload
        log_message('info', 'Stripe Webhook Payload: ' . $input);

        // Check if the signature header is present
        if (!isset($_SERVER['HTTP_STRIPE_SIGNATURE'])) {
            log_message('error', 'Stripe signature header missing');
            return $this->response->setStatusCode(400, 'Signature header missing');
        }

        $signature = $_SERVER['HTTP_STRIPE_SIGNATURE'];

        try {
            // Verify the signature and construct the event
            $event = \Stripe\Webhook::constructEvent(
                $input, $signature, $this->endpoint_secret
            );
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            log_message('error', 'Invalid payload: ' . $e->getMessage());
            return $this->response->setStatusCode(400, 'Invalid payload');
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            log_message('error', 'Invalid signature: ' . $e->getMessage());
            return $this->response->setStatusCode(400, 'Invalid signature');
        }

        // Handle the event
        switch ($event->type) {
            case 'customer.subscription.created':
                $this->handleSubscriptionCreated($event->data->object);
                break;
                
            case 'customer.subscription.updated':
                $this->handleSubscriptionUpdated($event->data->object);
                break;
                
            case 'customer.subscription.deleted':
                $this->handleSubscriptionDeleted($event->data->object);
                break;
                
            case 'invoice.payment_succeeded':
                $this->handlePaymentSucceeded($event->data->object);
                break;
                
            case 'invoice.payment_failed':
                $this->handlePaymentFailed($event->data->object);
                break;
                
            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event->data->object);
                break;
                
            default:
                log_message('info', 'Unhandled webhook event type: ' . $event->type);
        }

        return $this->response->setStatusCode(200, 'OK');
    }

    private function handleSubscriptionCreated($subscription)
    {
        log_message('info', 'Subscription created: ' . $subscription->id . ' for customer: ' . $subscription->customer);
        
        // Check for duplicate subscriptions
        $active_subs = $this->stripe_model->get_customer_active_subscriptions($subscription->customer);
        
        if ($active_subs['success'] && $active_subs['count'] > 1) {
            log_message('warning', 'Duplicate subscription detected for customer: ' . $subscription->customer . '. Total active: ' . $active_subs['count']);
            
            // Find the oldest subscription (keep the newest one)
            $oldest_subscription = null;
            $oldest_created = time();
            
            foreach ($active_subs['subscriptions'] as $sub) {
                if ($sub->created < $oldest_created && $sub->id !== $subscription->id) {
                    $oldest_created = $sub->created;
                    $oldest_subscription = $sub;
                }
            }
            
            // Cancel the oldest subscription
            if ($oldest_subscription) {
                try {
                    $cancel_result = $this->stripe_model->cancel_subscription($oldest_subscription->id);
                    if ($cancel_result['success']) {
                        log_message('info', 'Auto-cancelled duplicate subscription: ' . $oldest_subscription->id . ' for customer: ' . $subscription->customer);
                    }
                } catch (Exception $e) {
                    log_message('error', 'Failed to cancel duplicate subscription: ' . $oldest_subscription->id . '. Error: ' . $e->getMessage());
                }
            }
        }
        
        // Update subscriber record
        $this->updateSubscriberRecord($subscription->customer, $subscription->id, 'active');
    }

    private function handleSubscriptionUpdated($subscription)
    {
        log_message('info', 'Subscription updated: ' . $subscription->id . ' for customer: ' . $subscription->customer . '. Status: ' . $subscription->status);
        
        // Update subscriber record
        $this->updateSubscriberRecord($subscription->customer, $subscription->id, $subscription->status);
    }

    private function handleSubscriptionDeleted($subscription)
    {
        log_message('info', 'Subscription deleted: ' . $subscription->id . ' for customer: ' . $subscription->customer);
        
        // Update subscriber record
        $subscriber = $this->subscribers_model->where('stripe_customer', $subscription->customer)->first();
        if ($subscriber && $subscriber['stripe_subscription'] === $subscription->id) {
            // Check if customer has other active subscriptions
            $active_subs = $this->stripe_model->get_customer_active_subscriptions($subscription->customer);
            
            if ($active_subs['success'] && $active_subs['count'] > 0) {
                // Update to the most recent active subscription
                $newest_sub = $active_subs['subscriptions'][0];
                $this->subscribers_model->save([
                    'id' => $subscriber['id'],
                    'stripe_subscription' => $newest_sub->id,
                    'subscription_status' => $newest_sub->status
                ]);
                log_message('info', 'Updated subscriber to use active subscription: ' . $newest_sub->id);
            } else {
                // No active subscriptions, clear the subscription fields
                $this->subscribers_model->save([
                    'id' => $subscriber['id'],
                    'stripe_subscription' => null,
                    'subscription_status' => 'cancelled'
                ]);
                log_message('info', 'Cleared subscription for subscriber: ' . $subscriber['id']);
            }
        }
    }

    private function handlePaymentSucceeded($invoice)
    {
        if ($invoice->subscription) {
            log_message('info', 'Payment succeeded for subscription: ' . $invoice->subscription . ' customer: ' . $invoice->customer);
            
            // Update subscriber status to active
            $this->updateSubscriberRecord($invoice->customer, $invoice->subscription, 'active');
        }
    }

    private function handlePaymentFailed($invoice)
    {
        if ($invoice->subscription) {
            log_message('error', 'Payment failed for subscription: ' . $invoice->subscription . ' customer: ' . $invoice->customer);
            
            // Update subscriber status
            $this->updateSubscriberRecord($invoice->customer, $invoice->subscription, 'past_due');
        }
    }

    private function handlePaymentIntentFailed($paymentIntent)
    {
        $customerId = $paymentIntent->customer;
        
        if ($customerId) {
            try {
                $customer = \Stripe\Customer::retrieve($customerId);
                $outcomeType = isset($paymentIntent->charges->data[0]->outcome->type) ? $paymentIntent->charges->data[0]->outcome->type : 'N/A';
                
                log_message('error', 'PaymentIntent failed: ' . $outcomeType . ' for customer: ' . $customer->email);
                
                // Add to blacklist if multiple failures
                $this->blacklist_model->save([
                    'email' => $customer->email,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'webhook',
                    'stripe_status' => 'payment_failed'
                ]);
                
            } catch (Exception $e) {
                log_message('error', 'Error handling payment intent failure: ' . $e->getMessage());
            }
        }
    }

    private function updateSubscriberRecord($customer_id, $subscription_id, $status)
    {
        $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();
        
        if ($subscriber) {
            $update_data = [
                'id' => $subscriber['id'],
                'stripe_subscription' => $subscription_id,
                'subscription_status' => $status
            ];
            
            // Update subscription type based on the subscription details
            try {
                $subscription_details = $this->stripe_model->retrieve_subscription($subscription_id);
                if ($subscription_details['success']) {
                    $interval = $subscription_details['subscription']['plan']['interval'] ?? 'unknown';
                    $plans = [
                        'week' => 'Weekly Plan',
                        'month' => 'Monthly Plan', 
                        'year' => 'Annual Plan'
                    ];
                    
                    if (isset($plans[$interval])) {
                        $update_data['subscription_type'] = $plans[$interval];
                    }
                }
            } catch (Exception $e) {
                log_message('error', 'Error retrieving subscription details: ' . $e->getMessage());
            }
            
            $this->subscribers_model->save($update_data);
            log_message('info', 'Updated subscriber record for customer: ' . $customer_id);
        } else {
            log_message('warning', 'No subscriber found for customer: ' . $customer_id);
        }
    }
}
