<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>

</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content pb-5 mb-100">
        <div class="container">
            <div class="flex aic jcsb page-title">
                <h1 class="h3"><?php echo isset($current['id']) ? '' : 'New' ?> Coupon <?php echo isset($current['id']) ? 'Details' : '' ?></h1>
                <a href="admin/codes" class="btn btn-border white-bg black ml-auto" title="Cancel"><?php echo isset($current['id']) ? 'Back' : 'Back' ?></a>
            </div>
            <hr class="mt-0 mb-45">
        </div>
        <form action="admin/codes/save" method="post" class="default_submit container border-bottom coupons_form" id="main_form">
            <div class="row">
                <div class="col-12">
                <h5 class="mb-4 f-14 semibold">CODE INFO</h5>
                <h5 class="mb-1 f-11">CODE NAME *</h5>
                    <div class="input-container" id="title_container">
                        <input type="text" name="code" <?php echo (isset($current['code']) AND $current['order_id'] != '') ? 'readonly' : ''; ?>class="line-input" placeholder="Enter" value="<?php echo isset($current['code']) ? $current['code'] : '' ?>" />
                    </div>
                </div>
                <!--<div class="col-4">
                    <h3 class="mb-3">&nbsp;</h3>
                    <div class="input-container" id="title_container">
                        <a href="javascript:;" class="btn red-bg white" onclick="make_code(12)">Generate random code</a>
                    </div>
                </div>-->
            </div>
            <!-- <hr class="my-5"> -->

            <div class="row">
                <div class="col-12">
                <h5 class="mb-1 f-11">DISCOUNT</h5>
                    <div class="input-container">
                        <select class="line-input" name="discount">
                            <option value="">Select</option>
                            <option value="10" <?php echo (isset($current['discount']) AND $current['discount'] == 10) ? 'SELECTED' : ''; ?>>10%</option>
                            <option value="15" <?php echo (isset($current['discount']) AND $current['discount'] == 15) ? 'SELECTED' : ''; ?>>15%</option>
                            <option value="20" <?php echo (isset($current['discount']) AND $current['discount'] == 20) ? 'SELECTED' : ''; ?>>20%</option>
                            <option value="25" <?php echo (isset($current['discount']) AND $current['discount'] == 25) ? 'SELECTED' : ''; ?>>25%</option>
                            <option value="30" <?php echo (isset($current['discount']) AND $current['discount'] == 30) ? 'SELECTED' : ''; ?>>30%</option>
                            <option value="35" <?php echo (isset($current['discount']) AND $current['discount'] == 35) ? 'SELECTED' : ''; ?>>35%</option>
                            <option value="40" <?php echo (isset($current['discount']) AND $current['discount'] == 40) ? 'SELECTED' : ''; ?>>40%</option>
                            <option value="45" <?php echo (isset($current['discount']) AND $current['discount'] == 45) ? 'SELECTED' : ''; ?>>45%</option>
                            <option value="50" <?php echo (isset($current['discount']) AND $current['discount'] == 50) ? 'SELECTED' : ''; ?>>50%</option>
                            <option value="55" <?php echo (isset($current['discount']) AND $current['discount'] == 55) ? 'SELECTED' : ''; ?>>55%</option>
                            <option value="60" <?php echo (isset($current['discount']) AND $current['discount'] == 60) ? 'SELECTED' : ''; ?>>60%</option>
                            <option value="65" <?php echo (isset($current['discount']) AND $current['discount'] == 65) ? 'SELECTED' : ''; ?>>65%</option>
                            <option value="70" <?php echo (isset($current['discount']) AND $current['discount'] == 70) ? 'SELECTED' : ''; ?>>70%</option>
                            <option value="75" <?php echo (isset($current['discount']) AND $current['discount'] == 75) ? 'SELECTED' : ''; ?>>75%</option>
                            <option value="80" <?php echo (isset($current['discount']) AND $current['discount'] == 80) ? 'SELECTED' : ''; ?>>80%</option>
                            <option value="85" <?php echo (isset($current['discount']) AND $current['discount'] == 85) ? 'SELECTED' : ''; ?>>85%</option>
                            <option value="90" <?php echo (isset($current['discount']) AND $current['discount'] == 90) ? 'SELECTED' : ''; ?>>90%</option>
                            <option value="95" <?php echo (isset($current['discount']) AND $current['discount'] == 95) ? 'SELECTED' : ''; ?>>95%</option>
                            <option value="100" <?php echo (isset($current['discount']) AND $current['discount'] == 100) ? 'SELECTED' : ''; ?>>100%</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                <h5 class="mb-1 f-11">MAX REDEMPTIONS *</h5>
                    <div class="row">
                        <div class="col-12">
                            <div class="input-container" id="redemption_container">
                                <input type="text" class="line-input" placeholder="Enter" name="redemption" value="<?php echo (isset($current['redemption']) AND $current['redemption'] != '') ? $current['redemption'] : ''; ?>" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-25 mb-45">
            <div class="row mb-3 pb-05">
                <div class="col-6">
                <h5 class="mb-4 f-14 semibold">APPLIES TO:</h5>
                    <div class="checkbox mb-15" id="products_container">
                        <input type="checkbox" class="" name="products[]" id="products1" <?php echo (isset($current['products']) AND in_array($_ENV['product_week'], $current['products'])) ? 'checked' : '' ?> value="<?php echo $_ENV['product_week']; ?>">
                        <label for="products1" class="f-14">Weekly</label>
                    </div>
                    <div class="checkbox mb-15" id="products_container">
                        <input type="checkbox" class="" name="products[]" id="products2" <?php echo (isset($current['products']) AND in_array($_ENV['product_month'], $current['products'])) ? 'checked' : '' ?> value="<?php echo $_ENV['product_month']; ?>">
                        <label for="products2" class="f-14">Monthly</label>
                    </div>
                    <div class="checkbox mb-15" id="products_container">
                        <input type="checkbox" class="" name="products[]" id="products3" <?php echo (isset($current['products']) AND in_array($_ENV['product_year'], $current['products'])) ? 'checked' : '' ?> value="<?php echo $_ENV['product_year']; ?>">
                        <label for="products3" class="f-14">Yearly</label>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-5">
            <div class="row">
                <div class="col-12 for_submit flex aic">
                    <!-- <input type="hidden" name="id" id="id" value="<?php // echo isset($current['id']) ? $current['id'] : 0 ?>"> -->
                    <button type="submit" class="btn btn-tall red-bg white mr-2">Publish</button>
                    <a href="/admin/codes" class="cancel-link" title="Cancel">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
function make_code(length) {
    var result           = '';
    var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for ( var i = 0; i < length; i++ ) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
   $('[name=code]').val(result);
}
</script>
</body>
</html>