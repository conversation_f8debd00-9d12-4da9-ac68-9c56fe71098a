<?php namespace App\Controllers\Admin;

use App\Models\TeachersModel;
use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Login extends Admincontroller
{
	use ResponseTrait;

	public function __construct() {
		$this->model = new TeachersModel();
		$settingsModel = model('SettingsModel');
		$ClassesModel = model('ClassesModel');
		$ConversationsModel = model('ConversationsModel');
		$CommentsModel = model('CommentsModel');
		$this->settings = $settingsModel->where(['id' => 1])->first();
		$this->pending_classes = $ClassesModel->all_pending();
		$this->new_messages = $ConversationsModel->count_conversations_admin();
		$this->new_comments = count($CommentsModel->nonapproved_comments_admin());
		$this->admin_table = model('AdminTableSettingsModel');
	}

    public function index()
    {
		$data['settings'] = $this->settings;
		$data['new_messages'] = $this->new_messages;
		$data['new_comments'] = $this->new_comments;

        $uri = service('uri');
        $menu = $uri->getSegment(2);

        if($menu != 'login'){
            return redirect()->to('admin/login');
        }

		echo view('admin/login/login_view', $data);
    }

    public function validate_login()
    {
		$request = service('request');

		$data = $request->getPost();
		$response = $this->model->login_user($data);
		if($response['success']){
			session()->set('admin', $response['user_id']);
			session()->set('super_admin', $response['super_admin']);
			session()->set('certified', $response['user_info']['certified']);
			session()->set('admin_info', $response['user_info']);
            $admin = $this->admin_table->where(['admin_id' => $response['user_id']])->first();
            if(!isset($admin)){
                $data_admin_settings = [
                    'admin_id' => $response['user_id']
                ];
                $response['admin_settings_saved'] = $this->admin_table->save($data_admin_settings);
                $admin = $this->admin_table->where(['admin_id' => $response['user_id']])->first();
            }
            session()->set('classes_sort', $admin['classes_sort']);
			session()->set('classes_per_page', $admin['classes_per_page']);
			session()->set('classes_search', $admin['classes_search']);
            session()->set('howto_sort', $admin['howto_sort']);
			session()->set('howto_per_page', $admin['howto_per_page']);
			session()->set('howto_search', $admin['howto_search']);
            session()->set('exercises_sort', $admin['exercises_sort']);
			session()->set('exercises_per_page', $admin['exercises_per_page']);
			session()->set('exercises_search', $admin['exercises_search']);
            session()->set('courses_sort', $admin['courses_sort']);
			session()->set('courses_per_page', $admin['courses_per_page']);
			session()->set('courses_search', $admin['courses_search']);
		}
		$response['method'] = $request->getMethod();
		$response['request'] = $request;

		//echo json_encode($response);
		return $this->respond($response);
    }

    public function logout()
    {
		session()->remove('admin');
		session()->remove('admin_info');
		session()->remove('super_admin');
        session()->remove('classes_sort');
        session()->remove('classes_per_page');
        session()->remove('howto_sort');
        session()->remove('howto_per_page');
        session()->remove('exercises_sort');
        session()->remove('exercises_per_page');
        session()->remove('courses_sort');
        session()->remove('courses_per_page');

        $response['success'] = TRUE;
		return redirect('admin/login');
    }

    public function session()
    {
        echo "<pre>";
		// if(session()->has('admin')){
        //     echo 'ADMIN SESSION ID: ';
        //     print_r(session()->get('admin'));
        //     echo "<br>";

        //     if(session()->has('admin_info')){
        //         echo 'ADMIN INFO: <br>';
        //         print_r(session()->get('admin_info'));
        //         echo "<br>";
        //     }
		// }
        echo 'GLOBAL INFO: <br>';
		print_r(session()->get());
        echo "</pre>";
    }
}