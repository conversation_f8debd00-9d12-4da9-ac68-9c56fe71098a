<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class SubscribersExercises extends Model
{
    protected $table = 'subscribers_exercises';
	protected $allowedFields = ['exercise_id', 'subscriber_id', 'seller_id', 'price', 'seller_earning', 'date', 'stripe_charge', 'stripe_transfer', 'purchase_type'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = false;
    protected $useTimestamps = false;

    protected $validationRules    = [
        'exercise_id'       => 'required',
        'subscriber_id'  => 'required',
        'seller_id'      => 'required',
        'price'          => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	protected $beforeUpdate = ['prepare_data'];

	protected function prepare_data(array $data)
	{
		return $data;
	}

}