<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<style>
.line-input.border.px-2 + .input-label {
	left: 20px;
	top: -7px;
	font-size: 12px;
}
.line-input.border.px-2:focus, .line-input.border.px-2:active {
	color: #000 !important;
}
</style>

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column">
                            <p class="line-height-small f-24 white semibold text-uppercase pb-1 mb-05 mb-mob-0">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
    <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w100 line-height-small semibold">
                        SUPPORT
                        </h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>

                        </div>
                    </div>
                </div>
                <div class="col-12 pl-0">
                    <div class="pt-3 pb-4 border px-4 px-mob-2 pt-mob-2 pb-mob-3 radius-10 radius-mob-6">
                        <div class="row mt-05">
                            <div class="col-12 px-mob-2">
                                <p class="f-14 f-12-mob lh-25 line-height-normal">Need assistance? Let us know how we can help.</p>
                                <p class="f-14 f-12-mob midGray line-height-normal">Or find answers in our <a class="midGray text-underline" href="/help-center">Help Center</a>.</p>
                            </div>
                        </div>
                        <form action="account/support_form" id="support_form" method="post" class="row mt-3 mt-mob-2">
                            <div class="col-12 mb-0">
                                <div class="input-container mb-15">
                                    <input type="text" name="subject" class="line-input border" id="subject" required placeholder="Subject">
                                </div>
                            </div>
                            <div class="col-12 mb-0">
                                <div class="input-container">
                                    <textarea class="line-input " name="message" id="message" required placeholder="Message"></textarea>
                                </div>
                            </div>
                            <div class="col-12 mt-mob-1">
                                <input type="hidden" name="email" value="<?php echo $logged_user['email']; ?>">
                                <input type="hidden" name="fullname" value="<?php echo $logged_user['firstname'] . ' ' . $logged_user['lastname']; ?>">
                                <div class="input-container mb-0 mt-1 mt-mob-0">
                                    <button type="submit" class="btn black-bg white w100-mob">Send</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
              </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
</body>
</html>