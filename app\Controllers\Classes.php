<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use App\Models\ShopifyModel;
use App\Models\WooCommerceModel;

class Classes extends Frontcontroller
{
    use ResponseTrait;

    protected $cache;
    protected $model;
    protected $user;
    protected $settings;

    public function __construct() 
    {
        parent::__construct();
        $this->model = model('ClassesModel');
        $this->cache = \Config\Services::cache();
    }

    public function index()
    {
        $collections_model = model('CollectionsModel');
        $data['logged_user'] = $this->user;
        $data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $teachers_model = model('TeachersModel');
        $db = \Config\Database::connect();

        // $cacheKey = 'classes_index_data';
        // $cacheData = $this->cache->get($cacheKey);
        // $cacheStatus = 'HIT';
        // if ($cacheData === NULL) {
            $data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                                FROM machines
                                                LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                                HAVING countMachine > 0
                                                ORDER BY sort ASC
                                          ')->getResultArray();
            $data['all_accessories'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countAccessories
                                                FROM accessories
                                                LEFT OUTER JOIN (SELECT class_accessories, count(*) AS cnt FROM classes_accessories GROUP BY class_accessories) x ON x.class_accessories = accessories.id
                                                HAVING countAccessories > 0
                                          ')->getResultArray();
            foreach($data['machines'] as $key => $value) {
                $data['accessories'][$key]['machine_name'] = $value['short_name'];
                $data['accessories'][$key]['id'] = $value['id'];
                $data['accessories'][$key]['count_accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
                $data['accessories'][$key]['accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
            }

            $data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM difficulty
                                                LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                                HAVING countClasses > 0
                                            ')->getResultArray();
            $data['languages'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM languages
                                                LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                                HAVING countClasses > 0
                                            ')->getResultArray();
            $data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
            $data['duration_less10_20'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1200')->getResultArray();
            $data['duration_less20_30'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1200 AND duration < 1800')->getResultArray();
            $data['duration_less30_40'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1800 AND duration < 2400')->getResultArray();
            $data['duration_less40_50'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 2400 AND duration < 3000')->getResultArray();
            $data['duration_less50_60'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 3000 AND duration < 3600')->getResultArray();
            $data['duration_more60'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 3600')->getResultArray();
            $data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                                FROM body_parts
                                                LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                                HAVING countBodyParts > 0
                                            ')->getResultArray();
            $data['tempo'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countTempo
                                                FROM tempo
                                                LEFT OUTER JOIN (SELECT class_tempo, count(*) AS cnt FROM classes_tempo GROUP BY class_tempo) x ON x.class_tempo = tempo.id
                                                HAVING countTempo > 0
                                            ')->getResultArray();
            $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM teachers
                                                LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                                WHERE status IN (0,1)
                                                AND deleted_at IS NULL
                                                HAVING countClasses > 0
                                                ORDER BY firstname ASC
                                              ')->getResultArray();

            $data['featured_collections'] = $collections_model->all_collections(0, 2);
            $data['all_classes'] = $this->model->all_lagree_classes(0, 9);

        //     // Cache the data for 1 hour
        //     $this->cache->save($cacheKey, $data, 3600);

        //     $cacheStatus = 'MISS';
        // } else {
        //     $data = $cacheData;
        // }

        // Clear any existing Cache-Control headers to avoid conflicts
        // $this->response->removeHeader('Cache-Control');

        // Adjust Cache-Control headers
        // $this->response->setHeader('Cache-Control', 'max-age=3600, public');

        $data['current']['image'] = base_url() . 'images/classes1.jpg';
        $data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Classes | Lagree On Demand';
        $data['current']['seo_description'] = "Change your body from the comfort of your home or on the go with Lagree Micro, Mini, and Megaformer classes available online 24/7! Get Lagree On Demand today!";

        // Ensure headers are set just before returning the view
        // return $this->response->setHeader('Cache-Control', 'max-age=3600, public')
        //     ->setHeader('X-Cache-Status', $cacheStatus)
        //     ->setBody(view('front/classes/index_view', $data));
        return view('front/classes/index_view', $data);
    }

    public function slug($slug = '')
    {
        $data['logged_user'] = $this->user;
        $data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        $data['slug'] = $slug;

        if(empty($data['logged_user'])){
            return redirect()->to(base_url() . '/classes');
        }
        if($slug == 'micro' OR $slug == 'micropro' OR $slug == 'mini' OR $slug == 'minipro' OR $slug == 'mega' OR $slug == 'megapro' OR $slug == 'evo' OR $slug == 'evo2' OR $slug == 'ramp' OR $slug == 'sliders' OR $slug == 'abwheel'){
            $teachers_model = model('TeachersModel');
            $db = \Config\Database::connect();
            $machines = [
                'micro' => 1,
                'micropro' => 14,
                'mini' => 3,
                'minipro' => 4,
                'mega' => 2,
                'megapro' => 13,
                'evo' => 10,
                'evo2' => 11,
                'ramp' => 7,
                'sliders' => 9,
                'abwheel' => 5
            ];
            $machines_title = [
                'micro' => 'Micro Classes',
                'micropro' => 'Micro Pro Classes',
                'mini' => 'Mini Classes',
                'minipro' => 'Mini Pro Classes',
                'mega' => 'Mega Classes',
                'megapro' => 'Mega Pro Classes',
                'evo' => 'Evo Classes',
                'evo2' => 'Evo 2 Classes',
                'ramp' => 'Ramp Classes',
                'sliders' => 'Sliders Classes',
                'abwheel' => 'Ab Wheel'
            ];
            $data['current_machine'] = $machines[$slug];
            
            $data['current_title'] = $machines_title[$slug];

            $data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                                FROM machines
                                                LEFT OUTER JOIN (SELECT class_machine, count(*) AS cnt FROM classes_machine GROUP BY class_machine) x ON x.class_machine = machines.id
                                                HAVING countMachine > 0
                                                ORDER BY sort ASC
                                            ')->getResultArray();
            $data['all_accessories'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countAccessories
                                                FROM accessories
                                                LEFT OUTER JOIN (SELECT class_accessories, count(*) AS cnt FROM classes_accessories GROUP BY class_accessories) x ON x.class_accessories = accessories.id
                                                HAVING countAccessories > 0
                                            ')->getResultArray();
            foreach($data['machines'] as $key => $value) {
                $data['accessories'][$key]['machine_name'] = $value['short_name'];
                $data['accessories'][$key]['id'] = $value['id'];
                $data['accessories'][$key]['count_accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
                $data['accessories'][$key]['accessories'] = $db->query('SELECT * FROM accessories WHERE machine = ' . $value['id']. '')->getResultArray();
            }
    
            $data['difficulty'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM difficulty
                                                LEFT OUTER JOIN (SELECT difficulty, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY difficulty) x ON x.difficulty = difficulty.id
                                                HAVING countClasses > 0
                                            ')->getResultArray();
            $data['languages'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM languages
                                                LEFT OUTER JOIN (SELECT language, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY language) x ON x.language = languages.id
                                                HAVING countClasses > 0
                                            ')->getResultArray();
            $data['duration_less10'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration < 600')->getResultArray();
            $data['duration_less10_20'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 600 AND duration < 1200')->getResultArray();
            $data['duration_less20_30'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1200 AND duration < 1800')->getResultArray();
            $data['duration_less30_40'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 1800 AND duration < 2400')->getResultArray();
            $data['duration_less40_50'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 2400 AND duration < 3000')->getResultArray();
            $data['duration_less50_60'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 3000 AND duration < 3600')->getResultArray();
            $data['duration_more60'] = $db->query('SELECT count(*) AS cnt FROM classes WHERE deleted_at IS NULL AND duration > 3600')->getResultArray();
    
            $data['body_parts'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countBodyParts
                                                FROM body_parts
                                                LEFT OUTER JOIN (SELECT class_body_parts, count(*) AS cnt FROM classes_body_parts GROUP BY class_body_parts) x ON x.class_body_parts = body_parts.id
                                                HAVING countBodyParts > 0
                                            ')->getResultArray();
            $data['tempo'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countTempo
                                                FROM tempo
                                                LEFT OUTER JOIN (SELECT class_tempo, count(*) AS cnt FROM classes_tempo GROUP BY class_tempo) x ON x.class_tempo = tempo.id
                                                HAVING countTempo > 0
                                            ')->getResultArray();
            $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                                FROM teachers
                                                LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM classes WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                                WHERE status IN (0,1)
                                                AND deleted_at IS NULL
                                                HAVING countClasses > 0
                                                ORDER BY firstname ASC
                                                ')->getResultArray();
    
            $data['all_classes'] = $this->model->all_lagree_classes(0, 9);
    
            $data['current']['image'] = base_url() . 'images/classes1.jpg';
            $data['current']['title'] = 'Online ' . $slug . ', Mini, and Megaformer Classes | Lagree On Demand';
            $data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Classes | Lagree On Demand';
            $data['current']['seo_description'] = "Change your body from the comfort of your home or on the go with Lagree Micro, Mini, and Megaformer classes available online 24/7! Get Lagree On Demand today!";

            return view('front/classes/index_view', $data);
        }else{
            helper('text');
            $classes_views_model = model('ClassesViewModel');
            $ClassesRate_model = model('ClassesRateModel');
            $comments_model = model('CommentsModel');
            $SubscribersClasses = model('SubscribersClasses');
            $shopify_model = new ShopifyModel();
            $WooCommerceModel = new WooCommerceModel();
            $data['logged_user'] = $this->user;
            $data['settings'] = $this->settings;
            $data['current'] = $this->model->current($slug);
            $data['nums'] = create_session_nums();

            $data['current']['video_state'] = $this->model->query("SELECT * FROM video_state WHERE video_id = '" . $data['current']['slug'] . "' AND user_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND video_type = 'classes'")->getRowArray();

            $data['prev_next'] = $this->model->prev_next($data['current']['id']);
            $data['class_exercises'] = $this->model->exercises_for_class($data['current']['id']);
            $data['body_parts_percentage'] = [];
            $data['body_parts'] = [];
            $body_parts_count = [];
            $body_parts_total = 0;
            
            if(isset($data['class_exercises']) AND is_array($data['class_exercises']) AND count($data['class_exercises']) > 0){
                foreach($data['class_exercises'] as $key => $single){
                    if(!isset($single['transition']) AND isset($single['id']) AND $single['id'] > 0){
                        $parts = explode(',', $single['all_body_parts']);
                        if(count($parts) > 0){
                            foreach($parts as $part){
                                $part = trim($part);
                                if(!isset($body_parts_count[$part])){
                                    $body_parts_count[$part] = 0;
                                }
                            }
                            foreach($parts as $part){
                                $part = trim($part);
                                $body_parts_count[$part] = $body_parts_count[$part] + 1;
                                $body_parts_total++;
                            }                        
                        }
                    }
                }
                // get percentage for every body part
                foreach($body_parts_count as $key => $value){
                    $data['body_parts_percentage'][$key] = round(($value / $body_parts_total) * 100) . "%";
                }
            }
            
            $data['comments'] = $comments_model->get_comments($data['current']['id'], 'classes');
            $data['count_comments'] = 0;

            if(!empty($data['comments']) AND count($data['comments']) > 0){
                $data['count_comments'] = count($data['comments']);
                foreach($data['comments'] as $key => $single){
                    $data['comments'][$key]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'classes'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                    if(!empty($data['comments'][$key]['reply']) AND count($data['comments'][$key]['reply']) > 0){
                        $data['comments'][$key]['count_replys'] = count($data['comments'][$key]['reply']);
                        $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply']);

                        foreach($data['comments'][$key]['reply'] as $key2 => $single2){
                            $data['comments'][$key]['reply'][$key2]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single2['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'classes'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                            if(!empty($data['comments'][$key]['reply'][$key2]['reply']) AND count($data['comments'][$key]['reply'][$key2]['reply']) > 0){
                                $data['comments'][$key]['count_replys'] = $data['comments'][$key]['count_replys'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                                $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                            }
                        }
                    }
                }
            }

            // $data['bought'] = $SubscribersClasses->where(['class_id' => $data['current']['id'], 'subscriber_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0), 'purchase_type' => NULL])->find();
            // $data['rented'] = $SubscribersClasses->query("SELECT *, DATE_ADD(MAX(date), INTERVAL 1 DAY) as expiry_rent_date FROM subscribers_classes  WHERE class_id = " . $data['current']['id'] . " AND subscriber_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND purchase_type = 'rent' AND DATE_ADD(date, INTERVAL 1 DAY) > CURDATE()")->getResultArray();
            $data['own'] = $this->model->where(['id' => $data['current']['id'],'teacher' => (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0)])->find();
            // $data['similar_classes'] = $this->model->similar_classes(0, 3, NULL, 'created_at DESC', $data['current']['id']);

            $data['rated'] = $ClassesRate_model->where(['class_id' => $data['current']['id'], 'user_id' => (isset($data['logged_user']) ? $data['logged_user']['id'] : 0)])->find();

            // if($data['current']['all_class_machines_shopify'] != NULL){
            //     $shopify_machines = explode(',', $data['current']['all_class_machines_shopify']);
            //     $data['shopify_machines_titles'] = explode(',', $data['current']['all_class_machines']);
            //     foreach($shopify_machines as $product){
            //         if($product != '' AND $product != NULL){
            //             $data['shopify_machines'][] = $shopify_model->single_product($product);
            //             $data['woo_machines'][] = $WooCommerceModel->single_product($product);
            //         }
            //     }
            // }

            // if($data['current']['all_class_accessories_shopify'] != NULL){
            //     $shopify_accessories = explode(',', $data['current']['all_class_accessories_shopify']);
            //     $data['shopify_accessories_titles'] = explode(',', $data['current']['all_class_accessories']);
            //     foreach($shopify_accessories as $key => $product){
            //         if($product != '' AND $product != NULL){
            //             $data['shopify_accessories'][] = $shopify_model->single_product($product);
            //         }
            //     }
            // }
            if($data['current']['id'] == NULL){
                return redirect()->to('/');
            }
        }

        return view('front/classes/single_view', $data);
    }
    
    public function filter()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'classes.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Mini Classes | Lagree Mini Classes On Demand';
		$data['current']['seo_description'] = "Maximize your Lagree Mini's full potential with Mini classes on Lagree On Demand. View our Mini classes and start creating the body you've always wanted.";
		$data['current']['seo_keywords'] = 'Lagree On Demand Classes';

        $data['all_classes'] = $this->model->filter_classes($filter_data);

		$response['view'] = view('front/classes/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_classes']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }

    public function filter_buy_rent()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'classes.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Lagree Mini Classes | Lagree Mini Classes On Demand';
		$data['current']['seo_description'] = "Maximize your Lagree Mini's full potential with Mini classes on Lagree On Demand. View our Mini classes and start creating the body you've always wanted.";
		$data['current']['seo_keywords'] = 'Lagree On Demand Classes';

        $data['all_classes'] = $this->model->filter_buy_rent($filter_data);

		$response['view'] = view('front/classes/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_classes']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }
    public function watch_later()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $data = $this->request->getPost();

        $save_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user'], 
            'type' => $data['type'],
            'date' => date('Y-m-d')
        ];
        $remove_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user']
        ];

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
        }

		return $this->respond($response);
    }
    public function mark_as_watched()
    {
        $subscribersWatched_model = model('SubscribersWatchedModel');
		$request = service('request');
        $data = $request->getPost();
        $save_watched = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $subscribersWatched_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $subscribersWatched_model->save($save_watched);
        }

		return $this->respond($response);
    }
    public function mark_as_viewed()
    {
        $ClassesViewModel = model('ClassesViewModel');
		$request = service('request');
        $data = $request->getPost();
        $save_viewed = array('class_id' => $data['class'], 'user_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['success'] = $ClassesViewModel->save($save_viewed);

		return $this->respond($response);
    }
    public function save_video_state()
    {
        $request = service('request');
        $data = $request->getPost();
		$VideoStateModel = model('VideoStateModel');

        // $save_data = [
        //     'user_id'    => $data['user_id'],
        //     'video_id'   => $data['video_id'],
        //     'video_time' => $data['video_time'],
        //     'video_type' => $data['video_type']
        // ];
        $response['save_data'] = $data;

        $response['success'] = FALSE;

		if ($data['user_id'] != '' AND $data['video_id'] != '' AND (int)$data['video_time'] > 0){
			$response['success'] = $VideoStateModel->save($data);
            $response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $VideoStateModel->getInsertID();
		}
        return $this->respond($response);
    }

    public function rate_class()
    {
        $ClassesRate_model = model('ClassesRateModel');
        // $NotificationsModel = model('NotificationsModel');
		$request = service('request');
        $data = $request->getPost();
        $save_rate = array('class_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['data'] = $data;
        $response['favs'] = $ClassesRate_model->where(["class_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $ClassesRate_model->save($save_rate);
            $response['liked'] = TRUE;
            $response['success'] = TRUE;
        }else{
            $response['status'] = $ClassesRate_model->delete($response['favs']['id']);
            $response['success'] = TRUE;
            $response['liked'] = FALSE;
        }
        // $class = $this->class_info($data['class']);
        // if($class['type'] == 1){
        //     $seller = $this->teacher_info($class['teacher']);
        //     $user_from_teacher = $this->subscriber_from_teacher_info($seller['email']);


        //     if(isset($user_from_teacher['id'])){
        //         $notification_data = array(
        //             'content'   => 'Someone has rated your class <span class="text-underline">' . $class['title'] . '</span>.',
        //             'link'      => base_url() . '/account/classes',
        //             'author'    => 'system',
        //             'subscriber_id'    => isset($user_from_teacher['id']) ? $user_from_teacher['id'] : 0,
        //             'type' => 'class_rated_notif',
        //             'date'    => date('Y-m-d H:i:s')
        //         );
        //         $response['notification_saved'] = $NotificationsModel->save($notification_data);
        //     }
        // }

		return $this->respond($response);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
        $rules = [
            'title'         => 'required|min_length[2]',
            'slug'          => 'required|alpha_dash|is_unique[classes.slug,id,{id}]',
            'video'         => 'required',
            'machine'       => 'required',
            'difficulty'    => 'required',
            'teacher'       => 'required',
        ];
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/classes', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/classes/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/classes/' . $name, 98);
				$data['image'] = 'uploads/classes/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('classes_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function sess()
    {
        $seller = $this->teacher_info(21);

        // session()->remove('per_page');
        echo '<pre>';
        var_dump($seller);
        echo round((4.99 * 0.7), 2) * 100;
        echo '</pre>';
    }
}