<?php
$uri = service('uri');
$menu = $uri->getSegment(2);
?>
<ul class="dropdown-menu side-links accountmenu">
    <li><a href="/account/dashboard" class="side-link f-14 <?php echo $menu == 'dashboard' ? 'active' : ''; ?>" title="Dashboard">Dashboard</a></li>
    <?php if(session('teacher') !== NULL AND session('teacher') > 0){ ?>
    <!--<li class="mb-6"><a href="/account/classes" class="side-link f-14 <?php echo $menu == 'classes' ? 'active'  : ''; ?>" title="My Classes">My Classes</a></li>-->
    <?php } ?>
    <li><a href="/account" class="side-link f-14 <?php echo empty($menu) ? 'active' : ''; ?>" title="Account">Account</a></li>
    <li><a href="/account/devices" class="side-link f-14 <?php echo $menu == 'devices' ? 'active' : ''; ?>" title="Devices">Devices</a></li>
    <?php if(NULL === session('teacher') OR session('teacher') == 0){ ?>
    <li><a href="/account/payments" class="side-link f-14 <?php echo $menu == 'payments' ? 'active' : ''; ?>" title="Payment">Payments</a></li>
    <?php } ?>
    <!-- <li><a href="/account/connect" class="side-link <?php echo $menu == 'connect' ? 'active' : ''; ?>" title="Connect">Connect</a></li> -->
    <li><a href="/account/notifications" class="side-link f-14 <?php echo $menu == 'notifications' ? 'active' : ''; ?>" title="Notifications">Notifications</a></li>
    <li><a href="/account/support" class="side-link f-14 <?php echo $menu == 'support' ? 'active' : ''; ?>" title="Support">Support</a></li>
    <li <?php echo (NULL === session('teacher') OR session('teacher') == 0) ? '' : 'class="ml-auto"'; ?>><a href="/login/logout" class="side-link mt-0 acc-logout" title="Logout">Logout</a></li>
</ul>

<script>
//moves my account scrolling navigation active link to left so it's always visible on mob
window.onload = function () {
    var totalWidth = $(".lodacc-menu").outerWidth()
    $('.lodacc-menu ul').css('width', totalWidth);
    if($('.active').length){
        var myScrollPos = $('.active').offset().left + $('.active').outerWidth(true) / 2 + $('.accountmenu').scrollLeft() - $('.accountmenu').width() / 2;
        $('.accountmenu').scrollLeft(myScrollPos);
    }
}
</script>