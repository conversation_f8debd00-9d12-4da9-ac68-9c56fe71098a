<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<link id="theme-css" href="admin_assets_new/css/calendar.css" rel="stylesheet" type="text/css" />
<style>
.hide_no_content {
    display: none;
}
.teacher_exercises {
	font-size: 14px;
	font-weight: 300;
	max-height: 30vh;
	overflow-y: auto;
}
.audio_item a {
	padding: 8px 20px !important;
	margin: 0 !important;
}
.audio_item {
	padding: 0 !important;
	margin: 0 !important;
	cursor: pointer;
}
.audio_item:hover {
	background: #f0f0f0;
}
</style>
<link id="theme-css" href="admin_assets_new/css/flatpickr.css" rel="stylesheet" type="text/css" />
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">CALENDAR</h1>
                <?php if(session('super_admin') != 0){ ?>
                <div class="ml-auto flex">
                    <a href="javascript:;" data-popup="new-events-popup" onclick="new_event($(this), '<?php echo date('m/d/Y'); ?>')" class="btn black-bg white" title="New Class">New Class</a>
                    <a href="javascript:;" data-popup="new-note-popup" onclick="$('.new-note-title').show();"class="btn btn-border black ml-2" title="New Note">New Note</a>
                </div>
                <?php } ?>
            </div>
            <hr class="mt-0 mb-0">
            <!-- CALENDAR -->
            <div class="calendar-container mb-3"></div>
            <!-- END CALENDAR -->
            <div class="flex aic jcr">
                <div class="f-10 ml-15 flex aic"><span class="small-circle mr-05 lightGreen-bg"></span> Paid</div>
                <div class="f-10 ml-15 flex aic"><span class="small-circle mr-05 lightRed-bg"></span> Unpaid</div>
                <div class="f-10 ml-15 flex aic"><span class="small-circle mr-05 gray-bg"></span> Upcoming</div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/flatpickr.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var current_month = '<?php echo date('m'); ?>';
var current_year = '<?php echo date('Y'); ?>';

function custom_select_dropdown(xx){
    console.log('radi drop');

    $('.audio_item').removeClass('selected');
    xx.closest('.dropdown').find('.audio').val(xx.data('val'));
    xx.parent().addClass('selected');
    xx.closest('.dropdown').find('.dropdown-value').html(xx.html());
    xx.closest('.dropdown').removeClass('opened');
    setTimeout(function(){
        $('.search_audio').val('');
        $('.audio_item').show();
    }, 300);
}
$('.search_audio').on('click focus', function(e){
    e.stopPropagation();
});
$('.search_audio').on('keyup', function(){
    var chars = $(this).val().length;
    var val = $(this).val().toLowerCase();

    if(chars > 1){
        $(".audio_item").hide();
        $(".audio_item").each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(val) != -1){
                $(this).show();
            }
        });
    }else{
        $('.audio_item').show();
    }
});

function get_all_day_events(xx, date){
    var all_events_container = xx.next();
    
    $.ajax({
        type: 'POST',
        url: 'admin/calendar/day_events',
        data: {
            date
        },
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                xx.parent().addClass('show_all');
                all_events_container.html(data.html).addClass('show');
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
$('.pay_status_form').on('submit', function(e){
    e.preventDefault();

    var form = $(this);
    var url = form.attr("action");
    
    $.ajax({
        type: 'POST',
        url: url,
        data: form.serialize(),
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                app_msg('Status changed successfully');
                call_month(current_month, current_year);
                $('body').removeClass('show-popup');
                $('.edit-events-popup').removeClass('show');
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});
function single_note(id){
    $('.new-note-title').hide();
    $.ajax({
        type: 'POST',
        url: 'admin/calendar/get_event_info',
        data: {
            id
        },
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                $('.hide_no_content').hide();
                $('#show-popup-title').text(data.title != '' ? data.title : 'NO TITLE');
                if(data.content != ''){
                    $('.hide_no_content').show();
                    $('#show-popup-content').text(data.content);
                }
                $('.delete-note-button').attr('onclick', "delete_note(" + data.id + ")");
                $('.edit-note-button').attr('onclick', "edit_note(" + data.id + ")");
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function edit_note(id){
    $('.new-note-title').hide();
    $.ajax({
        type: 'POST',
        url: 'admin/calendar/get_event_info',
        data: {
            id
        },
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                $('#new_note_id').val(data.id ?? '');
                $('#new_note_title').val(data.title ?? '');
                $('#new_note_content').val(data.content ?? '');
                $('#new_note_date').val(data.date ?? '');
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function delete_note(id){
    console.log('delete NOTE');
    $.ajax({
        type: 'POST',
        url: 'admin/calendar/delete_note/' + id,
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                call_month(current_month, current_year);
                close_all();
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function single_event(id){
    close_all();
    $('.dropdown-value').text('Select');

    $.ajax({
        type: 'POST',
        url: 'admin/calendar/get_event_info',
        data: {
            id
        },
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                // $('.edit-event-title').text(data.class_info.title);
                $('.edit-event-date').text(data.date + ' @ ' + data.time);
                $('.edit-event-teacher').text('Teacher: ' + data.teacher);
                var model;
                if(data.model == 'Model is missing'){
                    if(data.teacher_as_model != ''){
                        model = data.teacher_as_model;
                    }else{
                        model = 'Model is missing';
                    }
                }else{
                    model = data.model;
                }
                console.log(data.model);
                console.log(data.teacher_as_model);
                $('.edit-event-model').text('Model: ' + model);

                
                if(data.class_info.all_class_machines != null){
                    console.log('all_class_machines: ' + data.class_info.all_class_machines);
                    $('.edit-event-machine').text('Machine: ' + data.class_info.all_class_machines).attr('style', false);
                }else{
                    $('.edit-event-machine').text('Machine: XXX').attr('style', 'display: none !important;');
                }
                if(data.class_info.diff != null && data.class_info.diff != 0 && data.class_info.diff != '0' ){
                    console.log('diff: ' + data.class_info.diff);
                    $('.edit-event-difficulty').text('Difficulty: ' + data.class_info.diff).attr('style', false);
                }else{
                    $('.edit-event-difficulty').text('Difficulty: XXX').attr('style', 'display: none !important;');
                }
                if(data.class_info.all_class_accessories != null){
                    console.log('all_class_accessories: ' + data.class_info.all_class_accessories);
                    $('.edit-event-accessories').text('Accessories: ' + data.class_info.all_class_accessories).attr('style', false);
                }else{
                    $('.edit-event-accessories').text('Accessories: XXX').attr('style', 'display: none !important;');
                }
                $('.recorded_class_paid').val(data.paid);
                var paid_status = {
                    0: 'Status: <span class="textGreen ml-05">Paid</span>',
                    1: 'Status: <span class="normalRed ml-05">Unpaid</span>',
                    2: 'Status: <span class="normalRed ml-05">Unpaid</span>',
                    3: 'Status: <span class="normalRed ml-05">Teacher Not Paid</span>',
                    4: 'Status: <span class="normalRed ml-05">Model Not Paid</span>',
                    5: 'Status: <span class="normalRed ml-05">Canceled</span>'
                };
                if(data.paid == 5){
                    $('.cancel-class-schedule').text('RESTORE CLASS RECORDING');
                    $('.pay_status_form').hide();
                    $('.pay_status_restore_text').hide();
                    $('.pay_status_restore').show();
                }else{
                    $('.cancel-class-schedule').text('CANCEL CLASS RECORDING');
                    $('.pay_status_form').show();
                    $('.pay_status_restore_text').show();
                    $('.pay_status_restore').hide();
                }
                $('.recorded_paid_val').html(paid_status[data.paid]);
                $('.recorded_class_id').val(data.id);
                $('.edit-events-popup').find('.edit-class-schedule').attr('onclick', 'event.stopPropagation();edit_event(' + data.id + ')');
                $('.edit-events-popup').find('.remove-class-schedule').attr('data-id', data.id);
                $('.edit-events-popup').find('.cancel-class-schedule').attr('data-id', data.id);
                $('.teacher_exercises').html('');
                $('.class_status').text(data.video_status).removeClass('lightGreen-bg lightGreen').addClass((data.video_status == 'Recorded' ? 'lightGreen-bg lightGreen' : ''));
                // if(data.video_status == 'Recorded'){
                $('.save-buttons').hide();
                $('.' + (data.video_status).toLowerCase() + '-buttons').show();

                // }
                if(data.teacher_exercises.length){
                    var titles = '';
                    $.each(data.teacher_exercises, function(i ,el){
                        titles += `<span class=\"f-14 light w100 flex mb-1\">` + el.title + `<\span>`;
                        const span = document.createElement('span');
                        span.classList.add('f-14');
                        span.classList.add('light');
                        span.classList.add('w100');
                        span.classList.add('flex');
                        span.classList.add('mb-1');
                        span.innerHTML = ((el.orientation != null && el.orientation != '' && typeof el.orientation != "undefined") ? ('(' + el.orientation + ') ') : '') + el.title;
                        $('.teacher_exercises').append(span);
                    });
                    $('.teacher_exercises_title').html('LIST OF EXERCISES SUBMIT BY TEACHER').addClass('mb-4');
                }else{
                    $('.teacher_exercises_title').html('TEACHER DID NOT SUBMIT EXERCISES YET').removeClass('mb-4');
                }
                setTimeout(function(){
                    $('body').addClass('show-popup');
                    $('.edit-events-popup').addClass('show');
                    $('.new-events-popup button[type=submit]').attr('disabled', false);
                }, 100);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function edit_event(id){
    close_all();
    $('body').addClass('show-popup');
    $('.new-events-popup').addClass('show');
    $('.dropdown-value').text('Select');
    $('.new-events-popup').find('#status').val('');
    $('.new-events-popup').find('#time').val('');
    $('.new-events-popup').find('#paid').val('');
    $('.new-events-popup').find('#user_id').val('');
    $('.new-events-popup').find('#class_id').val('');
    $('.new-events-popup').find('#model_id').val('');
    $('.new-events-popup').find('#teacher_id').val('');
    $('.new-events-popup').find('#teacher_as_model_id').val('');
    $('.new-events-popup').find('#id').val('');
    $('#teacher_as_model_container .dropdown-value').html('Select');
    $('#model_container .dropdown-value').html('Select');
    $('#model_type_check1').trigger('click').change();
    $('.event-popup-title').text('EDIT CLASS RECORDING');
    $('.fusnote').text('The teacher and model will be emailed about the class edits.');

    $.ajax({
        type: 'POST',
        url: 'admin/calendar/get_event',
        data: {
            id
        },
        dataType: 'json',
        success: function (data) {
            console.log('SUCCESS');
            console.log(data);
            if(data.success){
                $('.new-events-popup').find('.class_schedule_date').val(data.date);
                $('.new-events-popup').find('.class_schedule_date').attr('value', data.date);
                $('.new-events-popup').find('.class_schedule_time').val(data.time);
                $('.new-events-popup').find('.class_schedule_teacher').val(data.teacher_id);
                $('.new-events-popup').find('.class_schedule_model').val(data.model_id);
                $('.new-events-popup').find('.class_schedule_teacher_as_model').val(data.teacher_as_model_id);
                $('.new-events-popup').find('#status').val(data.status);
                $('.new-events-popup').find('#paid').val(data.paid);
                $('.new-events-popup').find('#user_id').val(data.user_id);
                $('.new-events-popup').find('#class_id').val(data.class_id);
                $('.new-events-popup').find('#id').val(data.id);

                $('.new-events-popup').find('#time_container .dropdown-value').text((data.time).toLowerCase());
                $('.new-events-popup').find('#teacher_container .dropdown-value').html('<img src="' + data.teacher_image + '" alt="" style="width: 25px;height: 25px;margin-right: 10px;border-radius: 20px;">' + data.teacher);
                $('.new-events-popup').find('#model_container .dropdown-value').text(data.model == 'Model is missing' ? 'Select' : data.model);
                var teacher_as_model_image = data.teacher_as_model_image != 0 ? '<img src="' + data.teacher_as_model_image + '" alt="" style="width: 25px;height: 25px;margin-right: 10px;border-radius: 20px;">' : '';

                $('.new-events-popup').find('#teacher_as_model_container .dropdown-value').html(teacher_as_model_image + data.teacher_as_model);

                var flat = $(".datepickerrr").flatpickr({ 
                    dateFormat: "m/d/Y", 
                    defaultDate: data.date
                });
                setTimeout(function(){
                    $('.new-events-popup button[type=submit]').attr('disabled', false);
                    $(document).on('click', '.flatpickr-calendar', function(e){
                        e.stopPropagation();
                    });
                    $('.flatpickr-calendar').on('click', function(e){
                        e.stopPropagation();
                    });
                    if(data.model_id != '' && data.model_id != '0'){
                        $('#model_type_check1').trigger('click').change();
                        console.log('model/teacher changed: MODEL');
                        $('.models-container').show();
                        $('.teacher-as-model-container').hide();
                    }else if(data.teacher_as_model_id != '' && data.teacher_as_model_id != '0' && data.teacher_as_model_id != null){
                        $('#model_type_check2').trigger('click').change();
                        console.log('model/teacher changed: TEACHER AS MODEL' );
                        $('.models-container').hide();
                        $('.teacher-as-model-container').show();
                    }
                }, 100);
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
var flat_note = $(".datepicker-note").flatpickr({ 
    dateFormat: "m/d/Y", 
});
function isDateBeforeToday(date) {
    return new Date(date.toDateString()) < new Date(new Date().toDateString());
}
function new_event(xx, date){
    $('.new-events-popup').find('#paid').val('');
    $('.new-events-popup').find('#user_id').val('');
    $('.new-events-popup').find('#class_id').val('');
    $('.new-events-popup').find('#model_id').val('');
    $('.new-events-popup').find('#teacher_id').val('');
    $('.new-events-popup').find('#teacher_as_model_id').val('');
    $('.new-events-popup').find('#id').val('');
    $('.dropdown-value').text('Select');
    $('#class_schedule_date').val(date);
    $('.event-popup-title').text('SCHEDULE A CLASS RECORDING');
    $('.fusnote').text('The teacher and model will be emailed about the class schedule.');

    var flat = $(".datepickerrr").flatpickr({ 
        dateFormat: "m/d/Y", 
        defaultDate: date
    });
    setTimeout(function(){
        $('.new-events-popup button[type=submit]').attr('disabled', false);
        $(document).on('click', '.flatpickr-calendar', function(e){
            e.stopPropagation();
        });
        $('.flatpickr-calendar').on('click', function(e){
            e.stopPropagation();
        });
    }, 300);
}
function call_month(month, year){
    current_month = month;
    current_year = year;
    $.ajax({
        type: 'POST',
        url: 'admin/calendar/show',
        data: {
            year,
            month
        },
        dataType: 'json',
        success: function (data) {
            $('.calendar-container').html(data.html);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function close_all_events(xx){
    xx.removeClass('show');
    xx.closest('.show_all_events').removeClass('show');
    xx.closest('li').removeClass('show_all');
}
$(document).ready(function(){
    call_month(<?php echo date('m'); ?>, <?php echo date('Y'); ?>);
});
$('.add_new_note').on('submit', function(e){
    e.preventDefault();

    var form = $(this);
    var url = form.attr("action");
    
    $.ajax({
        type: 'POST',
        url: url,
        data: form.serialize(),
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                close_all();
                call_month(current_month, current_year);
                $('#add-new-note').trigger('reset');
                $('#end_date_container').hide();
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
});
</script>
</body>
</html>