<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\I18n\Time;

class CalendarModel extends Model
{
    protected $table = 'calendar_events';
	protected $allowedFields = ['title', 'content', 'is_note', 'user_id', 'class_id', 'paid', 'status', 'time', 'date', 'teacher_id', 'teacher_as_model_id', 'model_id'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'date'          => 'required',
        'class_id'      => 'required',
        'teacher_id'    => 'required',
        // 'model_id'    => 'required'
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

	protected function prepare_data(array $data)
	{
        return $data;
	}
}