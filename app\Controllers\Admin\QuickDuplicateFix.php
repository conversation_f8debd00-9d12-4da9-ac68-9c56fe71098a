<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\Admincontroller;
use App\Models\StripeModel;
use App\Models\SubscribersModel;

class QuickDuplicateFix extends Admincontroller
{
    protected $stripe_model;
    protected $subscribers_model;

    public function __construct()
    {
        parent::__construct();
        $this->stripe_model = new StripeModel();
        $this->subscribers_model = new SubscribersModel();
    }

    public function index()
    {
        $data = $this->data;
        $data['current']['title'] = "Quick Duplicate Fix | Admin";
        
        return view('admin/quick_duplicate_fix_view', $data);
    }

    /**
     * Find customers with subscriptions to both price IDs mentioned in the issue
     */
    public function find_cross_price_duplicates()
    {
        $price_ids = [
            'price_1KFFPGL6EaNAw2awJpDD8rTi',
            'price_1KJR2XL6EaNAw2awZrAvJzID'
        ];

        try {
            $stripe = new \Stripe\StripeClient(
                $this->stripe_model->stripe_config['api_key']
            );

            $results = [];
            $customer_subscriptions = [];

            // Get all active subscriptions for both price IDs
            foreach ($price_ids as $price_id) {
                $subscriptions = $stripe->subscriptions->all([
                    'price' => $price_id,
                    'status' => 'active',
                    'limit' => 100
                ]);

                foreach ($subscriptions->data as $sub) {
                    $customer_subscriptions[$sub->customer][] = [
                        'subscription_id' => $sub->id,
                        'price_id' => $price_id,
                        'status' => $sub->status,
                        'created' => $sub->created,
                        'amount' => ($sub->items->data[0]->price->unit_amount ?? 0) / 100,
                        'interval' => $sub->items->data[0]->price->recurring->interval ?? 'unknown'
                    ];
                }
            }

            // Find customers with multiple subscriptions
            $duplicates = [];
            foreach ($customer_subscriptions as $customer_id => $subs) {
                if (count($subs) > 1) {
                    // Get subscriber info
                    $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();
                    
                    $duplicates[] = [
                        'customer_id' => $customer_id,
                        'subscriber' => $subscriber,
                        'subscription_count' => count($subs),
                        'subscriptions' => $subs,
                        'total_monthly_charge' => array_sum(array_column($subs, 'amount'))
                    ];
                }
            }

            // Sort by total monthly charge (highest first)
            usort($duplicates, function($a, $b) {
                return $b['total_monthly_charge'] <=> $a['total_monthly_charge'];
            });

            return $this->respond([
                'success' => true,
                'duplicates' => $duplicates,
                'total_affected_customers' => count($duplicates),
                'price_ids_analyzed' => $price_ids
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get detailed timeline for a specific customer
     */
    public function get_customer_timeline()
    {
        $customer_id = $this->request->getGet('customer_id');
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            $stripe = new \Stripe\StripeClient(
                $this->stripe_model->stripe_config['api_key']
            );

            // Get customer info
            $customer = $stripe->customers->retrieve($customer_id);
            
            // Get all subscriptions (not just active)
            $all_subscriptions = $stripe->subscriptions->all([
                'customer' => $customer_id,
                'limit' => 100
            ]);

            // Get recent invoices
            $invoices = $stripe->invoices->all([
                'customer' => $customer_id,
                'limit' => 20
            ]);

            // Get payment methods
            $payment_methods = $stripe->paymentMethods->all([
                'customer' => $customer_id,
                'type' => 'card'
            ]);

            $timeline = [];

            // Add subscription events
            foreach ($all_subscriptions->data as $sub) {
                $timeline[] = [
                    'type' => 'subscription',
                    'action' => 'created',
                    'timestamp' => $sub->created,
                    'date' => date('Y-m-d H:i:s', $sub->created),
                    'details' => [
                        'subscription_id' => $sub->id,
                        'status' => $sub->status,
                        'price_id' => $sub->items->data[0]->price->id ?? 'unknown',
                        'amount' => ($sub->items->data[0]->price->unit_amount ?? 0) / 100,
                        'interval' => $sub->items->data[0]->price->recurring->interval ?? 'unknown'
                    ]
                ];

                if ($sub->cancelled_at) {
                    $timeline[] = [
                        'type' => 'subscription',
                        'action' => 'cancelled',
                        'timestamp' => $sub->cancelled_at,
                        'date' => date('Y-m-d H:i:s', $sub->cancelled_at),
                        'details' => [
                            'subscription_id' => $sub->id
                        ]
                    ];
                }
            }

            // Add invoice events
            foreach ($invoices->data as $invoice) {
                $timeline[] = [
                    'type' => 'payment',
                    'action' => $invoice->status === 'paid' ? 'successful' : 'failed',
                    'timestamp' => $invoice->created,
                    'date' => date('Y-m-d H:i:s', $invoice->created),
                    'details' => [
                        'invoice_id' => $invoice->id,
                        'amount' => $invoice->amount_paid / 100,
                        'status' => $invoice->status,
                        'subscription_id' => $invoice->subscription
                    ]
                ];
            }

            // Sort timeline by timestamp (newest first)
            usort($timeline, function($a, $b) {
                return $b['timestamp'] <=> $a['timestamp'];
            });

            // Get subscriber info
            $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();

            return $this->respond([
                'success' => true,
                'customer' => [
                    'id' => $customer->id,
                    'email' => $customer->email,
                    'created' => date('Y-m-d H:i:s', $customer->created)
                ],
                'subscriber' => $subscriber,
                'timeline' => $timeline,
                'active_subscriptions' => array_filter($all_subscriptions->data, function($sub) {
                    return $sub->status === 'active';
                }),
                'payment_methods' => $payment_methods->data
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Quick fix for a customer - keep newest subscription, cancel others
     */
    public function quick_fix_customer()
    {
        $data = $this->request->getPost();
        $customer_id = $data['customer_id'] ?? null;
        
        if (!$customer_id) {
            return $this->respond([
                'success' => false,
                'message' => 'Customer ID is required'
            ]);
        }

        try {
            // Get active subscriptions
            $active_subs = $this->stripe_model->get_customer_active_subscriptions($customer_id);
            
            if (!$active_subs['success'] || $active_subs['count'] <= 1) {
                return $this->respond([
                    'success' => false,
                    'message' => 'No duplicate subscriptions found for this customer'
                ]);
            }

            // Find the newest subscription
            $newest_subscription = null;
            $newest_created = 0;

            foreach ($active_subs['subscriptions'] as $sub) {
                if ($sub->created > $newest_created) {
                    $newest_created = $sub->created;
                    $newest_subscription = $sub;
                }
            }

            // Cancel all others
            $cancelled_subscriptions = [];
            foreach ($active_subs['subscriptions'] as $sub) {
                if ($sub->id !== $newest_subscription->id) {
                    $cancel_result = $this->stripe_model->cancel_subscription($sub->id);
                    if ($cancel_result['success']) {
                        $cancelled_subscriptions[] = $sub->id;
                    }
                }
            }

            // Update subscriber record
            $subscriber = $this->subscribers_model->where('stripe_customer', $customer_id)->first();
            if ($subscriber) {
                $this->subscribers_model->save([
                    'id' => $subscriber['id'],
                    'stripe_subscription' => $newest_subscription->id,
                    'subscription_status' => 'active'
                ]);
            }

            return $this->respond([
                'success' => true,
                'message' => 'Successfully fixed duplicate subscriptions',
                'kept_subscription' => $newest_subscription->id,
                'cancelled_subscriptions' => $cancelled_subscriptions,
                'total_cancelled' => count($cancelled_subscriptions)
            ]);

        } catch (Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Bulk fix all found duplicates
     */
    public function bulk_fix_duplicates()
    {
        $duplicates_result = $this->find_cross_price_duplicates();
        
        if (!$duplicates_result['success']) {
            return $this->respond($duplicates_result);
        }

        $fixed_customers = [];
        $errors = [];

        foreach ($duplicates_result['duplicates'] as $duplicate) {
            try {
                $fix_result = $this->quick_fix_customer_internal($duplicate['customer_id']);
                if ($fix_result['success']) {
                    $fixed_customers[] = $duplicate['customer_id'];
                } else {
                    $errors[] = [
                        'customer_id' => $duplicate['customer_id'],
                        'error' => $fix_result['message']
                    ];
                }
            } catch (Exception $e) {
                $errors[] = [
                    'customer_id' => $duplicate['customer_id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return $this->respond([
            'success' => true,
            'total_processed' => count($duplicates_result['duplicates']),
            'fixed_customers' => $fixed_customers,
            'total_fixed' => count($fixed_customers),
            'errors' => $errors,
            'total_errors' => count($errors)
        ]);
    }

    private function quick_fix_customer_internal($customer_id)
    {
        // Same logic as quick_fix_customer but without HTTP request handling
        $active_subs = $this->stripe_model->get_customer_active_subscriptions($customer_id);
        
        if (!$active_subs['success'] || $active_subs['count'] <= 1) {
            return [
                'success' => false,
                'message' => 'No duplicate subscriptions found'
            ];
        }

        // Find newest and cancel others
        $newest_subscription = null;
        $newest_created = 0;

        foreach ($active_subs['subscriptions'] as $sub) {
            if ($sub->created > $newest_created) {
                $newest_created = $sub->created;
                $newest_subscription = $sub;
            }
        }

        $cancelled_count = 0;
        foreach ($active_subs['subscriptions'] as $sub) {
            if ($sub->id !== $newest_subscription->id) {
                $cancel_result = $this->stripe_model->cancel_subscription($sub->id);
                if ($cancel_result['success']) {
                    $cancelled_count++;
                }
            }
        }

        return [
            'success' => true,
            'cancelled_count' => $cancelled_count,
            'kept_subscription' => $newest_subscription->id
        ];
    }
}
