<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.set_sort_byy.selected {
	color: #000 !important;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">Live Events</h1>
                <a href="admin/liveevents/edit" class="btn black-bg white ml-auto" title="New Live Event">New Live Event</a>
            </div>
            <hr class="mt-0 mb-2">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $liveevents_count == 1 ? $liveevents_count . ' Live Event' : $liveevents_count . ' Live Events'; ?></h5>
                <div class="flex aic jcsb">
                <div class="dropdown d-inline-block">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/liveevents/sort_by/liveevents.date/desc" class="set_sort_byy link midGray <?php echo $sort_by == 'Stream Date' ? 'selected' : ''; ?>" title="">Stream Date</a></li>
                        <li><a href="admin/liveevents/sort_by/liveevents.title/asc" class="set_sort_byy link midGray <?php echo $sort_by == 'Ascending' ? 'selected' : ''; ?>" title="">Ascending</a></li>
                        <li><a href="admin/liveevents/sort_by/liveevents.title/desc" class="set_sort_byy link midGray <?php echo $sort_by == 'Descending' ? 'selected' : ''; ?>" title="">Descending</a></li>
                        <li><a href="admin/liveevents/sort_by/countView/desc" class="set_sort_byy link midGray <?php echo $sort_by == 'Popularity' ? 'selected' : ''; ?>" title="">Popularity</a></li>
                        <li><a href="admin/liveevents/sort_by/classRate/desc" class="set_sort_byy link midGray <?php echo $sort_by == 'Best Rated' ? 'selected' : ''; ?>" title="">Best Rated</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="admin/liveevents/search" method="POST" class="search-form <?php echo isset($search_term) ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            </div>
            <hr class="mt-2 mb-2">
            <div class="flex aic jcsb">
                <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="liveevents" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div>

            </div>
            <hr class="mt-2 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders sortable-liveevents">
<?php
foreach($all_liveevents as $single){
?>
                        <div class="table-row" data-rowid="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div>
                                <a href="admin/liveevents/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="light mr-3"><img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : ((isset($single['cover_image']) AND $single['cover_image'] != '') ? $single['cover_image'] : ''); ?>" alt="" class="img-fluid" style="max-width: 210px;height: 120px;width: 210px;object-fit: cover;" /></a>
                                <div class="flex flex-column normal">
                                    <a href="admin/liveevents/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title medium mb-05"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></a>
                                    <span class="midGray mb-05 f-12">
                                        <?php echo (isset($single['all_class_machines']) AND $single['all_class_machines'] != '') ? $single['all_class_machines'] : ''; ?>,
                                        <?php echo (isset($single['duration']) AND $single['duration'] != '') ? $single['duration'] : ''; ?>,
                                        <span class="blockmob"></span>Difficulty: <?php echo (isset($single['diff']) AND $single['diff'] != '') ? $single['diff'] : ''; ?>,
                                    </span>
                                    <span class="midGray f-12"><?php echo (isset($single['date']) AND $single['date'] != '') ? date('m/d/Y', strtotime($single['date'])) : ''; ?>, <?php echo (isset($single['time']) AND $single['time'] != '') ? $single['time'] : ''; ?></span>
                                    <div class="row-actions f-1">
                                        <a href="admin/liveevents/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="link link-red red mr-1">Edit</a>
                                        <!-- |
                                        <a href="javascript:;" class="link link-midGray midGray ml-1 delete_record" data-popup="delete-popup" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="liveevents">Delete</a>-->
                                    </div>
                                </div>
                                <?php // if(isset($single['upcoming']) AND $single['upcoming'] != ''){ ?>
                                <div class="flex flex-column ml-auto text-right f-12">
                                    <!-- <span class="btn btn-sm red-bg white f-1">Upcoming</span> -->
                                    <span class="reorder"><img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-2 handle"></span>
                                </div>
                                <!-- <?php // }else{ ?>
                                <div class="flex flex-column ml-auto text-right f-14 pr-2">
                                    <a href="admin/liveevents/edit/<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="most-title"><?php // echo (isset($single['countView']) AND $single['countView'] != '') ? $single['countView'] : ''; ?> views</a>
                                    <span class="red f-16"><?php // echo (isset($single['classRate']) AND $single['classRate'] != '') ? number_format($single['classRate'], 1) : ''; ?> <i class="icon-star"></i></span>
                                </div>
                                <?php // } ?> -->
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($liveevents_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($all_liveevents); ?><span class="midGray mx-1">of <?php echo $liveevents_count; ?></span>
                    <a href="admin/liveevents/page/<?php echo $page > 1 ? $page - 1 : 1; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/liveevents/page/<?php echo $page + 1; ?>" class="table-arrow py-2 px-1 <?php echo ((count($all_liveevents) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($all_liveevents)) == $liveevents_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
if($('.sortable-liveevents').length){
    var fixHelper = function(e, ui) {
        ui.children().each(function() {
            $(this).width($(this).width());
        });
        return ui;
    };
    $(".table").sortable({
        helper: fixHelper,
        handle: ".handle",
        stop: function(event, ui) {
            var post_data = {
                "sorting": []
            };
            $(this).children().each(function(i) {
                var section_id = $(this).data("rowid");
                var pom = {
                    id: section_id,
                    sort: i + 1
                };
                post_data.sorting.push(pom);
            });
            console.log(post_data);
            $.ajax({
                type: "POST",
                url: "admin/liveevents/sort_table",
                data: post_data,
                dataType: "json",
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        console.log('SUCCESS');
                        app_msg('Saved');
                    } else {
                        console.log('NO SUCCESS');
                    }
                },
                error: function (result) {
                    console.log('ERROR WITH PHP');
                }
            });
        }
    }).disableSelection();
}
</script>
</body>
</html>