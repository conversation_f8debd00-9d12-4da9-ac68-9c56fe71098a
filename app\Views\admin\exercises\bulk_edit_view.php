<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.total-exercises-count {
	transform: translateY(0px);
	padding: 10px 17px !important;
    background: #f8f8f8;
    border: none !important;
    border-radius: 8px;
    margin-bottom: 0px;
    margin-top: 10px;
}
.pb-15 {
	padding-bottom: 15px !important;
}
.f-12.mb-0.lh-small br {
	display: block;
	margin-bottom: 10px;
}
.custom-selectbox:not(.with-checkboxes) .select_val {
	line-height: 39px;
	height: 39px;
    font-size: 12px;
}
.custom-selectbox-holder {
	height: 42px !important; 
}
.search-ajax-classes-container {
	padding-right: 12px;
}
.mt-15 {
    margin-top: 15px;
}
[type="checkbox"]:not(:checked) + label::before, [type="checkbox"]:checked + label::before {
	top: -1px;
}
[type="checkbox"]:not(:checked) + label::after, [type="checkbox"]:checked + label::after {
	left: 7px;
	top: 6px;
}
.single-class-rest {
	position: relative;
}
.colors {
	display: flex;
	position: absolute;
	height: 10px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
    right: 60px;
}
.colors span.red-bg {
	background: #DB1818 !important;
}
.colors span.white-bg {
	box-shadow: 0 0 0 1px rgba(0,0,0,0.2) inset !important;
}
.colors span {
	display: block;
	width: 10px;
	height: 10px;
	border-radius: 50%;
    margin-left: 5px;
}
.btn.btn-big {
	font-size: 14px !important;
}
[onclick]{
    cursor: pointer;
}
.beforee {
    position: relative;
    padding-left: 133px !important;
}
.custom-selectbox.not-empty:not(.opened) {
	background: #fff !important;
}
.beforee::before {
    content: 'Routine duration';
    position: absolute;
    height: 100%;
    top: 0;
    left: 20px;
}
.not-empty .beforee::before {
    content: 'Routine duration: ';
    position: absolute;
    height: 100%;
    top: 0;
    left: 20px;
}
.custom-select.active {
	position: relative;
	z-index: 11111;
}
.custom-selectbox:not(.with-checkboxes) ul li:before {
    display: none !important;
}
.custom-selectbox:not(.with-checkboxes) ul li {
    padding: 8px 0px 0px 10px !important;
}
.custom-selectbox:not(.with-checkboxes) ul {
	margin: 8px 0;
}
.custom-selectbox:not(.with-checkboxes) ul li {
	padding: 6px 10px 6px 20px !important;
	margin-bottom: 0;
}
.custom-selectbox:not(.with-checkboxes) ul li:hover {
	background: #fbfbfb;
}
.custom-select.error .custom-selectbox:not(.with-checkboxes) {
	border-color: red !important;
}
.graycheckbox, .checkbox-group .form-box {
	background: #fff;
}
.custom-selectbox:not(.with-checkboxes) ul {
    overflow-y: auto !important;
}
.custom-selectbox:not(.with-checkboxes) ul li:last-child {
	margin-bottom: 0;
}
.disabledd {
    pointer-events: none;
    opacity: 0.3;
}
.single-selected-exercises .handle,
.single-selected-howto .handle,
.single-selected-class .handle {
	position: absolute;
    margin-top: 37px;
    right: 0;
    z-index: 11;
}
.handle:hover {
    cursor: pointer;
}
.ajax-class > * {
	flex: 1;
	display: flex;
}
.ajax-class {
	position:relative;
    /* overflow: hidden; */
}
.search-ajax-classes {
	display: flex;
	flex-direction: column;
}
.ajax-class .single-class-image {
	min-width: 120px;
	width: 120px;
	height: 70px;
	min-height: 70px;
	margin-right: 15px;
	flex: 1;
	max-width: 120px;
}
.single-class-image + span {
	flex: 1;
	flex-direction: column;
	margin-left: 0;
	max-width: calc(100% - 45px - 10px);
}
.btn.btn-xs.red-bg.white.f-1.ml-auto {
  flex: initial;
  max-width: 40px;
  margin-left: auto !important;
  align-self: center;
  height: 40px;
  color: #000 !important;
  background: #fff !important;
  border: 1px solid #f0f0f0;
  width: 40px;
  position: absolute;
  right: 20px;
}
.btn.btn-xs.red-bg.white.f-1.ml-auto:hover {
  color: #fff !important;
  background: #000 !important;
  border: 1px solid black !important;
}
.audio_item:hover {
  background: #f0f0f0;
}
.audio_item {
  padding: 0 !important;
  margin: 0 !important;
  cursor: pointer;
}
.audio_item a{
  padding: 8px 20px !important;
  margin: 0 !important;
}
.audio_item.selected {
    font-weight: 700;
}
.dropdown > .dropdown-menu {
  padding: 25px 25px 25px 25px;
}
.upload-zone_audio,
.upload-zone {
    background: #fff;
    border: none;
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-zone_audio::before,
.upload-zone::before {
    content: "";
    position: absolute;
    border: 1px solid #f0f0f0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius:10px;
    background: #f8f8f8;
}
.upload-zone.dragOver::before {
    content: "Drop your video file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver::before {
    content: "Drop your audio file here";
    font-size: 24px;
    color: rgb(0 0 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border: 10px solid #f0f0f0;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px;
}
.upload-zone_audio.dragOver,
.upload-zone.dragOver {
	background: #f8f8f8;
	border: none;
}
.upload-zone_audio.no-border::before,
.upload-zone_audio.no-border {
	background: #fff;
	border: none;
}
#main_form h3.mb-3 {
	font-size: 14px !important;
  font-weight: 600 !important;
}
.bottom-fixed-buttons {
    position: fixed;
    bottom: 0;
    z-index: 9999;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100vw - 440px);
    max-width: 1170px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #F0F0F0;
}
#main_form {
	margin: 0 auto 60px;
}
.ajax-class{
    position: relative;
}
.exercises_add_duration {
	position: absolute;
	top: 0;
	width: 100%;
	display: flex;
	height: 100%;
	background: #fff;
	align-items: flex-start;
	justify-content: center;
	padding: 10px 25px;
	/* box-shadow: 0 0 20px 0 rgba(0,0,0,0.15); */
	right: 0;
	opacity: 0;
	pointer-events: none;
	visibility: hidden;
	transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
    flex-direction: column;
}
.exercises_add_duration.opened {
    opacity: 1;
    pointer-events: auto;
    visibility: visible;
	right: 0px;
    background: rgba(0,0,0,0.3);
}
.exercises_add_duration_input {
	width: 50%;
	border: 1px solid #f0f0f0;
	height: 40px;
	padding: 5px 5px 5px 20px;
	font-size: 14px;
	margin-right: 0;
	text-align: left;
}
.close_duration_popup {
	position: absolute;
	top: 10px;
	right: 20px;
	z-index: 1;
	cursor: pointer;
	color: #F0F0F0;
	font-size: 40px;
	padding: 0;
	font-weight: 300;
	left: auto;
	width: 30px !important;
}
<?php if($logged_user['super_admin'] != 1){ ?>
.msg-popup .link.link-red.red.ml-auto{
    display: none;
}
<?php } ?>
span.dropdown-button {font-size:14px !important; padding-left: 20px;}
/*new code*/
.reversecols {justify-content: space-between; max-width: 1260px; margin: 0 auto;}
.reversecols .col-6 {padding:0; max-width:47.6%;}
.reversecols .classroutinecol {}
.reversecols h5 {padding-bottom: 55px; margin-top: 5px;}
.reversecols .allexercisescol .ajax-class:first-of-type {border-top:none !important;}
.reversecols .allexercisescol .search-ajax-classes {border-top:1px solid #f0f0f0 !important; margin-top: 97px;}
.nomarleftright {margin-left:0; margin-right:0;}
/* .search-container {margin-top: -116px;} */
.twodropdwn {margin-top: 86px; margin-left: 0; margin-right: 0; justify-content: space-between;}
.twodropdwn .col-6 {padding: 0 !important; max-width: 48.6%;}
.find-exercises, .single-class-image {display:none !important;}
.remove-textual {
	width: 10px;
	height: 10px;
	position: absolute;
	right: 38px;
	top: 50%;
	transform: translateY(-50%);
}
.exercises_add_duration {position: fixed; z-index: 99999999999; padding: 0;}
.exercises_add_duration h5 {margin-left:30px; font-weight: 600; padding-bottom: 5px !important; margin-top: 0 !important;}
.pp-wrap {flex-direction:column; background: #fff; padding-top: 25px; padding-bottom: 30px; width: 100%; max-width: 400px; height: auto; top: 50%; left: 50%; transform: translate(-50%, -50%); position: absolute;}
.pp-durat {width: calc(100% - 60px); margin: 0 auto 25px;}
.pp-orient {width: calc(100% - 60px); margin: 0 auto;}
.pp-wrap button {font-size:14px !important; width: calc(100% - 60px); margin: 0 auto;}
.pp-sep {width:100%; height:1px; background:#f0f0f0;margin-top:20px; margin-bottom: 30px;}
.dur-min {position: absolute; margin-left: 123px; font: 12px 'Graphik';	color: #969696;}
.dur-sec {position: absolute; right: 50px; font: 12px 'Graphik'; color: #969696;}
.ajax-class:hover {background: none;}
.ajax-search-classes.search-form .search-button {top: 2px;}

.single-selected-exercises .handle, .single-selected-howto .handle, .single-selected-class .handle {
	margin-top: 0 !important;
	top: 50%;
	transform: translateY(-50%);
}
.single-selected-exercises .single-class {
	border-bottom: none;
	padding-bottom: 0;
	margin-bottom: 0;
}
.single-selected-exercises {
	border-bottom: 1px solid #F0F0F0;
	padding-bottom: 25px;
	padding-top: 25px;
}
.checkbox.contact-forms {
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}

@media screen and (max-width: 767px) {
    .bottom-fixed-buttons {width:100%; padding-right:20px;}
    #main_form {margin-bottom: 35px !important;}
    .reversecols h5 {margin-top: 0; padding-bottom: 30px;}
    .twodropdwn {margin-top: 83px;}
    .search-container {margin-top: -153px;}
    .reversecols .allexercisescol .search-ajax-classes {margin-top: 142px;}
    .row.big-gap.reversecols .col-6:first-of-type {margin-bottom: 20px;}
    .total-exercises-count {transform: translateY(123px);}
}

@media screen and (max-width: 480px) {
    .pp-wrap {max-width: 90%; margin-left: -45%;} 
    .close_duration_popup {left: initial; margin-left: 0; right: 0;	margin-right: 35px;}
    .dur-min {margin-left: 30%;}
}
.bulk-edit-options [type="checkbox"]:not(:checked) + label::before, 
.bulk-edit-options [type="checkbox"]:checked + label::before {
	top: -7px;
}
.bulk-edit-options [type="checkbox"]:not(:checked) + label::after, 
.bulk-edit-options [type="checkbox"]:checked + label::after {
	top: 0px;
}
.external-url {
    opacity: 0;
    margin-right: 15px;
}
.exercises-class:hover .external-url {
    opacity: 1;
}
.ajax-class.exercises-class:first-child .video_preview_tooltip,
.ajax-class.exercises-class:nth-child(2) .video_preview_tooltip {
    bottom: auto;
    top: 98%;
}
.ajax-class:hover .video_preview_tooltip {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;    
}
.video_preview_tooltip {
    transition: all 0.1s ease-in-out 0s;
	position: absolute;
	width: 200px;
	left: 36px;
    bottom: 98%;
	z-index: 99;
	background: #fff;
	box-shadow: 0 0 6px 0 rgba(0,0,0,0.05);
	height: calc(200px * 0.5625);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}
.video_preview_tooltip video {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover;
	position: absolute;
    z-index: 1;
	top: 0;
	left: 0;
}

</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content tab_content pb-5 mb-100" style="padding-bottom: 120px">
        <div class="container page-title">
            <h1 class="h3 mr-05 pr-1">BULK EDIT (EXERCISES)</h1>
        </div>
        <?php if(count($all_exercises) > 0){ ?>
            <div class="container">
                <div class="row bulkedit-wrap">
                    <div class="col-6 bulkedit-box bulkedit-left">
                        <div class="">
                            <h5 class="f-14 semibold flex aic jcsb lh-small rb-title">ALL EXERCISES
                                <span class="link midGray f-12 normal" onclick="clear_exercise_filters()">Clear</span>
                            </h5>
                        </div>
                        <div class="row normal-gap">
                            <div class="col-12 mb-1">
                                <div class="ajax-search-classes search-form show ml-0 px-0" style="height: 40px;">
                                    <input type="text" class="seach-input search-wide search_exercises_filter" placeholder="Search (enter at least 2 characters)...">
                                    <button type="button" class="search-button" style="right: 7px;border-radius: 0 !important;height: 36px;"><img src="admin_assets_new/images/search-newicon.svg" alt="" class="img-fluid" /></button>
                                </div>
                            </div>
                            <div class="col-6 pr-1">
                                <div class="custom-select small">
                                    <div class="custom-selectbox-holder mb-0">
                                        <div class="custom-selectbox with-checkboxes">
                                            <span class="select_val">Machines <span class="select_count select_count_machines"></span></span>
                                            <ul>
                                            <?php foreach($machines as $single){ ?>
                                                <li class="exercises_machines" data-name="<?php echo $single['title']; ?>" data-val="<?php echo strtolower(str_replace(' ', '_', $single['id'])); ?>"><?php echo $single['title']; ?></li>
                                            <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 pl-1">
                                <div class="custom-select small">
                                    <div class="custom-selectbox-holder mb-0">
                                        <div class="custom-selectbox with-checkboxes">
                                            <span class="select_val">Body Parts <span class="select_count select_count_body_parts"></span></span>
                                            <ul>
                                            <?php foreach($body_parts as $single){ ?>
                                                <li class="exercises_body_parts" data-val="<?php echo strtolower($single['id']); ?>"><?php echo $single['title']; ?></li>
                                            <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="find-exercises">
                            <p class="f-14 medium my-6">Didn’t find an exercise? Please submit a request <a href="mailto:<EMAIL>" class="link link-black black text-underline">here</a>.</p>
                        </div>
                        <div class="total-exercises-count">
                            <p class="f-12 normal"><span class="total-exercises"><?php echo $all_exercises_count; ?></span> Exercises</p>
                        </div>
                        <div class="selection_option" style="display: none;border: none !important">
                            <div class="flex aic jcsb py-25 pb-15">
                                <div>
                                    <span class="f-12 normalRed selection_count">3 exercises selected</span>
                                    <span class="f-12 normalRed selection_all"> (<span class="normalRed f-12 text-underline" onclick="selection_all()" style="cursor: pointer;">Select All</span>)</span>
                                </div>
                                <span class="f-12 link link-black black text-underline" onclick="deselect_all_exercises()">Deselect</span>
                            </div>
                        </div>
                        <div class="search-ajax-classes mt-1" id="search-ajax-classes" data-scrollbar style="max-height: 70vh;">
                            <h3 class="f-14 px-2 text-center no_result" style="display: none;">There are no exercises matching your filters. <br>Try removing some.</h3>
                            <div class="search-ajax-classes-container">
                                <?php
                                foreach($all_exercises as $single){
                                ?>
                                <div class="ajax-class exercises-class" onclick="get_checked_selected_exercises()" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" onmouseleave="remove_video_preview($(this))" onmouseenter="show_video_preview($(this))">
                                    <span class="video_preview_tooltip">
                                        <video loop muted playsinline data-src="<?php echo $single['video_preview']; ?>" poster="<?php echo (isset($single['image']) AND $single['image'] != '') AND $_ENV['CI_ENVIRONMENT'] == 'production' ? $single['image'] : ((isset($single['video_thumb']) AND $single['video_thumb'] != '' AND $_ENV['CI_ENVIRONMENT'] == 'production') ? $single['video_thumb'] : ''); ?>" class="video_preview_player" />
                                    </span>
                                    <div class="checkbox contact-forms">
                                        <input type="checkbox" class="exercises-class-checkbox" id="exercise_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove="" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                                        <label for="exercise_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                    </div>
                                    <span class="flex aic jcsb w100">
                                        <a href="javascript:;" class="f-12 text-left" data-popup="bulk-exercise-info" onclick="bulk_exercise_info(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 90%;display: block;font-weight: 500;margin-bottom: 1px;">
                                            <?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?><?php echo (isset($single['aka']) AND $single['aka'] != '') ? " (" . $single['aka'] . ")" : ''; ?>
                                        </a>
                                        <a href="/admin/exercises/edit/<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" class="external-url" style="width: 14px;height: 14px;" target="_blank" data-tooltip="Edit exercise">
                                            <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 122.6 122.88" style="enable-background:new 0 0 122.6 122.88" xml:space="preserve"><g><path d="M110.6,72.58c0-3.19,2.59-5.78,5.78-5.78c3.19,0,5.78,2.59,5.78,5.78v33.19c0,4.71-1.92,8.99-5.02,12.09 c-3.1,3.1-7.38,5.02-12.09,5.02H17.11c-4.71,0-8.99-1.92-12.09-5.02c-3.1-3.1-5.02-7.38-5.02-12.09V17.19 C0,12.48,1.92,8.2,5.02,5.1C8.12,2,12.4,0.08,17.11,0.08h32.98c3.19,0,5.78,2.59,5.78,5.78c0,3.19-2.59,5.78-5.78,5.78H17.11 c-1.52,0-2.9,0.63-3.91,1.63c-1.01,1.01-1.63,2.39-1.63,3.91v88.58c0,1.52,0.63,2.9,1.63,3.91c1.01,1.01,2.39,1.63,3.91,1.63h87.95 c1.52,0,2.9-0.63,3.91-1.63s1.63-2.39,1.63-3.91V72.58L110.6,72.58z M112.42,17.46L54.01,76.6c-2.23,2.27-5.89,2.3-8.16,0.07 c-2.27-2.23-2.3-5.89-0.07-8.16l56.16-56.87H78.56c-3.19,0-5.78-2.59-5.78-5.78c0-3.19,2.59-5.78,5.78-5.78h26.5 c5.12,0,11.72-0.87,15.65,3.1c2.48,2.51,1.93,22.52,1.61,34.11c-0.08,3-0.15,5.29-0.15,6.93c0,3.19-2.59,5.78-5.78,5.78 c-3.19,0-5.78-2.59-5.78-5.78c0-0.31,0.08-3.32,0.19-7.24C110.96,30.94,111.93,22.94,112.42,17.46L112.42,17.46z"/></g></svg>
                                        </a>
                                        <span class="link link-midGray midGray text-underline f-10 normal" onclick="$('.exercise_old_title').text($(this).prev().prev().text());$('#rename_id').val('<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>');$('#exercise_title').val('')" data-popup="rename-popup">Rename</span>
                                    </span>
                                </div>
                                <?php
                                }
                                ?>
                                <!-- <div class="f-14 bold text-center w100 loadMore py-5"></div> -->
                                <!-- <div class="f-14 bold text-center w100 loadMore py-5">LOADING...</div> -->
                                <div class="flex aic jcc p-2 load_more_click_container">
                                    <a href="javascript:;" class="btn black-bg white ml-0 f-10 load_more_click">LOAD MORE</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 bulkedit-box bulkedit-right">
                        <div class="">
                            <h5 class="f-14 semibold flex aic jcsb lh-small rb-title">BULK EDIT</h5>
                        </div>
                        <div class="row ml-mob-0 nomarleftright">
                        <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">machine</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('machines')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">accessories</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('accessories')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">aka</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-aka">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">description</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-description">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">spring load</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('springs')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">tempo count</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('tempo')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">Difficulty</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('difficulty')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">exercise type</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('exercise_type')">EDIT</a>
                            </div>      
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">Body Position</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('body_position')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">direction</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('direction')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">Terminology</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('terminology')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">TRAVEL DISTANCE</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('range_of_motion')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">tension</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('tension')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">body parts</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('body_parts')">EDIT</a>
                            </div>
                            <div class="flex aic jcsb bulkactions">
                                <span class="f-12 semibold text-uppercase">language</span>
                                <a href="javascript:;" class="btn btn-xs black-bg border-black to-border-hover white" data-popup="bulk-edit-checkboxes" onclick="checkboxes_load('languages')">EDIT</a>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="admin_assets_new/js/smooth-scrollbar.js"></script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="admin_assets_new/js/bulk_exercises.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="admin_assets_new/js/bulk_exercises.js?v=<?php //echo $_ENV['version']; ?>"></script> -->
<script>
Scrollbar.initAll({
    continuousScrolling: false,
    renderByPixels: true,
    alwaysShowTracks: true
});
function chars_left(xx) {
    var len = xx.val().length;
    $('.char_length').text(len);
    if (len > 199){
        return false;
    }
    return true;
}
function chars_count(xx) {
    var len = xx.val().length;
    console.log(len);
    $('.char_length').text(len);
    if(len > 200){
        $('.bulk-desc').addClass('border-normalRed');
    }else{
        $('.bulk-desc').removeClass('border-normalRed');
    }
}
function deselect_all_exercises(){
    $('.ajax-class input[type=checkbox]:checked').each(function(){ $(this).attr('checked', false).prop('checked', false); });
    get_checked_selected_exercises();
}
const date = "<?php echo date('Y-m-d'); ?>";
var statuss = 0;
function bulk_exercise_info(id){
    var form = $(this);
    var url = form.attr("action");
    $('.bulk-exercise-info-body').addClass('bg--loading');
    
    $.ajax({
        type: 'POST',
        url: 'admin/exercises/exercise_info/' + id,
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                $('.bulk-exercise-info-body').html(data.html);
            };
            setTimeout(function(){                
                $('.bulk-exercise-info-body').removeClass('bg--loading');
            }, 300);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function checkboxes_load(type){
    $('.bulk-edit-options').addClass('bg--loading');
    
    $.ajax({
        type: 'POST',
        url: 'admin/exercises/checkboxes_load',
        data: {
            type
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                $('.bulk-edit-options').html(data.html);
                if(type == 'springs'){
                    $('.type_of_edit').text('SPRING LOAD');
                }else if(type == 'range_of_motion'){
                    $('.type_of_edit').text('TRAVEL DISTANCE');
                }else{                    
                    $('.type_of_edit').text(type.replace('_', ' ').replace('_', ' '));
                }
                $('#checkboxes_type').val(type);
            };
            setTimeout(function(){                
                $('.bulk-edit-options').removeClass('bg--loading');
            }, 300);
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
    
}
function submit_checkboxes(xx){
    var type = xx.prev().val();
    var selected_type_items = [];
    $('.bulk-edit-options input[type=checkbox]:checked').each(function(i, e){
       selected_type_items.push($(e).data('id'));
    });
    var items = JSON.stringify(selected_type_items);
    var exercises = JSON.stringify(selected_exercises);

    $.ajax({
        type: 'POST',
        url: '/admin/exercises/bulk_update',
        data: {
            type,
            items,
            exercises
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function submit_desc(xx){
    var type = xx.prev().val();
    var selected_type_items = [];
    selected_type_items.push($('.bulk-desc').val());
    var items = JSON.stringify(selected_type_items);
    var exercises = JSON.stringify(selected_exercises);

    $.ajax({
        type: 'POST',
        url: '/admin/exercises/bulk_update',
        data: {
            type,
            items,
            exercises
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
function submit_aka(xx){
    var type = xx.prev().val();
    var selected_type_items = [];
    selected_type_items.push($('.bulk-aka').val());
    var items = JSON.stringify(selected_type_items);
    var exercises = JSON.stringify(selected_exercises);

    $.ajax({
        type: 'POST',
        url: '/admin/exercises/bulk_update',
        data: {
            type,
            items,
            exercises
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
</script>
</body>
</html>