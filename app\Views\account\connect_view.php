<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="account-page logged">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <section class="account-header">
        <div class="container800">
            <div class="row">
                <div class="col-12">
                    <div class="flex aic jcl flex-column-mob">
                        <span class="avatar150 mr-4 mb-mob-3 mr-mob-0">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column aic-mob">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 mt-1 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-0">
        <div class="container800">
            <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
            <div class="row">
                <div class="col-12">
                    <div class="py-5 bottom-border flex aic jcsb">
                        <h2 class="f-18 flex aic jcsb mob-w100 mb-mob-0 line-height-small">CONNECT</h2>
                        <div class="dropdown">
                            <span class="account-btn" data-dropdown="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                    <g id="hamburger" transform="translate(-273 -42)">
                                        <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                        <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="py-5 bottom-border">
                        <h3 class="mb-5 f-16 bold text-uppercase line-height-small">Facebook</h3>
                        <div class="row mb-5">
                            <div class="col-12">
                                <p class="medium">Connect your Facebook account</p>
                                <p class="light">This will allow you to log in via Facebook and use your Facebook avatar.</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <a id="fb_register" href="javascript:;" class="btn fb-bg-color white" title="Connect">Connect</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="py-5 bottom-border">
                        <h3 class="mb-5 f-16 bold text-uppercase line-height-small">Google</h3>
                        <div class="row mb-5">
                            <div class="col-12">
                                <p class="medium">Connect your Google account</p>
                                <p class="light">This will allow you to log in with Google.</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <a id="google_signin" href="javascript:;" class="btn btn-border white-bg black" title="Connect">Connect</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <hr class="my-80">
                <h3 class="mb-5">Apple</h3>
                <div class="row mb-5">
                    <div class="col-12">
                        <p class="medium">Connect your Apple account</p>
                        <p>This will allow you to log in with Apple.</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <a href="javascript:;" class="btn btn-border white-bg black" title="Connect">Connect</a>
                    </div>
                </div> -->
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_register.js"></script> -->
</body>
</html>