<!DOCTYPE html>
<html lang="en">
<head>
<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->
<style>
.check-holder {
	margin-left: auto;
	padding: 10px 0;
}
.disabledd {
    opacity: 0.4;
    pointer-events: none;
}
</style>
</head>
<body class="ob-body">
<?php echo view('front/templates/header.php'); ?>
<main id="onboarding-body">
    <div class="black-banner">
        <h1>HI, WELCOME ON BOARD!</h1>
        <p>Let us guide you through the platform.</p>
        <span class="stepof">STEP 2 OF 3</span>
    </div>
    <section class="pt-0">
        <div class="container">
            <div class="ob-title">
                <h4>LET'S CONTINUE WITH THE PLATFORM BASICS</h4>
                <p>This step is mandatory</p>
            </div>
            <div id="lipsum">
                <p>Hi, and Welcome to Lagree On Demand (LOD). LOD is more than your go-to site for anything Lagree; we are expanding our product and workout line as well. New classes are uploaded weekly and new exercises are uploaded monthly. You can also contact Sebastien directly and ask him anything about the method or machines. You can also put in a request for a customized workout. Most importantly, LOD is created for you and your participation (comments and rating) is important to us. Don't hesitate to share your experience with us. Tell us what you want to see more of and what you want to see less of.</p>
            </div>

            <div class="ob-title obtitle-video">
                <h4>PLATFORM FEATURES</h4>
                <p class="greytxt">Watch the video</p>
            </div>
            <div class="watch-video">
                <div class="videoWrapper">
                    <img src="images/playsingle-icon.svg" class="playtriangle" id="play-video2">
                    <div id="videoposter2" class="videoposter">
                        <div class="video-opacity"></div>
                        <img src="images/video1cover.jpg">
                    </div>
                    <iframe id="video2" src="https://www.youtube.com/embed/m5WNLQFBN38?rel=0" frameborder="0" allowfullscreen></iframe>
                </div>
                <!--end videoWrapper-->
                <div class="form-box flex">
                    <div class="check-holder" onclick="$(this).find('input').is(':checked') ? save_user_video($(this), 'second_video') : '';">
                        <input type="checkbox" id="watched2" name="watched2">
                        <label for="watched2" class="f-12">Mark as watched</label>
                    </div>
                </div>
                <!--end form-box-->
            </div>
            <!--end watch-video-->


            <div class="ob-title obtitle-video">
                <h4>THIS PLATFORM IS FOR YOU</h4>
                <p class="greytxt">Watch the video</p>
            </div>
            <div class="watch-video">
                <div class="videoWrapper">
                    <img src="images/playsingle-icon.svg" class="playtriangle" id="play-video">
                    <div id="videoposter" class="videoposter">
                        <div class="video-opacity"></div>
                        <img src="images/video2cover.jpg">
                    </div>
                    <iframe id="video" src="https://www.youtube.com/embed/X85LUC9rsEY" frameborder="0" allowfullscreen></iframe>
                </div>
                <!--end videoWrapper-->
                <div class="form-box flex">
                    <div class="check-holder" onclick="$(this).find('input').is(':checked') ? save_user_video($(this), 'first_video') : '';">
                        <input type="checkbox" id="watched1" name="watched1">
                        <label for="watched1" class="f-12">Mark as watched</label>
                    </div>
                </div>
                <!--end form-box-->
            </div>
            <!--end watch-video-->
           
            <a href="/onboarding/step3" class="survey-btn">CONTINUE</a>
            <p class="completesurvey">You need to watch videos to continue.</p>
        </div>
    </section>
</main>
<?php echo view('front/templates/footer.php'); ?>
<?php echo view('front/templates/popups.php'); ?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js" type="text/javascript"></script>
<script language="javascript" type="text/javascript">
    $(document).ready(function () {
        $("p").each(function () {
            SetMoreLess(this, 223, 20, "... <span>+ Read more</span>", "<span>- Read less</span>");
        });

        $("a.moreText").click(function () {
            $(this).hide();
            var pTag = $(this).parents("p.summary");

            $(pTag).find("a.lessText").fadeToggle();
            $(pTag).find("span.secondHalf").fadeToggle();
        });

        $("a.lessText").click(function () {
            $(this).hide();
            var pTag = $(this).parents("p.summary");

            $(pTag).find("a.moreText").fadeToggle();
            $(pTag).find("span.secondHalf").hide();
        });
    });

    function SetMoreLess(para, thrLength, tolerance, moreText, lessText) {
        var alltext = $(para).html().trim();

        $(para).addClass("summary");        // this class is added to identify the p tag, when more/less links is clicked

        if (alltext.length + tolerance < thrLength) {
            return;
        }
        else {
            var firstHalf = alltext.substring(0, thrLength);
            var secondHalf = alltext.substring(thrLength, alltext.length);

            var firstHalfSpan = '<span class="firstHalf">' + firstHalf + '</span>';
            var secondHalfSpan = '<span class="secondHalf">' + secondHalf + '</span>';
            var moreTextA = '<a class="moreText">' + moreText + '</a>';
            var lessTextA = '<a class="lessText">' + lessText + '</a>';

            var newHtml = firstHalfSpan + moreTextA + secondHalfSpan + lessTextA;

            $(para).html(newHtml);
        }
    }
</script>
<script>
$(document).ready(function() {
    $('#play-video').on('click', function(ev) {
        $('#play-video').hide();
        $('#videoposter').fadeOut(500);
        // $("#video")[0].src += "&autoplay=1";
    });

    $('#play-video2').on('click', function(ev) {
        $('#play-video2').hide();
        $('#videoposter2').fadeOut(500);
        // $("#video2")[0].src += "&autoplay=1";
    });
});

var tag = document.createElement('script');
tag.src = "https://www.youtube.com/iframe_api";
var firstScriptTag = document.getElementsByTagName('script')[0];
firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

let player;

function onYouTubeIframeAPIReady() {
    player = new YT.Player('player', { events: {
        onReady: onPlayerReady
    }});
}

function onPlayerReady() {
    $("#play-video").on('click', function(){
        console.log('clicked');
        player.playVideo();
    });
}

</script>
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
function save_user_video(t, xx){
    $.ajax({
        type: 'POST',
        url: '/surveys/save_user_video',
        data: {
            user_id: <?php echo (isset($logged_user['id']) ? $logged_user['id'] : ''); ?>,
            video: xx
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                t.parent().addClass('disabledd');
            };
            if($('#watched1').is(':checked') && $('#watched2').is(':checked')){
                $('.survey-btn').removeClass('disabledd')
            }
        },
        error: function (request, status, error) {
            console.log(data);
            console.log('PHP Error');
        }
    });
}
</script>
</body>
</html>
