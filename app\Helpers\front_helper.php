<?php
// Function: used to create slugs
if (!function_exists("duration")) {
    function duration($time = 0)
    {
        if($time < 60){
            $time_formated = gmdate("s\"", $time);
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i\' s\"", $time);
        }else if($time > 3599){
            $time_formated = substr(gmdate("H\h i\'", $time),1);
        }
        return $time_formated;
    }
}
if (!function_exists("duration_standard")) {
    function duration_standard($time = 0)
    {
        if($time < 60){
            $time_formated = '00:' . gmdate("s", $time);
        }else if($time > 59 && $time < 3600){
            $time_formated = gmdate("i:s", $time);
        }else if($time > 3599){
            $time_formated = substr(gmdate("H:i:s", $time),1);
        }
        return $time_formated;
    }
}
if (!function_exists("only_minutes")) {
    function only_minutes($time = 0)
    {
        $time = (int)$time;
        if($time < 60){
            $time_formated = 1;
        }else if($time > 59 && $time < 3600){
            $time_formated = round($time / 60);
        }else if($time > 3599){
            $hours = $time / 3600;
            $minutes = (($time - (3600 * floor($hours))) / 60);
            $seconds = (($time - (3600 * floor($hours)) - (60 * floor($minutes))) / 60);
            $time_formated = floor($hours) . 'h ' . floor($minutes);
        }
        return $time_formated;
    }
}
if (!function_exists("slugify")) {
    function slugify($text = '')
    {
        // replace non letter or digits by -
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);
        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);
        // trim
        $text = trim($text, '-');
        // remove duplicate -
        $text = preg_replace('~-+~', '-', $text);
        // lowercase
        $text = strtolower($text);
        if (empty($text)) {
            return 'n-a';
        }
        return $text;
    }
}
if (!function_exists("rentTimeDifference")) {
    function rentTimeDifference($start){
        $diff = strtotime($start . " + 1 day") - strtotime('now');
        $fullHours   = floor($diff/(60*60));
        $fullMinutes = floor(($diff-($fullHours*60*60))/60);
        $fullSeconds = floor($diff-($fullHours*60*60)-($fullMinutes*60));

        return sprintf("%02d",$fullHours) . ":" . sprintf("%02d",$fullMinutes) . ":" . sprintf("%02d",$fullSeconds);
    }
}
if (!function_exists('time_ago')){
	function time_ago($time){
		$periods = array("s", " min", "h", " days", " weeks", " months", " year", " decade");
		$lengths = array("60","60","24","7","4.35","12","10");
		$now = time();
        $difference     = $now - $time;

		for($j = 0; $difference >= $lengths[$j] && $j < count($lengths)-1; $j++) {
			$difference /= $lengths[$j];
		}
		$difference = round($difference);

		return ($difference < 0 ? 0 : $difference) . $periods[$j] . " ago ";
	}
}
if (!function_exists("percentage")) {
    function percentage($num = 0, $total = 0)
    {
        $percentage = ($num*100)/$total;
        return $percentage;
    }
}
if (!function_exists('user_initials')){
	function user_initials($id){
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = substr($user['firstname'], 0, 1) . substr($user['lastname'], 0, 1);
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('user_avatar')){
	function user_avatar($id){
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = $user['image'];
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('user_info')){
	function user_info($id){
        $SubscribersModel = model('SubscribersModel');
        if($id != 0){
            $user = $SubscribersModel->where(["id" =>  $id])->first();
        }
        if($user){
            $response = $user;
        }else{
            $response = NULL;
        }

        return $response;
	}
}
if (!function_exists('random_slug')){
	function random_slug(){
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < 8; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
	}
}
if (!function_exists('coupon_products')){
	function coupon_products($products){
        $env_products = [
            $_ENV['product_year'] => 'Annual',
            $_ENV['product_month'] => 'Monthly',
            $_ENV['product_week'] => 'Weekly'
        ];
        $env_products_codes = [
            'Annual' => $_ENV['product_year'],
            'Monthly' => $_ENV['product_month'],
            'Weekly' => $_ENV['product_week']
        ];

        if(is_array($products)){
            $products_text = array_keys(array_intersect($env_products_codes, $products));
        }else{
            $products_text = $env_products[$products];
        }

        return $products_text;
	}
}
if (!function_exists('create_session_nums')){
	function create_session_nums(){
        $result['broj1'] = rand(5,25);
        $result['broj2'] = rand(5,25);
        session()->set("broj1", $result['broj1']);
        session()->set("broj2", $result['broj2']);

        return $result;
	}
}
if (!function_exists('createRange')){
    function createRange($startHeight, $endHeight){
        $difference = $endHeight - $startHeight;
        $resultsArray = [];
        for($i=0;$i<$difference;$i++)
        {
            $currentHeight = $startHeight + $i;
            $remainder = ($currentHeight % 12);
            $numberOfFeet = ($currentHeight - $remainder)/12;
            $feetString = $numberOfFeet.'&apos;'.$remainder.'&quot;';
            $resultsArray[$i]['key'] = $currentHeight;
            $resultsArray[$i]['value'] = $feetString;
        }
        return $resultsArray;
    }
}
if (!function_exists('inchToHeight')){
    function inchToHeight($inch){
        $currentHeight = $inch;
        $remainder = ($currentHeight % 12);
        $numberOfFeet = ($currentHeight - $remainder)/12;
        $feetString = $numberOfFeet.'&apos;'.$remainder.'&quot;';
        $resultsArray = $feetString;

        return $resultsArray;
    }
}
if (!function_exists('resize_image')){

    /* USAGE
        resize_image(
            $_SERVER['DOCUMENT_ROOT'] . 'images/' . $image_name,
            $_SERVER['DOCUMENT_ROOT'] . 'images/thumb_' . $image_name,
            $image_name,
            'thumb_' . $image_name,
            400, // width
            266 // height
        );
    */
    function resize_image($source_image_path, $destination_image_path, $image_name, $new_image_name, $max_width, $max_height, $quality = 80) {

        if(!file_exists($destination_image_path)){
            // Create a new Imagick object
            $image = new Imagick($source_image_path);

            // Get the original image dimensions
            $orig_width = $image->getImageWidth();
            $orig_height = $image->getImageHeight();

            // Calculate the new image dimensions while maintaining the aspect ratio
            $width_ratio = $max_width / $orig_width;
            $height_ratio = $max_height / $orig_height;
            $ratio = min($width_ratio, $height_ratio);
            $new_width = $orig_width * $ratio;
            $new_height = $orig_height * $ratio;

            // Resize the image
            $image->resizeImage($new_width, $new_height, Imagick::FILTER_BOX, 1);

            // Set the image compression quality
            $image->setImageCompressionQuality($quality);

            // Save the resized image to the destination path
            $image->writeImage($destination_image_path);

            // Free up memory
            $image->clear();
            $image->destroy();

            // Return the destination image path
            return $new_image_name;
        }else{
            return $image_name;
        }
    }
}
if (!function_exists('exercises_count_menu')){
    function exercises_count_menu(){
        $ExercisesMachinesModel = model('ExercisesMachinesModel');
        $ExercisesModel = model('ExercisesModel');

        $resultsArray = $ExercisesMachinesModel->query("SELECT 
                                                            m.id AS machine_id, 
                                                            COUNT(e.id) AS exercise_count
                                                        FROM machines m
                                                        LEFT JOIN (
                                                            SELECT DISTINCT exercise_id, exercise_machine 
                                                            FROM exercises_machine
                                                        ) em ON m.id = em.exercise_machine
                                                        LEFT JOIN exercises e ON em.exercise_id = e.id AND e.status = 0 AND e.deleted_at IS NULL
                                                        GROUP BY m.id, m.title
                                                        ORDER BY exercise_count DESC"
                                                        )->getResultArray();
        
        foreach($resultsArray as $key => $value){
            $resultsArray[$value['machine_id']] = $value['exercise_count'];
        }
        $resultsArray[1000] = $ExercisesModel->where(['status' => 0])->countAllResults();

        return $resultsArray;
    }
}
if (!function_exists('classes_count_menu')){
    function classes_count_menu(){
        $ClassesMachinesModel = model('ClassesMachinesModel');
        $ClassesModel = model('ClassesModel');

        $resultsArray = $ClassesMachinesModel->query("SELECT 
                                                            m.id AS machine_id, 
                                                            COUNT(e.id) AS class_count
                                                        FROM machines m
                                                        LEFT JOIN (
                                                            SELECT DISTINCT class_id, class_machine 
                                                            FROM classes_machine
                                                        ) em ON m.id = em.class_machine
                                                        LEFT JOIN classes e ON em.class_id = e.id AND e.status = 0 AND e.deleted_at IS NULL
                                                        GROUP BY m.id, m.title
                                                        ORDER BY class_count DESC"
                                                        )->getResultArray();
        
        foreach($resultsArray as $key => $value){
            $resultsArray[$value['machine_id']] = $value['class_count'];
        }
        $resultsArray[1000] = $ClassesModel->where(['status' => 0])->countAllResults();

        return $resultsArray;
    }
}
