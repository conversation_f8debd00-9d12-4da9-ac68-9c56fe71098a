<?php
$uri = service('uri');
$url = $uri->getPath();
$segment = $uri->getSegment(3);
?>
<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-3 pt-85 border-bottom">
            <div class="flex aic jcsb minH45">
                <h1 class="h3 mb-05">Classes</h1>
                <!-- <div class="ml-auto mb-05">
                    <a href="admin/classes/edit" class="btn black-bg white" title="Upload">Upload</a>
                    <a href="admin/classes/multi" class="btn btn-border white-bg black ml-2" title="Bulk">Bulk</a>
                </div> -->
            </div>
            <hr class="mt-80 mb-3">
            <div class="row">
                <div class="col-12">
                    <?php 
                    // echo '<pre>';
                    // print_r($bunny_videos);
                    // die();
                    
                    // $ids = [];
                    if(isset($bunny_videos->items)){
                        foreach($bunny_videos->items as $single){
                            // $ids[] = $single->guid;
                    ?>
                    <div class="my-3">
                        <iframe
                            src="https://iframe.mediadelivery.net/embed/347156/<?php echo $single->guid; ?>?autoplay=false"
                            width="500"
                            height="280"
                            frameborder="0"
                            allow="autoplay; encrypted-media"
                            allowfullscreen>
                        </iframe>
                        <p><?php echo $single->title; ?></p>
                    </div>
                    <?php
                        }
                        // echo implode('<br>', $ids);
                    }
                    ?>
                </div>
            </div>
            <div class="row mt-5 pt-5">
                <div class="col-12">
                    <form action="/admin/classes/bunny_videos_upload" method="post" enctype="multipart/form-data">
                        <label for="videoTitle" class="f-12">Video Title:</label>
                        <input type="text" name="videoTitle" id="videoTitle" class="line-input" required><br><br>
                        <label for="videoFile">Select Video File:</label>
                        <input type="file" name="videoFile" id="videoFile" accept="video/*" required>
                        <div class="mt-3">
                            <input type="submit" value="Upload Video" class="btn black-bg white">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
var per_page = <?php echo (session('classes_per_page') == "") ? 25 : session('classes_per_page'); ?>;
var sort_by = '<?php echo (session('classes_sort') == "") ? 'classes.created_at/desc' : session('classes_sort'); ?>';
var search = '<?php echo (session('classes_search') == "") ? '' : session('classes_search'); ?>';
var order = '<?php echo (session('classes_sort') == "") ? 'classes.created_at/desc' : session('classes_sort'); ?>';
</script>
</body>
</html>