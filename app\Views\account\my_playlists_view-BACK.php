<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('front/templates/head.php'); ?>
<!-- <link id="theme-css" href="css/swiper-bundle.min.css" rel="stylesheet" type="text/css" /> -->

</head>
<body class="collection-page">

<?php echo view('front/templates/header.php'); ?>

<main id="site-root">
    <?php if(session('subscription') != NULL AND session('subscription') != 'active'){ ?>
    <div class="red_bar" style="background: #DB1818;">
        You don't have an active subscription. <a href="/account/payments" class="text-underline white">See plans</a>
    </div>        
    <?php } ?>
    <section class="account-header">
        <div class="row w100">
            <div class="account-hero">
                <div class="col-12">
                    <div class="flex aic jcl">
                        <span class="avatar120 mr-4 mr-mob-2">
                            <!-- <img src="images/big-avatar.jpg" alt="" class="img-fluid" /> -->
                            <?php if(isset($logged_user['image']) AND $logged_user['image'] != ''){ ?>
                                <img src="<?php echo $logged_user['image']; ?>" alt="" class="img-fluid" />
                            <?php }else{ ?>
                                <span class="initials bold"><?php echo user_initials($logged_user['id']);?></span>
                            <?php } ?>
                        </span>
                        <div class="flex flex-column aic-mob">
                            <p class="line-height-small f-24 white bold text-uppercase">HI, <?php echo $logged_user['firstname'] ; ?></p>
                            <p class="line-height-small f-12 white m-0 mt-1 text-uppercase"><?php echo str_replace('Subscription', 'plan', $logged_user['subscription_type']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pt-05 pt-0 pb-05 mbsec flex jcsb account-content">
    <div class="lodacc-menu"><?php echo view('front/templates/account-left-menu.php'); ?></div>
        <div class="container750">
            <div class="row mx-0 top-border-mob">
                <div class="col-12 pl-0">
                    <div class="account-main-title">
                        <h2 class="f-18 flex aic jcsb mob-w10 line-height-small semibold">MY PLAYLISTS</h2>
                        <div class="flex aic jcr newplaylist-btn">
                            <a href="javascript:;" class="btn black-bg white mr-2 new_user_playlist" data-popup="edit-playlist-popup" data-popup-title="Create Playlist" title="NEW PLAYLIST" onclick="$('.edit-playlist-popup').find('form').trigger('reset');$('.edit-playlist-popup').find('.image_preview').removeClass('has_image');$('.edit-playlist-popup').find('.image_preview').attr('src', 'admin_assets_new/images/upload-icon.svg');$('.edit-playlist-popup').find('.image_options').hide();$('.create_add_button').text('CREATE PLAYLIST')">NEW PLAYLIST</a>
                            <div class="dropdown">
                                <span class="account-btn" data-dropdown="">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10" viewBox="0 0 22 16" class="acc-menu-icon" style="margin-left: 2px;">
                                        <g id="hamburger" transform="translate(-273 -42)">
                                            <rect width="22" height="2" rx="1" transform="translate(273 49)" fill="#000"/>
                                            <rect width="17" height="2" rx="1" transform="translate(273 56)" fill="#000"/>
                                            <rect width="17" height="2" rx="1" transform="translate(273 42)" fill="#000"/>
                                        </g>
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="pb-5">
<?php foreach($all_playlists as $single){ ?>
                        <div class="single-user-playlist" data-playlist_id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="single-user-playlist-item">
                                <div class="row position-relative w100 ml-0">
                                    <div class="col-12 flex aic px-0">
                                        <img src="images/playlists-icon.svg" alt="" class="img-fluid mr-15" />
                                        <h4 class="f-14 bold text-uppercase cursor line-height-small" onclick="$(this).closest('.single-user-playlist').find('.single-user-playlist-content').slideToggle();$(this).closest('.single-user-playlist').find('.circle-arrow').toggleClass('opened')"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?></h4>
                                        <div class="flex aic ml-auto">
                                            <?php if($single['ownership'] == 'my'){ ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18.542" height="20" viewBox="0 0 18.542 20" title="My playlist" style="width: 15px;height: 15px;margin-right: 15px;" class="user-icon">
                                                    <g id="Group_4752" data-name="Group 4752" transform="translate(-543.047 -32.6)">
                                                        <g id="Group_4749" data-name="Group 4749" transform="translate(543.047 32.6)">
                                                        <path id="Path_1058" data-name="Path 1058" d="M134.922,9.844A4.922,4.922,0,1,0,130,4.922a4.922,4.922,0,0,0,4.922,4.922Zm0-8.378a3.455,3.455,0,1,1-3.455,3.455,3.455,3.455,0,0,1,3.455-3.455Z" transform="translate(-125.651 0)"></path>
                                                        <path id="Path_1059" data-name="Path 1059" d="M20.127,283.935a7.8,7.8,0,0,1,15.606,0H37.2a9.271,9.271,0,0,0-18.542,0Z" transform="translate(-18.659 -263.935)"></path>
                                                        </g>
                                                    </g>
                                                </svg>
                                            <?php } ?>
                                            <?php if($single['private'] == 1){ ?>
                                            <img src="images/lock-dark.svg" alt="" class="img-fluid mr-15" title="Private">
                                            <?php } ?>
                                            <span class="circle-arrow" onclick="$(this).closest('.single-user-playlist').find('.single-user-playlist-content').slideToggle();$(this).toggleClass('opened')"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="single-user-playlist-content" style="display: none;">
                                <div class="row aic">
                                    <div class="col-12 flex flex-column-mob">
                                        <div class="big-content-img" <?php echo (isset($single['image']) AND $single['image'] != '') ? '' : 'style="background: #f0f0f0;display: flex;align-items: center;justify-content: center;"'; ?>>
                                            <img src="<?php echo (isset($single['image']) AND $single['image'] != '') ? $single['image'] : 'images/playlist-bg-x2.jpg'; ?>" alt="" class="img-fluid" />
                                            <?php echo (isset($single['image']) AND $single['image'] != '') ? '' : '<span class="playlist-empty-title">' . $single['title'] . '</span>'; ?>
                                        </div>
                                        <div class="lightGray-bg w100 position-relative px-5 flex flex-column ail jcc px-mob-2 mt-mob-0 pb-mob-5 pt-mob-2 mb-mob-2">
                                            <div class="playlist-option dropdown">
                                                <span class="option-btn" data-dropdown>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                                        <g id="option-icon" transform="translate(-1501 -625)">
                                                            <g id="Rectangle_2141" data-name="Rectangle 2141" transform="translate(1501 625)" fill="none" stroke="#f0f0f0" stroke-width="1" style="fill: #fff;">
                                                                <rect width="30" height="30" rx="15" stroke="none"/>
                                                                <rect x="0.5" y="0.5" width="29" height="29" rx="14.5" fill="none"/>
                                                            </g>
                                                            <circle id="Ellipse_50" data-name="Ellipse 50" cx="1" cy="1" r="1" transform="translate(1511 639)"/>
                                                            <circle id="Ellipse_51" data-name="Ellipse 51" cx="1" cy="1" r="1" transform="translate(1515 639)"/>
                                                            <circle id="Ellipse_52" data-name="Ellipse 52" cx="1" cy="1" r="1" transform="translate(1519 639)"/>
                                                        </g>
                                                    </svg>
                                                </span>
                                                <ul class="dropdown-menu drop-right">
                                                    <?php if($single['private'] != 1){ ?>
                                                    <li><a href="javascript:;" class="f-12" data-popup="share-popup" data-share-link="<?php echo base_url() . '/playlists/' . $single['slug'] ?>" data-popup-title="Share This Playlist With Your Friends." title="">Share</a></li>
                                                    <?php } ?>
                                                    <?php if($single['ownership'] == 'my'){ ?>
                                                        <li><a href="javascript:;" class="f-12" data-popup="edit-playlist-popup" data-popup-title="Edit Playlist" onclick="load_playlist(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" title="Edit playlist">Edit playlist</a></li>
                                                        <li><a href="javascript:;" class="f-12 delete_record" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-table="playlists" data-popup="delete-popup" title="Delete">Delete</a></li>
                                                    <?php }else{ ?>
                                                        <li><a href="javascript:;" class="f-12 remove_playlist" data-id="<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" title="Remove From My List">Remove from my list</a></li>
                                                    <?php } ?>
                                                </ul>
                                            </div>
                                            <h2 class="mb-15 h3 line-height-small"><?php echo (isset($single['title']) AND $single['title'] != '') ? $single['title'] : ''; ?> PLAYLIST</h2>
                                            <?php if(isset($single['content']) AND $single['content'] != ''){ ?>
                                                <p class="light f-14 line-height-normal mb-15"><?php echo $single['content']; ?></p>
                                            <?php } ?>
                                            <p class="light f-14 line-height-small mb-0 mt-0 midGray"><?php echo count($single['all_playlists_classes']); ?> Classes (<?php echo only_minutes($single['total_duration']); ?> minutes), By: <?php echo (isset($single['users_name']) AND $single['users_name'] != '') ? $single['users_name'] : 'LagreeOD staff'; ?></p>
                                            <?php if($single['private'] == 1){ ?>
                                                <p class="f-12 flex aic line-height-small"><img src="images/lock-dark.svg" style="position: relative;top: -2px;" class="mr-1"> NOT PUBLIC</p>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                                <hr class="mt-5 mb-0 mt-mob-1">
                                <div class="row big-big-gap sortable">
                                    <?php
                                    foreach($single['all_playlists_classes'] as $single2){
                                    ?>
                                        <div class="col-12 <?php echo $single2['slug_type']; ?> playlist-class-container" data-playlist_class_id="<?php echo $single['id']; ?>-<?php echo $single2['id']; ?>" data-rowid="<?php echo (isset($single2['csc_id']) AND $single2['csc_id'] != '') ? $single2['csc_id'] : ''; ?>" data-id="<?php echo (isset($single2['id']) AND $single2['id'] != '') ? $single2['id'] : ''; ?>" data-type="<?php echo (isset($single2['type']) AND $single2['type'] != '') ? $single2['type'] : ''; ?>" data-sort="<?php echo (isset($single2['sort']) AND $single2['sort'] != '') ? $single2['sort'] : ''; ?>">
                                            <div class="single-playlist-list-item <?php echo $single2['slug_type']; ?>">
                                                <a href="playlists/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>/<?php echo substr($single2['slug_type'], 0, 1); ?>_<?php echo (isset($single2['slug']) AND $single2['slug'] != '') ? $single2['slug'] : ''; ?>" class="video-container">
                                                    <?php
                                                        if(isset($single2['watched']) AND $single2['watched'] == 1){
                                                    ?>
                                                        <span class="watched f-14 bold lettet-50 white flex aic"><img src="images/watch-again.svg" alt="" style="width: 20px;object-fit: cover;height: 20px;object-position: left center;" class="img-fluid mr-1" /></span>
                                                    <?php
                                                        }else{
                                                    ?>
                                                        <span class="play-button"><span></span></span>
                                                    <?php } ?>
                                                    <div class="image-overlay h100"><img src="<?php echo (isset($single2['image']) AND $single2['image'] != '') ? $single2['image'] : ((isset($single2['video_thumb']) AND $single2['video_thumb'] != '') ? $single2['video_thumb'] : ''); ?>" alt="<?php echo (isset($single2['title']) AND $single2['title'] != '') ? $single2['title'] : ''; ?>" class="img-fluid" <?php echo (isset($single2['watched']) AND $single2['watched'] == 1) ? 'style="opacity: 0.3"' : ''; ?> /></div>
                                                    <!-- LOCKED -->
                                                    <!-- <?php if(!empty($logged_user)){ ?>
                                                        <?php if($single2['type'] == 0){ ?>
                                                            <?php if(empty($logged_user) OR NULL === session('subscription') OR session('subscription') != 'active'){ ?>
                                                                <span class="locked"></span>
                                                            <?php } ?>
                                                        <?php }else{ ?>
                                                            <?php if($single2['own'] == 1 OR $single2['purchased'] == 1){ ?>
                                                            <?php }else{ ?>
                                                                <span class="locked"></span>
                                                            <?php } ?>
                                                        <?php } ?>
                                                    <?php }else{ ?>
                                                        <span class="locked"></span>
                                                    <?php } ?>
                                                    -->
                                                </a>
                                                <div class="video-text-container">
                                                    <a href="playlists/<?php echo (isset($single['slug']) AND $single['slug'] != '') ? $single['slug'] : ''; ?>/<?php echo substr($single2['slug_type'], 0, 1); ?>_<?php echo (isset($single2['slug']) AND $single2['slug'] != '') ? $single2['slug'] : ''; ?>">
                                                        <h4 class="flex jcsb bold mb-1 f-12 ail"><?php echo (isset($single2['title']) AND $single2['title'] != '') ? $single2['title'] : ''; ?></h4>
                                                    </a>
                                                    <p class="midGray f-12 mb-0 light line-height-small">
                                                        <span class="d-inline-block light line-height-small">
                                                            by:
                                                            <?php if(isset($single2['teach_slug']) AND $single2['teach_slug'] != ''){ ?>
                                                                <a href="teachers/<?php echo (isset($single2['teach_slug']) AND $single2['teach_slug'] != '') ? $single2['teach_slug'] : ''; ?>" class="link link-black black text-underline d-inline-block"><?php echo (isset($single2['teach']) AND $single2['teach'] != '') ? $single2['teach'] : 'NO TEACHER'; ?></a>,
                                                            <?php } ?>
                                                            <?php echo (isset($single2['duration']) AND $single2['duration'] != '') ? '<span class="d-inline-block light d-block-mob mt-mob-1 line-height-small">' . only_minutes($single2['duration']) . ' minutes</span>' : ''; ?>
                                                        </span>
                                                    </p>
                                                    <?php if($single['ownership'] == 'my'){ ?>
                                                    <span class="f-12 link link-black" onclick="remove_class_from_my_playlist(<?php echo $single['id']; ?>, <?php echo $single2['id']; ?>, '<?php echo $single2['type']; ?>')">Remove</span>
                                                    <?php } ?>
                                                </div>
                                                <?php if($single['ownership'] == 'my'){ ?>
                                                <img src="admin_assets_new/images/hamburger.svg" alt="" class="img-fluid ml-auto handle">
                                                <?php } ?>
                                            </div>
                                        </div>
                                    <?php
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
<?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php echo view('front/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="admin_assets_new/js/jquery.ui.touch-punch.js"></script>
<!-- <script src="js/swiper-bundle.min.js"></script> -->
<script src="js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script src="js/playlists.js?v=<?php echo $_ENV['version']; ?>"></script>
<!-- <script src="js/fb_register.js"></script>
<script src="js/fb_login.js"></script>
<script src="js/google_login.js"></script> -->
</body>
</html>