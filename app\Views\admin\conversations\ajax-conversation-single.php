<?php
foreach($conversation as $single){
    if($single['sender_id'] != 0){
?>
                <div class="single-msg user-msg <?php echo $single['admin_seen'] == 0 ? 'unread' : ''; ?>" data-message_id="<?php echo $single['id']; ?>" data-type="<?php echo $single['type']; ?>">
                    <div class="avatar40">
                        <?php echo user_avatar($single['sender_id']) != NULL ? '<img src="' . user_avatar($single['sender_id']) . '" class="img-fluid" />' : '<span class="initials no-border black-bg f-1 bold white">' . user_initials($single['sender_id']) . '</span>' ; ?>
                    </div>
                    <div class="msg-area">
                    <?php if(isset($single['type']) AND $single['type'] == 'url'){ ?>
                        <div class="message with_url">
                            <a href="<?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?>" target="_blank" class="link link-black black text-underline"><?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?></a>
                        </div>
                    <?php }else if(isset($single['type']) AND $single['type'] == 'file'){ ?>
                        <div class="message with_file">
                            <a href="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" download class="link link-black black text-underline"><?php echo (isset($single['file_name']) AND $single['file_name'] != '') ? $single['file_name'] : ''; ?></a>
                        </div>
                    <?php }else if(isset($single['type']) AND $single['type'] == 'img'){ ?>
                        <div class="message with_img">
                            <a href="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" download class="link link-black black text-underline">
                                <img src="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" class="" /></img>
                            </a>
                        </div>
                    <?php }else{ ?>
                        <div class="message">
                            <?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?>
                        </div>
                    <?php } ?>
                        <div class="msg-date">
                            Received on: <?php echo (isset($single['date']) AND $single['date'] != '') ? date('m/d/Y', strtotime($single['date'])) : ''; ?>
                        </div>
                    </div>
                </div>
<?php
    }else{
?>
                <div class="single-msg seb-msg" data-message_id="<?php echo $single['id']; ?>">
                    <div class="avatar40">
                        <img src="images/seb-msg.png" alt="" class="img-fluid" />
                    </div>
                    <div class="msg-area">
                    <?php if(isset($single['type']) AND $single['type'] == 'url'){ ?>
                        <div class="message with_url">
                            <a href="<?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?>" target="_blank" class="link link-black black text-underline"><?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?></a>
                        </div>
                    <?php }else if(isset($single['type']) AND $single['type'] == 'file'){ ?>
                        <div class="message with_file">
                            <a href="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" download class="link link-black black text-underline "><?php echo (isset($single['file_name']) AND $single['file_name'] != '') ? $single['file_name'] : ''; ?></a>
                        </div>
                    <?php }else if(isset($single['type']) AND $single['type'] == 'img'){ ?>
                        <div class="message with_img">
                            <a href="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" download class="download_img_icon">
                                <img src="<?php echo (isset($single['file']) AND $single['file'] != '') ? $single['file'] : ''; ?>" class="" /></img>
                            </a>
                        </div>
                    <?php }else{ ?>
                        <div class="message">
                            <?php echo (isset($single['message']) AND $single['message'] != '') ? nl2br($single['message']) : ''; ?>
                        </div>
                    <?php } ?>
                        <div class="flex-vertical">
                            <div class="msg-date">
                                Sent on: <?php echo (isset($single['date']) AND $single['date'] != '') ? date('m/d/Y', strtotime($single['date'])) : ''; ?>
                            </div>
                            <!-- <div class="msg-useful">
                                <?php // if($single['useful'] != 0){ ?>
                                    <img src="images/heart-full.svg" alt="" class="img-fluid mr-1" /> Useful answer.
                                <?php // } ?>
                            </div> -->
                        </div>
                    </div>
                </div>
<?php
    }
}
?>
