<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

// Load the system's routing file first, so that the app and ENVIRONMENT
// can override as needed.
if (file_exists(SYSTEMPATH . 'Config/Routes.php'))
{
	require SYSTEMPATH . 'Config/Routes.php';
}

/**
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Pages');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(true);
// $routes->set404Override();
$routes->set404Override(function(){
    echo view('front/pages/404_view');
});
$routes->setAutoRoute(true);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Pages::index');
$routes->get('/', 'SendMail::index');

$routes->group('admin', ['filter' => 'adminAuthFilter:dual,noreturn,blocked'], function($routes){
    $routes->group('dashboard', function ($routes) {
        $routes->add('', 'Admin\Dashboard::index');
        $routes->add('(:any)', 'Admin\Dashboard::$1');
    });
    $routes->group('classes', function ($routes) {
        $routes->add('', 'Admin\Classes::index');
        $routes->add('load_more_exercises', 'Admin\Classes::load_more_exercises');
        $routes->add('load_more_exercises/(:any)', 'Admin\Classes::load_more_exercises/$1');
        $routes->add('load_more_exercises/(:any)/(:any)', 'Admin\Classes::load_more_exercises/$1/$2');
        $routes->add('(:any)', 'Admin\Classes::$1');
    });
    $routes->group('courses', function ($routes) {
        $routes->add('', 'Admin\Courses::index');
        $routes->add('ajax_delete/(:any)/(:any)', 'Admin\Courses::ajax_delete/$1/$2');
        $routes->add('(:any)', 'Admin\Courses::$1');
        // $routes->add('save_video_item', 'Admin\Courses::save_video_item');
    });
    $routes->group('buy_rent', function ($routes) {
        $routes->add('', 'Admin\BuyRent::index');
        $routes->add('(:any)', 'Admin\BuyRent::$1');
    });
    $routes->group('featuredvideos', function ($routes) {
        $routes->add('', 'Admin\FeaturedVideos::index');
        $routes->add('(:any)', 'Admin\FeaturedVideos::$1');
    });
    $routes->group('comments', function ($routes) {
        $routes->add('', 'Admin\Comments::index');
        $routes->add('(:any)', 'Admin\Comments::$1');
        $routes->add('(:any)/(:any)', 'Admin\Comments::$1/$2');
    });
    $routes->group('exercises', function ($routes) {
        $routes->add('', 'Admin\Exercises::index');
        $routes->add('(:any)', 'Admin\Exercises::$1');
        $routes->add('(:any)/(:any)', 'Admin\Exercises::$1/$2');
    });
    $routes->group('howto', function ($routes) {
        $routes->add('', 'Admin\Howto::index');
        $routes->add('(:any)', 'Admin\Howto::$1');
    });
    $routes->group('surveys', function ($routes) {
        $routes->add('', 'Admin\Surveys::index');
        $routes->add('(:any)', 'Admin\Surveys::$1');
        $routes->add('(:any)/(:any)', 'Admin\Surveys::$1/$2');
    });
    $routes->group('routines', function ($routes) {
        $routes->add('', 'Admin\Routines::index');
        $routes->add('(:any)', 'Admin\Routines::$1');
        $routes->add('(:any)/(:any)', 'Admin\Routines::$1/$2');
        $routes->add('(:any)/(:any)/(:any)', 'Admin\Routines::$1/$2/$3');
    });
    $routes->group('studios', function ($routes) {
        $routes->add('', 'Admin\Studios::index');
        $routes->add('(:any)', 'Admin\Studios::$1');
        $routes->add('(:any)/(:any)', 'Admin\Studios::$1/$2');
    });
    $routes->group('models', function ($routes) {
        $routes->add('', 'Admin\Models::index');
        $routes->add('(:any)', 'Admin\Models::$1');
        $routes->add('(:any)/(:any)', 'Admin\Models::$1/$2');
    });
    $routes->group('calendar', function ($routes) {
        $routes->add('', 'Admin\Calendar::index');
        $routes->add('(:any)', 'Admin\Calendar::$1');
        $routes->add('(:any)/(:any)', 'Admin\Calendar::$1/$2');
    });
    $routes->group('collections', function ($routes) {
        $routes->add('', 'Admin\Collections::index');
        $routes->add('(:any)', 'Admin\Collections::$1');
    });
    $routes->group('surveys_questions', function ($routes) {
        $routes->add('', 'Admin\Collections::index');
        $routes->add('ajax_delete/(:any)/(:any)', 'Admin\Surveys::ajax_delete/$1/$2');
        $routes->add('(:any)', 'Admin\Collections::$1');
    });
    $routes->group('playlists', function ($routes) {
        $routes->add('', 'Admin\Playlists::index');
        $routes->add('(:any)', 'Admin\Playlists::$1');
    });
    $routes->group('liveevents', function ($routes) {
        $routes->add('', 'Admin\LiveEvents::index');
        $routes->add('(:any)', 'Admin\LiveEvents::$1');
    });
    $routes->group('subscribers', function ($routes) {
        $routes->add('', 'Admin\Subscribers::index');
        $routes->add('(:any)', 'Admin\Subscribers::$1');
    });
    $routes->group('teachers', function ($routes) {
        $routes->add('', 'Admin\Teachers::index');
        $routes->add('(:any)', 'Admin\Teachers::$1');
    });
    $routes->group('teachers_emails', function ($routes) {
        $routes->add('', 'Admin\TeachersEmails::index');
        $routes->add('(:any)', 'Admin\TeachersEmails::$1');
    });
    $routes->group('external_teachers', function ($routes) {
        $routes->add('', 'Admin\ExternalTeachers::index');
        $routes->add('(:any)', 'Admin\ExternalTeachers::$1');
    });
    $routes->group('settings', function ($routes) {
        $routes->add('', 'Admin\Settings::index');
        $routes->add('(:any)', 'Admin\Settings::$1');
    });
    $routes->group('users', function ($routes) {
        $routes->add('', 'Admin\Users::index');
        $routes->add('(:any)', 'Admin\Users::$1');
    });
    $routes->group('usertypes', function ($routes) {
        $routes->add('', 'Admin\Usertypes::index');
        $routes->add('(:any)', 'Admin\Usertypes::$1');
    });
    $routes->group('pages', function ($routes) {
        $routes->add('', 'Admin\Pages::index');
        $routes->add('(:any)', 'Admin\Pages::$1');
    });
    $routes->group('slider', function ($routes) {
        $routes->add('', 'Admin\Slider::index');
        $routes->add('(:any)', 'Admin\Slider::$1');
    });
    $routes->group('machines', function ($routes) {
        $routes->add('', 'Admin\Machines::index');
        $routes->add('(:any)', 'Admin\Machines::$1');
    });
    $routes->group('products', function ($routes) {
        $routes->add('', 'Admin\Products::index');
        $routes->add('(:any)', 'Admin\Products::$1');
    });
    $routes->group('accessories', function ($routes) {
        $routes->add('', 'Admin\Accessories::index');
        $routes->add('(:any)', 'Admin\Accessories::$1');
        $routes->add('(:any)/(:any)', 'Admin\Accessories::$1/$2');
    });
    $routes->group('custom_accessories', function ($routes) {
        $routes->add('', 'Admin\CustomAccessories::index');
        $routes->add('(:any)', 'Admin\CustomAccessories::$1');
        $routes->add('(:any)/(:any)', 'Admin\CustomAccessories::$1/$2');
    });
    $routes->group('codes', function ($routes) {
        $routes->add('', 'Admin\Codes::index');
        $routes->add('(:any)', 'Admin\Codes::$1');
    });
    $routes->group('audio', function ($routes) {
        $routes->add('', 'Admin\Audio::index');
        $routes->add('(:any)', 'Admin\Audio::$1');
    });
    $routes->group('notifications', function ($routes) {
        $routes->add('', 'Admin\Notifications::index');
        $routes->add('(:any)', 'Admin\Notifications::$1');
    });
    $routes->group('conversations', function ($routes) {
        $routes->add('', 'Admin\Conversations::index');
        $routes->add('(:any)', 'Admin\Conversations::$1');
    });
    $routes->group('test', function ($routes) {
        $routes->add('', 'Admin\Test::index');
        $routes->add('(:any)', 'Admin\Test::$1');
    });
    $routes->group('analytics', function ($routes) {
        $routes->add('', 'Admin\Analytics::index');
        $routes->add('hall-of-fame', 'Admin\Analytics::hall_of_fame');
        $routes->add('(:any)', 'Admin\Analytics::$1');
        $routes->add('(:any)/(:any)', 'Admin\Analytics::$1/$2');
    });
    $routes->group('duplicate_subscription_cleanup', function ($routes) {
        $routes->add('', 'Admin\DuplicateSubscriptionCleanup::index');
        $routes->add('(:any)', 'Admin\DuplicateSubscriptionCleanup::$1');
    });
    $routes->group('fix_specific_duplicates', function ($routes) {
        $routes->add('', 'Admin\FixSpecificDuplicates::index');
        $routes->add('(:any)', 'Admin\FixSpecificDuplicates::$1');
    });
    $routes->group('quick_duplicate_fix', function ($routes) {
        $routes->add('', 'Admin\QuickDuplicateFix::index');
        $routes->add('(:any)', 'Admin\QuickDuplicateFix::$1');
    });
});

$routes->add('admin', 'Admin\Login::index');

$routes->group('admin/login', function ($routes) {
	$routes->add('', 'Admin\Login::index');
	$routes->add('(:any)', 'Admin\Login::$1');
});
$routes->group('classes', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Classes::index');
	$routes->add('sess', 'Classes::sess');
    
	$routes->add('save_video_state', 'Classes::save_video_state');
	$routes->add('mark_as_watched', 'Classes::mark_as_watched');
	$routes->add('mark_as_viewed', 'Classes::mark_as_viewed');
	$routes->add('add_to_favs', 'Classes::add_to_favs');
	$routes->add('rate_class', 'Classes::rate_class');
	$routes->add('watch_later', 'Classes::watch_later');
	$routes->add('filter', 'Classes::filter');
	$routes->add('save', 'Classes::save');
	$routes->add('video_thumbnail', 'Classes::video_thumbnail');
	$routes->add('subscriber_from_teacher_info/(:any)', 'Classes::subscriber_from_teacher_info/$1');
	$routes->add('(:any)', 'Classes::slug/$1');
});
$routes->group('buy-rent', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'BuyRent::index');
	$routes->add('filter', 'BuyRent::filter');
	$routes->add('(:any)', 'BuyRent::slug/$1');
});
$routes->group('search', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Search::index');
	$routes->add('(:any)', 'Search::$1');
});
$routes->group('courses', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Courses::index');
	$routes->add('rate_course', 'Courses::rate_course');
	$routes->add('rate_course_video', 'Courses::rate_course_video');
	$routes->add('mark_as_viewed', 'Courses::mark_as_viewed');
	$routes->add('watch_later', 'Courses::watch_later');
	$routes->add('filter', 'Courses::filter');
	$routes->add('(:any)', 'Courses::slug/$1');
	$routes->add('(:any)/(:any)', 'Courses::slug/$1/$2');
});
$routes->group('exercises', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Exercises::index');
	$routes->add('sess', 'Exercises::sess');
	$routes->add('mark_as_viewed', 'Exercises::mark_as_viewed');
	$routes->add('mark_as_watched', 'Exercises::mark_as_watched');
	$routes->add('watch_later', 'Exercises::watch_later');
	$routes->add('add_to_favs', 'Exercises::add_to_favs');
	$routes->add('rate_exercise', 'Exercises::rate_exercise');
	$routes->add('filter', 'Exercises::filter');
	$routes->add('save', 'Exercises::save');
	$routes->add('video_thumbnail', 'Exercises::video_thumbnail');
	$routes->add('(:any)', 'Exercises::slug/$1');
});
$routes->group('videos', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	// $routes->add('', 'Videos::index');
	$routes->add('mark_as_viewed', 'Videos::mark_as_viewed');
	$routes->add('mark_as_watched', 'Videos::mark_as_watched');
	$routes->add('(:any)', 'Videos::slug/$1');
});
$routes->group('playlists', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Playlists::index');
	$routes->add('add_playlist', 'Playlists::add_playlist');
	$routes->add('remove_playlist/(:any)', 'Playlists::remove_playlist/$1');
	$routes->add('create_playlist', 'Playlists::create_playlist');
	$routes->add('my_playlists', 'Playlists::my_playlists');
	$routes->add('load_playlist/(:any)', 'Playlists::load_playlist/$1');
	$routes->add('my_playlists/(:any)', 'Playlists::my_playlists/$1');
    $routes->add('ajax_delete/(:any)/(:any)', 'Playlists::ajax_delete/$1/$2');
	$routes->add('add_class_to_playlist', 'Playlists::add_class_to_playlist');
	$routes->add('save', 'Playlists::save');
	$routes->add('sort_classes_table', 'Playlists::sort_classes_table');
	$routes->add('(:any)', 'Playlists::slug/$1');
	$routes->add('(:any)/(:any)', 'Playlists::slug/$1/$2');
});
$routes->group('collections', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Collections::index');
	$routes->add('(:any)', 'Collections::slug/$1');
});
$routes->group('liveevents', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'LiveEvents::index');
	$routes->add('add_to_favs', 'LiveEvents::add_to_favs');
	$routes->add('subscribe_to_event', 'LiveEvents::subscribe_to_event');
	$routes->add('(:any)', 'LiveEvents::slug/$1');
});
$routes->group('teachers', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Teachers::index');
	$routes->add('calendar', 'Teachers::calendar');
	$routes->add('calendar/(:any)', 'Teachers::calendar/$1');
	$routes->add('routine_tv/(:any)', 'Teachers::routine_tv/$1');
	$routes->add('ajax_date', 'Teachers::ajax_date');
	$routes->add('ajax_calendar', 'Teachers::ajax_calendar');
	$routes->add('(:any)', 'Teachers::slug/$1');
});
$routes->group('onboarding', function ($routes) {
	$routes->add('', 'Surveys::index');
	$routes->add('(:any)', 'Surveys::index');
});
$routes->group('surveys', function ($routes) {
	$routes->add('', 'Surveys::index');
	$routes->add('(:any)', 'Surveys::$1');
});
$routes->group('notifications', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Notifications::index');
	$routes->add('mark_as_seen', 'Notifications::mark_as_seen');
	$routes->add('mark_as_seen_hidden', 'Notifications::mark_as_seen_hidden');
	$routes->add('get_users_notifications', 'Notifications::get_users_notifications');
	$routes->add('check_new_notifications', 'Notifications::check_new_notifications');
});
$routes->group('asksebastien', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'AskSebastien::index');
	$routes->add('new_message', 'AskSebastien::new_message');
	$routes->add('mark_as_useful', 'AskSebastien::mark_as_useful');
	$routes->add('msg_mark_as_seen', 'AskSebastien::msg_mark_as_seen');
	$routes->add('get_users_conversations', 'AskSebastien::get_users_conversations');
	$routes->add('get_users_conversations/(:any)', 'AskSebastien::get_users_conversations/$1');
	$routes->add('check_new_conversations', 'AskSebastien::check_new_conversations');
});
$routes->group('account', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Account::index');
	$routes->add('(:any)', 'Account::$1');
	$routes->add('(:any)/(:any)', 'Account::$1/$2');
	$routes->add('(:any)/(:any)/(:any)', 'Account::$1/$2/$3');
	$routes->add('(:any)/(:any)/(:any)/(:any)', 'Account::$1/$2/$3/$4');
});
$routes->group('comments', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'Comments::index');
	$routes->add('(:any)', 'Comments::$1');
});
$routes->group('help-center', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	$routes->add('', 'HelpCenter::index');
	$routes->add('(:any)', 'HelpCenter::$1');
});
$routes->group('api', function ($routes) {
	$routes->add('', 'Api::index');
	$routes->add('(:any)', 'Api::$1');
    $routes->add('(:any)/(:any)', 'Api::$1/$2');
});
$routes->group('shop', ['filter' => 'basicAuthFilter:dual,noreturn,blocked'], function ($routes) {
	// $routes->add('', 'Events::index');
	$routes->add('get_products', 'Shop::get_products');
	$routes->add('category/(:any)', 'Shop::category/$1');
	$routes->add('show_all_products', 'Shop::show_all_products');
	$routes->add('cart', 'Shop::cart');
	$routes->add('add_to_cart', 'Shop::add_to_cart');
	$routes->add('remove_from_cart', 'Shop::remove_from_cart');
	$routes->add('empty_cart', 'Shop::empty_cart');
	$routes->add('update_cart', 'Shop::update_cart');
	$routes->add('search', 'Shop::search');
	$routes->add('(:any)', 'Shop::slug/$1');
});

$routes->add('products', 'Products::index');
$routes->add('products/all_products_collect', 'Products::all_products_collect');

$routes->add('check_code', 'Pages::check_code');
$routes->add('cron', 'Cron::index');
$routes->add('cron/(:any)', 'Cron::$1');
$routes->add('emails', 'Emails::index');
$routes->add('login/(:any)', 'Login::$1');
$routes->add('thank-you', 'Register::thankyou');
$routes->add('subscribe', 'Register::subscribe');
$routes->add('subscribe-confirmation', 'Register::subscribe_confirmation');
$routes->add('subscribe-payment', 'Register::subscribe_payment');
$routes->add('register/(:any)', 'Register::$1');
$routes->add('register/(:any)/(:any)', 'Register::$1/$2');
$routes->add('stripe/webhook', 'StripeWebhook::handle');
$routes->add('pages/get_shopify_collection/(:any)', 'Pages::get_shopify_collection/$1');

$routes->add('(:any)', 'Pages::slug/$1');

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */

if (file_exists(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php'))
{
	require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
