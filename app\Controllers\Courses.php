<?php namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class Courses extends Frontcontroller
{
	use ResponseTrait;

	public function __construct() {
		parent::__construct();
		$this->model = model('CoursesModel');
    }

    public function index()
    {
        $db = \Config\Database::connect();

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();

        $data['all_courses'] = $this->model->all_courses(0, 9);
        $data['machines'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countMachine
                                            FROM machines
                                            LEFT OUTER JOIN (SELECT course_machine, count(*) AS cnt FROM courses_machine GROUP BY course_machine) x ON x.course_machine = machines.id
                                            HAVING countMachine > 0
                                            ORDER BY sort ASC
                                        ')->getResultArray();
        $data['all_teachers'] = $db->query('SELECT *, COALESCE(x.cnt,0) AS countClasses
                                            FROM teachers
                                            LEFT OUTER JOIN (SELECT teacher, count(*) AS cnt FROM courses WHERE deleted_at IS NULL GROUP BY teacher) x ON x.teacher = teachers.id
                                            WHERE status IN (0,1)
                                            AND deleted_at IS NULL
                                            HAVING countClasses > 0
                                            ORDER BY firstname ASC
                                        ')->getResultArray();

		$data['current']['image'] = base_url() . 'images/courses1.jpg';
		$data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Courses | Lagree On Demand';
		$data['current']['seo_description'] = "Our certified teachers train all courses based on Lagree method available online 24/7! Get Lagree On Demand today!";
		echo view('front/courses/index_view', $data);
    }

    public function slug($slug = '', $video_id = "")
    {
		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;
        $data['nums'] = create_session_nums();
        $shopify_model = model('ShopifyModel');
        $comments_model = model('CommentsModel');

        if($video_id == ""){
            $data['current'] = $this->model->current($slug);
            if($data['current']['id'] == NULL){
                return redirect()->to('/');
            }
            $data['comments'] = $comments_model->get_comments($data['current']['id'], 'courses');
            $data['count_comments'] = 0;

            if(!empty($data['comments']) AND count($data['comments']) > 0){
                $data['count_comments'] = count($data['comments']);
                foreach($data['comments'] as $key => $single){
                    $data['comments'][$key]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'courses'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                    if(!empty($data['comments'][$key]['reply']) AND count($data['comments'][$key]['reply']) > 0){
                        $data['comments'][$key]['count_replys'] = count($data['comments'][$key]['reply']);
                        $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply']);

                        foreach($data['comments'][$key]['reply'] as $key2 => $single2){
                            $data['comments'][$key]['reply'][$key2]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single2['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'courses'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                            if(!empty($data['comments'][$key]['reply'][$key2]['reply']) AND count($data['comments'][$key]['reply'][$key2]['reply']) > 0){
                                $data['comments'][$key]['count_replys'] = $data['comments'][$key]['count_replys'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                                $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                            }
                        }
                    }
                }
            }
            
            if($data['current']['all_course_machines_shopify'] != NULL){
                $shopify_machines = explode(',', $data['current']['all_course_machines_shopify']);
                $data['shopify_machines_titles'] = explode(',', $data['current']['all_course_machines']);
                foreach($shopify_machines as $product){
                    if($product != ''){
                        $data['shopify_machines'][] = $shopify_model->single_product($product);
                    }
                }
            }

            return view('front/courses/single_view', $data);
        }else{
            $CoursesVideosModel = model('CoursesVideosModel');
            $data['current'] = $this->model->current($slug);

            $data['current']['video_state'] = $this->model->query("SELECT * FROM video_state WHERE video_id = '" . $data['current']['slug'] . "' AND user_id = " . (isset($data['logged_user']) ? $data['logged_user']['id'] : 0) . " AND video_type = 'courses_videos'")->getRowArray();

            $data['current_video'] = $CoursesVideosModel->current_video($video_id);
            
            if($data['current_video']['all_course_machines_shopify'] != NULL){
                $shopify_machines = explode(',', $data['current_video']['all_course_machines_shopify']);
                $data['shopify_machines_titles'] = explode(',', $data['current_video']['all_course_machines']);
                foreach($shopify_machines as $product){
                    if($product != ''){
                        $data['shopify_machines'][] = $shopify_model->single_product($product);
                    }
                }
            }
            if($data['current_video']['all_course_accessories_shopify'] != NULL){
                $shopify_accessories = explode(',', $data['current_video']['all_course_accessories_shopify']);
                $data['shopify_accessories_titles'] = explode(',', $data['current_video']['all_course_accessories']);
                foreach($shopify_accessories as $key => $product){
                    if($product != ''){
                        $data['shopify_accessories'][] = $shopify_model->single_product($product);
                    }
                }
            }

            $data['comments'] = $comments_model->get_comments($data['current_video']['id'], 'courses_videos');
            $data['count_comments'] = 0;

            if(!empty($data['comments']) AND count($data['comments']) > 0){
                $data['count_comments'] = count($data['comments']);
                foreach($data['comments'] as $key => $single){
                    $data['comments'][$key]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'courses_videos'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                    if(!empty($data['comments'][$key]['reply']) AND count($data['comments'][$key]['reply']) > 0){
                        $data['comments'][$key]['count_replys'] = count($data['comments'][$key]['reply']);
                        $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply']);

                        foreach($data['comments'][$key]['reply'] as $key2 => $single2){
                            $data['comments'][$key]['reply'][$key2]['reply'] = $comments_model->query("SELECT comments.*,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(teachers.firstname, ' ', teachers.lastname),
                                                                                        CONCAT(subscribers.firstname, ' ', subscribers.lastname)
                                                                                    ) as user_name,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        CONCAT(LEFT(teachers.firstname, 1), LEFT(teachers.lastname, 1)),
                                                                                        CONCAT(LEFT(subscribers.firstname, 1), LEFT(subscribers.lastname, 1))
                                                                                    ) as user_initials,
                                                                                    IF(comments.user_id = comments.teacher_replied, 
                                                                                        teachers.image,
                                                                                        subscribers.image
                                                                                    ) as user_image
                                                                                    FROM comments
                                                                                    LEFT JOIN subscribers on subscribers.id = comments.user_id
                                                                                    LEFT JOIN teachers on teachers.id = comments.teacher_replied
                                                                                    WHERE comments.parent = " . $single2['id'] . "
                                                                                    AND comments.status = 0
                                                                                    AND comments.type = 'courses_videos'
                                                                                    ORDER BY comments.date desc
                                                                                ")->getResultArray();
                            if(!empty($data['comments'][$key]['reply'][$key2]['reply']) AND count($data['comments'][$key]['reply'][$key2]['reply']) > 0){
                                $data['comments'][$key]['count_replys'] = $data['comments'][$key]['count_replys'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                                $data['count_comments'] = $data['count_comments'] + count($data['comments'][$key]['reply'][$key2]['reply']);
                            }
                        }
                    }
                }
            }

            $data['prev_next'] = $CoursesVideosModel->prev_next($data['current']['id'], $data['current_video']['id']);

            if($data['current']['id'] == NULL){
                return redirect()->to('/');
            }
            return view('front/courses/single_video_view', $data);
        }
    }
    // public function mark_as_watched()
    // {
    //     $subscribersWatched_model = model('SubscribersWatchedModel');
	// 	$request = service('request');
    //     $data = $request->getPost();
    //     $save_watched = array('class_id' => $data['class'], 'subscriber_id' => $data['user'], 'date' => date('Y-m-d'));

    //     $response['watched'] = $subscribersWatched_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

    //     $response['success'] = FALSE;
    //     if(empty($response['watched'])){
    //         $response['success'] = $subscribersWatched_model->save($save_watched);
    //     }

	// 	return $this->respond($response);
    // }

    public function mark_as_viewed()
    {
        $CoursesViewModel = model('CoursesViewModel');
		$request = service('request');
        $data = $request->getPost();
        if($data['type'] == 'course_video'){
            $save_viewed = array('video_id' => $data['class'], 'user_id' => $data['user'], 'date' => date('Y-m-d'));
    
            $response['watched'] = $CoursesViewModel->where(["course_video_id" => $data['class'], "user_id" => $data['user']])->first();
    
            $response['success'] = FALSE;
            if(empty($response['watched'])){
                $response['success'] = $CoursesViewModel->save($save_viewed);
            }
        }else{
            $save_viewed = array('course_video_id' => $data['class'], 'user_id' => $data['user'], 'date' => date('Y-m-d'));
    
            $response['watched'] = $CoursesViewModel->where(["course_video_id" => $data['class'], "user_id" => $data['user']])->first();
    
            $response['success'] = FALSE;
            if(empty($response['watched'])){
                $response['success'] = $CoursesViewModel->save($save_viewed);
            }
        }

		return $this->respond($response);
    }
    public function watch_later()
    {
        $SubscribersFavs_model = model('SubscribersFavsModel');
        $data = $this->request->getPost();

        $save_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user'], 
            'type' => $data['type'],
            'date' => date('Y-m-d')
        ];
        $remove_favs = [
            'class_id' => $data['class'], 
            'subscriber_id' => $data['user']
        ];

        $response['favs'] = $SubscribersFavs_model->where(["class_id" => $data['class'], "subscriber_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $SubscribersFavs_model->save($save_favs);
            $response['errors'] = $SubscribersFavs_model->errors();
            $response['success'] = TRUE;
        }else{
            $response['status'] = $SubscribersFavs_model->where($remove_favs)->delete();
            $response['errors'] = $SubscribersFavs_model->errors();
        }

		return $this->respond($response);
    }

    public function mark_rest_day()
    {
        $CoursesViewModel = model('CoursesViewModel');
		$request = service('request');
        $data = $request->getPost();
        $save_viewed = array('course_video_id' => $data['class'], 'user_id' => $data['user'], 'date' => date('Y-m-d'));

        $response['watched'] = $CoursesViewModel->where(["course_video_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['watched'])){
            $response['success'] = $CoursesViewModel->save($save_viewed);
        }

		return $this->respond($response);
    }

    public function save_video_state()
    {
        $request = service('request');
        $data = $request->getPost();
		$VideoStateModel = model('VideoStateModel');

        // $save_data = [
        //     'user_id'    => $data['user_id'],
        //     'video_id'   => $data['video_id'],
        //     'video_time' => $data['video_time'],
        //     'video_type' => $data['video_type']
        // ];
        $response['save_data'] = $data;

        $response['success'] = FALSE;

		if ($data['user_id'] != '' AND $data['video_id'] != '' AND (int)$data['video_time'] > 0){
			$response['success'] = $VideoStateModel->save($data);
            $response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $VideoStateModel->getInsertID();
		}
        return $this->respond($response);
    }

    public function rate_course()
    {
        $CoursesLikeModel = model('CoursesLikeModel');
		$request = service('request');
        $data = $request->getPost();
        $save_like = array('course_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['data'] = $data;
        $response['favs'] = $CoursesLikeModel->where(["course_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $CoursesLikeModel->save($save_like);
            $response['liked'] = TRUE;
            $response['success'] = TRUE;
        }else{
            $response['status'] = $CoursesLikeModel->delete($response['favs']['id']);
            $response['success'] = TRUE;
            $response['liked'] = FALSE;
        }
		return $this->respond($response);
    }
    public function rate_course_video()
    {
        $CoursesVideosLikeModel = model('CoursesVideosLikeModel');
		$request = service('request');
        $data = $request->getPost();
        $save_like = array('video_id' => $data['class'], 'user_id' => $data['user'], 'rate' => $data['rate'], 'date' => date('Y-m-d'));

        $response['data'] = $data;
        $response['favs'] = $CoursesVideosLikeModel->where(["video_id" => $data['class'], "user_id" => $data['user']])->first();

        $response['success'] = FALSE;
        if(empty($response['favs'])){
            $response['status'] = $CoursesVideosLikeModel->save($save_like);
            $response['liked'] = TRUE;
            $response['success'] = TRUE;
        }else{
            $response['status'] = $CoursesVideosLikeModel->delete($response['favs']['id']);
            $response['success'] = TRUE;
            $response['liked'] = FALSE;
        }
		return $this->respond($response);
    }
    public function save()
    {
        $NotificationsModel = model('NotificationsModel');
		$validation =  \Config\Services::validation();
		// $rules = $this->model->validationRules;
        $rules = [
            'title'         => 'required|min_length[2]',
            'slug'          => 'required|alpha_dash|is_unique[courses.slug,id,{id}]',
            'video'         => 'required',
            'machine'       => 'required',
            'difficulty'    => 'required',
            'teacher'       => 'required',
        ];
		$data = $this->request->getPost();
		$validation->reset();
		$validation->setRules($rules);
		if (!$validation->run($data)){
			// handle validation errors
			//$prepared['validation_list'] = $validation->listErrors();
			//$response['message'] = implode('</br>', $this->model->errors());
			$response['message'] = implode('</br>', $validation->getErrors());
			$response['json'] = $validation->getErrors();
		}else{
			$response['message'] = 'Class successfully saved';
			$files = $this->request->getFiles();
			$response['files'] = $files;
			if (isset($files['image']) AND $files['image']->isValid()){
				$file = $files['image'];
				$response['ClientExtension'] = $file->getClientExtension();
				$response['guessExtension'] = $file->guessExtension();
				$response['getClientMimeType'] = $file->getClientMimeType();
				$response['getMimeType'] = $file->getMimeType();
				$name = $file->getRandomName();
				$file->move(ROOTPATH . 'public/uploads/courses', $name);
				// \Config\Services::image()
				// 	->withFile(ROOTPATH . 'public/uploads/courses/' . $name)
				// 	// ->resize(1000, 562, true, 'width')
				// 	->save(ROOTPATH . 'public/uploads/courses/' . $name, 98);
				$data['image'] = 'uploads/courses/' . $name;
			}
            // $response['img_removed'] = $data['image_removed'];
            // return $this->respond($response);
            // die();
            if($data['image_removed'] == 1){
                $data['image'] = "";
            }
            unset($data['image_removed']);

			$response['success'] = $this->model->save($data);
			$response['inserted_id'] = $data['id'] > 0 ? $data['id'] : $this->model->getInsertID();
            !isset($data['accessories']) ? $data['accessories'] = [] : '';
            !isset($data['springs']) ? $data['springs'] = [] : '';
            !isset($data['body_parts']) ? $data['body_parts'] = [] : '';
            !isset($data['machine']) ? $data['machine'] = [] : '';

            foreach($data as $key => $single_field){
                if(is_array($single_field)){
                    $db      = \Config\Database::connect();
                    $builder = $db->table('courses_' . $key);
                    $builder->delete(['class_id' => $data['id']]);
                    $fields = array();
                    foreach($single_field as $k => $v){
                        $fields[] = array(
                            'class_id' => $response['inserted_id'],
                            'class_' . $key => $v,
                            'date' => date('Y-m-d'),
                        );
                    }
                    if (count($fields) > 0){
                        $builder->insertBatch($fields);
                    }
                }
            }
		}
		$response['data'] = $data;
		//echo json_encode($response);
		return $this->respond($response);
    }
    public function filter()
    {
		$filter_data = $this->request->getPost();
		// $filter_data['order'] = 'classes.created_at DESC';

		$data['logged_user'] = $this->user;
		$data['settings'] = $this->settings;

		$data['current']['image'] = base_url() . 'images/classes1.jpg';
		$data['current']['seo_title'] = 'Online Micro, Mini, and Megaformer Courses | Lagree On Demand';
		$data['current']['seo_description'] = "Our certified teachers train all courses based on Lagree method available online 24/7! Get Lagree On Demand today!";
		$data['current']['seo_keywords'] = 'Lagree On Demand Courses';

        $data['all_classes'] = $this->model->filter_courses($filter_data);

		$response['view'] = view('front/courses/ajax-filter_view', $data);
		$response['show_more'] = (count($data['all_classes']) < 6) ? FALSE : TRUE;

        return $this->respond($response);
    }

    public function sess()
    {
        $seller = $this->teacher_info(21);

        // session()->remove('per_page');
        echo '<pre>';
        var_dump($seller);
        echo round((4.99 * 0.7), 2) * 100;
        echo '</pre>';
    }
}