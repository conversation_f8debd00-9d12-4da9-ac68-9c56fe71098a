<!DOCTYPE html>
<html lang="en">
<head>

<?php echo view('admin/templates/head.php'); ?>
<style>
.set_sort_byy.selected {
	color: #000 !important;
}
</style>
</head>
<body class="homepage transparent">

<main id="site-root">
    <?php echo view('admin/templates/left-menu.php'); ?>
    <div class="main-content">
        <div class="container pb-100 mb-5 border-bottom">
            <div class="flex aic jcsb minH45 page-title">
                <h1 class="h3">MODEL HISTORY: <?php echo (isset($models_history[0]['model']) AND $models_history[0]['model'] != '') ? $models_history[0]['model'] : ''; ?></h1>
                <a href="admin/models" class="btn btn-border white-bg black ml-auto" title="Cancel">Back</a>
            </div>
            <hr class="mt-0 mb-3">
            <div class="flex aic jcsb">
                <h5 class="num_of_items text-capitalize f-12 normal"><?php echo $models_count == 1 ? $models_count . ' Class Recorded' : $models_count . ' Classes Recorded'; ?></h5>
                <div class="dropdown d-inline-block ml-auto">
                    <span class="dropdown-button f-12 midGray" data-dropdown=""><?php echo $sort_by == 0 ? "Paid" : "Unpaid"; ?> <i class="arrow-down ml-05"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="admin/models/history/<?php echo (isset($models_id) AND $models_id != '') ? $models_id : ''; ?>/1/0" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 0 ? 'selected' : ''; ?>" title="">Paid</a></li>
                        <li><a href="admin/models/history/<?php echo (isset($models_id) AND $models_id != '') ? $models_id : ''; ?>/1/1" class="set_sort_byy link link-midGray midGray <?php echo $sort_by == 1 ? 'selected' : ''; ?>" title="">Unpaid</a></li>
                    </ul>
                </div>
                <div class="search-container">
                    <form action="<?php echo current_url(); ?>" method="POST" class="search-form <?php echo (isset($search_term) AND $search_term != '') ? 'show' : ''; ?>">
                        <input type="text" name="search_term" class="seach-input" value="<?php echo isset($search_term) ? $search_term : ''; ?>">
                        <button type="submit" class="search-button"><img src="admin_assets_new/images/search-icon.svg" alt="" class="img-fluid" /></button>
                    </form>
                </div>
            </div>
            <hr class="mt-3 mb-25">
            <div class="flex aic jcsb">
                <!-- <div class="flex aic ml-2 select-fields">
                    <div class="checkbox contact-forms">
                        <input type="checkbox" class="check_all_table" id="select_all">
                        <label for="select_all" class="f-12 midGray d-inline-block pl-4">Select All</label>
                    </div>
                    <span class="deselect_checked" style="display: none">
                        <span class="ml-3 f-12 link midGray">Deselect (<span class="checked-amount">2</span>)</span>
                    </span>
                    <span class="delete_checked" style="display: none">
                        <span class="ml-3 f-12 link link-red flex aic delete_multiple" data-table="teachers" data-popup="delete-popup">Delete (<span class="checked-amount">2</span>)</span>
                    </span>
                </div> -->

                <div class="col-name mx-2 w100 flex aic jcsb">
                  <p class="f-12 medium midGray mr-6 pr-05">NAME</p>
                  <p class="f-12 medium midGray ml-6 mr-1 pr-05">STATUS</p>
                </div>

            </div>
            <hr class="mt-25 mb-0">
            <div class="row big-gap">
                <div class="col-12">
                    <div class="table rows-with-borders noimg-table">
<?php
foreach($models_history as $single){
?>
                        <div class="table-row" data-row-id="row_<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">
                            <div class="class-item flex aic">
                                <!-- <div class="checkbox contact-forms">
                                    <input type="checkbox" class="" id="class<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" data-remove data-id="<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>" />
                                    <label for="class<?php // echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>">&nbsp;</label>
                                </div> -->
                                <div class="flex flex-column light">
                                    <a href="admin/classes/edit/<?php echo (isset($single['class_id']) AND $single['class_id'] != '') ? $single['class_id'] : ''; ?>" target="_blank" class="most-title medium mb-05"><?php echo (isset($single['date']) AND $single['date'] != '') ? $single['date'] : ''; ?> @ <?php echo (isset($single['time']) AND $single['time'] != '') ? $single['time'] : ''; ?></a>
                                    <span class="midGray f-12 normal">
                                        <?php echo (isset($single['model']) AND $single['model'] != '') ? 'Model: ' . $single['model'] : ''; ?>
                                    </span>
                                </div>
                                <div class="flex aic jcr ml-auto">
                                    <span class="f-12 normal teacherdate flex aic jcr">
                                        <?php if(isset($single['paid']) AND in_array($single['paid'], [1,2,4])){ ?>
                                        <a href="javascript:;" onclick="mark_as_paid(<?php echo (isset($single['id']) AND $single['id'] != '') ? $single['id'] : ''; ?>)" class="link link-midGray f-10 midGray text-underline mr-2">Mark as paid</a>
                                        <?php } ?>
                                        <span class="flex-inline aic jcc <?php echo (isset($single['paid']) AND in_array($single['paid'], [1,2,4,5])) ? ((isset($single['paid']) AND $single['paid'] == 5) ? 'lightGray-bg midGray' : 'lightRed-bg normalRed') : 'lightGreen-bg textGreen'; ?> px-15 py-1 rounded medium">
                                            <?php echo (isset($single['paid']) AND in_array($single['paid'], [1,2,4,5])) ? ((isset($single['paid']) AND $single['paid'] == 5) ? 'Cancelled' : 'Unpaid') : 'Paid'; ?>
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
            <div class="pagination flex aic jcsb f-12">
                <div class="flex aic">
                    <?php echo (($page * session('per_page')) - session('per_page')) + ($models_count > 0 ? 1 : 0); ?>-<?php echo (($page * session('per_page')) - session('per_page')) + count($models_history); ?><span class="midGray mx-1">of <?php echo $models_count; ?></span>
                    <a href="admin/models/history/<?php echo $models_id; ?>/<?php echo $page > 1 ? $page - 1 : 1; ?>/<?php echo $sort_by; ?>" class="table-arrow py-2 pl-1 pr-05 <?php echo $page == 1 ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-left.svg" alt="" class="img-fluid" /></a>
                    <a href="admin/models/history/<?php echo $models_id; ?>/<?php echo $page + 1; ?>/<?php echo $sort_by; ?>" class="table-arrow py-2 px-1 <?php echo ((count($models_history) < session('per_page')) OR (((($page * session('per_page')) - session('per_page')) + count($models_history)) == $models_count)) ? 'disabled' : ''; ?>"><img src="admin_assets_new/images/arrow-right.svg" alt="" class="img-fluid" /></a>
                </div>
                <div class="dropdown d-inline-block">
                    <span class="midGray">Per Page: </span>
                    <span class="dropdown-button" data-dropdown=""><?php echo session('per_page'); ?> <i class="arrow-down ml-1"></i></span>
                    <ul class="dropdown-menu drop-right row-vertical">
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">10</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">25</a></li>
                        <li><a href="javascript:;" class="link link-darkGray darkGray per_page" title="">50</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<?php echo view('admin/templates/popups.php'); ?>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script>

</script>
<script src="admin_assets_new/js/app.js?v=<?php echo $_ENV['version']; ?>"></script>
<script>
var current_page = '<?php $uri = service('uri'); echo $uri->getSegment(2); ?>';
function mark_as_paid(event_id){
    $.ajax({
        type: 'POST',
        url: 'admin/models/mark_paid',
        data: {
            id: event_id
        },
        dataType: 'json',
        success: function (data) {
            console.log(data);
            console.log('Success');
            if(data.success){
                window.location.reload();                
            }
        },
        error: function (request, status, error) {
            console.log('PHP Error');
        }
    });
}
</script>
</body>
</html>