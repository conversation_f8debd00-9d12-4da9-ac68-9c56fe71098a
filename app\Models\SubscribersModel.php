<?php namespace App\Models;

use CodeIgniter\Model;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;

class SubscribersModel extends Model
{
    protected $table = 'subscribers';
	protected $allowedFields = ['firstname', 'lastname', 'image', 'stripe_customer', 'stripe_subscription', 'email', 'fb_id', 'google_id', 'google_token', 'password', 'subscription_type', 'subscription_status', 'status', 'new_class_notif', 'new_od_class_notif', 'new_collection_notif', 'new_liveevents_notif', 'new_teacher_notif', 'new_staff_playlist_notif', 'class_approved_notif', 'class_rejected_notif', 'class_bought_notif', 'class_rated_notif', 'payout_request_notif', 'first_login', 'onboarding', 'first_video', 'second_video'];
    protected $returnType     = 'array';
    // protected $protectFields = false; // to disable allowed fields

    protected $primaryKey = 'id';

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [
        'firstname'     => [
            'label'  => 'Firstname',
            'rules'  => 'required|alpha_numeric_space|min_length[2]',
        ],
        'lastname'     => [
            'label'  => 'Lastname',
            'rules'  => 'required|alpha_numeric_space|min_length[2]',
        ],
        'password'     => [
            'label'  => 'Password',
            'rules'  => 'required|min_length[8]',
        ],
        'email'     => [
            'label'  => 'Email',
            'rules'  => 'required|valid_email|is_unique[subscribers.email,id,{id}]',
            'errors' => [
                'is_unique' => 'That email has already been taken. Please choose another.',
            ],
        ],
    ];
    protected $validationMessages = [];
    protected $skipValidation     = false;

	protected $beforeInsert = ['prepare_data'];
	//protected $beforeUpdate = ['prepare_data'];

    public function all_subscribers($start = 0, $limit = 10, $search_term = NULL, $order = "subscribers.firstname ASC"){
        $limit_size =  ($limit != 0) ? " LIMIT $start, $limit" : "";
        if($search_term != NULL){
            $words = explode(" ", $search_term);
            $string = array_map(function(&$word){
                return "+" . $word . "*";
            }, $words);

            $term = implode(" ",$string);
            // $search =  "AND (MATCH(classes.title) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.firstname) AGAINST('{$term}' IN BOOLEAN MODE) OR MATCH(teachers.lastname) AGAINST('{$term}' IN BOOLEAN MODE))";
            $search =  "(
                MATCH(firstname) AGAINST('+{$term}*' IN BOOLEAN MODE)
                OR
                MATCH(lastname) AGAINST('+{$term}*' IN BOOLEAN MODE)
                OR
                MATCH(email) AGAINST('+{$term}*' IN BOOLEAN MODE)
            )";

        }else{
            $search = "";
        }

        // $search =  ($search_term != NULL) ? "AND classes.title LIKE '%$search_term%'" : "";
        $data = $this->query("SELECT *
                            FROM subscribers
                            WHERE subscribers.deleted_at IS NULL
                            " . $search . "
                            ORDER BY " . $order . "
                            " . $limit_size . "
                        ")->getResultArray();
        return $data;
    }

    public function login_user(array $data)
	{
		$stripe_model = new StripeModel();
		$TeachersModel = new TeachersModel();
		$response['success'] = FALSE;
		$teacher = $TeachersModel->where(['email' => $data['email'], 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))))])->findAll();

        if(count($teacher) == 1){
            $t = $teacher[0]['id'];            
            $users = $this->where(['email' => $data['email']])->findAll();
            // check if teacher has user acc
            if (count($users) == 1){
                $subscription = [];
                $subscription['success'] = 1;
                $subscription['subscription']['plan']['interval'] = 'teacher';
                
                $response['user_id'] = $users[0]['id'];
                $response['subscription'] = 'active';
                $response['subscription_info'] = $subscription;
                $response['subscription_plan'] = 'teacher';
                $response['teacher'] = $t;
                $response['first_login'] = $users[0]['first_login'];    
                $response['success'] = TRUE;
                $save_data = [
                    'id' => $users[0]['id'],
                    'subscription_type' => 'Teacher',
                ];
                $response['user_subscription_type_updated'] = $this->save($save_data);

            }else{
                $save_data = [
                    'firstname' => $teacher[0]['firstname'],
                    'lastname' => $teacher[0]['lastname'],
                    'email' => $teacher[0]['email'],
                    'password' => $data['password'],
                    'image' => $teacher[0]['image'],
                    'subscription_type' => 'Teacher',
                    'status' => 2,
                    'first_login' => 1,
                    'onboarding' => 1,
                    'first_video' => 1,
                    'second_video' => 1,
                ];
                $response['user_created'] = $this->save($save_data);
                $response['user_created_errors'] = $this->errors();
                $response['inserted_id'] = $this->getInsertID();
                $new_users = $this->where(['id' => $response['inserted_id']])->first();
                $subscription['success'] = 1;
                $subscription['subscription']['plan']['interval'] = 'teacher';

                $response['user_id'] = $new_users['id'];
                $response['subscription'] = 'active';
                $response['subscription_info'] = $subscription;
                $response['subscription_plan'] = 'teacher';
                $response['teacher'] = $t;
                $response['first_login'] = 1;
                $response['success'] = TRUE;
            }
        }else{
            $t = 0;
            $users = $this->where(['email' => $data['email'], 'password' => '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))))])->findAll();
            if (count($users) == 1){
                $subscription = !empty($users[0]['stripe_subscription']) ? $stripe_model->retrieve_subscription($users[0]['stripe_subscription']) : 'none';
                $status = isset($subscription['subscription']['status']) ? $subscription['subscription']['status'] : 'none';
                $response['user_id'] = $users[0]['id'];
                $response['subscription'] = $status;
                $response['subscription_info'] = $subscription;
                $response['subscription_plan'] = isset($subscription['subscription']['plan']) ? $subscription["subscription"]['plan']['interval'] : 'none';

                $response['teacher'] = $t;
                $response['first_login'] = $users[0]['first_login'];
                // if($users[0]['first_login'] == 0){
                //     $user_data = array('id' => $users[0]['id'], 'first_login' => 1);
                //     $response['first_login_saved'] = $this->save($user_data);
                // }
    
                $response['success'] = TRUE;
            }else{
                $response['subscription'] = 'none';
                $response['subscription_info'] = 'none';
                $response['subscription_plan'] = 'none';
                $response['error'] = 'Bad username or password.';
            }
        }
		return $response;
	}

	public function my_favs($user_id)
	{
        $data = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            IF(subscribers_favs.subscriber_id = " . $user_id . ", 1, 0) AS inFavs,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            IF(classes.id IN (
                                SELECT * FROM (
                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes .class_id = classes.id)
                            LEFT JOIN subscribers_watched on subscribers_watched.class_id = classes.id
                            WHERE classes.deleted_at IS NULL
                            AND subscribers_favs.subscriber_id = " . $user_id . "
                            GROUP BY classes.id
                        ")->getResultArray();
        return $data;
	}

	public function bought_classes($user_id)
	{
        $data = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug,
                            COALESCE(x.cnt,0) AS countView,
                            COALESCE(y.rate,0) AS classRate,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            video_state.video_time as video_state,
                            IF(classes.teacher = " . (isset($_SESSION['teacher']) ? $_SESSION['teacher'] : 0) . ", 1, 0) AS own,
                            IF(subscribers_favs.subscriber_id = " . $user_id . ", 1, 0) AS inFavs,
                            IF(classes.id IN (
                                SELECT * FROM (
                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user_id . " AND (subscribers_classes.purchase_type IS NULL OR subscribers_classes.purchase_type = 'buy')
                                ) as classes_purchased
                            ), 1, 0) as purchased,
                            IF(classes.id IN (
                                SELECT * FROM (
                                        SELECT class_id FROM subscribers_classes WHERE subscriber_id = " . $user_id . " AND subscribers_classes.purchase_type = 'rent' AND DATE_ADD(subscribers_classes.date, INTERVAL 1 DAY) > CURDATE()
                                ) as classes_rented
                            ), 1, 0) as rented,
                            IF(classes.id IN (
                                    SELECT * FROM (
                                            SELECT class_id FROM subscribers_watched WHERE subscriber_id = " . $user_id . "
                                    ) as classes_watched
                            ), 1, 0) as watched
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN (SELECT class_id, AVG(rate) as rate FROM classes_rate GROUP BY class_id) y on y.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes .class_id = classes.id)
                            LEFT JOIN subscribers_watched on subscribers_watched.class_id = classes.id
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND subscribers_classes.subscriber_id = " . $user_id . "
                            AND (subscribers_classes.purchase_type IS NULL OR subscribers_classes.purchase_type = 'buy')
                            GROUP BY classes.id
                        ")->getResultArray();
        return $data;
	}

	public function watched_classes($user_id = 0, $page = 1)
	{
        $data = $this->query("SELECT classes.*, difficulty.title as diff, teachers.slug  as teach_slug,
                            COALESCE(x.cnt,0) AS countView,
                            IF((SELECT count(id) FROM classes_rate WHERE class_id = classes.id AND user_id = " . (isset($_SESSION['user']) ? $_SESSION['user'] : 0) . ") > 0, 1, 0) as liked,
                            (SELECT count(rate > 3) as rate FROM classes_rate WHERE class_id = classes.id GROUP BY class_id) as likeCount,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            DATE_ADD(MAX(subscribers_classes.date), INTERVAL 1 DAY) as expiry_rent_date,
                            subscribers_watched.date as date_watched,
                            subscribers_favs.id as watch_id,
                            video_state.video_time as video_state
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT OUTER JOIN classes_rate ON (classes_rate.class_id = classes.id AND classes_rate.user_id = " . $user_id . ")
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN subscribers_classes ON (subscribers_classes .class_id = classes.id)
                            LEFT JOIN subscribers_watched on subscribers_watched.class_id = classes.id
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . $user_id . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND subscribers_watched.subscriber_id = " . $user_id . "
                            GROUP BY classes.id
                            ORDER BY date_watched desc
                            LIMIT " . (($page-1)*10) . ", 10
                        ")->getResultArray();
        return $data;
	}

	public function watch_later($user_id = 0, $page = 1)
	{
        $data['classes'] = $this->query("SELECT classes.id, classes.title, classes.slug, classes.image, classes.video_thumb, classes.duration, difficulty.title as diff, teachers.slug  as teach_slug,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            'classes' as type,
                            video_state.video_time as video_state,
                            subscribers_favs.date as date_watched,
                            subscribers_favs.id as watch_id
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . $user_id . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND subscribers_favs.subscriber_id = " . $user_id . "
                            GROUP BY classes.id
                            ORDER BY date_watched desc
                            -- LIMIT " . (($page-1)*10) . ", 10
                        ")->getResultArray();
        $data['exercises'] = $this->query("SELECT exercises.id, exercises.title, exercises.slug, exercises.image, exercises.video_thumb, exercises.duration, difficulty.title as diff, teachers.slug  as teach_slug,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            GROUP_CONCAT(DISTINCT tempo.title ORDER BY tempo.sort ASC SEPARATOR ', ') AS all_class_tempo,
                            'exercises' as type,
                            video_state.video_time as video_state,
                            subscribers_favs.date as date_watched,
                            subscribers_favs.id as watch_id
                            FROM exercises
                            LEFT OUTER JOIN (SELECT exercise_id, count(*) as cnt FROM exercises_views GROUP BY exercise_id) x on x.exercise_id = exercises.id
                            LEFT JOIN exercises_machine ON exercises_machine.exercise_id = exercises.id
                            LEFT JOIN machines ON machines.id = exercises_machine.exercise_machine
                            LEFT JOIN exercises_tempo ON  exercises_tempo.exercise_id = exercises.id
                            LEFT JOIN tempo ON tempo.id = exercises_tempo.exercise_tempo
                            LEFT JOIN difficulty on difficulty.id = exercises.difficulty
                            LEFT JOIN teachers on teachers.id = exercises.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = exercises.id
                            LEFT JOIN video_state on (video_state.video_id = exercises.slug AND video_state.user_id = " . $user_id . " AND video_state.video_type = 'exercises')
                            WHERE exercises.deleted_at IS NULL
                            AND subscribers_favs.subscriber_id = " . $user_id . "
                            GROUP BY exercises.id
                            ORDER BY date_watched desc
                            -- LIMIT " . (($page-1)*10) . ", 10
                        ")->getResultArray();
        $data['courses'] = $this->query("SELECT courses_videos.id, courses_videos.course_id, courses_videos.title, courses_videos.slug, courses_videos.video_thumb, courses_videos.duration, difficulty.title as diff, teachers.slug  as teach_slug,
                            CONCAT(teachers.firstname, ' ', teachers.lastname)  as teach,
                            GROUP_CONCAT(DISTINCT machines.short_name SEPARATOR ', ') AS all_class_machines,
                            '' AS all_class_tempo,
                            'courses' as type,
                            courses.title as course_title,
                            courses.slug as course_slug,
                            video_state.video_time as video_state,
                            subscribers_favs.date as date_watched,
                            subscribers_favs.id as watch_id
                            FROM courses_videos
                            LEFT OUTER JOIN (SELECT course_video_id, count(*) as cnt FROM courses_videos_views GROUP BY course_video_id) x on x.course_video_id = courses_videos.id
                            LEFT JOIN courses ON courses.id = courses_videos.course_id
                            LEFT JOIN courses_machine ON courses_machine.course_id = courses.id
                            LEFT JOIN machines ON machines.id = courses_machine.course_machine
                            LEFT JOIN difficulty on difficulty.id = courses.difficulty
                            LEFT JOIN teachers on teachers.id = courses_videos.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = courses_videos.id
                            LEFT JOIN video_state on (video_state.video_id = courses_videos.slug AND video_state.user_id = " . $user_id . " AND video_state.video_type = 'courses_videos')
                            WHERE courses_videos.deleted_at IS NULL
                            AND subscribers_favs.subscriber_id = " . $user_id . "
                            GROUP BY courses_videos.id
                            ORDER BY date_watched desc
                            -- LIMIT " . (($page-1)*10) . ", 10
                        ")->getResultArray();

        $data['count_all'] = count($data['classes']) + count($data['exercises']) + count($data['courses']);

        return $data;
	}

	public function count_watched_classes($user_id)
	{
        $data = $this->query("SELECT classes.id
                            FROM classes
                            LEFT OUTER JOIN (SELECT class_id, count(*) as cnt FROM classes_views GROUP BY class_id) x on x.class_id = classes.id
                            LEFT JOIN classes_machine ON classes_machine.class_id = classes.id
                            LEFT JOIN machines ON machines.id = classes_machine.class_machine
                            LEFT JOIN classes_tempo ON  classes_tempo.class_id = classes.id
                            LEFT JOIN tempo ON tempo.id = classes_tempo.class_tempo
                            LEFT JOIN difficulty on difficulty.id = classes.difficulty
                            LEFT JOIN teachers on teachers.id = classes.teacher
                            LEFT JOIN subscribers_favs ON subscribers_favs.class_id = classes.id
                            LEFT JOIN video_state on (video_state.video_id = classes.slug AND video_state.user_id = " . $user_id . " AND video_state.video_type = 'classes')
                            WHERE classes.deleted_at IS NULL
                            AND subscribers_favs.subscriber_id = " . $user_id . "
                            GROUP BY classes.id
                        ")->getResultArray();
        return $data;
	}

	protected function prepare_data(array $data)
	{

		if (isset($data['data']['password']) AND $data['data']['password'] <> ''){
			$data['data']['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['data']['password']))));
		}else{
			unset($data['data']['password']);
		}

		if (isset($data['password']) AND $data['password'] <> ''){
			$data['password'] = '*'.strtoupper(hash('sha512',pack('H*',hash('sha512', $data['password']))));
		}else{
			unset($data['password']);
		}

		return $data;
	}
}